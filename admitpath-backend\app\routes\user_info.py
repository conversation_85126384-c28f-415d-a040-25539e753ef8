from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from ..database import get_db
from ..models.user import User, Student, Counselor
from ..models.counseling_session import CounselingSession, SessionStatus, SessionReview, SessionNotes, StudentSessionNotes
from ..models.student_packages import StudentPackageSubscription, PackageSubscriptionStatus
from ..models.payment import Payment, PaymentStatus
from ..models.user_connection import UserConnection
from ..models.direct_message import DirectMessage
from ..models.stripe_payment import StripePayment
from ..models.reminders import SessionReminder
from ..models.student_profile import StudentPersonalInfo, StudentEducationalBackground, StudentExpectedServices
from ..models.student_bank_details import StudentBankAccount
from ..models.counselor_profile import (
    CounselorPersonalInfo, CounselorEducation, CounselorProfessionalExperience,
    CounselorServices, CounselorDocuments, CounselorCounselingExperience,
    CounselorCommitment, CounselorSocialLinks, FeaturedCounselor
)
from ..models.counselor_bank_details import CounselorBankAccount
from ..models.counselor_services import CounselorServicesOffered
from ..models.counselor_packages import CounselorPackage, PackageItem
from ..models.counselor_availability import CounselorAvailability
from ..models.resource import Resource
from ..schemas.user_info import UserInfoResponse
from ..utils.auth import get_current_user
from typing import Optional
from zoneinfo import ZoneInfo
from datetime import datetime, timezone

router = APIRouter()

@router.get("/me", response_model=UserInfoResponse)
async def get_user_info(
    timezone: Optional[str] = None,  # Add optional timezone parameter
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get information about the currently logged-in user.
    This endpoint works for both students and counselors.
    If a timezone is provided, it will update the user's timezone preference.
    """
    # Get the complete user object with relationships
    user = db.query(User).filter(User.id == current_user.id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update timezone if provided and different from stored value
    if timezone and (user.timezone is None or user.timezone != timezone):
        try:
            ZoneInfo(timezone)
            user.timezone = timezone
            db.commit()
        except:
            pass
    
    # Get the appropriate profile based on user type
    if user.user_type == "student":
        profile = db.query(Student).filter(Student.user_id == user.id).first()
    else:  # counselor
        profile = db.query(Counselor).filter(Counselor.user_id == user.id).first()
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{user.user_type.capitalize()} profile not found"
        )
    
    # Use the custom method to create the response
    return UserInfoResponse.from_orm_custom(user, profile)

@router.get("/{user_id}", response_model=UserInfoResponse)
async def get_user_info_by_id(
    user_id: int,
    current_user = Depends(get_current_user),  # Keep authentication to ensure only logged-in users can access
    db: Session = Depends(get_db)
):
    """
    Get information about a specific user by their ID.
    This endpoint works for both students and counselors.
    """
    # Get the complete user object with relationships
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Get the appropriate profile based on user type
    if user.user_type == "student":
        profile = db.query(Student).filter(Student.user_id == user.id).first()
    else:  # counselor
        profile = db.query(Counselor).filter(Counselor.user_id == user.id).first()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{user.user_type.capitalize()} profile not found"
        )

    # Use the custom method to create the response
    return UserInfoResponse.from_orm_custom(user, profile)


@router.delete("/delete-account")
async def delete_account(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete the current user's account.

    This endpoint will:
    1. Check if the user has any upcoming sessions or active packages
    2. If yes, return an error with details
    3. If no, proceed with account deletion:
       - Delete user profile data
       - Anonymize completed sessions and reviews
       - Preserve payment history for records
       - Delete the user account
    """

    # Get the user and their profile
    user = db.query(User).filter(User.id == current_user.id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Get the appropriate profile
    if user.user_type == "student":
        profile = db.query(Student).filter(Student.user_id == user.id).first()
        profile_id = profile.id if profile else None
    else:  # counselor
        profile = db.query(Counselor).filter(Counselor.user_id == user.id).first()
        profile_id = profile.id if profile else None

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{user.user_type.capitalize()} profile not found"
        )

    # Check for upcoming sessions
    upcoming_sessions = []
    if user.user_type == "student":
        upcoming_sessions = db.query(CounselingSession).filter(
            and_(
                CounselingSession.student_id == profile_id,
                CounselingSession.status == SessionStatus.UPCOMING.value,
                CounselingSession.start_time > datetime.now(timezone.utc)
            )
        ).all()
    else:  # counselor
        upcoming_sessions = db.query(CounselingSession).filter(
            and_(
                CounselingSession.counselor_id == profile_id,
                CounselingSession.status == SessionStatus.UPCOMING.value,
                CounselingSession.start_time > datetime.now(timezone.utc)
            )
        ).all()

    if upcoming_sessions:
        session_details = [
            {
                "id": session.id,
                "event_name": session.event_name,
                "start_time": session.start_time.isoformat(),
                "end_time": session.end_time.isoformat()
            }
            for session in upcoming_sessions
        ]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Cannot delete account with upcoming sessions",
                "upcoming_sessions": session_details,
                "count": len(upcoming_sessions)
            }
        )

    # Check for sessions that need feedback (completed but not reviewed)
    sessions_needing_feedback = []
    current_time = datetime.now(timezone.utc)

    if user.user_type == "student":
        sessions_needing_feedback = db.query(CounselingSession).filter(
            and_(
                CounselingSession.student_id == profile_id,
                CounselingSession.status == SessionStatus.UPCOMING.value,
                CounselingSession.end_time < current_time  # Session has ended
            )
        ).all()

    if sessions_needing_feedback:
        session_details = [
            {
                "id": session.id,
                "event_name": session.event_name,
                "start_time": session.start_time.isoformat(),
                "end_time": session.end_time.isoformat()
            }
            for session in sessions_needing_feedback
        ]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Cannot delete account with sessions that need to be marked as completed",
                "sessions_needing_feedback": session_details,
                "count": len(sessions_needing_feedback)
            }
        )

    # Check for active package subscriptions
    if user.user_type == "student":
        # Check for student's active packages with remaining sessions
        active_packages = db.query(StudentPackageSubscription).filter(
            and_(
                StudentPackageSubscription.student_id == profile_id,
                StudentPackageSubscription.status == PackageSubscriptionStatus.ACTIVE.value
            )
        ).all()

        # Filter packages that still have unused sessions
        packages_with_remaining_sessions = []
        for pkg in active_packages:
            if pkg.service_hours:
                for _, hours_data in pkg.service_hours.items():
                    if hours_data.get("remaining", 0) > 0:
                        packages_with_remaining_sessions.append(pkg)
                        break

        if packages_with_remaining_sessions:
            package_details = [
                {
                    "id": pkg.id,
                    "package_id": pkg.package_id,
                    "status": pkg.status,
                    "start_date": pkg.start_date.isoformat() if pkg.start_date else None,
                    "end_date": pkg.end_date.isoformat() if pkg.end_date else None,
                    "remaining_sessions": {
                        service: hours["remaining"]
                        for service, hours in pkg.service_hours.items()
                        if hours.get("remaining", 0) > 0
                    }
                }
                for pkg in packages_with_remaining_sessions
            ]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Cannot delete account with active package subscriptions that have remaining sessions",
                    "active_packages": package_details,
                    "count": len(packages_with_remaining_sessions)
                }
            )

    else:  # counselor
        # Check for counselor's involvement in active packages
        # Use subquery to avoid DISTINCT on JSON columns
        package_ids_subquery = db.query(CounselingSession.package_subscription_id).filter(
            and_(
                CounselingSession.counselor_id == profile_id,
                CounselingSession.status == SessionStatus.UPCOMING.value,
                CounselingSession.package_subscription_id.isnot(None)
            )
        ).distinct().subquery()

        active_packages_as_counselor = db.query(StudentPackageSubscription).filter(
            and_(
                StudentPackageSubscription.id.in_(
                    db.query(package_ids_subquery.c.package_subscription_id)
                ),
                StudentPackageSubscription.status == PackageSubscriptionStatus.ACTIVE.value
            )
        ).all()

        if active_packages_as_counselor:
            package_details = [
                {
                    "id": pkg.id,
                    "package_id": pkg.package_id,
                    "student_id": pkg.student_id,
                    "status": pkg.status,
                    "start_date": pkg.start_date.isoformat() if pkg.start_date else None,
                    "end_date": pkg.end_date.isoformat() if pkg.end_date else None
                }
                for pkg in active_packages_as_counselor
            ]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Cannot delete account while involved in active package subscriptions with upcoming sessions",
                    "active_packages": package_details,
                    "count": len(active_packages_as_counselor)
                }
            )

    # Check for pending payments
    pending_payments = db.query(Payment).filter(
        and_(
            or_(
                Payment.student_id == profile_id if user.user_type == "student" else False,
                Payment.counselor_id == profile_id if user.user_type == "counselor" else False
            ),
            Payment.status == PaymentStatus.PENDING.value
        )
    ).all()

    if pending_payments:
        payment_details = [
            {
                "id": payment.id,
                "amount": payment.amount,
                "service_type": payment.service_type,
                "created_at": payment.created_at.isoformat()
            }
            for payment in pending_payments
        ]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Cannot delete account with pending payments",
                "pending_payments": payment_details,
                "count": len(pending_payments)
            }
        )

    # If we reach here, account can be safely deleted
    # Start the deletion process

    try:
        print(f"Starting deletion process for user {user.id} ({user.user_type})")

        # STEP 1: Delete all records that reference sessions first (deepest level)
        # Use synchronize_session=False for bulk deletes to avoid loading objects into memory

        # Delete session reminders for all sessions involving this user
        if user.user_type == "student":
            session_ids = db.query(CounselingSession.id).filter(CounselingSession.student_id == profile_id).all()
        else:
            session_ids = db.query(CounselingSession.id).filter(CounselingSession.counselor_id == profile_id).all()

        session_ids = [sid[0] for sid in session_ids]
        if session_ids:
            db.query(SessionReminder).filter(SessionReminder.session_id.in_(session_ids)).delete(synchronize_session=False)

        # Delete stripe payments for sessions involving this user
        if user.user_type == "student":
            db.query(StripePayment).filter(StripePayment.student_id == profile_id).delete(synchronize_session=False)
        else:
            # For counselors, delete stripe payments for their sessions
            if session_ids:
                db.query(StripePayment).filter(StripePayment.session_id.in_(session_ids)).delete(synchronize_session=False)

        # Delete session notes for all sessions involving this user
        if session_ids:
            db.query(SessionNotes).filter(SessionNotes.session_id.in_(session_ids)).delete(synchronize_session=False)

        # Delete student session notes
        if user.user_type == "student":
            db.query(StudentSessionNotes).filter(StudentSessionNotes.student_id == profile_id).delete(synchronize_session=False)
        else:
            # For counselors, delete student session notes for their sessions
            if session_ids:
                db.query(StudentSessionNotes).filter(StudentSessionNotes.session_id.in_(session_ids)).delete(synchronize_session=False)

        # Delete session reviews
        if user.user_type == "student":
            db.query(SessionReview).filter(SessionReview.student_id == profile_id).delete(synchronize_session=False)
        else:
            db.query(SessionReview).filter(SessionReview.counselor_id == profile_id).delete(synchronize_session=False)

        # STEP 2: Delete payments that reference this user
        if user.user_type == "student":
            db.query(Payment).filter(Payment.student_id == profile_id).delete(synchronize_session=False)
        else:
            db.query(Payment).filter(Payment.counselor_id == profile_id).delete(synchronize_session=False)

        # STEP 3: Delete direct messages
        if user.user_type == "student":
            db.query(DirectMessage).filter(DirectMessage.student_id == profile_id).delete(synchronize_session=False)
            db.query(DirectMessage).filter(DirectMessage.sender_id == user.id).delete(synchronize_session=False)
        else:
            db.query(DirectMessage).filter(DirectMessage.counselor_id == profile_id).delete(synchronize_session=False)
            db.query(DirectMessage).filter(DirectMessage.sender_id == user.id).delete(synchronize_session=False)

        # STEP 4: Delete user connections
        db.query(UserConnection).filter(UserConnection.user_id == user.id).delete(synchronize_session=False)

        # STEP 4.5: Delete resources uploaded by this user
        db.query(Resource).filter(Resource.uploaded_by == user.id).delete(synchronize_session=False)

        # STEP 5: Handle sessions - delete upcoming, anonymize completed
        # IMPORTANT: We need to handle package_subscription_id references before deleting packages
        if user.user_type == "student":
            # First, remove package_subscription_id from all sessions that reference this student's packages
            # Get all package subscription IDs for this student
            student_package_ids = db.query(StudentPackageSubscription.id).filter(
                StudentPackageSubscription.student_id == profile_id
            ).all()
            student_package_ids = [pkg_id[0] for pkg_id in student_package_ids]

            if student_package_ids:
                # Remove package_subscription_id from sessions that reference these packages
                sessions_with_packages = db.query(CounselingSession).filter(
                    CounselingSession.package_subscription_id.in_(student_package_ids)
                ).all()
                for session in sessions_with_packages:
                    session.package_subscription_id = None  # Remove foreign key reference

            # Delete upcoming sessions
            db.query(CounselingSession).filter(
                and_(
                    CounselingSession.student_id == profile_id,
                    CounselingSession.status == SessionStatus.UPCOMING.value
                )
            ).delete(synchronize_session=False)

            # Anonymize completed sessions (preserve for counselor's history)
            completed_sessions = db.query(CounselingSession).filter(
                and_(
                    CounselingSession.student_id == profile_id,
                    CounselingSession.status == SessionStatus.COMPLETED.value
                )
            ).all()
            for session in completed_sessions:
                session.student_id = None  # Remove foreign key reference

        else:  # counselor
            # For counselors, we need to handle sessions that reference packages they're involved in
            # Get all sessions where this counselor is involved and has a package_subscription_id
            counselor_sessions_with_packages = db.query(CounselingSession).filter(
                and_(
                    CounselingSession.counselor_id == profile_id,
                    CounselingSession.package_subscription_id.isnot(None)
                )
            ).all()

            # Remove package_subscription_id from these sessions to avoid foreign key issues
            for session in counselor_sessions_with_packages:
                session.package_subscription_id = None

            # Delete upcoming sessions
            db.query(CounselingSession).filter(
                and_(
                    CounselingSession.counselor_id == profile_id,
                    CounselingSession.status == SessionStatus.UPCOMING.value
                )
            ).delete(synchronize_session=False)

            # Anonymize completed sessions (preserve for student's history)
            completed_sessions = db.query(CounselingSession).filter(
                and_(
                    CounselingSession.counselor_id == profile_id,
                    CounselingSession.status == SessionStatus.COMPLETED.value
                )
            ).all()
            for session in completed_sessions:
                session.counselor_id = None  # Remove foreign key reference

        # STEP 6: Delete package subscriptions (for students) - now safe to delete
        if user.user_type == "student":
            db.query(StudentPackageSubscription).filter(
                StudentPackageSubscription.student_id == profile_id
            ).delete(synchronize_session=False)

        # STEP 7: Delete profile-specific data in correct order (child records first)
        if user.user_type == "student":
            # Delete student bank accounts
            db.query(StudentBankAccount).filter(StudentBankAccount.student_id == profile_id).delete(synchronize_session=False)

            # Delete expected services
            db.query(StudentExpectedServices).filter(StudentExpectedServices.student_id == profile_id).delete(synchronize_session=False)

            # Delete educational background
            db.query(StudentEducationalBackground).filter(StudentEducationalBackground.student_id == profile_id).delete(synchronize_session=False)

            # Delete personal info
            db.query(StudentPersonalInfo).filter(StudentPersonalInfo.student_id == profile_id).delete(synchronize_session=False)

            # Finally delete the student profile
            db.delete(profile)
            db.flush()  # Ensure the profile deletion is committed before proceeding

        else:  # counselor
            # Delete counselor bank accounts
            db.query(CounselorBankAccount).filter(CounselorBankAccount.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete social links
            db.query(CounselorSocialLinks).filter(CounselorSocialLinks.counselor_id == profile_id).delete(synchronize_session=False)

            # Handle packages - need to handle student subscriptions that reference these packages
            packages = db.query(CounselorPackage).filter(CounselorPackage.counselor_id == profile_id).all()
            package_ids = [pkg.id for pkg in packages]

            if package_ids:
                # First, handle student package subscriptions that reference these packages
                # We need to remove the package_id reference to avoid foreign key violations
                student_subscriptions = db.query(StudentPackageSubscription).filter(
                    StudentPackageSubscription.package_id.in_(package_ids)
                ).all()

                for subscription in student_subscriptions:
                    subscription.package_id = None  # Remove foreign key reference

                # Delete package items first
                for package in packages:
                    db.query(PackageItem).filter(PackageItem.package_id == package.id).delete(synchronize_session=False)
                    db.delete(package)

            # Delete services offered
            db.query(CounselorServicesOffered).filter(CounselorServicesOffered.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete availability
            db.query(CounselorAvailability).filter(CounselorAvailability.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete commitment
            db.query(CounselorCommitment).filter(CounselorCommitment.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete counseling experience
            db.query(CounselorCounselingExperience).filter(CounselorCounselingExperience.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete documents
            db.query(CounselorDocuments).filter(CounselorDocuments.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete services
            db.query(CounselorServices).filter(CounselorServices.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete professional experience
            db.query(CounselorProfessionalExperience).filter(CounselorProfessionalExperience.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete education
            db.query(CounselorEducation).filter(CounselorEducation.counselor_id == profile_id).delete(synchronize_session=False)

            # Delete personal info
            db.query(CounselorPersonalInfo).filter(CounselorPersonalInfo.counselor_id == profile_id).delete(synchronize_session=False)

            # Finally delete the counselor profile
            db.delete(profile)
            db.flush()  # Ensure the profile deletion is committed before proceeding

        # STEP 8: Finally, delete the user account (after all foreign key references are removed)
        print(f"About to delete user {user.id}")
        db.delete(user)
        print(f"User {user.id} deleted successfully")

        # Commit all changes
        db.commit()

        return {
            "message": "Account deleted successfully",
            "user_type": user.user_type,
            "deleted_at": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete account: {str(e)}"
        )
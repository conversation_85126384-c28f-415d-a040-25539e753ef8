import { Calendar, Clock, Video } from "lucide-react";
import { Session } from "@/app/types/counselor/sessions";
// import { Button } from "@/app/components/ui/button";
import { useState, useEffect } from "react";
// import { SessionDetailsDialog } from "./session-details-dialog";
import UpdateSessionPopup from "./popup/UpdateSessionPopup";
import { useSessions } from "@/app/hooks/counselor/useSessions";
// import { MainButton } from "../../common/mainBtn";
import CancelSessionPopup from "./popup/CancelSessionPopup";
import ThreeDotIconDropdown from "../../common/threeDotIconMenu";
import { SendMessageDialog } from "./send-message-dialog";

interface SessionCardProps {
  session: Session;
}

export const SessionCard = ({ session }: SessionCardProps) => {
  const {
    getSingleSession,
    currentSession,
    deleteSession,
    fetchSessions,
    fetchSessionNotes,
    currentNotes,
  } = useSessions();

  const [updateSessionPopup, setUpdateSessionPopup] = useState(false);
  const [cancelSessionPopup, setCancelSessionPopup] = useState(false);
  const [showSendMessage, setShowSendMessage] = useState(false);
  const [needsFeedback, setNeedsFeedback] = useState(false);

  // Check if session needs feedback (status is upcoming but end time has passed)
  useEffect(() => {
    if (session.status === "upcoming") {
      const endTime = new Date(session.end_time);
      const currentTime = new Date();
      if (endTime < currentTime) {
        setNeedsFeedback(true);
      }
    }
  }, [session]);

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (start: string, end: string) => {
    // Display times in the user's local timezone
    const startTime = new Date(start).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });

    const endTime = new Date(end).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });

    return `${startTime} - ${endTime}`;
  };

  // No longer needed as we're using direct link

  const getStatusStyles = () => {
    if (needsFeedback) {
      return "bg-purple-100 text-purple-600 border border-purple-300";
    }

    switch (session.status) {
      case "cancelled":
        return "bg-yellow-100 text-yellow-600";
      case "completed":
        return "bg-green-600 text-white";
      default:
        return "bg-green-100 text-green-600";
    }
  };

  const getStatusLabel = () => {
    if (needsFeedback) {
      if (window.location.pathname.startsWith("/counselor/dashboard")) {
        return "Pending Feedback";
      }
      return "Feedback Needed";
    }

    switch (session.status) {
      case "cancelled":
        return "Cancelled";
      case "completed":
        return "Completed";
      default:
        return "Upcoming";
    }
  };

  const handleActionClick = () => {
    if (session.status === "completed" || needsFeedback) {
      return;
    }
    setShowSendMessage(true);
  };

  ////////////// function handles update session items
  const handleEditSession = async (sessionId: number) => {
    await getSingleSession(
      sessionId,
      Intl.DateTimeFormat().resolvedOptions().timeZone
    );
    setUpdateSessionPopup(true);
    await fetchSessionNotes(sessionId);
  };

  ////////////// function handles delete sesion items
  const handleCancelSession = async (serviceId: number) => {
    await getSingleSession(
      serviceId,
      Intl.DateTimeFormat().resolvedOptions().timeZone
    );
    setCancelSessionPopup(true);
  };

  return (
    <div className="border border-gray-200 rounded-lg">
      <div className="flex items-center justify-between sm:px-6 p-4 sm:py-5 gap-y-4 text-gray-700 bg-gray-100/60">
        <div className="flex md:flex-row flex-col-reverse md:items-center items-start gap-2">
          <h3 className="text-xl font-medium">{session.event_name}</h3>
          <div className="flex gap-2">
            <span
              className={`px-3 py-1 text-lg font-semibold rounded-2xl ${getStatusStyles()}`}
            >
              {getStatusLabel()}
            </span>
          </div>
        </div>

        {session.status === "upcoming" && (
          <ThreeDotIconDropdown
            handleEdit={() => handleEditSession(session.id)}
            handleRemove={() => handleCancelSession(session.id)}
            name="Reschedule"
          />
        )}
      </div>

      <div className="flex md:flex-row flex-col md:items-center items-start gap-6 sm:px-6 p-4 sm:py-5 gap-y-2 border-b-2 text-gray-600 text-md font-medium">
        <div className="flex items-center gap-2">
          <Calendar className="w-6 h-6" />
          <span>{formatDate(session.date)}</span>
        </div>
        <div className="flex items-center gap-2">
          <Clock className="w-6 h-6" />
          <span>{formatTime(session.start_time, session.end_time)}</span>
        </div>
      </div>

      <div className="flex md:flex-row flex-col justify-between md:items-center sm:px-6 p-4 sm:py-5 gap-y-5">
        <div className="space-y-4 text-md font-medium">
          <div className="space-y-2">
            <p className="text-gray-600">Student</p>
            <p className="text-lg font-medium">
              {session.student_name || "Deleted User"}
            </p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-600">Meeting link</p>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-blue-100 rounded-full">
                <Video className="w-5 h-5 text-blue-600" />
              </div>
              <a
                href={session.meeting_link}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 underline hover:text-blue-700 text-md"
              >
                Join Meeting
              </a>
            </div>
          </div>
        </div>

        {(session.status === "upcoming" || needsFeedback) && (
          <button
            onClick={handleActionClick}
            className="text-white bg-mainClr py-2 px-4 rounded-lg hover:bg-[#152070] focus:ring-blue-300"
          >
            {needsFeedback ? "Provide Feedback" : "Send Message"}
          </button>
        )}
      </div>

      <UpdateSessionPopup
        isPopupOpen={updateSessionPopup}
        setIsPopupOpen={setUpdateSessionPopup}
        sessionData={currentSession}
        currentSessionNotes={currentNotes}
      />

      <CancelSessionPopup
        isPopupOpen={cancelSessionPopup}
        setIsPopupOpen={setCancelSessionPopup}
        sessionData={currentSession}
        onSave={async () => {
          if (currentSession) {
            await deleteSession(currentSession?.id);
            setCancelSessionPopup(false);
            fetchSessions(Intl.DateTimeFormat().resolvedOptions().timeZone);
          }
        }}
      />
      <SendMessageDialog
        open={showSendMessage}
        onOpenChange={setShowSendMessage}
        student_id={session.student_user_id}
        student_name={session.student_name}
      />
    </div>
  );
};

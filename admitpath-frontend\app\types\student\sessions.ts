export interface Session {
  id: number;
  event_name: string;
  date: string;
  start_time: string;
  end_time: string;
  student_id: number;
  counselor_id: number | null;
  counselor_user_id: number | null;
  counselor_name: string | null;
  counselor_profile_picture?: string | null;
  student_name: string;
  meeting_link: string;
  status: "upcoming" | "completed" | "cancelled";
  payment: {
    id: number;
    amount: number;
    status: "pending" | "completed" | "failed";
    created_at: string;
    updated_at: string;
  };
  created_at: string;
  updated_at: string;
}

export interface SessionsResponse {
  total: number | null;
  items: Session[];
}

export interface SessionNote {
  id: number;
  session_id: number;
  student_id: number;
  student_notes: string;
  counselor_notes: string;
  created_at: string;
  updated_at: string;
}

export interface NotesPayload {
  student_notes: string;
  counselor_notes: string;
}

export interface SessionsState {
  loading: boolean;
  error: string | null;
  sessions: SessionsResponse | null;
  currentNotes: SessionNote | null;
  fetchSessions: (
    timezone: string,
    params?: {
      status?: string;
      from_date?: string;
      to_date?: string;
    }
  ) => Promise<void>;
  fetchSessionNotes: (sessionId: number) => Promise<void>;
  createSessionNotes: (sessionId: number, data: NotesPayload) => Promise<void>;
  createSession: (
    counselorId: number,
    sessionData: {
      service_type: string;
      description: string;
      date: string;
      start_time: string;
      end_time: string;
      amount: number;
      promo_code?: string | null;
    }
  ) => Promise<any>;
  updateSessionNotes: (sessionId: number, data: NotesPayload) => Promise<void>;
  deleteSessionNotes: (sessionId: number) => Promise<void>;
  clearError: () => void;
}

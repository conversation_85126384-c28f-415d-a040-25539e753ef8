"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@components/ui/dialog";
import { Button } from "@components/ui/button";
import { AlertTriangle, Loader2 } from "lucide-react";

interface DeleteAccountDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
  userType: "student" | "counselor";
  errorMessage?: string;
  cannotDeleteReason?: {
    message: string;
    upcoming_sessions?: Array<{
      id: number;
      event_name: string;
      start_time: string;
      end_time: string;
    }>;
    active_packages?: Array<{
      id: number;
      package_id: number;
      status: string;
      start_date?: string;
      end_date?: string;
    }>;
    pending_payments?: Array<{
      id: number;
      amount: number;
      service_type: string;
      created_at: string;
    }>;
    count: number;
  };
}

export const DeleteAccountDialog: React.FC<DeleteAccountDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
  userType,
  errorMessage,
  cannotDeleteReason,
}) => {
  const canDelete = !cannotDeleteReason;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Account
          </DialogTitle>
          <div className="py-3 text-left space-y-2">
            {canDelete ? (
              <>
                Are you sure you want to delete your {userType} account? This
                action cannot be undone.
              </>
            ) : (
              <>
                <strong className="text-red-600">Cannot delete account</strong>
                <h3>{cannotDeleteReason.message}</h3>
                {cannotDeleteReason.upcoming_sessions &&
                  cannotDeleteReason.upcoming_sessions.length > 0 && (
                    <div className="mt-3">
                      <strong>
                        Upcoming Sessions (
                        {cannotDeleteReason.upcoming_sessions.length}):
                      </strong>
                      <ul className="list-disc list-inside mt-1 space-y-1 text-sm">
                        {cannotDeleteReason.upcoming_sessions
                          .slice(0, 3)
                          .map((session) => (
                            <li key={session.id}>
                              {session.event_name} -{" "}
                              {new Date(
                                session.start_time
                              ).toLocaleDateString()}
                            </li>
                          ))}
                        {cannotDeleteReason.upcoming_sessions.length > 3 && (
                          <li>
                            ...and{" "}
                            {cannotDeleteReason.upcoming_sessions.length - 3}{" "}
                            more
                          </li>
                        )}
                      </ul>
                    </div>
                  )}

                {cannotDeleteReason.active_packages &&
                  cannotDeleteReason.active_packages.length > 0 && (
                    <div className="mt-3">
                      <strong>
                        Active Packages (
                        {cannotDeleteReason.active_packages.length}):
                      </strong>
                      <ul className="list-disc list-inside mt-1 space-y-1 text-sm">
                        {cannotDeleteReason.active_packages
                          .slice(0, 3)
                          .map((pkg) => (
                            <li key={pkg.id}>
                              Package #{pkg.package_id} - Status: {pkg.status}
                            </li>
                          ))}
                        {cannotDeleteReason.active_packages.length > 3 && (
                          <li>
                            ...and{" "}
                            {cannotDeleteReason.active_packages.length - 3} more
                          </li>
                        )}
                      </ul>
                    </div>
                  )}

                {cannotDeleteReason.pending_payments &&
                  cannotDeleteReason.pending_payments.length > 0 && (
                    <div className="mt-3">
                      <strong>
                        Pending Payments (
                        {cannotDeleteReason.pending_payments.length}):
                      </strong>
                      <ul className="list-disc list-inside mt-1 space-y-1 text-sm">
                        {cannotDeleteReason.pending_payments
                          .slice(0, 3)
                          .map((payment) => (
                            <li key={payment.id}>
                              ${payment.amount} - {payment.service_type}
                            </li>
                          ))}
                        {cannotDeleteReason.pending_payments.length > 3 && (
                          <li>
                            ...and{" "}
                            {cannotDeleteReason.pending_payments.length - 3}{" "}
                            more
                          </li>
                        )}
                      </ul>
                    </div>
                  )}
              </>
            )}
          </div>
        </DialogHeader>

        {errorMessage && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">{errorMessage}</p>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          {canDelete && (
            <Button
              variant="destructive"
              onClick={onConfirm}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Account"
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

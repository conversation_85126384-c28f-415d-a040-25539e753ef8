import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@components/ui/dialog";
import { Session } from "@/app/types/student/sessions";
import { SessionTabs } from "./tabs";
import { Calendar, Clock } from "lucide-react";
import { useState, useEffect } from "react";
import { SessionNotes } from "./session-notes";
import { useSessions } from "@hooks/student/useSessions";
import Image from "next/image";
import { useCounselor } from "@/app/hooks/student/useCounselor";
import { generatePlaceholder } from "@/app/utils/image";

interface SessionDetailsDialogProps {
  session: Session;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultTab?: "details" | "notes";
}

export const SessionDetailsDialog = ({
  session,
  open,
  onOpenChange,
  defaultTab = "details",
}: SessionDetailsDialogProps) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const { fetchSessionNotes, currentNotes } = useSessions();
  const { counselors } = useCounselor();

  useEffect(() => {
    fetchSessionNotes(session.id);
  }, [session.id, fetchSessionNotes]);

  const tabs = [
    { id: "details", label: "Details" },
    { id: "notes", label: "Session notes" },
  ];

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      day: "numeric",
      month: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (start: string, end: string) => {
    const startTime = new Date(start).toLocaleTimeString([], {
      hour: "numeric",
      minute: "2-digit",
    });
    const endTime = new Date(end).toLocaleTimeString([], {
      hour: "numeric",
      minute: "2-digit",
    });
    return `${startTime} - ${endTime}`;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl">Event details</DialogTitle>
        </DialogHeader>

        <div className="mt-6">
          <SessionTabs
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={(tab) => setActiveTab(tab as "details" | "notes")}
          />

          <div className="mt-6">
            {activeTab === "details" ? (
              <div className="space-y-4">
                <div>
                  <label className="text-gray-600 text-sm">Service*</label>
                  <p className="mt-1 p-3 bg-gray-50 rounded-lg">
                    {session.event_name}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-gray-600 text-sm">Date*</label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-lg flex items-center gap-2">
                      <Calendar className="w-5 h-5 text-gray-500" />
                      {formatDate(session.date)}
                    </div>
                  </div>
                  <div>
                    <label className="text-gray-600 text-sm">
                      Select Time*
                    </label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-lg flex items-center gap-2">
                      <Clock className="w-5 h-5 text-gray-500" />
                      {formatTime(session.start_time, session.end_time)}
                    </div>
                  </div>
                </div>

                <div>
                  <p className="text-gray-500 text-sm flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    (GMT+05:00) Pakistan Standard Time
                  </p>
                </div>

                <div>
                  <label className="text-gray-600 text-sm">Meeting link</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                    {session.meeting_link}
                  </div>
                </div>

                <div>
                  <label className="text-gray-600 text-sm">Guest</label>
                  <div className="flex justify-center items-center gap-2">
                    <Image
                      src={
                        (session.counselor_id &&
                          counselors[session.counselor_id]
                            ?.profile_picture_url) ||
                        generatePlaceholder(
                          session.counselor_id
                            ? counselors[session.counselor_id]?.first_name
                            : "D",
                          session.counselor_id
                            ? counselors[session.counselor_id]?.last_name
                            : "U"
                        )
                      }
                      alt="Counselor"
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                    <div className="text-gray-500 text-sm">
                      {session.counselor_id ? (
                        <>
                          {counselors[session.counselor_id]?.first_name || ""}{" "}
                          {counselors[session.counselor_id]?.last_name || ""}
                        </>
                      ) : (
                        "Deleted User"
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <SessionNotes
                sessionId={session.id}
                counselorNotes={currentNotes?.counselor_notes || null}
                studentNotes={currentNotes?.student_notes || null}
              />
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useSessions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { NotesPayload, SessionsState } from \"@/app/types/student/sessions\";\r\n\r\nexport const useSessions = create<SessionsState>((set, get) => ({\r\n    loading: false,\r\n    error: null,\r\n    sessions: { items: [], total: null },\r\n    currentNotes: null,\r\n\r\n    fetchSessions: async (timezone, params) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.get(\r\n                `/student/sessions/my-sessions?timezone=${timezone}`,\r\n                {\r\n                    params,\r\n                }\r\n            );\r\n            set({\r\n                sessions: {\r\n                    items: response.data.items || [],\r\n                    total: response.data.total || 0,\r\n                },\r\n                loading: false,\r\n            });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail || \"Failed to fetch sessions\",\r\n                loading: false,\r\n                sessions: { items: [], total: 0 },\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    fetchSessionNotes: async (sessionId: number) => {\r\n        if (!sessionId) return null;\r\n\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.get(\r\n                `/sessions/${sessionId}/notes`\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to fetch session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    createSessionNotes: async (sessionId: number, data: NotesPayload) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.post(\r\n                `/sessions/${sessionId}/notes`,\r\n                data\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to create session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    updateSessionNotes: async (sessionId: number, data: NotesPayload) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.put(\r\n                `/sessions/${sessionId}/notes`,\r\n                data\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to update session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    deleteSessionNotes: async (sessionId: number) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            await apiClient.delete(`/sessions/${sessionId}/notes`);\r\n            set({ currentNotes: null, loading: false });\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to delete session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    clearError: () => {\r\n        set({ error: null });\r\n    },\r\n\r\n    createSession: async (counselorId: number, data) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.post(\r\n                `/student/sessions/counselor/${counselorId}/sessions`,\r\n                {\r\n                    session: {\r\n                        service_type: data.service_type,\r\n                        description: data.description,\r\n                        date: data.date,\r\n                        start_time: data.start_time,\r\n                        end_time: data.end_time,\r\n                    },\r\n                    payment: {\r\n                        amount: data.amount,\r\n                        promo_code: data.promo_code,\r\n                    },\r\n                }\r\n            );\r\n            set({ loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail || \"Failed to create session\",\r\n                loading: false,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AADA;AAFA;;;AAMO,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QAC5D,SAAS;QACT,OAAO;QACP,UAAU;YAAE,OAAO,EAAE;YAAE,OAAO;QAAK;QACnC,cAAc;QAEd,eAAe,OAAO,UAAU;YAC5B,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,uCAAuC,EAAE,UAAU,EACpD;oBACI;gBACJ;gBAEJ,IAAI;oBACA,UAAU;wBACN,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;wBAChC,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI;oBAClC;oBACA,SAAS;gBACb;gBACA,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACpC,SAAS;oBACT,UAAU;wBAAE,OAAO,EAAE;wBAAE,OAAO;oBAAE;gBACpC;gBACA,MAAM;YACV;QACJ;QAEA,mBAAmB,OAAO;YACtB,IAAI,CAAC,WAAW,OAAO;YAEvB,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;gBAElC,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO,WAAmB;YAC1C,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACjC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAC9B;gBAEJ,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO,WAAmB;YAC1C,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAC9B;gBAEJ,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO;YACvB,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;gBACrD,IAAI;oBAAE,cAAc;oBAAM,SAAS;gBAAM;YAC7C,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,YAAY;YACR,IAAI;gBAAE,OAAO;YAAK;QACtB;QAEA,eAAe,OAAO,aAAqB;YACvC,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACjC,CAAC,4BAA4B,EAAE,YAAY,SAAS,CAAC,EACrD;oBACI,SAAS;wBACL,cAAc,KAAK,YAAY;wBAC/B,aAAa,KAAK,WAAW;wBAC7B,MAAM,KAAK,IAAI;wBACf,YAAY,KAAK,UAAU;wBAC3B,UAAU,KAAK,QAAQ;oBAC3B;oBACA,SAAS;wBACL,QAAQ,KAAK,MAAM;wBACnB,YAAY,KAAK,UAAU;oBAC/B;gBACJ;gBAEJ,IAAI;oBAAE,SAAS;gBAAM;gBACrB,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACpC,SAAS;gBACb;gBACA,MAAM;YACV;QACJ;IACJ,CAAC"}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,gBAAgB,mKAAgB,OAAO;AAE7C,MAAM,eAAe,mKAAgB,MAAM;AAE3C,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,mKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,mKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,mKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/tabs.tsx"], "sourcesContent": ["import { cn } from \"@components/lib/utils\";\r\n\r\ninterface Tab {\r\n  id: string;\r\n  label: string;\r\n  count?: number;\r\n}\r\n\r\ninterface SessionTabsProps {\r\n  tabs: Tab[];\r\n  activeTab: string;\r\n  onTabChange: (tabId: string) => void;\r\n  className?: string;\r\n}\r\n\r\nexport const SessionTabs = ({\r\n  tabs,\r\n  activeTab,\r\n  onTabChange,\r\n  className,\r\n}: SessionTabsProps) => {\r\n  return (\r\n    <div className={cn(\"flex gap-8 border-b\", className)}>\r\n      {tabs.map((tab) => (\r\n        <button\r\n          key={tab.id}\r\n          onClick={() => onTabChange(tab.id)}\r\n          className={`pb-4 px-2 relative text-gray-900 font-medium transition-colors ${\r\n            activeTab === tab.id\r\n              ? \"border-b-2 border-gray-900\"\r\n              : \"hover:text-gray-600\"\r\n          }`}\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            {tab.label}\r\n            {tab.count && tab.count > 0 && (\r\n              <span className=\"bg-yellow-100 text-yellow-800 text-xs px-2.5 py-0.5 rounded-full\">\r\n                {tab.count}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </button>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAeO,MAAM,cAAc,CAAC,EAC1B,IAAI,EACJ,SAAS,EACT,WAAW,EACX,SAAS,EACQ;IACjB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;kBACvC,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;gBAEC,SAAS,IAAM,YAAY,IAAI,EAAE;gBACjC,WAAW,CAAC,+DAA+D,EACzE,cAAc,IAAI,EAAE,GAChB,+BACA,uBACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,IAAI,KAAK;wBACT,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,mBACxB,8OAAC;4BAAK,WAAU;sCACb,IAAI,KAAK;;;;;;;;;;;;eAZX,IAAI,EAAE;;;;;;;;;;AAoBrB"}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-12 items-center justify-start border-b w-full overflow-x-auto\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap px-4 py-3 text-base font-medium text-gray-600 border-b-2 border-transparent transition-colors hover:text-gray-900\",\r\n      \"data-[state=active]:border-gray-900 data-[state=active]:text-gray-900\",\r\n      \"focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-4 ring-offset-background focus-visible:outline-none\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,iKAAc,IAAI;AAE/B,MAAM,yBAAW,sMAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,iKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+KACA,yEACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nexport interface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          \"flex min-h-[80px] w-full rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,yBAAW,sMAAM,UAAU,CAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG"}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/session-notes.tsx"], "sourcesContent": ["import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from \"@components/ui/tabs\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Clock, ChevronRight } from \"lucide-react\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Textarea } from \"./textarea\";\r\nimport Image from \"next/image\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@components/ui/dialog\";\r\nimport { useSessions } from \"@hooks/student/useSessions\";\r\nimport { toast } from \"react-toastify\";\r\n\r\ninterface SessionNotesProps {\r\n  sessionId: number;\r\n  counselorNotes: string | null;\r\n  studentNotes: string | null;\r\n}\r\n\r\ninterface NoteDetailProps {\r\n  content: string;\r\n  author: string;\r\n  timestamp: string;\r\n  onRemove: () => Promise<void>;\r\n}\r\n\r\nconst NoteDetail = ({\r\n  content,\r\n  author,\r\n  timestamp,\r\n  onRemove,\r\n}: NoteDetailProps) => {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <h3 className=\"text-xl font-medium\">{author}</h3>\r\n        <div className=\"flex items-center gap-2 text-gray-500\">\r\n          <Clock className=\"w-4 h-4\" />\r\n          <span>{new Date(timestamp).toLocaleTimeString()}</span>\r\n        </div>\r\n      </div>\r\n      <p className=\"text-gray-600 whitespace-pre-wrap\">{content}</p>\r\n      <div className=\"flex justify-between items-center mt-4\">\r\n        <button\r\n          onClick={onRemove}\r\n          className=\"text-red-500 hover:text-red-600 transition-colors\"\r\n        >\r\n          Remove\r\n        </button>\r\n        <Button variant=\"outline\" onClick={() => window.history.back()}>\r\n          <ChevronRight className=\"w-4 h-4 mr-2\" />\r\n          Back\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst formatTime = (timestamp: string) => {\r\n  return new Date(timestamp).toLocaleString(\"en-US\", {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    hour: \"numeric\",\r\n    minute: \"2-digit\",\r\n    hour12: true,\r\n  });\r\n};\r\n\r\nexport const SessionNotes = ({\r\n  sessionId,\r\n  counselorNotes,\r\n  studentNotes,\r\n}: SessionNotesProps) => {\r\n  const [newNote, setNewNote] = useState(\"\");\r\n  const [showSuccess, setShowSuccess] = useState(false);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [isViewingNote, setIsViewingNote] = useState(false);\r\n  const [selectedNoteType, setSelectedNoteType] = useState<\r\n    \"counselor\" | \"student\" | null\r\n  >(null);\r\n\r\n  const {\r\n    updateSessionNotes,\r\n    createSessionNotes,\r\n    loading,\r\n    currentNotes,\r\n    fetchSessionNotes,\r\n  } = useSessions();\r\n\r\n  useEffect(() => {\r\n    fetchSessionNotes(sessionId);\r\n  }, [sessionId, fetchSessionNotes]);\r\n\r\n  const handleSaveNote = async () => {\r\n    if (!newNote.trim()) return;\r\n\r\n    try {\r\n      const payload = {\r\n        student_notes: newNote,\r\n        counselor_notes: counselorNotes || \" \",\r\n      };\r\n\r\n      // If either type of note exists, use PUT, otherwise use POST\r\n      if (currentNotes || counselorNotes || studentNotes) {\r\n        await updateSessionNotes(sessionId, payload);\r\n      } else {\r\n        await createSessionNotes(sessionId, payload);\r\n      }\r\n\r\n      setShowSuccess(true);\r\n      setNewNote(\"\");\r\n      setIsEditing(false);\r\n      setIsViewingNote(false);\r\n    } catch (error) {\r\n      toast.error(\"Failed to save note\");\r\n    }\r\n  };\r\n\r\n  const renderNoteInput = () => {\r\n    return (\r\n      <div className=\"space-y-2\">\r\n        <label className=\"text-gray-600\">\r\n          Add notes<span className=\"text-red-500\">*</span>\r\n        </label>\r\n        <div className=\"relative\">\r\n          <Textarea\r\n            value={newNote}\r\n            onChange={(e) => setNewNote(e.target.value)}\r\n            placeholder=\"Enter here...\"\r\n            className=\"min-h-[200px] resize-none\"\r\n            maxLength={400}\r\n          />\r\n          <span className=\"absolute bottom-2 right-2 text-sm text-gray-500\">\r\n            {newNote.length}/400\r\n          </span>\r\n        </div>\r\n        <div className=\"flex justify-end gap-3 mt-4\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => {\r\n              setNewNote(\"\");\r\n              setIsEditing(false);\r\n            }}\r\n          >\r\n            Discard\r\n          </Button>\r\n          <Button\r\n            onClick={handleSaveNote}\r\n            disabled={!newNote.trim() || loading}\r\n          >\r\n            Save\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const renderNotePreview = (\r\n    content: string,\r\n    timestamp: string,\r\n    author: string,\r\n    type: \"counselor\" | \"student\"\r\n  ) => (\r\n    <div\r\n      onClick={() => {\r\n        if (type === \"student\") {\r\n          setNewNote(content);\r\n          setSelectedNoteType(type);\r\n          setIsViewingNote(true);\r\n        }\r\n      }}\r\n      className={`bg-gray-50 rounded-lg p-4 ${\r\n        type === \"student\" ? \"cursor-pointer hover:bg-gray-100\" : \"\"\r\n      } transition-colors`}\r\n    >\r\n      <div className=\"flex items-center justify-between mb-2\">\r\n        <h4 className=\"font-medium\">{author}</h4>\r\n        <div className=\"flex items-center gap-2 text-gray-500 text-sm\">\r\n          <Clock className=\"w-4 h-4\" />\r\n          <span>{formatTime(timestamp)}</span>\r\n        </div>\r\n      </div>\r\n      <p className=\"text-gray-600 line-clamp-2\">{content}</p>\r\n      {type === \"student\" && (\r\n        <ChevronRight className=\"w-4 h-4 text-gray-400 mt-2\" />\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  if (isViewingNote) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        {isEditing ? (\r\n          <>\r\n            <div className=\"relative\">\r\n              <Textarea\r\n                value={newNote}\r\n                onChange={(e) => setNewNote(e.target.value)}\r\n                className=\"min-h-[300px] resize-none\"\r\n                maxLength={400}\r\n              />\r\n              <span className=\"absolute bottom-2 right-2 text-sm text-gray-500\">\r\n                {newNote.length}/400\r\n              </span>\r\n            </div>\r\n            <div className=\"flex justify-between items-center mt-4\">\r\n              <Button variant=\"outline\" onClick={() => setIsEditing(false)}>\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                onClick={handleSaveNote}\r\n                disabled={!newNote.trim() || loading}\r\n              >\r\n                Save Changes\r\n              </Button>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <>\r\n            <div className=\"flex items-center justify-between\">\r\n              <h3 className=\"text-xl font-medium\">Your Note</h3>\r\n              <div className=\"flex items-center gap-2 text-gray-500\">\r\n                <Clock className=\"w-4 h-4\" />\r\n                <span>{formatTime(currentNotes?.created_at || \"\")}</span>\r\n              </div>\r\n            </div>\r\n            <p className=\"text-gray-600 whitespace-pre-wrap\">{newNote}</p>\r\n            <div className=\"flex justify-between items-center mt-4\">\r\n              <Button variant=\"outline\" onClick={() => setIsEditing(true)}>\r\n                Edit\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => {\r\n                  setIsViewingNote(false);\r\n                  setNewNote(\"\");\r\n                }}\r\n              >\r\n                Back\r\n              </Button>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Tabs defaultValue=\"counsellor\" className=\"w-full\">\r\n        <div className=\"overflow-x-auto scrollbar-hide -mx-2 px-2\">\r\n          <TabsList className=\"bg-gray-100/80 p-1 rounded-lg inline-flex w-auto\">\r\n            <TabsTrigger\r\n              value=\"counsellor\"\r\n              className=\"data-[state=active]:bg-white rounded-md px-4 py-2 whitespace-nowrap\"\r\n            >\r\n              For counsellor\r\n            </TabsTrigger>\r\n            <TabsTrigger\r\n              value=\"yourself\"\r\n              className=\"data-[state=active]:bg-white rounded-md px-4 py-2 whitespace-nowrap\"\r\n            >\r\n              For yourself\r\n            </TabsTrigger>\r\n          </TabsList>\r\n        </div>\r\n\r\n        <TabsContent value=\"counsellor\" className=\"mt-6\">\r\n          {counselorNotes && counselorNotes.trim() ? (\r\n            renderNotePreview(\r\n              counselorNotes,\r\n              currentNotes?.created_at || \"\",\r\n              \"Counselor\",\r\n              \"counselor\"\r\n            )\r\n          ) : (\r\n            <p className=\"text-gray-500\">No notes from counselor yet.</p>\r\n          )}\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"yourself\" className=\"mt-6\">\r\n          {studentNotes && studentNotes.trim()\r\n            ? renderNotePreview(\r\n                studentNotes,\r\n                currentNotes?.created_at || \"\",\r\n                \"You\",\r\n                \"student\"\r\n              )\r\n            : renderNoteInput()}\r\n        </TabsContent>\r\n      </Tabs>\r\n\r\n      {/* Success Dialog */}\r\n      <Dialog open={showSuccess} onOpenChange={setShowSuccess}>\r\n        <DialogContent className=\"text-center\">\r\n          <DialogHeader>\r\n            <DialogTitle>Successfully saved!</DialogTitle>\r\n          </DialogHeader>\r\n          <div className=\"py-6\">\r\n            <Image\r\n              src=\"/images/success.png\"\r\n              alt=\"Success\"\r\n              width={100}\r\n              height={100}\r\n              className=\"mx-auto\"\r\n            />\r\n          </div>\r\n          <DialogDescription>\r\n            Your note has been saved successfully.\r\n          </DialogDescription>\r\n          <Button onClick={() => setShowSuccess(false)} className=\"mt-4\">\r\n            Done\r\n          </Button>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AAZA;AAAA;;;;;;;;;;;AA2BA,MAAM,aAAa,CAAC,EAClB,OAAO,EACP,MAAM,EACN,SAAS,EACT,QAAQ,EACQ;IAChB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuB;;;;;;kCACrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAM,IAAI,KAAK,WAAW,kBAAkB;;;;;;;;;;;;;;;;;;0BAGjD,8OAAC;gBAAE,WAAU;0BAAqC;;;;;;0BAClD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;;0CAC1D,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAMnD;AAEA,MAAM,aAAa,CAAC;IAClB,OAAO,IAAI,KAAK,WAAW,cAAc,CAAC,SAAS;QACjD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAEO,MAAM,eAAe,CAAC,EAC3B,SAAS,EACT,cAAc,EACd,YAAY,EACM;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAErD;IAEF,MAAM,EACJ,kBAAkB,EAClB,kBAAkB,EAClB,OAAO,EACP,YAAY,EACZ,iBAAiB,EAClB,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG;QAAC;QAAW;KAAkB;IAEjC,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,IAAI;YACF,MAAM,UAAU;gBACd,eAAe;gBACf,iBAAiB,kBAAkB;YACrC;YAEA,6DAA6D;YAC7D,IAAI,gBAAgB,kBAAkB,cAAc;gBAClD,MAAM,mBAAmB,WAAW;YACtC,OAAO;gBACL,MAAM,mBAAmB,WAAW;YACtC;YAEA,eAAe;YACf,WAAW;YACX,aAAa;YACb,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,WAAU;;wBAAgB;sCACtB,8OAAC;4BAAK,WAAU;sCAAe;;;;;;;;;;;;8BAE1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,qJAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC1C,aAAY;4BACZ,WAAU;4BACV,WAAW;;;;;;sCAEb,8OAAC;4BAAK,WAAU;;gCACb,QAAQ,MAAM;gCAAC;;;;;;;;;;;;;8BAGpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;gCACP,WAAW;gCACX,aAAa;4BACf;sCACD;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,QAAQ,IAAI,MAAM;sCAC9B;;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,oBAAoB,CACxB,SACA,WACA,QACA,qBAEA,8OAAC;YACC,SAAS;gBACP,IAAI,SAAS,WAAW;oBACtB,WAAW;oBACX,oBAAoB;oBACpB,iBAAiB;gBACnB;YACF;YACA,WAAW,CAAC,0BAA0B,EACpC,SAAS,YAAY,qCAAqC,GAC3D,kBAAkB,CAAC;;8BAEpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAe;;;;;;sCAC7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAM,WAAW;;;;;;;;;;;;;;;;;;8BAGtB,8OAAC;oBAAE,WAAU;8BAA8B;;;;;;gBAC1C,SAAS,2BACR,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;;IAK9B,IAAI,eAAe;QACjB,qBACE,8OAAC;YAAI,WAAU;sBACZ,0BACC;;kCACE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qJAAA,CAAA,WAAQ;gCACP,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,WAAU;gCACV,WAAW;;;;;;0CAEb,8OAAC;gCAAK,WAAU;;oCACb,QAAQ,MAAM;oCAAC;;;;;;;;;;;;;kCAGpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,aAAa;0CAAQ;;;;;;0CAG9D,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC,QAAQ,IAAI,MAAM;0CAC9B;;;;;;;;;;;;;6CAML;;kCACE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAM,WAAW,cAAc,cAAc;;;;;;;;;;;;;;;;;;kCAGlD,8OAAC;wBAAE,WAAU;kCAAqC;;;;;;kCAClD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,aAAa;0CAAO;;;;;;0CAG7D,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;oCACP,iBAAiB;oCACjB,WAAW;gCACb;0CACD;;;;;;;;;;;;;;;;;;;IAQb;IAEA,qBACE;;0BACE,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAa,WAAU;;kCACxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;8CACX;;;;;;8CAGD,8OAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAML,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAa,WAAU;kCACvC,kBAAkB,eAAe,IAAI,KACpC,kBACE,gBACA,cAAc,cAAc,IAC5B,aACA,6BAGF,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;kCAIjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACrC,gBAAgB,aAAa,IAAI,KAC9B,kBACE,cACA,cAAc,cAAc,IAC5B,OACA,aAEF;;;;;;;;;;;;0BAKR,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAa,cAAc;0BACvC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;sCAGnB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,eAAe;4BAAQ,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;AAOzE"}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useCounselor.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\n\r\ninterface CounselorState {\r\n  loading: boolean;\r\n  error: string | null;\r\n  counselors: { [key: number]: CounselorPublicProfile };\r\n  fetchCounselorInfo: (\r\n    counselor_user_id: number\r\n  ) => Promise<CounselorPublicProfile | null>;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useCounselor = create<CounselorState>((set, get) => ({\r\n  loading: false,\r\n  error: null,\r\n  counselors: {},\r\n\r\n  fetchCounselorInfo: async (counselor_id: number) => {\r\n    // Check if already cached\r\n    const cachedCounselor = get().counselors[counselor_id];\r\n    if (cachedCounselor) {\r\n      return cachedCounselor;\r\n    }\r\n\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      // Get counselor profile by counselor_id instead of user_id\r\n      const profileResponse = await apiClient.get(\r\n        `/counselor/profile/by-counselor-id/${counselor_id}`\r\n      );\r\n      const counselorProfile = profileResponse.data;\r\n\r\n      set((state) => ({\r\n        counselors: {\r\n          ...state.counselors,\r\n          [counselor_id]: counselorProfile,\r\n        },\r\n        loading: false,\r\n      }));\r\n\r\n      return counselorProfile;\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch counselor info\",\r\n        loading: false,\r\n      });\r\n      return null;\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AADA;AAFA;;;AAgBO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,KAAK,MAAQ,CAAC;QAChE,SAAS;QACT,OAAO;QACP,YAAY,CAAC;QAEb,oBAAoB,OAAO;YACzB,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,UAAU,CAAC,aAAa;YACtD,IAAI,iBAAiB;gBACnB,OAAO;YACT;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,2DAA2D;gBAC3D,MAAM,kBAAkB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CACzC,CAAC,mCAAmC,EAAE,cAAc;gBAEtD,MAAM,mBAAmB,gBAAgB,IAAI;gBAE7C,IAAI,CAAC,QAAU,CAAC;wBACd,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,CAAC,aAAa,EAAE;wBAClB;wBACA,SAAS;oBACX,CAAC;gBAED,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;gBACA,OAAO;YACT;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/session-details-dialog.tsx"], "sourcesContent": ["import {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>nt,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n} from \"@components/ui/dialog\";\r\nimport { Session } from \"@/app/types/student/sessions\";\r\nimport { SessionTabs } from \"./tabs\";\r\nimport { Calendar, Clock } from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { SessionNotes } from \"./session-notes\";\r\nimport { useSessions } from \"@hooks/student/useSessions\";\r\nimport Image from \"next/image\";\r\nimport { useCounselor } from \"@/app/hooks/student/useCounselor\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\ninterface SessionDetailsDialogProps {\r\n  session: Session;\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  defaultTab?: \"details\" | \"notes\";\r\n}\r\n\r\nexport const SessionDetailsDialog = ({\r\n  session,\r\n  open,\r\n  onOpenChange,\r\n  defaultTab = \"details\",\r\n}: SessionDetailsDialogProps) => {\r\n  const [activeTab, setActiveTab] = useState(defaultTab);\r\n  const { fetchSessionNotes, currentNotes } = useSessions();\r\n  const { counselors } = useCounselor();\r\n\r\n  useEffect(() => {\r\n    fetchSessionNotes(session.id);\r\n  }, [session.id, fetchSessionNotes]);\r\n\r\n  const tabs = [\r\n    { id: \"details\", label: \"Details\" },\r\n    { id: \"notes\", label: \"Session notes\" },\r\n  ];\r\n\r\n  const formatDate = (date: string) => {\r\n    return new Date(date).toLocaleDateString(\"en-US\", {\r\n      day: \"numeric\",\r\n      month: \"numeric\",\r\n      year: \"numeric\",\r\n    });\r\n  };\r\n\r\n  const formatTime = (start: string, end: string) => {\r\n    const startTime = new Date(start).toLocaleTimeString([], {\r\n      hour: \"numeric\",\r\n      minute: \"2-digit\",\r\n    });\r\n    const endTime = new Date(end).toLocaleTimeString([], {\r\n      hour: \"numeric\",\r\n      minute: \"2-digit\",\r\n    });\r\n    return `${startTime} - ${endTime}`;\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"max-w-2xl\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-2xl\">Event details</DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"mt-6\">\r\n          <SessionTabs\r\n            tabs={tabs}\r\n            activeTab={activeTab}\r\n            onTabChange={(tab) => setActiveTab(tab as \"details\" | \"notes\")}\r\n          />\r\n\r\n          <div className=\"mt-6\">\r\n            {activeTab === \"details\" ? (\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"text-gray-600 text-sm\">Service*</label>\r\n                  <p className=\"mt-1 p-3 bg-gray-50 rounded-lg\">\r\n                    {session.event_name}\r\n                  </p>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <label className=\"text-gray-600 text-sm\">Date*</label>\r\n                    <div className=\"mt-1 p-3 bg-gray-50 rounded-lg flex items-center gap-2\">\r\n                      <Calendar className=\"w-5 h-5 text-gray-500\" />\r\n                      {formatDate(session.date)}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-gray-600 text-sm\">\r\n                      Select Time*\r\n                    </label>\r\n                    <div className=\"mt-1 p-3 bg-gray-50 rounded-lg flex items-center gap-2\">\r\n                      <Clock className=\"w-5 h-5 text-gray-500\" />\r\n                      {formatTime(session.start_time, session.end_time)}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <p className=\"text-gray-500 text-sm flex items-center gap-2\">\r\n                    <Clock className=\"w-4 h-4\" />\r\n                    (GMT+05:00) Pakistan Standard Time\r\n                  </p>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"text-gray-600 text-sm\">Meeting link</label>\r\n                  <div className=\"mt-1 p-3 bg-gray-50 rounded-lg\">\r\n                    {session.meeting_link}\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"text-gray-600 text-sm\">Guest</label>\r\n                  <div className=\"flex justify-center items-center gap-2\">\r\n                    <Image\r\n                      src={\r\n                        (session.counselor_id &&\r\n                          counselors[session.counselor_id]\r\n                            ?.profile_picture_url) ||\r\n                        generatePlaceholder(\r\n                          session.counselor_id\r\n                            ? counselors[session.counselor_id]?.first_name\r\n                            : \"D\",\r\n                          session.counselor_id\r\n                            ? counselors[session.counselor_id]?.last_name\r\n                            : \"U\"\r\n                        )\r\n                      }\r\n                      alt=\"Counselor\"\r\n                      width={40}\r\n                      height={40}\r\n                      className=\"rounded-full\"\r\n                    />\r\n                    <div className=\"text-gray-500 text-sm\">\r\n                      {session.counselor_id ? (\r\n                        <>\r\n                          {counselors[session.counselor_id]?.first_name || \"\"}{\" \"}\r\n                          {counselors[session.counselor_id]?.last_name || \"\"}\r\n                        </>\r\n                      ) : (\r\n                        \"Deleted User\"\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <SessionNotes\r\n                sessionId={session.id}\r\n                counselorNotes={currentNotes?.counselor_notes || null}\r\n                studentNotes={currentNotes?.student_notes || null}\r\n              />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAOA;AAEA;AACA;AACA;AACA;AACA;AACA;AANA;AAAA;;;;;;;;;;;AAeO,MAAM,uBAAuB,CAAC,EACnC,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,aAAa,SAAS,EACI;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,iBAAiB,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACtD,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAElC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB,QAAQ,EAAE;IAC9B,GAAG;QAAC,QAAQ,EAAE;QAAE;KAAkB;IAElC,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;QAAU;QAClC;YAAE,IAAI;YAAS,OAAO;QAAgB;KACvC;IAED,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC,OAAe;QACjC,MAAM,YAAY,IAAI,KAAK,OAAO,kBAAkB,CAAC,EAAE,EAAE;YACvD,MAAM;YACN,QAAQ;QACV;QACA,MAAM,UAAU,IAAI,KAAK,KAAK,kBAAkB,CAAC,EAAE,EAAE;YACnD,MAAM;YACN,QAAQ;QACV;QACA,OAAO,GAAG,UAAU,GAAG,EAAE,SAAS;IACpC;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAW;;;;;;;;;;;8BAGpC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iJAAA,CAAA,cAAW;4BACV,MAAM;4BACN,WAAW;4BACX,aAAa,CAAC,MAAQ,aAAa;;;;;;sCAGrC,8OAAC;4BAAI,WAAU;sCACZ,cAAc,0BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAwB;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DACV,QAAQ,UAAU;;;;;;;;;;;;kDAIvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAwB;;;;;;kEACzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,WAAW,QAAQ,IAAI;;;;;;;;;;;;;0DAG5B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAwB;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,WAAW,QAAQ,UAAU,EAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;kDAKtD,8OAAC;kDACC,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAKjC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAwB;;;;;;0DACzC,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,YAAY;;;;;;;;;;;;kDAIzB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAwB;;;;;;0DACzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KACE,AAAC,QAAQ,YAAY,IACnB,UAAU,CAAC,QAAQ,YAAY,CAAC,EAC5B,uBACN,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAChB,QAAQ,YAAY,GAChB,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,aAClC,KACJ,QAAQ,YAAY,GAChB,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,YAClC;wDAGR,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,YAAY,iBACnB;;gEACG,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,cAAc;gEAAI;gEACpD,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,aAAa;;2EAGlD;;;;;;;;;;;;;;;;;;;;;;;qDAOV,8OAAC,6JAAA,CAAA,eAAY;gCACX,WAAW,QAAQ,EAAE;gCACrB,gBAAgB,cAAc,mBAAmB;gCACjD,cAAc,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D"}}, {"offset": {"line": 1503, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,sMAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG"}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,sMAAM,UAAU,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/send-message-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport * as z from \"zod\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@components/ui/dialog\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Textarea } from \"@components/ui/textarea\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@components/ui/avatar\";\r\nimport { useWebSocketChat } from \"@/app/hooks/useWebSocketChat\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\nconst messageSchema = z.object({\r\n  message: z\r\n    .string()\r\n    .min(1, \"Message is required\")\r\n    .max(500, \"Message cannot exceed 500 characters\"),\r\n});\r\n\r\ntype MessageFormData = z.infer<typeof messageSchema>;\r\n\r\ninterface CounselorInfo {\r\n  user_id: number | null;\r\n  first_name: string;\r\n  last_name: string;\r\n  profile_picture_url: string | null;\r\n}\r\n\r\ninterface SendMessageDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  counselorInfo: CounselorInfo;\r\n}\r\n\r\nexport function SendMessageDialog({\r\n  open,\r\n  onOpenChange,\r\n  counselorInfo,\r\n}: SendMessageDialogProps) {\r\n  const [isSending, setIsSending] = useState(false);\r\n  const router = useRouter();\r\n  const { userInfo } = useProfile();\r\n  const { sendInitialMessage } = useWebSocketChat();\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    reset,\r\n  } = useForm<MessageFormData>({\r\n    resolver: zodResolver(messageSchema),\r\n  });\r\n\r\n  const onSubmit = async (data: MessageFormData) => {\r\n    if (!userInfo || !counselorInfo?.user_id) return;\r\n\r\n    setIsSending(true);\r\n    try {\r\n      await sendInitialMessage({\r\n        message: data.message,\r\n        recipient_id: counselorInfo.user_id,\r\n      });\r\n\r\n      toast.success(\"Message sent successfully!\");\r\n      reset();\r\n      onOpenChange(false);\r\n      router.push(\"/student/dashboard/messages\");\r\n    } catch (error: any) {\r\n      if (\r\n        error?.response?.data?.detail?.includes(\r\n          \"between a counselor and a student\"\r\n        )\r\n      ) {\r\n        toast.error(\"You cannot send any messages to this person.\");\r\n      } else {\r\n        toast.error(\"Failed to send message. Please try again.\");\r\n      }\r\n    } finally {\r\n      setIsSending(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-2xl font-bold text-center\">\r\n            Send message to:\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"flex items-center space-x-4 mt-4\">\r\n          <Avatar className=\"w-16 h-16\">\r\n            <AvatarImage\r\n              src={\r\n                counselorInfo?.profile_picture_url ||\r\n                generatePlaceholder(\r\n                  counselorInfo?.first_name,\r\n                  counselorInfo?.last_name\r\n                )\r\n              }\r\n              alt={counselorInfo?.first_name || \"\"}\r\n            />\r\n            <AvatarFallback>{counselorInfo?.first_name || \"\"}</AvatarFallback>\r\n          </Avatar>\r\n          <div className=\"flex flex-col\">\r\n            <h3 className=\"text-xl font-semibold\">\r\n              {counselorInfo\r\n                ? `${counselorInfo.first_name} ${counselorInfo.last_name}`\r\n                : \"loading...\"}\r\n            </h3>\r\n          </div>\r\n        </div>\r\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4 mt-4\">\r\n          <div>\r\n            <Textarea\r\n              {...register(\"message\")}\r\n              placeholder=\"Type your message here...\"\r\n              className=\"min-h-[100px]\"\r\n            />\r\n            {errors.message && (\r\n              <p className=\"text-red-500 text-sm mt-1\">\r\n                {errors.message.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full bg-blue-800 hover:bg-blue-800/90\"\r\n            disabled={isSending}\r\n          >\r\n            {isSending ? \"Sending...\" : \"Send Message\"}\r\n          </Button>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAFA;AAHA;;;;;;;;;;;;;;;AAqBA,MAAM,gBAAgB,qIAAE,MAAM,CAAC;IAC7B,SAAS,qIACN,MAAM,GACN,GAAG,CAAC,GAAG,uBACP,GAAG,CAAC,KAAK;AACd;AAiBO,SAAS,kBAAkB,EAChC,IAAI,EACJ,YAAY,EACZ,aAAa,EACU;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAE9C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,YAAY,CAAC,eAAe,SAAS;QAE1C,aAAa;QACb,IAAI;YACF,MAAM,mBAAmB;gBACvB,SAAS,KAAK,OAAO;gBACrB,cAAc,cAAc,OAAO;YACrC;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,aAAa;YACb,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,IACE,OAAO,UAAU,MAAM,QAAQ,SAC7B,sCAEF;gBACA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAiC;;;;;;;;;;;8BAI1D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCACV,KACE,eAAe,uBACf,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAChB,eAAe,YACf,eAAe;oCAGnB,KAAK,eAAe,cAAc;;;;;;8CAEpC,8OAAC,kIAAA,CAAA,iBAAc;8CAAE,eAAe,cAAc;;;;;;;;;;;;sCAEhD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,gBACG,GAAG,cAAc,UAAU,CAAC,CAAC,EAAE,cAAc,SAAS,EAAE,GACxD;;;;;;;;;;;;;;;;;8BAIV,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAChD,8OAAC;;8CACC,8OAAC,oIAAA,CAAA,WAAQ;oCACN,GAAG,SAAS,UAAU;oCACvB,aAAY;oCACZ,WAAU;;;;;;gCAEX,OAAO,OAAO,kBACb,8OAAC;oCAAE,WAAU;8CACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;sCAI7B,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;sCAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAMxC"}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/session-card.tsx"], "sourcesContent": ["import { Calendar, ChevronDown, Clock, Video } from \"lucide-react\";\r\nimport { Session } from \"@/app/types/student/sessions\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { SessionDetailsDialog } from \"./session-details-dialog\";\r\nimport { SendMessageDialog } from \"./send-message-dialog\";\r\nimport Image from \"next/image\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ninterface SessionCardProps {\r\n  session: Session;\r\n}\r\n\r\nexport const SessionCard = ({ session }: SessionCardProps) => {\r\n  // State for UI interactions\r\n  const [showSendMessage, setShowSendMessage] = useState(false);\r\n  const [showDetails, setShowDetails] = useState(false);\r\n  const [defaultTab, setDefaultTab] = useState<\"details\" | \"notes\">(\"details\");\r\n  const [needsFeedback, setNeedsFeedback] = useState(false);\r\n  const router = useRouter();\r\n\r\n  // Check if session needs feedback (status is upcoming but end time has passed)\r\n  useEffect(() => {\r\n    if (session.status === \"upcoming\") {\r\n      const endTime = new Date(session.end_time);\r\n      const currentTime = new Date();\r\n      if (endTime < currentTime) {\r\n        setNeedsFeedback(true);\r\n      }\r\n    }\r\n  }, [session]);\r\n\r\n  const formatDate = (date: string) => {\r\n    return new Date(date).toLocaleDateString(\"en-US\", {\r\n      weekday: \"long\",\r\n      month: \"long\",\r\n      day: \"numeric\",\r\n    });\r\n  };\r\n\r\n  const formatTime = (start: string, end: string) => {\r\n    const startTime = new Date(start).toLocaleTimeString([], {\r\n      hour: \"2-digit\",\r\n      minute: \"2-digit\",\r\n    });\r\n    const endTime = new Date(end).toLocaleTimeString([], {\r\n      hour: \"2-digit\",\r\n      minute: \"2-digit\",\r\n    });\r\n    return `${startTime} - ${endTime}`;\r\n  };\r\n\r\n  // No longer needed as we're using direct link\r\n\r\n  const getStatusStyles = () => {\r\n    if (needsFeedback) {\r\n      return \"bg-purple-100 text-purple-600 border border-purple-300\";\r\n    }\r\n\r\n    switch (session.status) {\r\n      case \"cancelled\":\r\n        return \"bg-yellow-100 text-yellow-600\";\r\n      case \"completed\":\r\n        return \"bg-green-600 text-white\";\r\n      default:\r\n        return \"bg-green-100 text-green-600\";\r\n    }\r\n  };\r\n\r\n  const getStatusLabel = () => {\r\n    if (needsFeedback) {\r\n      return \"Feedback Needed\";\r\n    }\r\n\r\n    switch (session.status) {\r\n      case \"cancelled\":\r\n        return \"Cancelled\";\r\n      case \"completed\":\r\n        return \"Completed\";\r\n      default:\r\n        return \"Upcoming\";\r\n    }\r\n  };\r\n\r\n  const handleActionClick = () => {\r\n    if (session.status === \"completed\") {\r\n      setShowDetails(true);\r\n      setDefaultTab(\"notes\");\r\n      return;\r\n    }\r\n    if (needsFeedback) {\r\n      router.push(\"/student/dashboard\");\r\n      return;\r\n    }\r\n    setShowSendMessage(true);\r\n  };\r\n\r\n  return (\r\n    <div className=\"border border-gray-200 rounded-lg\">\r\n      <div className=\"flex md:flex-row flex-col md:items-center items-start justify-between sm:px-6 p-4 sm:py-5 gap-y-4 text-gray-700 bg-gray-100/60\">\r\n        <div className=\"flex md:flex-row flex-col-reverse md:items-center items-start gap-2\">\r\n          <h3 className=\"text-xl font-medium\">{session.event_name}</h3>\r\n          <span\r\n            className={`px-3 py-1 text-lg font-semibold rounded-2xl ${getStatusStyles()}`}\r\n          >\r\n            {getStatusLabel()}\r\n          </span>\r\n        </div>\r\n\r\n        <Button onClick={() => setShowDetails(true)}>\r\n          View Details\r\n          <ChevronDown className=\"w-6 h-6 transform transition-transform duration-200 hover:rotate-180 rotate-0\" />\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"flex md:flex-row flex-col md:items-center items-start gap-6 sm:px-6 p-4 sm:py-5 gap-y-2 border-b-2 text-gray-600 text-md font-medium\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Calendar className=\"w-6 h-6\" />\r\n          <span>{formatDate(session.date)}</span>\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Clock className=\"w-6 h-6\" />\r\n          <span>{formatTime(session.start_time, session.end_time)}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex md:flex-row flex-col justify-between md:items-center sm:px-6 p-4 sm:py-5 gap-y-5\">\r\n        <div className=\"space-y-4 text-md font-medium\">\r\n          <div className=\"space-y-2\">\r\n            <p className=\"text-gray-600\">Counselor</p>\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"relative w-10 h-10 flex-shrink-0\">\r\n                <Image\r\n                  src={\r\n                    session.counselor_profile_picture ||\r\n                    generatePlaceholder(\r\n                      session.counselor_name?.split(\" \")[0] || \"D\",\r\n                      session.counselor_name?.split(\" \")[1] || \"U\"\r\n                    )\r\n                  }\r\n                  alt=\"Counselor\"\r\n                  fill\r\n                  className=\"rounded-full object-cover\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <div className=\"font-medium text-gray-900\">\r\n                  {session.counselor_name || \"Deleted User\"}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">Counselor</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"space-y-2 \">\r\n            <p className=\"text-gray-600\">Meeting link</p>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"p-2 bg-blue-100 rounded-full\">\r\n                <Video className=\"w-5 h-5 text-blue-600\" />\r\n              </div>\r\n              <a\r\n                href={session.meeting_link}\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-blue-500 underline hover:text-blue-700 text-sm mt-2 inline-block\"\r\n              >\r\n                Join Meeting\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <button\r\n          onClick={handleActionClick}\r\n          className=\"w-[70%] mx-auto sm:mx-0 sm:w-auto text-md bg-gray-900 hover:bg-gray-950 rounded-2xl sm:p-3 p-2 text-white font-medium\"\r\n        >\r\n          {needsFeedback\r\n            ? \"Provide Feedback\"\r\n            : session.status === \"upcoming\"\r\n            ? \"Send Message\"\r\n            : \"View Notes\"}\r\n        </button>\r\n      </div>\r\n\r\n      <SessionDetailsDialog\r\n        session={session}\r\n        open={showDetails}\r\n        onOpenChange={setShowDetails}\r\n        defaultTab={defaultTab}\r\n      />\r\n      <SendMessageDialog\r\n        open={showSendMessage}\r\n        onOpenChange={setShowSendMessage}\r\n        counselorInfo={{\r\n          user_id: session.counselor_user_id,\r\n          first_name: session.counselor_name?.split(\" \")[0] || \"\",\r\n          last_name: session.counselor_name?.split(\" \")[1] || \"\",\r\n          profile_picture_url: session.counselor_profile_picture || null,\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAAA;AAAA;AAAA;;;;;;;;;;AAcO,MAAM,cAAc,CAAC,EAAE,OAAO,EAAoB;IACvD,4BAA4B;IAC5B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,+EAA+E;IAC/E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM,KAAK,YAAY;YACjC,MAAM,UAAU,IAAI,KAAK,QAAQ,QAAQ;YACzC,MAAM,cAAc,IAAI;YACxB,IAAI,UAAU,aAAa;gBACzB,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,SAAS;YACT,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC,OAAe;QACjC,MAAM,YAAY,IAAI,KAAK,OAAO,kBAAkB,CAAC,EAAE,EAAE;YACvD,MAAM;YACN,QAAQ;QACV;QACA,MAAM,UAAU,IAAI,KAAK,KAAK,kBAAkB,CAAC,EAAE,EAAE;YACnD,MAAM;YACN,QAAQ;QACV;QACA,OAAO,GAAG,UAAU,GAAG,EAAE,SAAS;IACpC;IAEA,8CAA8C;IAE9C,MAAM,kBAAkB;QACtB,IAAI,eAAe;YACjB,OAAO;QACT;QAEA,OAAQ,QAAQ,MAAM;YACpB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,eAAe;YACjB,OAAO;QACT;QAEA,OAAQ,QAAQ,MAAM;YACpB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,QAAQ,MAAM,KAAK,aAAa;YAClC,eAAe;YACf,cAAc;YACd;QACF;QACA,IAAI,eAAe;YACjB,OAAO,IAAI,CAAC;YACZ;QACF;QACA,mBAAmB;IACrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuB,QAAQ,UAAU;;;;;;0CACvD,8OAAC;gCACC,WAAW,CAAC,4CAA4C,EAAE,mBAAmB;0CAE5E;;;;;;;;;;;;kCAIL,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,eAAe;;4BAAO;0CAE3C,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;;0BAI3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAM,WAAW,QAAQ,IAAI;;;;;;;;;;;;kCAEhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAM,WAAW,QAAQ,UAAU,EAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;0BAI1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KACE,QAAQ,yBAAyB,IACjC,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAChB,QAAQ,cAAc,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI,KACzC,QAAQ,cAAc,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;oDAG7C,KAAI;oDACJ,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAGd,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,cAAc,IAAI;;;;;;kEAE7B,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDACC,MAAM,QAAQ,YAAY;gDAC1B,QAAO;gDACP,KAAI;gDACJ,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAMP,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAET,gBACG,qBACA,QAAQ,MAAM,KAAK,aACnB,iBACA;;;;;;;;;;;;0BAIR,8OAAC,yKAAA,CAAA,uBAAoB;gBACnB,SAAS;gBACT,MAAM;gBACN,cAAc;gBACd,YAAY;;;;;;0BAEd,8OAAC,sKAAA,CAAA,oBAAiB;gBAChB,MAAM;gBACN,cAAc;gBACd,eAAe;oBACb,SAAS,QAAQ,iBAAiB;oBAClC,YAAY,QAAQ,cAAc,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;oBACrD,WAAW,QAAQ,cAAc,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;oBACpD,qBAAqB,QAAQ,yBAAyB,IAAI;gBAC5D;;;;;;;;;;;;AAIR"}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/sessions-list.tsx"], "sourcesContent": ["import { Session } from \"@/app/types/student/sessions\";\r\nimport { SessionCard } from \"./session-card\";\r\nimport { Calendar } from \"lucide-react\";\r\n\r\ninterface SessionsListProps {\r\n  sessions: Session[];\r\n  status: \"upcoming\" | \"completed\" | \"cancelled\" | \"pending\";\r\n}\r\n\r\nexport const SessionsList = ({ sessions, status }: SessionsListProps) => {\r\n  const filteredSessions = sessions.filter(\r\n    (session) => session.status === status\r\n  );\r\n\r\n  if (filteredSessions.length === 0) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center py-12 text-center\">\r\n        <Calendar className=\"w-12 h-12 text-gray-400 mb-3\" />\r\n        <p className=\"text-gray-600 font-medium\">\r\n          {status === \"upcoming\" && \"No upcoming sessions\"}\r\n          {status === \"completed\" && \"No completed sessions\"}\r\n          {status === \"cancelled\" && \"No cancelled sessions\"}\r\n          {status === \"pending\" && \"No sessions needing feedback\"}\r\n        </p>\r\n        <p className=\"text-gray-500 text-sm mt-1\">\r\n          {status === \"upcoming\" && \"Book a session with one of our counselors\"}\r\n          {status === \"completed\" && \"All sessions are up to date\"}\r\n          {status === \"cancelled\" && \"Your cancelled sessions will appear here\"}\r\n          {status === \"pending\" &&\r\n            \"Sessions that need feedback will appear here\"}\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4 \">\r\n      {filteredSessions.map((session) => (\r\n        <SessionCard key={session.id} session={session} />\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAOO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAqB;IAClE,MAAM,mBAAmB,SAAS,MAAM,CACtC,CAAC,UAAY,QAAQ,MAAM,KAAK;IAGlC,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC;oBAAE,WAAU;;wBACV,WAAW,cAAc;wBACzB,WAAW,eAAe;wBAC1B,WAAW,eAAe;wBAC1B,WAAW,aAAa;;;;;;;8BAE3B,8OAAC;oBAAE,WAAU;;wBACV,WAAW,cAAc;wBACzB,WAAW,eAAe;wBAC1B,WAAW,eAAe;wBAC1B,WAAW,aACV;;;;;;;;;;;;;IAIV;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,4JAAA,CAAA,cAAW;gBAAkB,SAAS;eAArB,QAAQ,EAAE;;;;;;;;;;AAIpC"}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2251, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/skeleton.tsx"], "sourcesContent": ["import { Skeleton } from \"@components/ui/skeleton\";\r\n\r\nexport const PendingSessionsSkeleton = () => {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {[1, 2].map((i) => (\r\n        <div\r\n          key={i}\r\n          className=\"border border-gray-200 rounded-lg p-4 bg-gray-50/50\"\r\n        >\r\n          <div className=\"flex flex-col gap-4\">\r\n            {/* Top row */}\r\n            <div className=\"flex sm:flex-row flex-col sm:items-start items-center justify-between gap-x-4 gap-y-5\">\r\n              <div className=\"flex items-start gap-3 flex-grow\">\r\n                <Skeleton className=\"h-10 w-10 rounded-xl\" />\r\n                <div>\r\n                  <Skeleton className=\"h-6 w-48 mb-2\" />\r\n                  <Skeleton className=\"h-4 w-32\" />\r\n                </div>\r\n              </div>\r\n              <Skeleton className=\"h-10 w-32 rounded-lg\" />\r\n            </div>\r\n\r\n            {/* Bottom row */}\r\n            <div className=\"flex items-center gap-3 border-t pt-3\">\r\n              <Skeleton className=\"h-10 w-10 rounded-full\" />\r\n              <div>\r\n                <Skeleton className=\"h-5 w-32 mb-1\" />\r\n                <Skeleton className=\"h-4 w-20\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const SessionsSkeleton = () => {\r\n  return (\r\n    <div className=\"space-y-6 sm:space-y-8 md:space-y-12\">\r\n      {/* Tabs Skeleton */}\r\n      <div className=\"flex flex-wrap gap-8 border-b sm:gap-12 md:gap-16\">\r\n        {[1, 2, 3].map((i) => (\r\n          <div key={i} className=\"pb-4 px-2 sm:px-4 md:px-6 w-full sm:w-auto\">\r\n            <Skeleton className=\"h-6 w-full sm:w-24 md:w-32\" />\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Session Cards Skeleton */}\r\n      <div className=\"space-y-4 sm:space-y-8 md:space-y-12\">\r\n        {[1].map((i) => (\r\n          <div\r\n            key={i}\r\n            className=\"border border-gray-200 rounded-lg sm:max-w-md md:max-w-xl lg:max-w-3xl xl:max-w-4xl mx-auto\"\r\n          >\r\n            {/* Header */}\r\n            <div className=\"flex items-center justify-between px-6 py-5 bg-gray-100/60\">\r\n              <div className=\"flex items-center gap-2 sm:gap-4 md:gap-6\">\r\n                <Skeleton className=\"h-7 w-48 sm:w-64 md:w-80\" />\r\n                <Skeleton className=\"h-7 w-24 sm:w-32 md:w-40 rounded-2xl\" />\r\n              </div>\r\n              <Skeleton className=\"h-10 w-32 sm:w-40 md:w-48\" />\r\n            </div>\r\n\r\n            {/* Date/Time */}\r\n            <div className=\"flex items-center gap-6 px-6 py-5 border-b-2 sm:gap-8 md:gap-12\">\r\n              <div className=\"flex items-center gap-2 sm:gap-4 md:gap-6\">\r\n                <Skeleton className=\"h-6 w-6 sm:w-8 md:w-10\" />\r\n                <Skeleton className=\"h-6 w-32 sm:w-40 md:w-48\" />\r\n              </div>\r\n              <div className=\"flex items-center gap-2 sm:gap-4 md:gap-6\">\r\n                <Skeleton className=\"h-6 w-6 sm:w-8 md:w-10\" />\r\n                <Skeleton className=\"h-6 w-32 sm:w-40 md:w-48\" />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Details */}\r\n            <div className=\"flex md:flex-row flex-col justify-between md:items-center px-6 py-5 sm:py-8 md:py-12\">\r\n              <div className=\"space-y-4 sm:space-y-8 md:space-y-12\">\r\n                <div className=\"space-y-2 sm:space-y-4 md:space-y-6\">\r\n                  <Skeleton className=\"h-5 w-24 sm:w-32 md:w-40\" />\r\n                  <div className=\"flex items-center gap-3 sm:gap-4 md:gap-6\">\r\n                    <Skeleton className=\"h-12 w-12 sm:h-16 sm:w-16 md:h-20 md:w-20 rounded-full\" />\r\n                    <Skeleton className=\"h-6 w-32 sm:w-40 md:w-48\" />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-2 sm:space-y-4 md:space-y-6\">\r\n                  <Skeleton className=\"h-5 w-24 sm:w-32 md:w-40\" />\r\n                  <div className=\"flex items-center gap-2 sm:gap-4 md:gap-6\">\r\n                    <Skeleton className=\"h-9 w-9 sm:h-12 sm:w-12 md:h-16 md:w-16 rounded-full\" />\r\n                    <Skeleton className=\"h-6 w-32 sm:w-40 md:w-48\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <Skeleton className=\"w-full sm:w-32 md:w-48 h-12 rounded-2xl mt-4 sm:mt-6 md:mt-8\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEO,MAAM,0BAA0B;IACrC,qBACE,8OAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,kBACX,8OAAC;gBAEC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;;8DACC,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAGxB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAItB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;;sDACC,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;eArBrB;;;;;;;;;;AA6Bf;AAEO,MAAM,mBAAmB;IAC9B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;wBAAY,WAAU;kCACrB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;uBADZ;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;0BACZ;oBAAC;iBAAE,CAAC,GAAG,CAAC,CAAC,kBACR,8OAAC;wBAEC,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAItB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAKxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAIxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAI1B,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;uBA3CjB;;;;;;;;;;;;;;;;AAkDjB"}}, {"offset": {"line": 2630, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2636, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSessions } from \"@hooks/student/useSessions\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { SessionsList } from \"./sessions-list\";\r\nimport { SessionsSkeleton, PendingSessionsSkeleton } from \"./skeleton\";\r\nimport { Session } from \"@/app/types/student/sessions\";\r\nimport { SessionCard } from \"./session-card\";\r\nimport { Calendar } from \"lucide-react\";\r\n\r\ntype TabType = \"upcoming\" | \"completed\" | \"cancelled\" | \"pending\";\r\n\r\ninterface Tab {\r\n  id: string;\r\n  label: string;\r\n  count?: number | null;\r\n}\r\n\r\n// Helper function to check if a session needs feedback\r\nconst isPendingFeedback = (session: Session): boolean => {\r\n  if (session.status === \"upcoming\") {\r\n    const endTime = new Date(session.end_time);\r\n    const currentTime = new Date();\r\n    return endTime < currentTime;\r\n  }\r\n  return false;\r\n};\r\n\r\nexport default function Sessions() {\r\n  const { sessions, fetchSessions } = useSessions();\r\n  const [activeTab, setActiveTab] = useState<TabType>(\"upcoming\");\r\n  const [tabs, setTabs] = useState<Tab[]>([]);\r\n\r\n  useEffect(() => {\r\n    if (!sessions || !sessions.total) {\r\n      fetchSessions(Intl.DateTimeFormat().resolvedOptions().timeZone);\r\n    }\r\n  }, [fetchSessions]);\r\n\r\n  useEffect(() => {\r\n    if (sessions) {\r\n      const upcomingCount = sessions.items.filter(\r\n        (s) => s.status === \"upcoming\" && !isPendingFeedback(s)\r\n      ).length;\r\n      const completedCount = sessions.items.filter(\r\n        (s) => s.status === \"completed\"\r\n      ).length;\r\n      const cancelledCount = sessions.items.filter(\r\n        (s) => s.status === \"cancelled\"\r\n      ).length;\r\n      const pendingCount = sessions.items.filter((s) =>\r\n        isPendingFeedback(s)\r\n      ).length;\r\n\r\n      const tabsList = [\r\n        {\r\n          id: \"upcoming\",\r\n          label: \"Upcoming\",\r\n          count: upcomingCount > 0 ? upcomingCount : null,\r\n        },\r\n        {\r\n          id: \"pending\",\r\n          label: \"Feedback Needed\",\r\n          count: pendingCount > 0 ? pendingCount : null,\r\n        },\r\n        {\r\n          id: \"completed\",\r\n          label: \"Completed\",\r\n          count: completedCount > 0 ? completedCount : null,\r\n        },\r\n        {\r\n          id: \"cancelled\",\r\n          label: \"Cancelled\",\r\n          count: cancelledCount > 0 ? cancelledCount : null,\r\n        },\r\n      ];\r\n      setTabs(tabsList);\r\n    }\r\n  }, [sessions]);\r\n\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (sessions) {\r\n      setLoading(false);\r\n    }\r\n  }, [sessions]);\r\n\r\n  return (\r\n    <div className=\"container bg-white mx-auto md:p-8 sm:p-6 p-4 rounded-xl space-y-6\">\r\n      <div className=\"space-y-2\">\r\n        <h1 className=\"text-2xl font-semibold\">Your Sessions</h1>\r\n        <p className=\"text-gray-600\">You can view all of your sessions here</p>\r\n      </div>\r\n\r\n      {!sessions ? (\r\n        <SessionsSkeleton />\r\n      ) : (\r\n        <>\r\n          <div className=\"border-b\">\r\n            <div className=\"flex gap-8 overflow-x-auto\">\r\n              {tabs.map((tab) => (\r\n                <button\r\n                  key={tab.id}\r\n                  onClick={() => setActiveTab(tab.id as TabType)}\r\n                  className={`pb-4 px-2 relative text-base font-medium transition-colors ${\r\n                    activeTab === tab.id\r\n                      ? \"text-gray-900 border-b-2 border-gray-900\"\r\n                      : \"text-gray-600 hover:text-gray-900\"\r\n                  }`}\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    {tab.label}\r\n                    {tab.count && (\r\n                      <span className=\"bg-yellow-100 text-yellow-800 text-xs px-2.5 py-0.5 rounded-full\">\r\n                        {tab.count}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-6\">\r\n            {activeTab === \"upcoming\" &&\r\n              (loading ? (\r\n                <SessionsSkeleton />\r\n              ) : (\r\n                <SessionsList\r\n                  sessions={sessions.items.filter((s) => !isPendingFeedback(s))}\r\n                  status=\"upcoming\"\r\n                />\r\n              ))}\r\n            {activeTab === \"pending\" &&\r\n              (loading ? (\r\n                <PendingSessionsSkeleton />\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {sessions.items\r\n                    .filter((s) => isPendingFeedback(s))\r\n                    .map((session) => (\r\n                      <SessionCard key={session.id} session={session} />\r\n                    ))}\r\n                  {sessions.items.filter((s) => isPendingFeedback(s)).length ===\r\n                    0 && (\r\n                    <div className=\"flex flex-col items-center justify-center py-12 text-center\">\r\n                      <Calendar className=\"w-12 h-12 text-gray-400 mb-3\" />\r\n                      <p className=\"text-gray-600 font-medium\">\r\n                        No sessions needing feedback\r\n                      </p>\r\n                      <p className=\"text-gray-500 text-sm mt-1\">\r\n                        Sessions that need feedback will appear here\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            {activeTab === \"completed\" &&\r\n              (loading ? (\r\n                <SessionsSkeleton />\r\n              ) : (\r\n                <SessionsList sessions={sessions.items} status=\"completed\" />\r\n              ))}\r\n            {activeTab === \"cancelled\" &&\r\n              (loading ? (\r\n                <SessionsSkeleton />\r\n              ) : (\r\n                <SessionsList sessions={sessions.items} status=\"cancelled\" />\r\n              ))}\r\n          </div>\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AARA;;;;;;;;AAkBA,uDAAuD;AACvD,MAAM,oBAAoB,CAAC;IACzB,IAAI,QAAQ,MAAM,KAAK,YAAY;QACjC,MAAM,UAAU,IAAI,KAAK,QAAQ,QAAQ;QACzC,MAAM,cAAc,IAAI;QACxB,OAAO,UAAU;IACnB;IACA,OAAO;AACT;AAEe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,EAAE;YAChC,cAAc,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;QAChE;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,MAAM,gBAAgB,SAAS,KAAK,CAAC,MAAM,CACzC,CAAC,IAAM,EAAE,MAAM,KAAK,cAAc,CAAC,kBAAkB,IACrD,MAAM;YACR,MAAM,iBAAiB,SAAS,KAAK,CAAC,MAAM,CAC1C,CAAC,IAAM,EAAE,MAAM,KAAK,aACpB,MAAM;YACR,MAAM,iBAAiB,SAAS,KAAK,CAAC,MAAM,CAC1C,CAAC,IAAM,EAAE,MAAM,KAAK,aACpB,MAAM;YACR,MAAM,eAAe,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,IAC1C,kBAAkB,IAClB,MAAM;YAER,MAAM,WAAW;gBACf;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO,gBAAgB,IAAI,gBAAgB;gBAC7C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO,eAAe,IAAI,eAAe;gBAC3C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO,iBAAiB,IAAI,iBAAiB;gBAC/C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO,iBAAiB,IAAI,iBAAiB;gBAC/C;aACD;YACD,QAAQ;QACV;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAG9B,CAAC,yBACA,8OAAC,qJAAA,CAAA,mBAAgB;;;;qCAEjB;;kCACE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,6CACA,qCACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,IAAI,KAAK;4CACT,IAAI,KAAK,kBACR,8OAAC;gDAAK,WAAU;0DACb,IAAI,KAAK;;;;;;;;;;;;mCAZX,IAAI,EAAE;;;;;;;;;;;;;;;kCAqBnB,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,cACb,CAAC,wBACC,8OAAC,qJAAA,CAAA,mBAAgB;;;;qDAEjB,8OAAC,6JAAA,CAAA,eAAY;gCACX,UAAU,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,IAAM,CAAC,kBAAkB;gCAC1D,QAAO;;;;;oCAEV;4BACF,cAAc,aACb,CAAC,wBACC,8OAAC,qJAAA,CAAA,0BAAuB;;;;qDAExB,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,KAAK,CACZ,MAAM,CAAC,CAAC,IAAM,kBAAkB,IAChC,GAAG,CAAC,CAAC,wBACJ,8OAAC,4JAAA,CAAA,cAAW;4CAAkB,SAAS;2CAArB,QAAQ,EAAE;;;;;oCAE/B,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,IAAM,kBAAkB,IAAI,MAAM,KACxD,mBACA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAE,WAAU;0DAA4B;;;;;;0DAGzC,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;oCAMjD;4BACF,cAAc,eACb,CAAC,wBACC,8OAAC,qJAAA,CAAA,mBAAgB;;;;qDAEjB,8OAAC,6JAAA,CAAA,eAAY;gCAAC,UAAU,SAAS,KAAK;gCAAE,QAAO;;;;;oCAChD;4BACF,cAAc,eACb,CAAC,wBACC,8OAAC,qJAAA,CAAA,mBAAgB;;;;qDAEjB,8OAAC,6JAAA,CAAA,eAAY;gCAAC,UAAU,SAAS,KAAK;gCAAE,QAAO;;;;;oCAChD;;;;;;;;;;;;;;;AAMf"}}, {"offset": {"line": 2895, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2901, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/sessions/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Sessions from \"@/app/components/student/sessions\";\r\n\r\nexport default function SessionsPage() {\r\n  return (\r\n      <Sessions/>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACI,8OAAC,kJAAA,CAAA,UAAQ;;;;;AAEf"}}, {"offset": {"line": 2916, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
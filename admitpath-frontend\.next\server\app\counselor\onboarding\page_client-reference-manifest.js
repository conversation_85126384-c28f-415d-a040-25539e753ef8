globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/counselor/onboarding/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/app/counselor/layout.tsx <module evaluation>":{"id":"[project]/app/counselor/layout.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_6ed894._.js","static/chunks/_46ad82._.js","static/chunks/app_counselor_layout_tsx_c2af5c._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/app/layout.tsx":{"id":"[project]/app/layout.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js"],"async":false},"[project]/app/components/counselor/profile/create/index.tsx <module evaluation>":{"id":"[project]/app/components/counselor/profile/create/index.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_6ed894._.js","static/chunks/_46ad82._.js","static/chunks/app_counselor_layout_tsx_c2af5c._.js","static/chunks/_b769d8._.js","static/chunks/node_modules_next_bf6e9a._.js","static/chunks/node_modules_@fortawesome_free-solid-svg-icons_index_mjs_6d4e8d._.js","static/chunks/node_modules_@headlessui_react_dist_d896b9._.js","static/chunks/node_modules_c37d52._.js","static/chunks/app_counselor_onboarding_page_tsx_1a77d3._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/app/counselor/layout.tsx":{"id":"[project]/app/counselor/layout.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_6ed894._.js","static/chunks/_46ad82._.js","static/chunks/app_counselor_layout_tsx_c2af5c._.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/app/components/counselor/profile/create/index.tsx":{"id":"[project]/app/components/counselor/profile/create/index.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_6ed894._.js","static/chunks/_46ad82._.js","static/chunks/app_counselor_layout_tsx_c2af5c._.js","static/chunks/_b769d8._.js","static/chunks/node_modules_next_bf6e9a._.js","static/chunks/node_modules_@fortawesome_free-solid-svg-icons_index_mjs_6d4e8d._.js","static/chunks/node_modules_@headlessui_react_dist_d896b9._.js","static/chunks/node_modules_c37d52._.js","static/chunks/app_counselor_onboarding_page_tsx_1a77d3._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/app/layout.tsx <module evaluation>":{"id":"[project]/app/layout.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false}},"ssrModuleMapping":{"[project]/app/layout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/layout.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__873444._.js","server/chunks/ssr/[root of the server]__f9c7b3._.css"],"async":false}},"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/app/components/counselor/profile/create/index.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/counselor/profile/create/index.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__873444._.js","server/chunks/ssr/[root of the server]__f9c7b3._.css","server/chunks/ssr/node_modules_5c1ad3._.js","server/chunks/ssr/[root of the server]__e666a3._.js","server/chunks/ssr/_84667a._.js","server/chunks/ssr/node_modules_@fortawesome_free-solid-svg-icons_index_mjs_f47400._.js","server/chunks/ssr/node_modules_@headlessui_react_dist_8e25c2._.js","server/chunks/ssr/node_modules_ec47cb._.js","server/chunks/ssr/app_components_common_inputField_index_module_1a2828.css"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/app/counselor/layout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/counselor/layout.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__873444._.js","server/chunks/ssr/[root of the server]__f9c7b3._.css","server/chunks/ssr/node_modules_5c1ad3._.js","server/chunks/ssr/[root of the server]__e666a3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/app/components/counselor/profile/create/index.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/counselor/profile/create/index.tsx (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/app/counselor/layout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/counselor/layout.tsx (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}},"[project]/app/layout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/layout.tsx (client proxy)","name":"*","chunks":["server/app/counselor/onboarding/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/app/counselor/layout":[{"path":"static/chunks/[root of the server]__f9c7b3._.css","inlined":false}],"[project]/app/favicon.ico":[],"[project]/app/layout":[{"path":"static/chunks/[root of the server]__f9c7b3._.css","inlined":false}],"[project]/app/counselor/onboarding/page":[{"path":"static/chunks/[root of the server]__f9c7b3._.css","inlined":false},{"path":"static/chunks/app_components_common_inputField_index_module_1a2828.css","inlined":false}]},"entryJSFiles":{"[project]/app/counselor/onboarding/page":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_6ed894._.js","static/chunks/_46ad82._.js","static/chunks/app_counselor_layout_tsx_c2af5c._.js","static/chunks/_b769d8._.js","static/chunks/node_modules_next_bf6e9a._.js","static/chunks/node_modules_@fortawesome_free-solid-svg-icons_index_mjs_6d4e8d._.js","static/chunks/node_modules_@headlessui_react_dist_d896b9._.js","static/chunks/node_modules_c37d52._.js","static/chunks/app_counselor_onboarding_page_tsx_1a77d3._.js"],"[project]/app/layout":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js"],"[project]/app/counselor/layout":["static/chunks/[root of the server]__7240a4._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_6ed894._.js","static/chunks/_46ad82._.js","static/chunks/app_counselor_layout_tsx_c2af5c._.js"],"[project]/app/favicon.ico":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"]}}

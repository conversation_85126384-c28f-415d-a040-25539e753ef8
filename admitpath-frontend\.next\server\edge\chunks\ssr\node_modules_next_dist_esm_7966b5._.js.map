{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/api/constants.ts"], "sourcesContent": ["export * from '../shared/lib/constants'\n"], "names": [], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/api/image.ts"], "sourcesContent": ["export { default } from '../shared/lib/image-external'\nexport * from '../shared/lib/image-external'\n"], "names": ["default"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/export/helpers/is-dynamic-usage-error.ts"], "sourcesContent": ["import { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\n\nexport const isDynamicUsageError = (err: unknown) =>\n  isDynamicServerError(err) ||\n  isBailoutToCSRError(err) ||\n  isNextRouterError(err) ||\n  isDynamicPostpone(err)\n"], "names": ["isDynamicServerError", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone", "isDynamicUsageError", "err"], "mappings": ";;;AAAA,SAASA,oBAAoB,QAAQ,+CAA8C;AACnF,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,iBAAiB,QAAQ,4CAA2C;;;;;AAEtE,MAAMC,sBAAsB,CAACC,4MAClCL,uBAAAA,EAAqBK,QACrBJ,kOAAAA,EAAoBI,iNACpBH,oBAAAA,EAAkBG,2MAClBF,oBAAAA,EAAkBE,KAAI", "ignoreList": [0]}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
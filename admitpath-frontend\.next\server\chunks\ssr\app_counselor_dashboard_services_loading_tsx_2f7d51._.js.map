{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/counselor/dashboard/services/loading.tsx"], "sourcesContent": ["import Loader from \"@/app/components/student/loader\";\r\n\r\nexport default function Loading() {\r\n  return <Loader />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,uIAAA,CAAA,UAAM;;;;;AAChB"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
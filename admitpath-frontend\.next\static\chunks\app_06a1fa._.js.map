{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/usePackages.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nexport interface StudentPackageItem {\r\n  service_name: string;\r\n  hours: {\r\n    total: number;\r\n    used: number;\r\n    remaining: number;\r\n  };\r\n}\r\n\r\nexport interface StudentPackage {\r\n  id: number;\r\n  package: {\r\n    id: number;\r\n    title: string;\r\n    description: string;\r\n    counselor_name: string;\r\n    counselor_profile_picture: string;\r\n  };\r\n  start_date: string;\r\n  end_date: string | null;\r\n  status: 'pending' | 'active' | 'completed' | 'cancelled' | 'expired';\r\n  service_hours: {\r\n    [service_name: string]: {\r\n      total: number;\r\n      used: number;\r\n    };\r\n  };\r\n  sessions: {\r\n    id: number;\r\n    date: string;\r\n    start_time: string;\r\n    end_time: string;\r\n    status: string;\r\n    service_name: string;\r\n  }[];\r\n  progress?: {\r\n    [service_name: string]: {\r\n      total: number;\r\n      used: number;\r\n      remaining: number;\r\n    };\r\n  };\r\n}\r\n\r\nexport interface FirstSessionData {\r\n  event_name: string;\r\n  date: string;\r\n  start_time: string;\r\n  end_time: string;\r\n  student_email: string;\r\n}\r\n\r\nexport interface PackageSubscriptionPayload {\r\n  package_id: number;\r\n  selected_service: string;\r\n  first_session: FirstSessionData;\r\n  promo_code?: string;\r\n}\r\n\r\nexport interface SessionFromPackagePayload {\r\n  selected_service: string;\r\n  date: string;\r\n  start_time: string;\r\n  end_time: string;\r\n}\r\n\r\nexport interface PackagesState {\r\n  packages: StudentPackage[];\r\n  selectedPackage: StudentPackage | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  activePackages: StudentPackage[];\r\n  completedPackages: StudentPackage[];\r\n  fetchPackages: (status?: string) => Promise<void>;\r\n  fetchPackageById: (subscriptionId: number) => Promise<void>;\r\n  subscribeToPackage: (data: PackageSubscriptionPayload) => Promise<any>;\r\n  clearError: () => void;\r\n}\r\n\r\nconst usePackages = create<PackagesState>((set, get) => ({\r\n  packages: [],\r\n  selectedPackage: null,\r\n  loading: false,\r\n  error: null,\r\n  activePackages: [],\r\n  completedPackages: [],\r\n\r\n  fetchPackages: async (status = \"active\") => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.get<StudentPackage[]>(\r\n        `/student/packages/my-packages?status=${status}`\r\n      );\r\n\r\n      // Store packages in the appropriate state based on status\r\n      if (status === \"active\") {\r\n        set({\r\n          packages: response.data,\r\n          activePackages: response.data,\r\n          loading: false\r\n        });\r\n      } else if (status === \"completed\") {\r\n        set({\r\n          completedPackages: response.data,\r\n          loading: false\r\n        });\r\n      } else {\r\n        set({ packages: response.data, loading: false });\r\n      }\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.message || \"Failed to fetch packages\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchPackageById: async (subscriptionId) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.get<StudentPackage>(\r\n        `/student/packages/${subscriptionId}`\r\n      );\r\n      set({ selectedPackage: response.data, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.message || \"Failed to fetch package details\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  subscribeToPackage: async (data) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.post(\r\n        \"/student/packages/subscribe\",\r\n        data\r\n      );\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage = error.response?.data?.message || \"Failed to subscribe to package\";\r\n      set({ error: errorMessage, loading: false });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n\r\nexport default usePackages;\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAmFA,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QACvD,UAAU,EAAE;QACZ,iBAAiB;QACjB,SAAS;QACT,OAAO;QACP,gBAAgB,EAAE;QAClB,mBAAmB,EAAE;QAErB,eAAe,OAAO,SAAS,QAAQ;YACrC,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,qCAAqC,EAAE,QAAQ;gBAGlD,0DAA0D;gBAC1D,IAAI,WAAW,UAAU;oBACvB,IAAI;wBACF,UAAU,SAAS,IAAI;wBACvB,gBAAgB,SAAS,IAAI;wBAC7B,SAAS;oBACX;gBACF,OAAO,IAAI,WAAW,aAAa;oBACjC,IAAI;wBACF,mBAAmB,SAAS,IAAI;wBAChC,SAAS;oBACX;gBACF,OAAO;oBACL,IAAI;wBAAE,UAAU,SAAS,IAAI;wBAAE,SAAS;oBAAM;gBAChD;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;oBACxC,SAAS;gBACX;YACF;QACF;QAEA,kBAAkB,OAAO;YACvB,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,gBAAgB;gBAEvC,IAAI;oBAAE,iBAAiB,SAAS,IAAI;oBAAE,SAAS;gBAAM;YACvD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;oBACxC,SAAS;gBACX;YACF;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,+BACA;gBAEF,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACtD,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC;uCAEc"}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/packages/package-card.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport { ArrowRight, Calendar, Clock } from \"lucide-react\";\r\nimport { StudentPackage } from \"@/app/hooks/student/usePackages\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\n// Define the props interface\r\ninterface PackageCardProps {\r\n  pkg: StudentPackage;\r\n  onSelect: (pkg: StudentPackage) => void;\r\n}\r\n\r\nexport const PackageCard: React.FC<PackageCardProps> = ({ pkg, onSelect }) => {\r\n  // Calculate overall progress\r\n  const calculateProgress = () => {\r\n    let totalHours = 0;\r\n    let usedHours = 0;\r\n\r\n    Object.values(pkg.service_hours).forEach((service) => {\r\n      totalHours += service.total;\r\n      usedHours += service.used;\r\n    });\r\n\r\n    return {\r\n      totalHours,\r\n      usedHours,\r\n      percentage:\r\n        totalHours > 0 ? Math.round((usedHours / totalHours) * 100) : 0,\r\n    };\r\n  };\r\n\r\n  const progress = calculateProgress();\r\n  const daysLeft =\r\n    pkg.end_date && pkg.status === \"active\" && pkg.end_date !== null\r\n      ? Math.max(\r\n          0,\r\n          Math.ceil(\r\n            (new Date(pkg.end_date).getTime() - new Date().getTime()) /\r\n              (1000 * 60 * 60 * 24)\r\n          )\r\n        )\r\n      : null;\r\n\r\n  return (\r\n    <div\r\n      className=\"bg-white rounded-xl p-5 space-y-4 border hover:shadow-md cursor-pointer transition-all duration-200\"\r\n      onClick={() => onSelect(pkg)}\r\n    >\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold text-gray-800 line-clamp-1\">\r\n            {pkg.package.title}\r\n          </h3>\r\n          <p className=\"text-sm text-gray-500 flex items-center gap-1 mt-1\">\r\n            <Calendar className=\"h-3 w-3\" />\r\n            Started: {new Date(pkg.start_date).toLocaleDateString()}\r\n          </p>\r\n        </div>\r\n        <div className=\"text-right\">\r\n          <span\r\n            className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${\r\n              pkg.status === \"active\"\r\n                ? \"bg-green-100 text-green-800\"\r\n                : pkg.status === \"pending\"\r\n                ? \"bg-yellow-100 text-yellow-800\"\r\n                : pkg.status === \"completed\"\r\n                ? \"bg-blue-100 text-blue-800\"\r\n                : pkg.status === \"cancelled\"\r\n                ? \"bg-red-100 text-red-800\"\r\n                : \"bg-gray-100 text-gray-800\"\r\n            }`}\r\n          >\r\n            {pkg.status.charAt(0).toUpperCase() + pkg.status.slice(1)}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex items-center gap-2 pt-2\">\r\n        <Image\r\n          src={\r\n            pkg.package.counselor_profile_picture ||\r\n            generatePlaceholder(\r\n              pkg.package.counselor_name.split(\" \")[0],\r\n              pkg.package.counselor_name.split(\" \")[1]\r\n            )\r\n          }\r\n          alt={pkg.package.counselor_name}\r\n          width={40}\r\n          height={40}\r\n          className=\"rounded-full\"\r\n        />\r\n        <div>\r\n          <span className=\"font-medium text-gray-800\">\r\n            {pkg.package.counselor_name}\r\n          </span>\r\n          {daysLeft !== null && (\r\n            <p className=\"text-sm text-gray-500 flex items-center gap-1\">\r\n              <Clock className=\"h-3 w-3\" />\r\n              {daysLeft} days left\r\n            </p>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-1\">\r\n        <div className=\"flex justify-between text-sm\">\r\n          <span className=\"text-gray-600\">Progress</span>\r\n          <span className=\"font-medium\">\r\n            {progress.usedHours}/{progress.totalHours} hours used\r\n          </span>\r\n        </div>\r\n        <div className=\"h-2 w-full bg-gray-100 rounded-full overflow-hidden\">\r\n          <div\r\n            className=\"h-full bg-blue-500 rounded-full\"\r\n            style={{ width: `${progress.percentage}%` }}\r\n          ></div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"pt-2 flex justify-between items-center\">\r\n        <div className=\"text-sm text-gray-500\">\r\n          <span className=\"font-medium text-gray-700\">\r\n            {pkg.sessions.length}\r\n          </span>{\" \"}\r\n          sessions\r\n        </div>\r\n        <button className=\"flex items-center text-blue-600 font-medium text-sm\">\r\n          View Details\r\n          <ArrowRight className=\"w-4 h-4 ml-1\" />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AAFA;AAAA;AAAA;;;;;AAUO,MAAM,cAA0C,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE;IACvE,6BAA6B;IAC7B,MAAM,oBAAoB;QACxB,IAAI,aAAa;QACjB,IAAI,YAAY;QAEhB,OAAO,MAAM,CAAC,IAAI,aAAa,EAAE,OAAO,CAAC,CAAC;YACxC,cAAc,QAAQ,KAAK;YAC3B,aAAa,QAAQ,IAAI;QAC3B;QAEA,OAAO;YACL;YACA;YACA,YACE,aAAa,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc,OAAO;QAClE;IACF;IAEA,MAAM,WAAW;IACjB,MAAM,WACJ,IAAI,QAAQ,IAAI,IAAI,MAAM,KAAK,YAAY,IAAI,QAAQ,KAAK,OACxD,KAAK,GAAG,CACN,GACA,KAAK,IAAI,CACP,CAAC,IAAI,KAAK,IAAI,QAAQ,EAAE,OAAO,KAAK,IAAI,OAAO,OAAO,EAAE,IACtD,CAAC,OAAO,KAAK,KAAK,EAAE,MAG1B;IAEN,qBACE,6LAAC;QACC,WAAU;QACV,SAAS,IAAM,SAAS;;0BAExB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CACX,IAAI,OAAO,CAAC,KAAK;;;;;;0CAEpB,6LAAC;gCAAE,WAAU;;kDACX,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;oCACtB,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;kCAGzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAW,CAAC,wDAAwD,EAClE,IAAI,MAAM,KAAK,WACX,gCACA,IAAI,MAAM,KAAK,YACf,kCACA,IAAI,MAAM,KAAK,cACf,8BACA,IAAI,MAAM,KAAK,cACf,4BACA,6BACJ;sCAED,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;0BAK7D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KACE,IAAI,OAAO,CAAC,yBAAyB,IACrC,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAChB,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EACxC,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBAG5C,KAAK,IAAI,OAAO,CAAC,cAAc;wBAC/B,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;0CACb,IAAI,OAAO,CAAC,cAAc;;;;;;4BAE5B,aAAa,sBACZ,6LAAC;gCAAE,WAAU;;kDACX,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAChB;oCAAS;;;;;;;;;;;;;;;;;;;0BAMlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;;oCACb,SAAS,SAAS;oCAAC;oCAAE,SAAS,UAAU;oCAAC;;;;;;;;;;;;;kCAG9C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,SAAS,UAAU,CAAC,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;0BAKhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,IAAI,QAAQ,CAAC,MAAM;;;;;;4BACd;4BAAI;;;;;;;kCAGd,6LAAC;wBAAO,WAAU;;4BAAsD;0CAEtE,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKhC;KAzHa"}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,gBAAgB,sKAAgB,OAAO;AAE7C,MAAM,eAAe,sKAAgB,MAAM;AAE3C,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,sKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,sKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,8JAAM,UAAU,OAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-12 items-center justify-start border-b w-full overflow-x-auto\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap px-4 py-3 text-base font-medium text-gray-600 border-b-2 border-transparent transition-colors hover:text-gray-900\",\r\n      \"data-[state=active]:border-gray-900 data-[state=active]:text-gray-900\",\r\n      \"focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-4 ring-offset-background focus-visible:outline-none\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,oKAAc,IAAI;AAE/B,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,oKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,+KAC<PERSON>,yEACA,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@components/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/packages/SessionsListDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} from \"@/app/components/ui/dialog\";\r\nimport { Badge } from \"@/app/components/ui/badge\";\r\nimport {\r\n  Calendar,\r\n  Clock,\r\n  FileText,\r\n  CheckCircle,\r\n  XCircle,\r\n  BookOpen,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from \"@/app/components/ui/tabs\";\r\nimport { motion } from \"framer-motion\";\r\nimport { PackageSubscription } from \"@/app/types/student/package\";\r\n\r\ninterface SessionsListDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  subscription: PackageSubscription | null;\r\n}\r\n\r\nexport default function SessionsListDialog({\r\n  isOpen,\r\n  onClose,\r\n  subscription,\r\n}: SessionsListDialogProps) {\r\n  const [activeTab, setActiveTab] = useState(\"all\");\r\n\r\n  if (!subscription) return null;\r\n\r\n  // Filter sessions based on active tab\r\n  const filteredSessions = subscription.sessions.filter((session) => {\r\n    if (activeTab === \"all\") return true;\r\n    return session.status === activeTab;\r\n  });\r\n\r\n  // Get status color\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return \"bg-green-100 text-green-800 border-green-200\";\r\n      case \"upcoming\":\r\n        return \"bg-blue-100 text-blue-800 border-blue-200\";\r\n      case \"cancelled\":\r\n        return \"bg-red-100 text-red-800 border-red-200\";\r\n      default:\r\n        return \"bg-gray-100 text-gray-800 border-gray-200\";\r\n    }\r\n  };\r\n\r\n  // Get status icon\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return <CheckCircle className=\"w-4 h-4 text-green-600\" />;\r\n      case \"upcoming\":\r\n        return <Clock className=\"w-4 h-4 text-blue-600\" />;\r\n      case \"cancelled\":\r\n        return <XCircle className=\"w-4 h-4 text-red-600\" />;\r\n      default:\r\n        return <FileText className=\"w-4 h-4 text-gray-600\" />;\r\n    }\r\n  };\r\n\r\n  // Calculate total hours used and available\r\n  const totalHours = Object.values(subscription.service_hours).reduce(\r\n    (\r\n      acc: { used: number; total: number },\r\n      curr: { used: number; total: number }\r\n    ) => {\r\n      return {\r\n        used: acc.used + curr.used,\r\n        total: acc.total + curr.total,\r\n      };\r\n    },\r\n    { used: 0, total: 0 }\r\n  );\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-3xl\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-2xl font-bold flex items-center gap-2\">\r\n            <BookOpen className=\"w-5 h-5\" />\r\n            Package Sessions\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Package:{\" \"}\r\n            <span className=\"font-medium\">{subscription.package.title}</span>\r\n            <span className=\"mx-2\">•</span>\r\n            <span className=\"font-medium\">\r\n              Counselor: {subscription.package.counselor_name}\r\n            </span>\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"mt-4\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\r\n            <div className=\"bg-gray-50 p-4 rounded-lg col-span-1 md:col-span-2\">\r\n              <h4 className=\"text-sm text-gray-500 mb-1\">Package Progress</h4>\r\n              <div className=\"flex items-center justify-between mb-2\">\r\n                <p className=\"font-medium\">Total Hours</p>\r\n                <span className=\"text-sm font-medium\">\r\n                  {totalHours.used}/{totalHours.total} hours used\r\n                </span>\r\n              </div>\r\n              <div className=\"bg-gray-200 h-3 rounded-full overflow-hidden\">\r\n                <div\r\n                  className=\"bg-blue-500 h-full\"\r\n                  style={{\r\n                    width: `${(totalHours.used / totalHours.total) * 100}%`,\r\n                  }}\r\n                ></div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n              <h4 className=\"text-sm text-gray-500 mb-1\">Package Status</h4>\r\n              <div className=\"flex items-center gap-2 mt-1\">\r\n                <span\r\n                  className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getStatusColor(\r\n                    subscription.status\r\n                  )}`}\r\n                >\r\n                  {subscription.status.charAt(0).toUpperCase() +\r\n                    subscription.status.slice(1)}\r\n                </span>\r\n                <span className=\"text-sm\">\r\n                  {!subscription.end_date\r\n                    ? subscription.status\r\n                    : subscription.status === \"active\"\r\n                    ? \"Valid until \" +\r\n                      new Date(subscription.end_date).toLocaleDateString()\r\n                    : subscription.status === \"expired\"\r\n                    ? \"Expired on \" +\r\n                      new Date(subscription.end_date).toLocaleDateString()\r\n                    : \"\"}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <Tabs\r\n            defaultValue=\"all\"\r\n            className=\"w-full\"\r\n            onValueChange={setActiveTab}\r\n          >\r\n            <TabsList className=\"mb-4\">\r\n              <TabsTrigger value=\"all\">All Sessions</TabsTrigger>\r\n              <TabsTrigger value=\"upcoming\">Upcoming</TabsTrigger>\r\n              <TabsTrigger value=\"completed\">Completed</TabsTrigger>\r\n              <TabsTrigger value=\"cancelled\">Cancelled</TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value={activeTab} className=\"mt-0\">\r\n              {filteredSessions.length === 0 ? (\r\n                <div className=\"text-center py-12 bg-gray-50 rounded-lg\">\r\n                  <h3 className=\"text-lg font-medium text-gray-700 mb-2\">\r\n                    No {activeTab !== \"all\" ? activeTab : \"\"} sessions found\r\n                  </h3>\r\n                  <p className=\"text-gray-500\">\r\n                    {activeTab === \"upcoming\"\r\n                      ? \"No upcoming sessions scheduled yet\"\r\n                      : activeTab === \"completed\"\r\n                      ? \"No completed sessions yet\"\r\n                      : activeTab === \"cancelled\"\r\n                      ? \"No cancelled sessions\"\r\n                      : \"No sessions have been scheduled yet\"}\r\n                  </p>\r\n                  {activeTab === \"upcoming\" && (\r\n                    <Button className=\"mt-4\">Schedule a Session</Button>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-3\">\r\n                  {filteredSessions.map((session, index) => (\r\n                    <motion.div\r\n                      key={session.id}\r\n                      initial={{ opacity: 0, y: 10 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ duration: 0.2, delay: index * 0.05 }}\r\n                      className=\"border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow\"\r\n                    >\r\n                      <div className=\"flex flex-col md:flex-row md:items-center justify-between gap-4\">\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            {getStatusIcon(session.status)}\r\n                            <h3 className=\"font-medium text-lg\">\r\n                              {session.service_name}\r\n                            </h3>\r\n                            <Badge\r\n                              variant=\"outline\"\r\n                              className={getStatusColor(session.status)}\r\n                            >\r\n                              {session.status}\r\n                            </Badge>\r\n                          </div>\r\n\r\n                          <div className=\"flex flex-col sm:flex-row sm:items-center gap-x-4 gap-y-1 text-sm text-gray-600 mt-2\">\r\n                            <div className=\"flex items-center gap-1\">\r\n                              <Calendar className=\"w-4 h-4\" />\r\n                              <span>\r\n                                {new Date(session.date).toLocaleDateString()}\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"flex items-center gap-1\">\r\n                              <Clock className=\"w-4 h-4\" />\r\n                              <span>\r\n                                {new Date(\r\n                                  session.start_time\r\n                                ).toLocaleTimeString([], {\r\n                                  hour: \"2-digit\",\r\n                                  minute: \"2-digit\",\r\n                                })}\r\n                                {\" - \"}\r\n                                {new Date(session.end_time).toLocaleTimeString(\r\n                                  [],\r\n                                  {\r\n                                    hour: \"2-digit\",\r\n                                    minute: \"2-digit\",\r\n                                  }\r\n                                )}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-2 self-end md:self-center\">\r\n                          {session.status === \"completed\" && (\r\n                            <Button variant=\"outline\" size=\"sm\">\r\n                              View Notes\r\n                            </Button>\r\n                          )}\r\n                          {session.status === \"upcoming\" && (\r\n                            <Button variant=\"outline\" size=\"sm\">\r\n                              Join Session\r\n                            </Button>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </TabsContent>\r\n          </Tabs>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AASA;AACA;AATA;AAAA;AAAA;AAAA;AAAA;AAeA;AAfA;;;AAXA;;;;;;;;AAmCe,SAAS,mBAAmB,EACzC,MAAM,EACN,OAAO,EACP,YAAY,EACY;;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,CAAC,cAAc,OAAO;IAE1B,sCAAsC;IACtC,MAAM,mBAAmB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,cAAc,OAAO,OAAO;QAChC,OAAO,QAAQ,MAAM,KAAK;IAC5B;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YAC<PERSON>,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,2CAA2C;IAC3C,MAAM,aAAa,OAAO,MAAM,CAAC,aAAa,aAAa,EAAE,MAAM,CACjE,CACE,KACA;QAEA,OAAO;YACL,MAAM,IAAI,IAAI,GAAG,KAAK,IAAI;YAC1B,OAAO,IAAI,KAAK,GAAG,KAAK,KAAK;QAC/B;IACF,GACA;QAAE,MAAM;QAAG,OAAO;IAAE;IAGtB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCACR;8CACT,6LAAC;oCAAK,WAAU;8CAAe,aAAa,OAAO,CAAC,KAAK;;;;;;8CACzD,6LAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,6LAAC;oCAAK,WAAU;;wCAAc;wCAChB,aAAa,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;8BAKrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAK,WAAU;;wDACb,WAAW,IAAI;wDAAC;wDAAE,WAAW,KAAK;wDAAC;;;;;;;;;;;;;sDAGxC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,GAAG,AAAC,WAAW,IAAI,GAAG,WAAW,KAAK,GAAI,IAAI,CAAC,CAAC;gDACzD;;;;;;;;;;;;;;;;;8CAKN,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAW,CAAC,sEAAsE,EAAE,eAClF,aAAa,MAAM,GAClB;8DAEF,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KACxC,aAAa,MAAM,CAAC,KAAK,CAAC;;;;;;8DAE9B,6LAAC;oDAAK,WAAU;8DACb,CAAC,aAAa,QAAQ,GACnB,aAAa,MAAM,GACnB,aAAa,MAAM,KAAK,WACxB,iBACA,IAAI,KAAK,aAAa,QAAQ,EAAE,kBAAkB,KAClD,aAAa,MAAM,KAAK,YACxB,gBACA,IAAI,KAAK,aAAa,QAAQ,EAAE,kBAAkB,KAClD;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC,mIAAA,CAAA,OAAI;4BACH,cAAa;4BACb,WAAU;4BACV,eAAe;;8CAEf,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAM;;;;;;sDACzB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;sDAC9B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAY;;;;;;sDAC/B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAY;;;;;;;;;;;;8CAGjC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAO;oCAAW,WAAU;8CACtC,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAyC;oDACjD,cAAc,QAAQ,YAAY;oDAAG;;;;;;;0DAE3C,6LAAC;gDAAE,WAAU;0DACV,cAAc,aACX,uCACA,cAAc,cACd,8BACA,cAAc,cACd,0BACA;;;;;;4CAEL,cAAc,4BACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAO;;;;;;;;;;;6DAI7B,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAK;gDACjD,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,QAAQ,MAAM;sFAC7B,6LAAC;4EAAG,WAAU;sFACX,QAAQ,YAAY;;;;;;sFAEvB,6LAAC,oIAAA,CAAA,QAAK;4EACJ,SAAQ;4EACR,WAAW,eAAe,QAAQ,MAAM;sFAEvC,QAAQ,MAAM;;;;;;;;;;;;8EAInB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,6LAAC;8FACE,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;;;;;;;;;;;;sFAG9C,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,6LAAC;;wFACE,IAAI,KACH,QAAQ,UAAU,EAClB,kBAAkB,CAAC,EAAE,EAAE;4FACvB,MAAM;4FACN,QAAQ;wFACV;wFACC;wFACA,IAAI,KAAK,QAAQ,QAAQ,EAAE,kBAAkB,CAC5C,EAAE,EACF;4FACE,MAAM;4FACN,QAAQ;wFACV;;;;;;;;;;;;;;;;;;;;;;;;;sEAOV,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,MAAM,KAAK,6BAClB,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,MAAK;8EAAK;;;;;;gEAIrC,QAAQ,MAAM,KAAK,4BAClB,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,MAAK;8EAAK;;;;;;;;;;;;;;;;;;+CAzDrC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyErC;GArOwB;KAAA"}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/packages/package-dialog.tsx"], "sourcesContent": ["import {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogTitle,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogFooter,\r\n} from \"@/app/components/ui/dialog\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { StudentPackage } from \"@/app/hooks/student/usePackages\";\r\nimport { PackageSubscription } from \"@/app/types/student/package\";\r\nimport {\r\n  Tabs,\r\n  TabsContent,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from \"@/app/components/ui/tabs\";\r\nimport { Calendar, Clock, CheckCircle, List } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useState } from \"react\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport SessionsListDialog from \"./SessionsListDialog\";\r\n\r\ninterface PackageDialogProps {\r\n  pkg: StudentPackage;\r\n  onClose: () => void;\r\n}\r\n\r\nexport function PackageDialog({ pkg, onClose }: PackageDialogProps) {\r\n  const [sessionsDialogOpen, setSessionsDialogOpen] = useState(false);\r\n\r\n  // Calculate days remaining\r\n  const daysLeft =\r\n    pkg.end_date && pkg.status === \"active\" && pkg.end_date !== null\r\n      ? Math.max(\r\n          0,\r\n          Math.ceil(\r\n            (new Date(pkg.end_date).getTime() - new Date().getTime()) /\r\n              (1000 * 60 * 60 * 24)\r\n          )\r\n        )\r\n      : null;\r\n\r\n  // Format date\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\r\n      year: \"numeric\",\r\n      month: \"long\",\r\n      day: \"numeric\",\r\n    });\r\n  };\r\n\r\n  // Format time\r\n  const formatTime = (timeString: string) => {\r\n    return new Date(timeString).toLocaleTimeString(\"en-US\", {\r\n      hour: \"2-digit\",\r\n      minute: \"2-digit\",\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Dialog open={!!pkg} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <div className=\"flex items-center justify-between\">\r\n            <DialogTitle className=\"text-xl\">{pkg.package.title}</DialogTitle>\r\n            <span\r\n              className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${\r\n                pkg.status === \"active\"\r\n                  ? \"bg-green-100 text-green-800\"\r\n                  : pkg.status === \"pending\"\r\n                  ? \"bg-yellow-100 text-yellow-800\"\r\n                  : pkg.status === \"completed\"\r\n                  ? \"bg-blue-100 text-blue-800\"\r\n                  : pkg.status === \"cancelled\"\r\n                  ? \"bg-red-100 text-red-800\"\r\n                  : \"bg-gray-100 text-gray-800\"\r\n              }`}\r\n            >\r\n              {pkg.status.charAt(0).toUpperCase() + pkg.status.slice(1)}\r\n            </span>\r\n          </div>\r\n          <DialogDescription className=\"text-base mt-2\">\r\n            {pkg.package.description}\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex items-center gap-4 py-4 border-y my-4\">\r\n          <Image\r\n            src={\r\n              pkg.package.counselor_profile_picture ||\r\n              generatePlaceholder(\r\n                pkg.package.counselor_name.split(\" \")[0],\r\n                pkg.package.counselor_name.split(\" \")[1]\r\n              )\r\n            }\r\n            alt={pkg.package.counselor_name}\r\n            width={48}\r\n            height={48}\r\n            className=\"rounded-full\"\r\n          />\r\n          <div>\r\n            <h3 className=\"font-medium\">{pkg.package.counselor_name}</h3>\r\n            <p className=\"text-sm text-gray-500\">Your counselor</p>\r\n          </div>\r\n        </div>\r\n\r\n        <Tabs defaultValue=\"overview\" className=\"w-full\">\r\n          <TabsList className=\"mb-4\">\r\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\r\n            <TabsTrigger value=\"services\">Services & Hours</TabsTrigger>\r\n            <TabsTrigger value=\"sessions\">Sessions</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"overview\" className=\"space-y-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                <h4 className=\"text-sm text-gray-500 mb-1\">Start Date</h4>\r\n                <p className=\"font-medium flex items-center gap-2\">\r\n                  <Calendar className=\"w-4 h-4 text-blue-500\" />\r\n                  {formatDate(pkg.start_date)}\r\n                </p>\r\n              </div>\r\n              {pkg.end_date !== null && (\r\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                  <h4 className=\"text-sm text-gray-500 mb-1\">End Date</h4>\r\n                  <p className=\"font-medium flex items-center gap-2\">\r\n                    <Calendar className=\"w-4 h-4 text-blue-500\" />\r\n                    {formatDate(pkg.end_date)}\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"services\" className=\"space-y-4\">\r\n            <div className=\"space-y-4\">\r\n              <h3 className=\"font-medium\">Service Hours</h3>\r\n              <div className=\"grid gap-3\">\r\n                {Object.entries(pkg.service_hours).map(([service, hours]) => (\r\n                  <div key={service} className=\"border rounded-lg p-4\">\r\n                    <div className=\"flex justify-between items-center mb-2\">\r\n                      <h4 className=\"font-medium\">{service}</h4>\r\n                      <span className=\"text-sm font-medium\">\r\n                        {hours.used}/{hours.total} hours used\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"h-2 w-full bg-gray-100 rounded-full overflow-hidden\">\r\n                      <div\r\n                        className=\"h-full bg-blue-500 rounded-full\"\r\n                        style={{\r\n                          width: `${(hours.used / hours.total) * 100}%`,\r\n                        }}\r\n                      ></div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"sessions\" className=\"space-y-4\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h3 className=\"font-medium\">Your Sessions</h3>\r\n            </div>\r\n\r\n            {pkg.sessions.length === 0 ? (\r\n              <div className=\"text-center py-8 bg-gray-50 rounded-lg\">\r\n                <h4 className=\"text-gray-700 mb-2\">\r\n                  No sessions scheduled yet\r\n                </h4>\r\n                <p className=\"text-gray-500 mb-4\">\r\n                  Your counselor will schedule sessions for you. Please check\r\n                  back later or contact your counselor.\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-3\">\r\n                {pkg.sessions.map((session) => (\r\n                  <div key={session.id} className=\"border rounded-lg p-4\">\r\n                    <div className=\"flex justify-between items-start\">\r\n                      <div>\r\n                        <h4 className=\"font-medium\">{session.service_name}</h4>\r\n                        <p className=\"text-sm text-gray-500\">\r\n                          {formatDate(session.date)} •{\" \"}\r\n                          {formatTime(session.start_time)} -{\" \"}\r\n                          {formatTime(session.end_time)}\r\n                        </p>\r\n                      </div>\r\n                      <span\r\n                        className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${\r\n                          session.status === \"completed\"\r\n                            ? \"bg-green-100 text-green-800\"\r\n                            : session.status === \"upcoming\"\r\n                            ? \"bg-blue-100 text-blue-800\"\r\n                            : session.status === \"cancelled\"\r\n                            ? \"bg-red-100 text-red-800\"\r\n                            : \"bg-gray-100 text-gray-800\"\r\n                        }`}\r\n                      >\r\n                        {session.status}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n\r\n                {pkg.sessions.length > 3 && (\r\n                  <div className=\"flex justify-center mt-4\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"flex items-center gap-2\"\r\n                      onClick={() => setSessionsDialogOpen(true)}\r\n                    >\r\n                      <List className=\"w-4 h-4\" />\r\n                      View All Sessions\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </TabsContent>\r\n        </Tabs>\r\n\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={onClose}>\r\n            Close\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n\r\n      <SessionsListDialog\r\n        isOpen={sessionsDialogOpen}\r\n        onClose={() => setSessionsDialogOpen(false)}\r\n        subscription={pkg as unknown as PackageSubscription}\r\n      />\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AAGA;AAOA;AACA;AACA;AACA;AAJA;AAAA;;;;;;;;;;;AAWO,SAAS,cAAc,EAAE,GAAG,EAAE,OAAO,EAAsB;;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,2BAA2B;IAC3B,MAAM,WACJ,IAAI,QAAQ,IAAI,IAAI,MAAM,KAAK,YAAY,IAAI,QAAQ,KAAK,OACxD,KAAK,GAAG,CACN,GACA,KAAK,IAAI,CACP,CAAC,IAAI,KAAK,IAAI,QAAQ,EAAE,OAAO,KAAK,IAAI,OAAO,OAAO,EAAE,IACtD,CAAC,OAAO,KAAK,KAAK,EAAE,MAG1B;IAEN,cAAc;IACd,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,cAAc;IACd,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM,CAAC,CAAC;QAAK,cAAc;;0BACjC,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,6LAAC,qIAAA,CAAA,eAAY;;0CACX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,cAAW;wCAAC,WAAU;kDAAW,IAAI,OAAO,CAAC,KAAK;;;;;;kDACnD,6LAAC;wCACC,WAAW,CAAC,wDAAwD,EAClE,IAAI,MAAM,KAAK,WACX,gCACA,IAAI,MAAM,KAAK,YACf,kCACA,IAAI,MAAM,KAAK,cACf,8BACA,IAAI,MAAM,KAAK,cACf,4BACA,6BACJ;kDAED,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;0CAG3D,6LAAC,qIAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC1B,IAAI,OAAO,CAAC,WAAW;;;;;;;;;;;;kCAI5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KACE,IAAI,OAAO,CAAC,yBAAyB,IACrC,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAChB,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EACxC,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gCAG5C,KAAK,IAAI,OAAO,CAAC,cAAc;gCAC/B,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAe,IAAI,OAAO,CAAC,cAAc;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAIzC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAW,WAAU;;0CACtC,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;;;;;;;0CAGhC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,WAAW,IAAI,UAAU;;;;;;;;;;;;;wCAG7B,IAAI,QAAQ,KAAK,sBAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,WAAW,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0CAOlC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,MAAM,iBACtD,6LAAC;oDAAkB,WAAU;;sEAC3B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAe;;;;;;8EAC7B,6LAAC;oEAAK,WAAU;;wEACb,MAAM,IAAI;wEAAC;wEAAE,MAAM,KAAK;wEAAC;;;;;;;;;;;;;sEAG9B,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,GAAG,AAAC,MAAM,IAAI,GAAG,MAAM,KAAK,GAAI,IAAI,CAAC,CAAC;gEAC/C;;;;;;;;;;;;mDAZI;;;;;;;;;;;;;;;;;;;;;0CAqBlB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAc;;;;;;;;;;;oCAG7B,IAAI,QAAQ,CAAC,MAAM,KAAK,kBACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DAGnC,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;6DAMpC,6LAAC;wCAAI,WAAU;;4CACZ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACjB,6LAAC;oDAAqB,WAAU;8DAC9B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAe,QAAQ,YAAY;;;;;;kFACjD,6LAAC;wEAAE,WAAU;;4EACV,WAAW,QAAQ,IAAI;4EAAE;4EAAG;4EAC5B,WAAW,QAAQ,UAAU;4EAAE;4EAAG;4EAClC,WAAW,QAAQ,QAAQ;;;;;;;;;;;;;0EAGhC,6LAAC;gEACC,WAAW,CAAC,wDAAwD,EAClE,QAAQ,MAAM,KAAK,cACf,gCACA,QAAQ,MAAM,KAAK,aACnB,8BACA,QAAQ,MAAM,KAAK,cACnB,4BACA,6BACJ;0EAED,QAAQ,MAAM;;;;;;;;;;;;mDArBX,QAAQ,EAAE;;;;;4CA2BrB,IAAI,QAAQ,CAAC,MAAM,GAAG,mBACrB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,sBAAsB;;sEAErC,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU1C,6LAAC,qIAAA,CAAA,eAAY;kCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAS;;;;;;;;;;;;;;;;;0BAMhD,6LAAC,kKAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,cAAc;;;;;;;;;;;;AAItB;GAjNgB;KAAA"}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/packages/skeleton.tsx"], "sourcesContent": ["import { Skeleton } from \"@components/ui/skeleton\";\r\n\r\nexport default function PackagesSkeleton() {\r\n  return (\r\n    <div className=\"w-full space-y-4\">\r\n      <Skeleton className=\"h-8 w-32\" /> {/* My Packages title */}\r\n      <div className=\"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3\">\r\n        {[1, 2, 3].map((i) => (\r\n          <div key={i} className=\"rounded-lg border bg-card p-6 shadow-sm\">\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <Skeleton className=\"h-6 w-24\" /> {/* Package 1 */}\r\n              <Skeleton className=\"h-6 w-16\" /> {/* $15 */}\r\n            </div>\r\n\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <Skeleton className=\"h-4 w-32\" /> {/* Start: Jan 20 2024 */}\r\n                <Skeleton className=\"h-4 w-24\" /> {/* Amount paid */}\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-4 w-20\" /> {/* Instructor */}\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Skeleton className=\"h-8 w-8 rounded-full\" /> {/* Avatar */}\r\n                  <Skeleton className=\"h-4 w-32\" /> {/* Counsellor name */}\r\n                </div>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-4 w-32\" /> {/* Sessions progress */}\r\n                <Skeleton className=\"h-2 w-full rounded-full\" />{\" \"}\r\n                {/* Progress bar */}\r\n                <div className=\"flex justify-end\">\r\n                  <Skeleton className=\"h-4 w-12\" /> {/* 4/10 */}\r\n                </div>\r\n              </div>\r\n              <Skeleton className=\"h-10 w-full rounded-md\" />{\" \"}\r\n              {/* View Details button */}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,uIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAa;0BACjC,6LAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;wBAAY,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAa;kDACjC,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAa;;;;;;;0CAGnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAa;0DACjC,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAa;;;;;;;kDAEnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAa;0DACjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAyB;kEAC7C,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAa;;;;;;;;;;;;;kDAGrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAa;0DACjC,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA6B;0DAEjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAa;;;;;;;;;;;;;kDAGrC,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;;uBA1B1C;;;;;;;;;;;;;;;;AAkCpB;KAxCwB"}}, {"offset": {"line": 2022, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/head-wrapper.tsx"], "sourcesContent": ["import Head from \"next/head\";\r\n\r\nexport default function HeadWrapper({\r\n  title,\r\n  description,\r\n}: {\r\n  title: string;\r\n  description?: string;\r\n}) {\r\n  return (\r\n    <Head>\r\n      <title>{`${title || \"\"} | Student's Portal | AdmitPath`}</title>\r\n      <meta\r\n        name=\"description\"\r\n        content={`Student Dashboard Portal for accessing and managing all your sessions and resources. ${\r\n          description || \"\"\r\n        } | AdmitPath`}\r\n      />\r\n    </Head>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,YAAY,EAClC,KAAK,EACL,WAAW,EAIZ;IACC,qBACE,6LAAC,uKAAA,CAAA,UAAI;;0BACH,6LAAC;0BAAO,GAAG,SAAS,GAAG,+BAA+B,CAAC;;;;;;0BACvD,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC,qFAAqF,EAC7F,eAAe,GAChB,YAAY,CAAC;;;;;;;;;;;;AAItB;KAlBwB"}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2072, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/packages/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport usePackages, { StudentPackage } from \"@/app/hooks/student/usePackages\";\r\nimport { PackageCard } from \"./package-card\";\r\nimport { PackageDialog } from \"./package-dialog\";\r\nimport PackagesSkeleton from \"./skeleton\";\r\nimport {\r\n  Tabs,\r\n  Ta<PERSON>Content,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from \"@/app/components/ui/tabs\";\r\nimport { Clock, CheckCircle } from \"lucide-react\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport HeadWrapper from \"../head-wrapper\";\r\n\r\nexport default function Packages() {\r\n  const { activePackages, completedPackages, loading, fetchPackages } =\r\n    usePackages();\r\n  const [selectedPackage, setSelectedPackage] = useState<StudentPackage | null>(\r\n    null\r\n  );\r\n  const [activeTab, setActiveTab] = useState(\"active\");\r\n  const router = useRouter();\r\n\r\n  // Fetch packages based on the active tab\r\n  useEffect(() => {\r\n    fetchPackages(activeTab);\r\n  }, [fetchPackages, activeTab]);\r\n\r\n  // Initial fetch of active packages\r\n  useEffect(() => {\r\n    if (activePackages.length === 0) {\r\n      fetchPackages(\"active\");\r\n    }\r\n  }, [fetchPackages, activePackages.length]);\r\n\r\n  return (\r\n    <>\r\n      <HeadWrapper title=\"My Packages\" />\r\n      <div className=\"sm:p-6 p-3 bg-white rounded-2xl flex-1\">\r\n        <h1 className=\"text-2xl font-semibold mb-6\">My Packages</h1>\r\n\r\n        <Tabs\r\n          defaultValue=\"active\"\r\n          className=\"w-full\"\r\n          onValueChange={(value) => setActiveTab(value)}\r\n        >\r\n          <TabsList className=\"mb-6\">\r\n            <TabsTrigger value=\"active\" className=\"px-6\">\r\n              <Clock className=\"mr-2 h-4 w-4\" />\r\n              Active Packages\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"completed\" className=\"px-6\">\r\n              <CheckCircle className=\"mr-2 h-4 w-4\" />\r\n              Completed Packages\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"active\" className=\"mt-0\">\r\n            {loading ? (\r\n              <PackagesSkeleton />\r\n            ) : (\r\n              <>\r\n                {activePackages.length === 0 ? (\r\n                  <div className=\"text-center py-12 bg-gray-50 rounded-lg\">\r\n                    <h3 className=\"text-lg font-medium text-gray-700 mb-2\">\r\n                      No active packages\r\n                    </h3>\r\n                    <p className=\"text-gray-500 mb-6\">\r\n                      Browse counselors to find and purchase packages\r\n                    </p>\r\n                    <Button onClick={() => router.push(\"/explore\")}>\r\n                      Browse Counselors\r\n                    </Button>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                    {activePackages.map((pkg) => (\r\n                      <PackageCard\r\n                        key={pkg.id}\r\n                        pkg={pkg}\r\n                        onSelect={() => setSelectedPackage(pkg)}\r\n                      />\r\n                    ))}\r\n                  </div>\r\n                )}\r\n              </>\r\n            )}\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"completed\" className=\"mt-0\">\r\n            {loading ? (\r\n              <PackagesSkeleton />\r\n            ) : (\r\n              <>\r\n                {completedPackages.length === 0 ? (\r\n                  <div className=\"text-center py-12 bg-gray-50 rounded-lg\">\r\n                    <h3 className=\"text-lg font-medium text-gray-700 mb-2\">\r\n                      No completed packages\r\n                    </h3>\r\n                    <p className=\"text-gray-500\">\r\n                      Your completed packages will appear here\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                    {completedPackages.map((pkg) => (\r\n                      <PackageCard\r\n                        key={pkg.id}\r\n                        pkg={pkg}\r\n                        onSelect={() => setSelectedPackage(pkg)}\r\n                      />\r\n                    ))}\r\n                  </div>\r\n                )}\r\n              </>\r\n            )}\r\n          </TabsContent>\r\n        </Tabs>\r\n\r\n        {selectedPackage && (\r\n          <PackageDialog\r\n            pkg={selectedPackage}\r\n            onClose={() => setSelectedPackage(null)}\r\n          />\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAHA;AAAA;;;AAbA;;;;;;;;;;;AAkBe,SAAS;;IACtB,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,OAAO,EAAE,aAAa,EAAE,GACjE,CAAA,GAAA,yIAAA,CAAA,UAAW,AAAD;IACZ,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnD;IAEF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,cAAc;QAChB;6BAAG;QAAC;QAAe;KAAU;IAE7B,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,cAAc;YAChB;QACF;6BAAG;QAAC;QAAe,eAAe,MAAM;KAAC;IAEzC,qBACE;;0BACE,6LAAC,mJAAA,CAAA,UAAW;gBAAC,OAAM;;;;;;0BACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAE5C,6LAAC,mIAAA,CAAA,OAAI;wBACH,cAAa;wBACb,WAAU;wBACV,eAAe,CAAC,QAAU,aAAa;;0CAEvC,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAS,WAAU;;0DACpC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAK5C,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;0CACnC,wBACC,6LAAC,wJAAA,CAAA,UAAgB;;;;yDAEjB;8CACG,eAAe,MAAM,KAAK,kBACzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAGvD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,OAAO,IAAI,CAAC;0DAAa;;;;;;;;;;;6DAKlD,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,oBACnB,6LAAC,+JAAA,CAAA,cAAW;gDAEV,KAAK;gDACL,UAAU,IAAM,mBAAmB;+CAF9B,IAAI,EAAE;;;;;;;;;;;;;;;;0CAWzB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACtC,wBACC,6LAAC,wJAAA,CAAA,UAAgB;;;;yDAEjB;8CACG,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAGvD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;6DAK/B,6LAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,oBACtB,6LAAC,+JAAA,CAAA,cAAW;gDAEV,KAAK;gDACL,UAAU,IAAM,mBAAmB;+CAF9B,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAY1B,iCACC,6LAAC,iKAAA,CAAA,gBAAa;wBACZ,KAAK;wBACL,SAAS,IAAM,mBAAmB;;;;;;;;;;;;;;AAM9C;GAlHwB;;QAEpB,yIAAA,CAAA,UAAW;QAKE,qIAAA,CAAA,YAAS;;;KAPF"}}, {"offset": {"line": 2345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/packages/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Packages from \"@/app/components/student/packages\";\r\n\r\nexport default function PackagesPage() {\r\n  // return <h1>Coming soon</h1>;\r\n  return <Packages />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,+BAA+B;IAC/B,qBAAO,6LAAC,qJAAA,CAAA,UAAQ;;;;;AAClB;KAHwB"}}, {"offset": {"line": 2373, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
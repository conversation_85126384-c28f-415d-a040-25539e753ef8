# app/routes/student_dashboard.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, case, or_, and_
from typing import Optional
from datetime import datetime

from ..database import get_db
from ..models.counseling_session import CounselingSession
from ..models.user import Student, Counselor, User
from ..models.payment import Payment
from ..schemas.student_dashboard import *
from ..utils.auth import get_current_user

router = APIRouter()

@router.get("/sessions", response_model=StudentSessionListResponse)
async def get_student_all_sessions(
    status: Optional[str] = Query(None, enum=["pending", "completed", "cancelled"]),
    from_date: Optional[datetime] = None,
    to_date: Optional[datetime] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student exists and get their details
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )

    # Build base query (use LEFT joins for deleted counselors)
    query = db.query(
        CounselingSession,
        User.id.label('counselor_user_id')
    ).outerjoin(
        Counselor, CounselingSession.counselor_id == Counselor.id
    ).outerjoin(
        User, Counselor.user_id == User.id
    ).options(
        joinedload(CounselingSession.payment)
    ).filter(
        CounselingSession.student_id == student.id
    )

    # Apply filters
    if status:
        query = query.filter(CounselingSession.status == status)
    if from_date:
        query = query.filter(CounselingSession.date >= from_date)
    if to_date:
        query = query.filter(CounselingSession.date <= to_date)

    # Execute query
    total = query.count()
    results = query.order_by(CounselingSession.date.desc()).all()

    # Process results
    sessions = []
    for session, counselor_user_id in results:
        session_dict = {
            "id": session.id,
            "event_name": session.event_name,
            "date": session.date,
            "start_time": session.start_time,
            "end_time": session.end_time,
            "student_id": session.student_id,
            "counselor_id": session.counselor_id,
            "counselor_user_id": counselor_user_id,
            "meeting_link": session.meeting_link,
            "status": session.status,
            "payment": session.payment,
            "created_at": session.created_at,
            "updated_at": session.updated_at
        }
        sessions.append(session_dict)

    return StudentSessionListResponse(total=total, items=sessions)

@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    now = datetime.utcnow()

    # Get student ID from current user
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )

    # Get total sessions booked by this student (count ones with completed payment or part of a package)
    total_sessions = db.query(func.count(CounselingSession.id))\
        .outerjoin(Payment, CounselingSession.id == Payment.session_id)\
        .filter(
            CounselingSession.student_id == student.id,
            or_(
                and_(Payment.id.isnot(None), Payment.status == "completed"),
                CounselingSession.package_subscription_id.isnot(None)
            )
        ).scalar()

    # Get upcoming sessions count for this student
    upcoming_sessions = db.query(func.count(CounselingSession.id)).filter(
        CounselingSession.student_id == student.id,
        CounselingSession.date >= now,
        CounselingSession.status != "cancelled"
    ).scalar()

    # Get total number of distinct counselors this student has booked with
    total_counsellors = db.query(func.count(func.distinct(CounselingSession.counselor_id)))\
        .outerjoin(Payment, CounselingSession.id == Payment.session_id)\
        .filter(
            CounselingSession.student_id == student.id,
            or_(
                and_(Payment.id.isnot(None), Payment.status == "completed"),
                CounselingSession.package_subscription_id.isnot(None)
            )
    ).scalar()

    return DashboardStats(
        total_sessions=total_sessions,
        upcoming_sessions=upcoming_sessions,
        total_counsellors=total_counsellors
    )

@router.get("/counselors", response_model=CounselorListResponse)
async def get_student_counselors(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student exists and get their details
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )

    # Current timestamp for upcoming sessions calculation
    now = datetime.utcnow()

    # Get all unique counselors who have sessions with the student
    counselors_query = (
        db.query(
            Counselor.id.label('counselor_id'),
            User.id.label('user_id'),
            User.first_name,
            User.last_name,
            User.email,
            User.profile_picture_url,
            Counselor.is_verified,
            func.max(CounselingSession.date).label('last_session_date'),
            func.count(CounselingSession.id).label('total_sessions'),
            func.sum(case(
                (CounselingSession.date > now, 1),
                else_=0
            )).label('upcoming_sessions')
        )
        .outerjoin(User, User.id == Counselor.user_id)
        .join(CounselingSession, CounselingSession.counselor_id == Counselor.id)
        .filter(
            CounselingSession.student_id == student.id,
            CounselingSession.status != 'cancelled'
        )
        .group_by(
            Counselor.id, User.id, User.first_name, User.last_name,
            User.email, Counselor.is_verified
        )
        .order_by(desc('last_session_date'))
    )

    # Execute query
    counselors = counselors_query.all()

    # Prepare response
    counselor_list = [
        CounselorInfo(
            counselor_id=counselor.counselor_id,
            user_id=counselor.user_id,
            first_name=counselor.first_name,
            last_name=counselor.last_name,
            email=counselor.email,
            profile_picture_url=counselor.profile_picture_url,
            last_session_date=counselor.last_session_date,
            total_sessions=counselor.total_sessions,
            upcoming_sessions=counselor.upcoming_sessions,
            is_verified=counselor.is_verified
        )
        for counselor in counselors
    ]

    return CounselorListResponse(
        total=len(counselor_list),
        counselors=counselor_list
    )
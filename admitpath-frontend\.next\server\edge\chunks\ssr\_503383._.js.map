{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,2KAAgB,IAAI;AAEnC,MAAM,gBAAgB,2KAAgB,OAAO;AAE7C,MAAM,eAAe,2KAAgB,MAAM;AAE3C,MAAM,cAAc,2KAAgB,KAAK;AAEzC,MAAM,8BAAgB,mKAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,2KAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,2KAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,mKAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,kMAAC;;0BACC,kMAAC;;;;;0BACD,kMAAC,2KAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,kMAAC,2KAAgB,KAAK;wBAAC,WAAU;;0CAC/B,kMAAC,oMAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,kMAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,2KAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,kMAAC;QACC,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,kMAAC;QACC,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,mKAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,2KAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,2KAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,mKAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,2KAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/NavigationTabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\n\r\ninterface NavigationTabsProps {\r\n  counselor: CounselorPublicProfile;\r\n}\r\n\r\nexport function NavigationTabs({ counselor }: NavigationTabsProps) {\r\n  const [activeSection, setActiveSection] = useState(\"overview\");\r\n\r\n  const availableSections = [\r\n    { name: \"Overview\", id: \"overview\", show: true },\r\n    { name: \"Services\", id: \"services\", show: counselor.services.length > 0 },\r\n    { name: \"Packages\", id: \"packages\", show: true },\r\n    {\r\n      name: \"Education\",\r\n      id: \"education\",\r\n      show: counselor.education.length > 0,\r\n    },\r\n    {\r\n      name: \"Experience\",\r\n      id: \"experience\",\r\n      show: counselor.professional_experience.length > 0,\r\n    },\r\n    { name: \"Reviews\", id: \"reviews\", show: true },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const sections = availableSections\r\n        .filter((section) => section.show)\r\n        .map((section) => section.id);\r\n      const header = document.querySelector(\".sticky-header\");\r\n      const headerOffset = header?.clientHeight || 0;\r\n\r\n      // Find the section that is currently in view\r\n      for (const sectionId of sections) {\r\n        const element = document.getElementById(sectionId);\r\n        if (element) {\r\n          const rect = element.getBoundingClientRect();\r\n          if (rect.top <= headerOffset + 100 && rect.bottom >= headerOffset) {\r\n            setActiveSection(sectionId);\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", handleScroll);\r\n    return () => window.removeEventListener(\"scroll\", handleScroll);\r\n  }, [availableSections]);\r\n\r\n  const smoothScroll = (targetPosition: number, duration: number = 500) => {\r\n    const startPosition = window.pageYOffset;\r\n    const distance = targetPosition - startPosition;\r\n    let startTime: number | null = null;\r\n\r\n    function animation(currentTime: number) {\r\n      if (startTime === null) startTime = currentTime;\r\n      const timeElapsed = currentTime - startTime;\r\n      const run = easeInOutQuad(timeElapsed, startPosition, distance, duration);\r\n      window.scrollTo(0, run);\r\n      if (timeElapsed < duration) requestAnimationFrame(animation);\r\n    }\r\n\r\n    function easeInOutQuad(t: number, b: number, c: number, d: number) {\r\n      t /= d / 2;\r\n      if (t < 1) return (c / 2) * t * t + b;\r\n      t--;\r\n      return (-c / 2) * (t * (t - 2) - 1) + b;\r\n    }\r\n\r\n    requestAnimationFrame(animation);\r\n  };\r\n\r\n  const handleScroll = (id: string) => {\r\n    const element = document.getElementById(id);\r\n    const header = document.querySelector(\".sticky-header\");\r\n    const headerOffset = header?.clientHeight || 0;\r\n\r\n    if (element) {\r\n      const elementPosition = element.getBoundingClientRect().top;\r\n      const offsetPosition =\r\n        elementPosition + window.pageYOffset - headerOffset - 20;\r\n\r\n      smoothScroll(offsetPosition);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"sticky bg-white z-10 border-b border-[#E2E8F0]\">\r\n      <div className=\"flex overflow-x-auto hide-scrollbar\">\r\n        {availableSections\r\n          .filter((section) => section.show)\r\n          .map((item) => (\r\n            <button\r\n              key={item.name}\r\n              onClick={() => handleScroll(item.id)}\r\n              className={`px-5 py-2.5 text-[13px] transition-all duration-300 ease-in-out ${\r\n                item.id === activeSection\r\n                  ? \"text-blue-950 border-b-2 border-blue-950 font-medium\"\r\n                  : \"text-gray-500 hover:text-blue-950 font-medium\"\r\n              }`}\r\n            >\r\n              {item.name}\r\n            </button>\r\n          ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,eAAe,EAAE,SAAS,EAAuB;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB;QACxB;YAAE,MAAM;YAAY,IAAI;YAAY,MAAM;QAAK;QAC/C;YAAE,MAAM;YAAY,IAAI;YAAY,MAAM,UAAU,QAAQ,CAAC,MAAM,GAAG;QAAE;QACxE;YAAE,MAAM;YAAY,IAAI;YAAY,MAAM;QAAK;QAC/C;YACE,MAAM;YACN,IAAI;YACJ,MAAM,UAAU,SAAS,CAAC,MAAM,GAAG;QACrC;QACA;YACE,MAAM;YACN,IAAI;YACJ,MAAM,UAAU,uBAAuB,CAAC,MAAM,GAAG;QACnD;QACA;YAAE,MAAM;YAAW,IAAI;YAAW,MAAM;QAAK;KAC9C;IAED,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,WAAW,kBACd,MAAM,CAAC,CAAC,UAAY,QAAQ,IAAI,EAChC,GAAG,CAAC,CAAC,UAAY,QAAQ,EAAE;YAC9B,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,eAAe,QAAQ,gBAAgB;YAE7C,6CAA6C;YAC7C,KAAK,MAAM,aAAa,SAAU;gBAChC,MAAM,UAAU,SAAS,cAAc,CAAC;gBACxC,IAAI,SAAS;oBACX,MAAM,OAAO,QAAQ,qBAAqB;oBAC1C,IAAI,KAAK,GAAG,IAAI,eAAe,OAAO,KAAK,MAAM,IAAI,cAAc;wBACjE,iBAAiB;wBACjB;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAkB;IAEtB,MAAM,eAAe,CAAC,gBAAwB,WAAmB,GAAG;QAClE,MAAM,gBAAgB,OAAO,WAAW;QACxC,MAAM,WAAW,iBAAiB;QAClC,IAAI,YAA2B;QAE/B,SAAS,UAAU,WAAmB;YACpC,IAAI,cAAc,MAAM,YAAY;YACpC,MAAM,cAAc,cAAc;YAClC,MAAM,MAAM,cAAc,aAAa,eAAe,UAAU;YAChE,OAAO,QAAQ,CAAC,GAAG;YACnB,IAAI,cAAc,UAAU,sBAAsB;QACpD;QAEA,SAAS,cAAc,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;YAC/D,KAAK,IAAI;YACT,IAAI,IAAI,GAAG,OAAO,AAAC,IAAI,IAAK,IAAI,IAAI;YACpC;YACA,OAAO,AAAC,CAAC,IAAI,IAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;QACxC;QAEA,sBAAsB;IACxB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,eAAe,QAAQ,gBAAgB;QAE7C,IAAI,SAAS;YACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBACJ,kBAAkB,OAAO,WAAW,GAAG,eAAe;YAExD,aAAa;QACf;IACF;IAEA,qBACE,kMAAC;QAAI,WAAU;kBACb,cAAA,kMAAC;YAAI,WAAU;sBACZ,kBACE,MAAM,CAAC,CAAC,UAAY,QAAQ,IAAI,EAChC,GAAG,CAAC,CAAC,qBACJ,kMAAC;oBAEC,SAAS,IAAM,aAAa,KAAK,EAAE;oBACnC,WAAW,CAAC,gEAAgE,EAC1E,KAAK,EAAE,KAAK,gBACR,yDACA,iDACJ;8BAED,KAAK,IAAI;mBARL,KAAK,IAAI;;;;;;;;;;;;;;;AAc5B"}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,mKAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,kMAAC;QACC,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG"}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,mKAAM,UAAU,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,2KAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,2KAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,mKAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,2KAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,2KAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,mKAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,2KAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,2KAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/utils/time-helpers.ts"], "sourcesContent": ["// Convert date to ISO format\r\nexport const formatDateISO = (date: string) => {\r\n  // Create a date object from the date string\r\n  const dateObj = new Date(date);\r\n\r\n  // Set the time to midnight (00:00:00) to get just the date\r\n  dateObj.setHours(0, 0, 0, 0);\r\n\r\n  // Return the ISO string\r\n  return dateObj.toISOString();\r\n};\r\n\r\n// Convert to long date string\r\nexport function formatToLongDateString(dateString: string) {\r\n  const date = new Date(dateString);\r\n\r\n  return date.toLocaleDateString(\"en-US\", {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n  });\r\n}\r\n\r\n// Convert date to 'yyyy-mm-dd' format in LOCAL TIMEZONE\r\nexport const formatNormalDate = (date: string) => {\r\n  const d = new Date(date);\r\n  const year = d.getFullYear();\r\n  const month = String(d.getMonth() + 1).padStart(2, \"0\"); // Months are 0-based\r\n  const day = String(d.getDate()).padStart(2, \"0\");\r\n  return `${year}-${month}-${day}`;\r\n};\r\n\r\n// Convert ISO time to \"HH:mm\" in LOCAL TIMEZONE\r\nexport function formatTimeForInput(isoTime: string) {\r\n  const date = new Date(isoTime);\r\n  const hours = String(date.getHours()).padStart(2, \"0\"); // Local hours\r\n  const minutes = String(date.getMinutes()).padStart(2, \"0\"); // Local minutes\r\n  return `${hours}:${minutes}`;\r\n}\r\n\r\n// Convert \"HH:mm\" from time input back to ISO format\r\nexport function formatTimeToISO(date: string, time: string) {\r\n  // Create a date object from the date and time strings\r\n  const [hours, minutes] = time.split(':').map(Number);\r\n  const dateObj = new Date(date);\r\n\r\n  // Set the hours and minutes\r\n  dateObj.setHours(hours, minutes, 0, 0);\r\n\r\n  // Return the ISO string\r\n  return dateObj.toISOString();\r\n}\r\n\r\nexport function dateNowWithTZOffset() {\r\n  const now = new Date();\r\n\r\n  const year = now.getFullYear();\r\n  const month = String(now.getMonth() + 1).padStart(2, \"0\");\r\n  const day = String(now.getDate()).padStart(2, \"0\");\r\n  const hours = String(now.getHours()).padStart(2, \"0\");\r\n  const minutes = String(now.getMinutes()).padStart(2, \"0\");\r\n  const seconds = String(now.getSeconds()).padStart(2, \"0\");\r\n\r\n  const microseconds = String(Math.floor(Math.random() * 1000000)).padStart(\r\n    6,\r\n    \"0\"\r\n  );\r\n\r\n  const tzOffset = now.getTimezoneOffset();\r\n  const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60));\r\n  const tzOffsetMinutes = Math.abs(tzOffset % 60);\r\n  const tzOffsetSign = tzOffset <= 0 ? \"+\" : \"-\";\r\n\r\n  const tzFormatted = `${tzOffsetSign}${String(tzOffsetHours).padStart(\r\n    2,\r\n    \"0\"\r\n  )}:${String(tzOffsetMinutes).padStart(2, \"0\")}`;\r\n\r\n  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}${tzFormatted}`;\r\n}\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;;;;AACtB,MAAM,gBAAgB,CAAC;IAC5B,4CAA4C;IAC5C,MAAM,UAAU,IAAI,KAAK;IAEzB,2DAA2D;IAC3D,QAAQ,QAAQ,CAAC,GAAG,GAAG,GAAG;IAE1B,wBAAwB;IACxB,OAAO,QAAQ,WAAW;AAC5B;AAGO,SAAS,uBAAuB,UAAkB;IACvD,MAAM,OAAO,IAAI,KAAK;IAEtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,OAAO,EAAE,WAAW;IAC1B,MAAM,QAAQ,OAAO,EAAE,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM,qBAAqB;IAC9E,MAAM,MAAM,OAAO,EAAE,OAAO,IAAI,QAAQ,CAAC,GAAG;IAC5C,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;AAClC;AAGO,SAAS,mBAAmB,OAAe;IAChD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,MAAM,cAAc;IACtE,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG,MAAM,gBAAgB;IAC5E,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;AAC9B;AAGO,SAAS,gBAAgB,IAAY,EAAE,IAAY;IACxD,sDAAsD;IACtD,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;IAC7C,MAAM,UAAU,IAAI,KAAK;IAEzB,4BAA4B;IAC5B,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;IAEpC,wBAAwB;IACxB,OAAO,QAAQ,WAAW;AAC5B;AAEO,SAAS;IACd,MAAM,MAAM,IAAI;IAEhB,MAAM,OAAO,IAAI,WAAW;IAC5B,MAAM,QAAQ,OAAO,IAAI,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IACrD,MAAM,MAAM,OAAO,IAAI,OAAO,IAAI,QAAQ,CAAC,GAAG;IAC9C,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG;IACjD,MAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,GAAG;IACrD,MAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,GAAG;IAErD,MAAM,eAAe,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,QAAQ,CACvE,GACA;IAGF,MAAM,WAAW,IAAI,iBAAiB;IACtC,MAAM,gBAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,WAAW;IACrD,MAAM,kBAAkB,KAAK,GAAG,CAAC,WAAW;IAC5C,MAAM,eAAe,YAAY,IAAI,MAAM;IAE3C,MAAM,cAAc,GAAG,eAAe,OAAO,eAAe,QAAQ,CAClE,GACA,KACA,CAAC,EAAE,OAAO,iBAAiB,QAAQ,CAAC,GAAG,MAAM;IAE/C,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,eAAe,aAAa;AAC/F"}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/useWebSocketChat.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport {\r\n  ChatState,\r\n  Message,\r\n  SendMessagePayload,\r\n  InitialMessagePayload,\r\n  WebSocketMessage,\r\n  WebSocketConnectionStatus,\r\n} from \"@/app/types/chat\";\r\nimport { dateNowWithTZOffset } from \"../utils/time-helpers\";\r\n\r\nconst LOCALSTORAGE_KEY = \"chat_latest_messages\";\r\n\r\ninterface LatestMessagesStorage {\r\n  [channelId: string]: string;\r\n}\r\n\r\nclass WebSocketManager {\r\n  private socket: WebSocket | null = null;\r\n  private channelId: string | null = null;\r\n  private token: string | null = null;\r\n  private reconnectAttempts = 0;\r\n  private maxReconnectAttempts = 5;\r\n  private reconnectInterval = 3000; // 3 seconds\r\n  private reconnectTimeoutId: NodeJS.Timeout | null = null;\r\n  private messageHandler: ((data: any) => void) | null = null;\r\n  private statusChangeHandler: ((status: WebSocketConnectionStatus) => void) | null = null;\r\n  private errorHandler: ((error: any) => void) | null = null;\r\n\r\n  constructor() {\r\n    this.connect = this.connect.bind(this);\r\n    this.disconnect = this.disconnect.bind(this);\r\n    this.sendMessage = this.sendMessage.bind(this);\r\n    this.handleOpen = this.handleOpen.bind(this);\r\n    this.handleMessage = this.handleMessage.bind(this);\r\n    this.handleClose = this.handleClose.bind(this);\r\n    this.handleError = this.handleError.bind(this);\r\n    this.reconnect = this.reconnect.bind(this);\r\n  }\r\n\r\n  public connect(channelId: string, token: string): void {\r\n    this.channelId = channelId;\r\n    this.token = token;\r\n\r\n    if (this.socket) {\r\n      this.socket.close();\r\n    }\r\n\r\n    try {\r\n      const wsUrl = `${this.getWebSocketBaseUrl()}/ws/chat/${channelId}?token=${token}`;\r\n      this.socket = new WebSocket(wsUrl);\r\n\r\n      this.socket.onopen = this.handleOpen;\r\n      this.socket.onmessage = this.handleMessage;\r\n      this.socket.onclose = this.handleClose;\r\n      this.socket.onerror = this.handleError;\r\n\r\n      if (this.statusChangeHandler) {\r\n        this.statusChangeHandler('connecting');\r\n      }\r\n    } catch (error) {\r\n      console.error(\"WebSocket connection error:\", error);\r\n      if (this.errorHandler) {\r\n        this.errorHandler(error);\r\n      }\r\n    }\r\n  }\r\n\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.close();\r\n      this.socket = null;\r\n    }\r\n\r\n    if (this.reconnectTimeoutId) {\r\n      clearTimeout(this.reconnectTimeoutId);\r\n      this.reconnectTimeoutId = null;\r\n    }\r\n\r\n    this.channelId = null;\r\n    this.token = null;\r\n    this.reconnectAttempts = 0;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('disconnected');\r\n    }\r\n  }\r\n\r\n  public sendMessage(message: WebSocketMessage): boolean {\r\n    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {\r\n      console.error(\"WebSocket is not connected\");\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      this.socket.send(JSON.stringify(message));\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Error sending message:\", error);\r\n      if (this.errorHandler) {\r\n        this.errorHandler(error);\r\n      }\r\n      return false;\r\n    }\r\n  }\r\n\r\n  public onMessage(handler: (data: any) => void): void {\r\n    this.messageHandler = handler;\r\n  }\r\n\r\n  public onStatusChange(handler: (status: WebSocketConnectionStatus) => void): void {\r\n    this.statusChangeHandler = handler;\r\n  }\r\n\r\n  public onError(handler: (error: any) => void): void {\r\n    this.errorHandler = handler;\r\n  }\r\n\r\n  public isConnected(): boolean {\r\n    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;\r\n  }\r\n\r\n  private handleOpen(_event: Event): void {\r\n    this.reconnectAttempts = 0;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('connected');\r\n    }\r\n  }\r\n\r\n  private handleMessage(event: MessageEvent): void {\r\n    try {\r\n      const data = JSON.parse(event.data);\r\n\r\n      const now = new Date();\r\n      data._receivedAt = now.toISOString();\r\n\r\n      if (!this._recentMessages) {\r\n        this._recentMessages = [];\r\n      }\r\n\r\n      const fingerprint = `${data.sender_id || ''}-${data.created_at || ''}-${(data.message || '').substring(0, 20)}`;\r\n\r\n      // Check if we've seen this message recently (within 5 seconds)\r\n      const isDuplicate = this._recentMessages.some(m => {\r\n        // If fingerprints match and received within 5 seconds, it's likely a duplicate\r\n        if (m.fingerprint === fingerprint) {\r\n          const prevTime = new Date(m.receivedAt).getTime();\r\n          const currentTime = now.getTime();\r\n          return (currentTime - prevTime) < 5000; // 5 seconds\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (isDuplicate) {\r\n        return;\r\n      }\r\n\r\n      this._recentMessages.push({\r\n        fingerprint,\r\n        receivedAt: now.toISOString()\r\n      });\r\n\r\n      if (this._recentMessages.length > 20) {\r\n        this._recentMessages = this._recentMessages.slice(-20);\r\n      }\r\n\r\n      if (this.messageHandler) {\r\n        this.messageHandler(data);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error parsing message:\", error);\r\n    }\r\n  }\r\n\r\n  private _recentMessages: Array<{fingerprint: string, receivedAt: string}> = [];\r\n\r\n  private handleClose(event: CloseEvent): void {\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('disconnected');\r\n    }\r\n\r\n    if (!event.wasClean && this.channelId && this.token) {\r\n      this.reconnect();\r\n    }\r\n  }\r\n\r\n  private handleError(event: Event): void {\r\n    console.error(\"WebSocket error:\", event);\r\n    if (this.errorHandler) {\r\n      this.errorHandler(event);\r\n    }\r\n  }\r\n\r\n  private reconnect(): void {\r\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n      return;\r\n    }\r\n\r\n    this.reconnectAttempts++;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('reconnecting');\r\n    }\r\n\r\n    this.reconnectTimeoutId = setTimeout(() => {\r\n      if (this.channelId && this.token) {\r\n        this.connect(this.channelId, this.token);\r\n      }\r\n    }, this.reconnectInterval);\r\n  }\r\n\r\n  private getWebSocketBaseUrl(): string {\r\n    const isSecure = window.location.protocol === 'https:';\r\n    const protocol = isSecure ? 'wss:' : 'ws:';\r\n\r\n    let apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';\r\n\r\n    apiBaseUrl = apiBaseUrl.replace(/^https?:\\/\\//, '');\r\n\r\n    return `${protocol}//${apiBaseUrl}`;\r\n  }\r\n}\r\n\r\nconst wsManager = new WebSocketManager();\r\n\r\nlet currentUserInfo: { user_id: number | string } | null = null;\r\n\r\nexport const setWebSocketChatUserInfo = (userInfo: { user_id: number | string }) => {\r\n  currentUserInfo = userInfo;\r\n};\r\n\r\nexport const useWebSocketChat = create<ChatState>((set, get) => ({\r\n  channels: null,\r\n  currentChannel: null,\r\n  messages: null,\r\n  messageIds: new Set<number>(),\r\n  pendingMessages: new Set<number>(),\r\n  unseenMessages: {},\r\n  unseenChannelsCount: 0,\r\n  loading: false,\r\n  error: null,\r\n  connectionStatus: 'disconnected',\r\n\r\n  initWebSocket: () => {\r\n    wsManager.onMessage((data) => {\r\n      const message = data as Message;\r\n      const state = get();\r\n\r\n\r\n      if (currentUserInfo?.user_id && message.sender_id === currentUserInfo.user_id) {\r\n        return;\r\n      }\r\n\r\n      if (state.currentChannel && message.channel_id !== state.currentChannel.channel_id) {\r\n        return;\r\n      }\r\n\r\n      if (message.id && state.messageIds.has(message.id)) {\r\n        return;\r\n      }\r\n\r\n      const messageFingerprint = `${message.sender_id}-${message.created_at}-${message.message.substring(0, 20)}`;\r\n\r\n      const isDuplicate = state.messages?.some(m => {\r\n        const existingFingerprint = `${m.sender_id}-${m.created_at}-${m.message.substring(0, 20)}`;\r\n        return existingFingerprint === messageFingerprint;\r\n      });\r\n\r\n      if (isDuplicate) {\r\n        return;\r\n      }\r\n\r\n      get().addMessage(message);\r\n\r\n      if (state.currentChannel) {\r\n        get().updateLatestSeenMessage(\r\n          state.currentChannel.channel_id,\r\n          message.created_at\r\n        );\r\n      }\r\n    });\r\n\r\n    wsManager.onStatusChange((status) => {\r\n      set({ connectionStatus: status });\r\n    });\r\n\r\n    wsManager.onError((_error) => {\r\n      set({ error: \"WebSocket connection error\" });\r\n    });\r\n  },\r\n\r\n  fetchChannels: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/messages/channels\");\r\n      set({ channels: response.data.channels, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch channels\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchMessages: async (channelId: string, skip = 0, limit = 15) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/messages/channel/${channelId}?skip=${skip}&limit=${limit}`\r\n      );\r\n      const newMessages = response.data.messages;\r\n\r\n      const messageIds = new Set(get().messageIds);\r\n      newMessages.forEach((msg: Message) => messageIds.add(msg.id));\r\n\r\n      set((state) => ({\r\n        messages:\r\n          skip === 0\r\n            ? newMessages\r\n            : [\r\n                ...(state.messages || []),\r\n                ...newMessages.filter(\r\n                  (msg: Message) =>\r\n                    !state.messages?.some(\r\n                      (existingMsg: Message) => existingMsg.id === msg.id\r\n                    )\r\n                ),\r\n              ],\r\n        messageIds,\r\n        loading: false,\r\n      }));\r\n\r\n      if (limit !== 1 && newMessages.length > 0) {\r\n        get().updateLatestSeenMessage(channelId, newMessages[0].created_at);\r\n      }\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch messages\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  sendMessage: async (payload: SendMessagePayload) => {\r\n    const currentChannel = get().currentChannel;\r\n    if (!currentChannel) return;\r\n\r\n    const state = get();\r\n    const messageFingerprint = `${payload.sender_id}-${dateNowWithTZOffset()}-${payload.message.substring(0, 20)}`;\r\n\r\n    const isDuplicate = state.messages?.some(m => {\r\n      if (m.sender_id !== payload.sender_id) return false;\r\n\r\n      if (m.message !== payload.message) return false;\r\n\r\n      const msgTime = new Date(m.created_at).getTime();\r\n      const now = new Date().getTime();\r\n      return (now - msgTime) < 2000; // 2 seconds\r\n    });\r\n\r\n    if (isDuplicate) {\r\n      return;\r\n    }\r\n\r\n    // Create optimistic message\r\n    const tempId = Math.floor(Math.random() * 1000000);\r\n    const optimisticMessage: Message = {\r\n      id: tempId,\r\n      channel_id: currentChannel.channel_id,\r\n      message: payload.message,\r\n      sender_id: payload.sender_id,\r\n      sender_name: payload.sender_name,\r\n      created_at: dateNowWithTZOffset(),\r\n    };\r\n\r\n    try {\r\n      get().addMessage(optimisticMessage);\r\n      set((state) => ({\r\n        pendingMessages: new Set([...state.pendingMessages, tempId]),\r\n      }));\r\n\r\n      let serverMessage: Message | null = null;\r\n\r\n      if (wsManager.isConnected()) {\r\n        const wsMessage: WebSocketMessage = {\r\n          type: \"message\",\r\n          message: payload.message,\r\n          recipient_id: payload.recipient_id,\r\n          created_at: optimisticMessage.created_at,\r\n        };\r\n\r\n        const sentViaWebSocket = wsManager.sendMessage(wsMessage);\r\n\r\n        if (sentViaWebSocket) {\r\n\r\n\r\n          set((state) => ({\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          }));\r\n\r\n          return;\r\n        }\r\n      }\r\n\r\n      const response = await apiClient.post(`/messages`, {\r\n        recipient_id: payload.recipient_id,\r\n        message: payload.message,\r\n        created_at: optimisticMessage.created_at,\r\n      });\r\n\r\n      serverMessage = response.data;\r\n\r\n      if (serverMessage) {\r\n        get().updateLatestSeenMessage(\r\n          currentChannel.channel_id,\r\n          serverMessage.created_at\r\n        );\r\n      }\r\n\r\n      set((state) => {\r\n        const serverMessageExists = serverMessage && state.messages?.some(msg => msg.id === serverMessage.id);\r\n\r\n        const serverMessageFingerprint = serverMessage ?\r\n          `${serverMessage.sender_id}-${serverMessage.created_at}-${serverMessage.message.substring(0, 20)}` : '';\r\n\r\n        const similarMessageExists = serverMessage && state.messages?.some(msg => {\r\n          if (msg.id === tempId) return false; // Skip the optimistic message\r\n          const msgFingerprint = `${msg.sender_id}-${msg.created_at}-${msg.message.substring(0, 20)}`;\r\n          return msgFingerprint === serverMessageFingerprint;\r\n        });\r\n\r\n        if (serverMessageExists || similarMessageExists) {\r\n          return {\r\n            messages: (state.messages || []).filter(msg => msg.id !== tempId),\r\n            messageIds: state.messageIds,\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          };\r\n        }\r\n\r\n        if (!serverMessage) {\r\n          return {\r\n            messages: (state.messages || []).filter(msg => msg.id !== tempId),\r\n            messageIds: state.messageIds,\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          };\r\n        }\r\n\r\n        return {\r\n          messages: (state.messages || []).map((msg) =>\r\n            msg.id === tempId ? serverMessage : msg\r\n          ),\r\n          messageIds: new Set(\r\n            [...(state.messageIds || [])]\r\n              .filter((id) => id !== tempId)\r\n              .concat(serverMessage.id)\r\n          ),\r\n          pendingMessages: new Set(\r\n            [...state.pendingMessages].filter((id) => id !== tempId)\r\n          ),\r\n        };\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to send message:\", error);\r\n      get().removeMessage(tempId);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  sendInitialMessage: async (payload: InitialMessagePayload) => {\r\n    try {\r\n      await apiClient.post(`/messages`, {\r\n        ...payload,\r\n        created_at: dateNowWithTZOffset(),\r\n      });\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  setCurrentChannel: (channel) => {\r\n    const currentChannelId = get().currentChannel?.channel_id;\r\n\r\n    set({\r\n      messages: null,\r\n      messageIds: new Set<number>()\r\n    });\r\n\r\n    if (currentChannelId) {\r\n      wsManager.disconnect();\r\n    }\r\n\r\n    set({ currentChannel: channel });\r\n\r\n    if (channel) {\r\n\r\n      const token = localStorage.getItem(\"access_token\");\r\n\r\n      if (token) {\r\n        get().initWebSocket();\r\n\r\n        wsManager.connect(channel.channel_id, token);\r\n\r\n        get().fetchMessages(channel.channel_id);\r\n      } else {\r\n        set({ error: \"Authentication token not found\" });\r\n      }\r\n    }\r\n  },\r\n\r\n  addMessage: (message: Message) => {\r\n    set((state) => {\r\n      if (state.currentChannel && message.channel_id !== state.currentChannel.channel_id) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      if (message.id && state.messageIds.has(message.id)) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      const messageFingerprint = `${message.sender_id}-${message.created_at}-${message.message.substring(0, 20)}`;\r\n\r\n      const messageExistsByFingerprint = state.messages?.some(m => {\r\n        const existingFingerprint = `${m.sender_id}-${m.created_at}-${m.message.substring(0, 20)}`;\r\n        return existingFingerprint === messageFingerprint;\r\n      });\r\n\r\n      if (messageExistsByFingerprint) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      return {\r\n        messages: [...(state.messages || []), message],\r\n        messageIds: message.id ? new Set(state.messageIds).add(message.id) : state.messageIds,\r\n      };\r\n    });\r\n  },\r\n\r\n  removeMessage: (messageId: number) => {\r\n    set((state) => ({\r\n      messages: (state.messages || []).filter((msg) => msg.id !== messageId),\r\n      messageIds: new Set(\r\n        [...state.messageIds].filter((id) => id !== messageId)\r\n      ),\r\n      pendingMessages: new Set(\r\n        [...state.pendingMessages].filter((id) => id !== messageId)\r\n      ),\r\n    }));\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n\r\n  updateLatestSeenMessage: (channelId: string, timestamp: string) => {\r\n    try {\r\n      let latestMessages: LatestMessagesStorage = {};\r\n      const storedData = localStorage.getItem(LOCALSTORAGE_KEY);\r\n\r\n      if (storedData) {\r\n        latestMessages = JSON.parse(storedData) as LatestMessagesStorage;\r\n      }\r\n\r\n      if (latestMessages[channelId] === timestamp) return;\r\n\r\n      latestMessages[channelId] = timestamp;\r\n\r\n      localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(latestMessages));\r\n\r\n      set((state) => ({\r\n        unseenMessages: {\r\n          ...state.unseenMessages,\r\n          [channelId]: 0,\r\n        },\r\n        unseenChannelsCount: Math.max(0, state.unseenChannelsCount - 1),\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Failed to update latest seen message:\", error);\r\n    }\r\n  },\r\n\r\n  getUnseenChannelsCount: async (): Promise<number> => {\r\n    try {\r\n      let latestSeenMessages: LatestMessagesStorage = {};\r\n      const storedData = localStorage.getItem(LOCALSTORAGE_KEY);\r\n\r\n      if (storedData) {\r\n        latestSeenMessages = JSON.parse(storedData) as LatestMessagesStorage;\r\n      }\r\n\r\n      if (!get().channels) {\r\n        await get().fetchChannels();\r\n      }\r\n\r\n      const channels = get().channels;\r\n      if (!channels) return 0;\r\n\r\n      let unseenMessages = {} as Record<string, number>;\r\n      let unseenChannelsCount = 0;\r\n\r\n      const promises = channels.map(async (channel) => {\r\n        const response = await apiClient.get(\r\n          `/messages/channel/${channel.channel_id}?skip=0&limit=1`\r\n        );\r\n\r\n        if (response.data.messages && response.data.messages.length > 0) {\r\n          const latestMessage = response.data.messages[0];\r\n          const latestSeenTimestamp = latestSeenMessages[channel.channel_id];\r\n\r\n          if (\r\n            !latestSeenTimestamp ||\r\n            new Date(latestMessage.created_at) > new Date(latestSeenTimestamp)\r\n          ) {\r\n            unseenMessages[channel.channel_id] = 1;\r\n            unseenChannelsCount++;\r\n          }\r\n        }\r\n      });\r\n\r\n      await Promise.all(promises);\r\n      set({\r\n        unseenMessages,\r\n        unseenChannelsCount,\r\n      });\r\n      return unseenChannelsCount;\r\n    } catch (error) {\r\n      console.error(\"Failed to get unseen channels count:\", error);\r\n      return 0;\r\n    }\r\n  },\r\n\r\n  reconnectWebSocket: () => {\r\n    const currentChannel = get().currentChannel;\r\n    if (currentChannel) {\r\n      const token = localStorage.getItem(\"access_token\");\r\n      if (token) {\r\n        wsManager.disconnect();\r\n        wsManager.connect(currentChannel.channel_id, token);\r\n      }\r\n    } else {\r\n      wsManager.disconnect();\r\n    }\r\n  },\r\n\r\n  isWebSocketConnected: () => {\r\n    return wsManager.isConnected();\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;;AAGA;AASA;AAVA;AAFA;;;;AAcA,MAAM,mBAAmB;AAMzB,MAAM;IACI,SAA2B,KAAK;IAChC,YAA2B,KAAK;IAChC,QAAuB,KAAK;IAC5B,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;IACzB,oBAAoB,KAAK;IACzB,qBAA4C,KAAK;IACjD,iBAA+C,KAAK;IACpD,sBAA4E,KAAK;IACjF,eAA8C,KAAK;IAE3D,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;QAC3C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;QAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;IAC3C;IAEO,QAAQ,SAAiB,EAAE,KAAa,EAAQ;QACrD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;QAEb,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK;QACnB;QAEA,IAAI;YACF,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,GAAG,SAAS,EAAE,UAAU,OAAO,EAAE,OAAO;YACjF,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU;YAE5B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU;YACpC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa;YAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;YACtC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;YAEtC,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,mBAAmB,CAAC;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC;YACpB;QACF;IACF;IAEO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,aAAa,IAAI,CAAC,kBAAkB;YACpC,IAAI,CAAC,kBAAkB,GAAG;QAC5B;QAEA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,iBAAiB,GAAG;QAEzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;IACF;IAEO,YAAY,OAAyB,EAAW;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YAC7D,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QAEA,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC;YACpB;YACA,OAAO;QACT;IACF;IAEO,UAAU,OAA4B,EAAQ;QACnD,IAAI,CAAC,cAAc,GAAG;IACxB;IAEO,eAAe,OAAoD,EAAQ;QAChF,IAAI,CAAC,mBAAmB,GAAG;IAC7B;IAEO,QAAQ,OAA6B,EAAQ;QAClD,IAAI,CAAC,YAAY,GAAG;IACtB;IAEO,cAAuB;QAC5B,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI;IAC1E;IAEQ,WAAW,MAAa,EAAQ;QACtC,IAAI,CAAC,iBAAiB,GAAG;QAEzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;IACF;IAEQ,cAAc,KAAmB,EAAQ;QAC/C,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;YAElC,MAAM,MAAM,IAAI;YAChB,KAAK,WAAW,GAAG,IAAI,WAAW;YAElC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACzB,IAAI,CAAC,eAAe,GAAG,EAAE;YAC3B;YAEA,MAAM,cAAc,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC,EAAE,KAAK,UAAU,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,OAAO,IAAI,EAAE,EAAE,SAAS,CAAC,GAAG,KAAK;YAE/G,+DAA+D;YAC/D,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC5C,+EAA+E;gBAC/E,IAAI,EAAE,WAAW,KAAK,aAAa;oBACjC,MAAM,WAAW,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;oBAC/C,MAAM,cAAc,IAAI,OAAO;oBAC/B,OAAO,AAAC,cAAc,WAAY,MAAM,YAAY;gBACtD;gBACA,OAAO;YACT;YAEA,IAAI,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBACxB;gBACA,YAAY,IAAI,WAAW;YAC7B;YAEA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI;gBACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACrD;YAEA,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEQ,kBAAoE,EAAE,CAAC;IAEvE,YAAY,KAAiB,EAAQ;QAE3C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;QAEA,IAAI,CAAC,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;YACnD,IAAI,CAAC,SAAS;QAChB;IACF;IAEQ,YAAY,KAAY,EAAQ;QACtC,QAAQ,KAAK,CAAC,oBAAoB;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC;QACpB;IACF;IAEQ,YAAkB;QACxB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACvD;QACF;QAEA,IAAI,CAAC,iBAAiB;QAEtB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;QAEA,IAAI,CAAC,kBAAkB,GAAG,WAAW;YACnC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;gBAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK;YACzC;QACF,GAAG,IAAI,CAAC,iBAAiB;IAC3B;IAEQ,sBAA8B;QACpC,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ,KAAK;QAC9C,MAAM,WAAW,WAAW,SAAS;QAErC,IAAI,aAAa,6DAAwC;QAEzD,aAAa,WAAW,OAAO,CAAC,gBAAgB;QAEhD,OAAO,GAAG,SAAS,EAAE,EAAE,YAAY;IACrC;AACF;AAEA,MAAM,YAAY,IAAI;AAEtB,IAAI,kBAAuD;AAEpD,MAAM,2BAA2B,CAAC;IACvC,kBAAkB;AACpB;AAEO,MAAM,mBAAmB,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC/D,UAAU;QACV,gBAAgB;QAChB,UAAU;QACV,YAAY,IAAI;QAChB,iBAAiB,IAAI;QACrB,gBAAgB,CAAC;QACjB,qBAAqB;QACrB,SAAS;QACT,OAAO;QACP,kBAAkB;QAElB,eAAe;YACb,UAAU,SAAS,CAAC,CAAC;gBACnB,MAAM,UAAU;gBAChB,MAAM,QAAQ;gBAGd,IAAI,iBAAiB,WAAW,QAAQ,SAAS,KAAK,gBAAgB,OAAO,EAAE;oBAC7E;gBACF;gBAEA,IAAI,MAAM,cAAc,IAAI,QAAQ,UAAU,KAAK,MAAM,cAAc,CAAC,UAAU,EAAE;oBAClF;gBACF;gBAEA,IAAI,QAAQ,EAAE,IAAI,MAAM,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG;oBAClD;gBACF;gBAEA,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;gBAE3G,MAAM,cAAc,MAAM,QAAQ,EAAE,KAAK,CAAA;oBACvC,MAAM,sBAAsB,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;oBAC1F,OAAO,wBAAwB;gBACjC;gBAEA,IAAI,aAAa;oBACf;gBACF;gBAEA,MAAM,UAAU,CAAC;gBAEjB,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,uBAAuB,CAC3B,MAAM,cAAc,CAAC,UAAU,EAC/B,QAAQ,UAAU;gBAEtB;YACF;YAEA,UAAU,cAAc,CAAC,CAAC;gBACxB,IAAI;oBAAE,kBAAkB;gBAAO;YACjC;YAEA,UAAU,OAAO,CAAC,CAAC;gBACjB,IAAI;oBAAE,OAAO;gBAA6B;YAC5C;QACF;QAEA,eAAe;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAAE,SAAS;gBAAM;YACzD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,eAAe,OAAO,WAAmB,OAAO,CAAC,EAAE,QAAQ,EAAE;YAC3D,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,UAAU,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO;gBAE9D,MAAM,cAAc,SAAS,IAAI,CAAC,QAAQ;gBAE1C,MAAM,aAAa,IAAI,IAAI,MAAM,UAAU;gBAC3C,YAAY,OAAO,CAAC,CAAC,MAAiB,WAAW,GAAG,CAAC,IAAI,EAAE;gBAE3D,IAAI,CAAC,QAAU,CAAC;wBACd,UACE,SAAS,IACL,cACA;+BACM,MAAM,QAAQ,IAAI,EAAE;+BACrB,YAAY,MAAM,CACnB,CAAC,MACC,CAAC,MAAM,QAAQ,EAAE,KACf,CAAC,cAAyB,YAAY,EAAE,KAAK,IAAI,EAAE;yBAG1D;wBACP;wBACA,SAAS;oBACX,CAAC;gBAED,IAAI,UAAU,KAAK,YAAY,MAAM,GAAG,GAAG;oBACzC,MAAM,uBAAuB,CAAC,WAAW,WAAW,CAAC,EAAE,CAAC,UAAU;gBACpE;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,aAAa,OAAO;YAClB,MAAM,iBAAiB,MAAM,cAAc;YAC3C,IAAI,CAAC,gBAAgB;YAErB,MAAM,QAAQ;YACd,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,IAAI,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;YAE9G,MAAM,cAAc,MAAM,QAAQ,EAAE,KAAK,CAAA;gBACvC,IAAI,EAAE,SAAS,KAAK,QAAQ,SAAS,EAAE,OAAO;gBAE9C,IAAI,EAAE,OAAO,KAAK,QAAQ,OAAO,EAAE,OAAO;gBAE1C,MAAM,UAAU,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;gBAC9C,MAAM,MAAM,IAAI,OAAO,OAAO;gBAC9B,OAAO,AAAC,MAAM,UAAW,MAAM,YAAY;YAC7C;YAEA,IAAI,aAAa;gBACf;YACF;YAEA,4BAA4B;YAC5B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YAC1C,MAAM,oBAA6B;gBACjC,IAAI;gBACJ,YAAY,eAAe,UAAU;gBACrC,SAAS,QAAQ,OAAO;gBACxB,WAAW,QAAQ,SAAS;gBAC5B,aAAa,QAAQ,WAAW;gBAChC,YAAY,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD;YAChC;YAEA,IAAI;gBACF,MAAM,UAAU,CAAC;gBACjB,IAAI,CAAC,QAAU,CAAC;wBACd,iBAAiB,IAAI,IAAI;+BAAI,MAAM,eAAe;4BAAE;yBAAO;oBAC7D,CAAC;gBAED,IAAI,gBAAgC;gBAEpC,IAAI,UAAU,WAAW,IAAI;oBAC3B,MAAM,YAA8B;wBAClC,MAAM;wBACN,SAAS,QAAQ,OAAO;wBACxB,cAAc,QAAQ,YAAY;wBAClC,YAAY,kBAAkB,UAAU;oBAC1C;oBAEA,MAAM,mBAAmB,UAAU,WAAW,CAAC;oBAE/C,IAAI,kBAAkB;wBAGpB,IAAI,CAAC,QAAU,CAAC;gCACd,iBAAiB,IAAI,IAAI;uCAAI,MAAM,eAAe;iCAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;4BAC5E,CAAC;wBAED;oBACF;gBACF;gBAEA,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE;oBACjD,cAAc,QAAQ,YAAY;oBAClC,SAAS,QAAQ,OAAO;oBACxB,YAAY,kBAAkB,UAAU;gBAC1C;gBAEA,gBAAgB,SAAS,IAAI;gBAE7B,IAAI,eAAe;oBACjB,MAAM,uBAAuB,CAC3B,eAAe,UAAU,EACzB,cAAc,UAAU;gBAE5B;gBAEA,IAAI,CAAC;oBACH,MAAM,sBAAsB,iBAAiB,MAAM,QAAQ,EAAE,KAAK,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;oBAEpG,MAAM,2BAA2B,gBAC/B,GAAG,cAAc,SAAS,CAAC,CAAC,EAAE,cAAc,UAAU,CAAC,CAAC,EAAE,cAAc,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG;oBAEvG,MAAM,uBAAuB,iBAAiB,MAAM,QAAQ,EAAE,KAAK,CAAA;wBACjE,IAAI,IAAI,EAAE,KAAK,QAAQ,OAAO,OAAO,8BAA8B;wBACnE,MAAM,iBAAiB,GAAG,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;wBAC3F,OAAO,mBAAmB;oBAC5B;oBAEA,IAAI,uBAAuB,sBAAsB;wBAC/C,OAAO;4BACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;4BAC1D,YAAY,MAAM,UAAU;4BAC5B,iBAAiB,IAAI,IAAI;mCAAI,MAAM,eAAe;6BAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;wBAC5E;oBACF;oBAEA,IAAI,CAAC,eAAe;wBAClB,OAAO;4BACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;4BAC1D,YAAY,MAAM,UAAU;4BAC5B,iBAAiB,IAAI,IAAI;mCAAI,MAAM,eAAe;6BAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;wBAC5E;oBACF;oBAEA,OAAO;wBACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,MACpC,IAAI,EAAE,KAAK,SAAS,gBAAgB;wBAEtC,YAAY,IAAI,IACd;+BAAK,MAAM,UAAU,IAAI,EAAE;yBAAE,CAC1B,MAAM,CAAC,CAAC,KAAO,OAAO,QACtB,MAAM,CAAC,cAAc,EAAE;wBAE5B,iBAAiB,IAAI,IACnB;+BAAI,MAAM,eAAe;yBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;oBAErD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,MAAM,aAAa,CAAC;gBACpB,MAAM;YACR;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,MAAM,wHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE;oBAChC,GAAG,OAAO;oBACV,YAAY,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;QAEA,mBAAmB,CAAC;YAClB,MAAM,mBAAmB,MAAM,cAAc,EAAE;YAE/C,IAAI;gBACF,UAAU;gBACV,YAAY,IAAI;YAClB;YAEA,IAAI,kBAAkB;gBACpB,UAAU,UAAU;YACtB;YAEA,IAAI;gBAAE,gBAAgB;YAAQ;YAE9B,IAAI,SAAS;gBAEX,MAAM,QAAQ,aAAa,OAAO,CAAC;gBAEnC,IAAI,OAAO;oBACT,MAAM,aAAa;oBAEnB,UAAU,OAAO,CAAC,QAAQ,UAAU,EAAE;oBAEtC,MAAM,aAAa,CAAC,QAAQ,UAAU;gBACxC,OAAO;oBACL,IAAI;wBAAE,OAAO;oBAAiC;gBAChD;YACF;QACF;QAEA,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,IAAI,MAAM,cAAc,IAAI,QAAQ,UAAU,KAAK,MAAM,cAAc,CAAC,UAAU,EAAE;oBAClF,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,IAAI,QAAQ,EAAE,IAAI,MAAM,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG;oBAClD,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;gBAE3G,MAAM,6BAA6B,MAAM,QAAQ,EAAE,KAAK,CAAA;oBACtD,MAAM,sBAAsB,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;oBAC1F,OAAO,wBAAwB;gBACjC;gBAEA,IAAI,4BAA4B;oBAC9B,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,OAAO;oBACL,UAAU;2BAAK,MAAM,QAAQ,IAAI,EAAE;wBAAG;qBAAQ;oBAC9C,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI,MAAM,UAAU,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,MAAM,UAAU;gBACvF;YACF;QACF;QAEA,eAAe,CAAC;YACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;oBAC5D,YAAY,IAAI,IACd;2BAAI,MAAM,UAAU;qBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;oBAE9C,iBAAiB,IAAI,IACnB;2BAAI,MAAM,eAAe;qBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;gBAErD,CAAC;QACH;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;QAEpC,yBAAyB,CAAC,WAAmB;YAC3C,IAAI;gBACF,IAAI,iBAAwC,CAAC;gBAC7C,MAAM,aAAa,aAAa,OAAO,CAAC;gBAExC,IAAI,YAAY;oBACd,iBAAiB,KAAK,KAAK,CAAC;gBAC9B;gBAEA,IAAI,cAAc,CAAC,UAAU,KAAK,WAAW;gBAE7C,cAAc,CAAC,UAAU,GAAG;gBAE5B,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;gBAEtD,IAAI,CAAC,QAAU,CAAC;wBACd,gBAAgB;4BACd,GAAG,MAAM,cAAc;4BACvB,CAAC,UAAU,EAAE;wBACf;wBACA,qBAAqB,KAAK,GAAG,CAAC,GAAG,MAAM,mBAAmB,GAAG;oBAC/D,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;QAEA,wBAAwB;YACtB,IAAI;gBACF,IAAI,qBAA4C,CAAC;gBACjD,MAAM,aAAa,aAAa,OAAO,CAAC;gBAExC,IAAI,YAAY;oBACd,qBAAqB,KAAK,KAAK,CAAC;gBAClC;gBAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;oBACnB,MAAM,MAAM,aAAa;gBAC3B;gBAEA,MAAM,WAAW,MAAM,QAAQ;gBAC/B,IAAI,CAAC,UAAU,OAAO;gBAEtB,IAAI,iBAAiB,CAAC;gBACtB,IAAI,sBAAsB;gBAE1B,MAAM,WAAW,SAAS,GAAG,CAAC,OAAO;oBACnC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,QAAQ,UAAU,CAAC,eAAe,CAAC;oBAG1D,IAAI,SAAS,IAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;wBAC/D,MAAM,gBAAgB,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE;wBAC/C,MAAM,sBAAsB,kBAAkB,CAAC,QAAQ,UAAU,CAAC;wBAElE,IACE,CAAC,uBACD,IAAI,KAAK,cAAc,UAAU,IAAI,IAAI,KAAK,sBAC9C;4BACA,cAAc,CAAC,QAAQ,UAAU,CAAC,GAAG;4BACrC;wBACF;oBACF;gBACF;gBAEA,MAAM,QAAQ,GAAG,CAAC;gBAClB,IAAI;oBACF;oBACA;gBACF;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,OAAO;YACT;QACF;QAEA,oBAAoB;YAClB,MAAM,iBAAiB,MAAM,cAAc;YAC3C,IAAI,gBAAgB;gBAClB,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,UAAU,UAAU;oBACpB,UAAU,OAAO,CAAC,eAAe,UAAU,EAAE;gBAC/C;YACF,OAAO;gBACL,UAAU,UAAU;YACtB;QACF;QAEA,sBAAsB;YACpB,OAAO,UAAU,WAAW;QAC9B;IACF,CAAC"}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/student/sessions/send-message-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport * as z from \"zod\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@components/ui/dialog\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Textarea } from \"@components/ui/textarea\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@components/ui/avatar\";\r\nimport { useWebSocketChat } from \"@/app/hooks/useWebSocketChat\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\nconst messageSchema = z.object({\r\n  message: z\r\n    .string()\r\n    .min(1, \"Message is required\")\r\n    .max(500, \"Message cannot exceed 500 characters\"),\r\n});\r\n\r\ntype MessageFormData = z.infer<typeof messageSchema>;\r\n\r\ninterface CounselorInfo {\r\n  user_id: number | null;\r\n  first_name: string;\r\n  last_name: string;\r\n  profile_picture_url: string | null;\r\n}\r\n\r\ninterface SendMessageDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  counselorInfo: CounselorInfo;\r\n}\r\n\r\nexport function SendMessageDialog({\r\n  open,\r\n  onOpenChange,\r\n  counselorInfo,\r\n}: SendMessageDialogProps) {\r\n  const [isSending, setIsSending] = useState(false);\r\n  const router = useRouter();\r\n  const { userInfo } = useProfile();\r\n  const { sendInitialMessage } = useWebSocketChat();\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    reset,\r\n  } = useForm<MessageFormData>({\r\n    resolver: zodResolver(messageSchema),\r\n  });\r\n\r\n  const onSubmit = async (data: MessageFormData) => {\r\n    if (!userInfo || !counselorInfo?.user_id) return;\r\n\r\n    setIsSending(true);\r\n    try {\r\n      await sendInitialMessage({\r\n        message: data.message,\r\n        recipient_id: counselorInfo.user_id,\r\n      });\r\n\r\n      toast.success(\"Message sent successfully!\");\r\n      reset();\r\n      onOpenChange(false);\r\n      router.push(\"/student/dashboard/messages\");\r\n    } catch (error: any) {\r\n      if (\r\n        error?.response?.data?.detail?.includes(\r\n          \"between a counselor and a student\"\r\n        )\r\n      ) {\r\n        toast.error(\"You cannot send any messages to this person.\");\r\n      } else {\r\n        toast.error(\"Failed to send message. Please try again.\");\r\n      }\r\n    } finally {\r\n      setIsSending(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-2xl font-bold text-center\">\r\n            Send message to:\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"flex items-center space-x-4 mt-4\">\r\n          <Avatar className=\"w-16 h-16\">\r\n            <AvatarImage\r\n              src={\r\n                counselorInfo?.profile_picture_url ||\r\n                generatePlaceholder(\r\n                  counselorInfo?.first_name,\r\n                  counselorInfo?.last_name\r\n                )\r\n              }\r\n              alt={counselorInfo?.first_name || \"\"}\r\n            />\r\n            <AvatarFallback>{counselorInfo?.first_name || \"\"}</AvatarFallback>\r\n          </Avatar>\r\n          <div className=\"flex flex-col\">\r\n            <h3 className=\"text-xl font-semibold\">\r\n              {counselorInfo\r\n                ? `${counselorInfo.first_name} ${counselorInfo.last_name}`\r\n                : \"loading...\"}\r\n            </h3>\r\n          </div>\r\n        </div>\r\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4 mt-4\">\r\n          <div>\r\n            <Textarea\r\n              {...register(\"message\")}\r\n              placeholder=\"Type your message here...\"\r\n              className=\"min-h-[100px]\"\r\n            />\r\n            {errors.message && (\r\n              <p className=\"text-red-500 text-sm mt-1\">\r\n                {errors.message.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full bg-blue-800 hover:bg-blue-800/90\"\r\n            disabled={isSending}\r\n          >\r\n            {isSending ? \"Sending...\" : \"Send Message\"}\r\n          </Button>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAaA;AAfA;AAHA;;;;;;;;;;;;;;;AAqBA,MAAM,gBAAgB,6IAAE,MAAM,CAAC;IAC7B,SAAS,6IACN,MAAM,GACN,GAAG,CAAC,GAAG,uBACP,GAAG,CAAC,KAAK;AACd;AAiBO,SAAS,kBAAkB,EAChC,IAAI,EACJ,YAAY,EACZ,aAAa,EACU;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD;IAE9C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,YAAY,CAAC,eAAe,SAAS;QAE1C,aAAa;QACb,IAAI;YACF,MAAM,mBAAmB;gBACvB,SAAS,KAAK,OAAO;gBACrB,cAAc,cAAc,OAAO;YACrC;YAEA,2JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,aAAa;YACb,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,IACE,OAAO,UAAU,MAAM,QAAQ,SAC7B,sCAEF;gBACA,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,kMAAC,0IAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,kMAAC,0IAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,kMAAC,0IAAA,CAAA,eAAY;8BACX,cAAA,kMAAC,0IAAA,CAAA,cAAW;wBAAC,WAAU;kCAAiC;;;;;;;;;;;8BAI1D,kMAAC;oBAAI,WAAU;;sCACb,kMAAC,0IAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,kMAAC,0IAAA,CAAA,cAAW;oCACV,KACE,eAAe,uBACf,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAChB,eAAe,YACf,eAAe;oCAGnB,KAAK,eAAe,cAAc;;;;;;8CAEpC,kMAAC,0IAAA,CAAA,iBAAc;8CAAE,eAAe,cAAc;;;;;;;;;;;;sCAEhD,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC;gCAAG,WAAU;0CACX,gBACG,GAAG,cAAc,UAAU,CAAC,CAAC,EAAE,cAAc,SAAS,EAAE,GACxD;;;;;;;;;;;;;;;;;8BAIV,kMAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAChD,kMAAC;;8CACC,kMAAC,4IAAA,CAAA,WAAQ;oCACN,GAAG,SAAS,UAAU;oCACvB,aAAY;oCACZ,WAAU;;;;;;gCAEX,OAAO,OAAO,kBACb,kMAAC;oCAAE,WAAU;8CACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;sCAI7B,kMAAC,0IAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;sCAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAMxC"}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/ProfileHeader.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport { But<PERSON> } from \"@components/ui/button\";\r\nimport { NavigationTabs } from \"./NavigationTabs\";\r\nimport { ArrowLeft } from \"lucide-react\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { SendMessageDialog } from \"../../student/sessions/send-message-dialog\";\r\nimport { useState } from \"react\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\ninterface ProfileHeaderProps {\r\n  counselorUserId: number;\r\n  profileData: CounselorPublicProfile;\r\n  setIsBookingOpen: (open: boolean) => void;\r\n}\r\n\r\nexport function ProfileHeader({\r\n  counselorUserId,\r\n  profileData,\r\n  setIsBookingOpen,\r\n}: ProfileHeaderProps) {\r\n  const [showSendMessage, setShowSendMessage] = useState(false);\r\n\r\n  const handleCloseTab = () => {\r\n    window.close();\r\n\r\n    // If somehow still here\r\n    history.back();\r\n  };\r\n\r\n  return (\r\n    <div className=\"sticky top-0 bg-white/95 backdrop-blur-sm z-20 pt-3 sticky-header shadow-sm\">\r\n      <button\r\n        onClick={handleCloseTab}\r\n        className=\"flex items-center gap-2 text-[13px] mb-6 font-medium font-clash text-blue-950 hover:text-blue-800 transition-colors duration-200 group\"\r\n      >\r\n        <ArrowLeft className=\"w-6 h-6 mr-1.5 font-medium transition-transform duration-200 group-hover:-translate-x-1\" />\r\n        Go back\r\n      </button>\r\n\r\n      <div className=\"flex flex-col md:flex-row items-start justify-between gap-5 mb-6\">\r\n        <div className=\"flex md:items-center gap-5\">\r\n          <div className=\"relative w-20 h-20 md:w-[150px] md:h-[150px] flex-shrink-0 group\">\r\n            <Image\r\n              src={\r\n                profileData?.profile_picture_url ||\r\n                generatePlaceholder(\r\n                  profileData?.first_name,\r\n                  profileData?.last_name\r\n                )\r\n              }\r\n              alt=\"Profile picture\"\r\n              width={150}\r\n              height={150}\r\n              className=\"rounded-full object-cover w-full h-full ring-4 ring-gray-50 transition-transform duration-300 group-hover:scale-[1.02] shadow-md\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex-shrink\">\r\n            <div className=\"flex items-center gap-3 mb-2\">\r\n              <h1 className=\"text-[16px] md:text-[24px] font-[500] text-blue-950 truncate\">\r\n                {profileData?.first_name || \"Counselor\"}{\" \"}\r\n                {profileData?.last_name || \"\"}\r\n              </h1>\r\n            </div>\r\n\r\n            <p className=\"text-[16px] md:text-[20px] break-words text-gray-600\">\r\n              {profileData?.tagline || \"\"}\r\n            </p>\r\n\r\n            <p className=\"text-[13px] md:text-[16px] text-gray-600\">\r\n              {profileData?.professional_experience?.[0]?.role || \"Counselor\"}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"w-full md:w-auto md:min-w-[380px] p-4 md:p-5 space-y-3 md:space-y-4 border border-gray-200 rounded-xl bg-white shadow-sm hover:shadow-md hover:border-gray-300 transition-all duration-300\">\r\n          <div className=\"flex gap-3\">\r\n            <Button\r\n              onClick={() => {\r\n                localStorage.getItem(\"access_token\")\r\n                  ? setShowSendMessage(true)\r\n                  : (window.location.href = \"/auth/login\");\r\n              }}\r\n              variant=\"outline\"\r\n              className=\"flex-1 h-9 md:h-10 text-[13px] md:text-[14px] bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-colors duration-200\"\r\n            >\r\n              Message\r\n            </Button>\r\n            <Button\r\n              className=\"flex-1 h-9 md:h-10 text-[13px] md:text-[14px] bg-blue-950 hover:bg-blue-900 transition-colors duration-200 shadow-sm hover:shadow\"\r\n              onClick={() => setIsBookingOpen(true)}\r\n            >\r\n              Select Services\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <NavigationTabs counselor={profileData} />\r\n      <SendMessageDialog\r\n        open={showSendMessage}\r\n        onOpenChange={setShowSendMessage}\r\n        counselorInfo={profileData}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAGA;AACA;AACA;AAJA;AAHA;;;;;;;;;AAeO,SAAS,cAAc,EAC5B,eAAe,EACf,WAAW,EACX,gBAAgB,EACG;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,iBAAiB;QACrB,OAAO,KAAK;QAEZ,wBAAwB;QACxB,QAAQ,IAAI;IACd;IAEA,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,kMAAC,wNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAA4F;;;;;;;0BAInH,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAI,WAAU;0CACb,cAAA,kMAAC,iLAAA,CAAA,UAAK;oCACJ,KACE,aAAa,uBACb,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAChB,aAAa,YACb,aAAa;oCAGjB,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAId,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAI,WAAU;kDACb,cAAA,kMAAC;4CAAG,WAAU;;gDACX,aAAa,cAAc;gDAAa;gDACxC,aAAa,aAAa;;;;;;;;;;;;kDAI/B,kMAAC;wCAAE,WAAU;kDACV,aAAa,WAAW;;;;;;kDAG3B,kMAAC;wCAAE,WAAU;kDACV,aAAa,yBAAyB,CAAC,EAAE,EAAE,QAAQ;;;;;;;;;;;;;;;;;;kCAK1D,kMAAC;wBAAI,WAAU;kCACb,cAAA,kMAAC;4BAAI,WAAU;;8CACb,kMAAC,0IAAA,CAAA,SAAM;oCACL,SAAS;wCACP,aAAa,OAAO,CAAC,kBACjB,mBAAmB,QAClB,OAAO,QAAQ,CAAC,IAAI,GAAG;oCAC9B;oCACA,SAAQ;oCACR,WAAU;8CACX;;;;;;8CAGD,kMAAC,0IAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,iBAAiB;8CACjC;;;;;;;;;;;;;;;;;;;;;;;0BAMP,kMAAC,iKAAA,CAAA,iBAAc;gBAAC,WAAW;;;;;;0BAC3B,kMAAC,8KAAA,CAAA,oBAAiB;gBAChB,MAAM;gBACN,cAAc;gBACd,eAAe;;;;;;;;;;;;AAIvB"}}, {"offset": {"line": 1328, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/Overview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\n\r\ninterface OverviewProps {\r\n  profileData: CounselorPublicProfile;\r\n}\r\n\r\nexport function Overview({ profileData }: OverviewProps) {\r\n  if (!profileData || !profileData.bio) {\r\n    return (\r\n      <div\r\n        id=\"overview\"\r\n        className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6 mb-6\"\r\n      >\r\n        <h2 className=\"text-[15px] font-medium text-blue-950 mb-3\">Overview</h2>\r\n        <p className=\"text-[13px] text-gray-500\">\r\n          No overview information available.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      id=\"overview\"\r\n      className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6 mb-6\"\r\n    >\r\n      <h2 className=\"text-[15px] font-medium text-blue-950 mb-3\">Overview</h2>\r\n      <p className=\"text-[13px] text-gray-500\">{profileData.bio}</p>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAQO,SAAS,SAAS,EAAE,WAAW,EAAiB;IACrD,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,EAAE;QACpC,qBACE,kMAAC;YACC,IAAG;YACH,WAAU;;8BAEV,kMAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAC3D,kMAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;IAK/C;IAEA,qBACE,kMAAC;QACC,IAAG;QACH,WAAU;;0BAEV,kMAAC;gBAAG,WAAU;0BAA6C;;;;;;0BAC3D,kMAAC;gBAAE,WAAU;0BAA6B,YAAY,GAAG;;;;;;;;;;;;AAG/D"}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nCard.displayName = \"Card\";\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardHeader.displayName = \"CardHeader\";\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardTitle.displayName = \"CardTitle\";\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardDescription.displayName = \"CardDescription\";\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n));\r\nCardContent.displayName = \"CardContent\";\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardFooter.displayName = \"CardFooter\";\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardDescription,\r\n  CardContent,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,mKAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,mKAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,mKAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,mKAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mKAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,mKAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/Services.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { Card } from \"@components/ui/card\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport type { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { User } from \"lucide-react\";\r\n// import { PackageSelectionDialog } from \"./PackageSelection\";\r\n\r\ninterface ServicesProps {\r\n  setIsBookingOpen: (open: boolean) => void;\r\n  profileData: CounselorPublicProfile;\r\n  setSelectedService: (serviceType: string) => void;\r\n}\r\n\r\nexport function Services({\r\n  setIsBookingOpen,\r\n  profileData,\r\n  setSelectedService,\r\n}: ServicesProps) {\r\n  const hasServices = profileData.services && profileData.services.length > 0;\r\n\r\n  if (!hasServices) {\r\n    return (\r\n      <div\r\n        id=\"services\"\r\n        className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n      >\r\n        <h2 className=\"text-[15px] font-medium text-blue-950 mb-3\">Services</h2>\r\n        <p className=\"text-[13px] text-gray-500\">\r\n          No services available at the moment.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const freeIntroCall = profileData.services.find(\r\n    (service) =>\r\n      service.service_type.toLowerCase().includes(\"free\") &&\r\n      service.service_type.toLowerCase().includes(\"intro\")\r\n  );\r\n\r\n  const otherServices = profileData.services.filter(\r\n    (service) =>\r\n      !service.service_type.toLowerCase().includes(\"free\") ||\r\n      !service.service_type.toLowerCase().includes(\"intro\")\r\n  );\r\n\r\n  return (\r\n    <div id=\"services\" className=\"w-full space-y-6\">\r\n      {/* Services Section */}\r\n      <div className=\"bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\">\r\n        <h2 className=\"text-xl font-semibold text-blue-950 mb-6\">Services</h2>\r\n\r\n        {/* Free Intro Call Section */}\r\n        {freeIntroCall && (\r\n          <div className=\"mb-6 flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 p-4 gap-4\">\r\n            <div className=\"flex items-start sm:items-center gap-4 w-full sm:w-auto\">\r\n              <div className=\"bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                <User className=\"w-5 h-5 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <h3 className=\"text-lg font-medium text-blue-950\">\r\n                  {freeIntroCall.service_type}\r\n                </h3>\r\n                <p className=\"text-sm text-gray-600\">\r\n                  {freeIntroCall.description}\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <Button\r\n              className=\"w-full sm:w-32 bg-blue-950 hover:bg-blue-950/90 text-white whitespace-nowrap sm:ml-4\"\r\n              onClick={() => {\r\n                setSelectedService(freeIntroCall.service_type);\r\n                setIsBookingOpen(true);\r\n              }}\r\n            >\r\n              Book Free Call\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        {/* Services Grid */}\r\n        <div className=\"mt-8\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n            {otherServices.map((service) => (\r\n              <Card\r\n                key={service.id}\r\n                className=\"p-6 flex flex-col h-[400px] border border-gray-100 transition-all duration-300 hover:border-blue-200 hover:shadow-[0_8px_30px_rgb(0,0,0,0.06)] group rounded-xl overflow-hidden\"\r\n              >\r\n                <div className=\"flex flex-col h-full\">\r\n                  {\" \"}\r\n                  {/* Service Icon - Fixed Height */}\r\n                  <div\r\n                    className=\"w-12 h-12 rounded-full flex items-center justify-center mb-4 transition-all duration-300 group-hover:scale-105\"\r\n                    style={{\r\n                      backgroundColor: service.service_type.includes(\"Essay\")\r\n                        ? \"#FFF7E6\"\r\n                        : service.service_type.includes(\"Hour\")\r\n                        ? \"#E6F6EC\"\r\n                        : \"#E6F0FF\",\r\n                    }}\r\n                  >\r\n                    <User\r\n                      className=\"w-6 h-6\"\r\n                      style={{\r\n                        color: service.service_type.includes(\"Essay\")\r\n                          ? \"#FFB020\"\r\n                          : service.service_type.includes(\"Hour\")\r\n                          ? \"#10B981\"\r\n                          : \"#2563EB\",\r\n                      }}\r\n                    />\r\n                  </div>\r\n                  {/* Service Name - Fixed Height */}\r\n                  <h3 className=\"text-xl font-medium text-blue-950 mb-4 line-clamp-2 transition-colors duration-300 group-hover:text-blue-900\">\r\n                    {service.service_type}\r\n                  </h3>\r\n                  {/* Description - Scrollable */}\r\n                  <div className=\"flex-1 line-clamp-4 mb-4 pr-2\">\r\n                    <p className=\"text-gray-500\">{service.description}</p>\r\n                  </div>\r\n                  {/* Price and Button - Fixed Height */}\r\n                  <div className=\"mt-auto\">\r\n                    <div className=\"mb-4\">\r\n                      <div className=\"text-sm text-gray-500\">Price</div>\r\n                      <div className=\"text-2xl font-medium text-blue-950/90 group-hover:text-blue-950 transition-colors duration-300\">\r\n                        ${service.price}\r\n                      </div>\r\n                    </div>\r\n                    <Button\r\n                      className=\"w-full bg-blue-950 hover:bg-blue-900 rounded-lg transition-all duration-300\"\r\n                      onClick={() => {\r\n                        setSelectedService(service.service_type);\r\n                        setIsBookingOpen(true);\r\n                      }}\r\n                    >\r\n                      Select Service\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Package selection is disabled in this phase */}\r\n      {/* <PackageSelectionDialog\r\n        open={packageDialogOpen}\r\n        onOpenChange={setPackageDialogOpen}\r\n        profileData={profileData}\r\n      /> */}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAeO,SAAS,SAAS,EACvB,gBAAgB,EAChB,WAAW,EACX,kBAAkB,EACJ;IACd,MAAM,cAAc,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG;IAE1E,IAAI,CAAC,aAAa;QAChB,qBACE,kMAAC;YACC,IAAG;YACH,WAAU;;8BAEV,kMAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAC3D,kMAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;IAK/C;IAEA,MAAM,gBAAgB,YAAY,QAAQ,CAAC,IAAI,CAC7C,CAAC,UACC,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAC5C,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC;IAGhD,MAAM,gBAAgB,YAAY,QAAQ,CAAC,MAAM,CAC/C,CAAC,UACC,CAAC,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAC7C,CAAC,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC;IAGjD,qBACE,kMAAC;QAAI,IAAG;QAAW,WAAU;kBAE3B,cAAA,kMAAC;YAAI,WAAU;;8BACb,kMAAC;oBAAG,WAAU;8BAA2C;;;;;;gBAGxD,+BACC,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAI,WAAU;;8CACb,kMAAC;oCAAI,WAAU;8CACb,cAAA,kMAAC,0MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,kMAAC;;sDACC,kMAAC;4CAAG,WAAU;sDACX,cAAc,YAAY;;;;;;sDAE7B,kMAAC;4CAAE,WAAU;sDACV,cAAc,WAAW;;;;;;;;;;;;;;;;;;sCAIhC,kMAAC,0IAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS;gCACP,mBAAmB,cAAc,YAAY;gCAC7C,iBAAiB;4BACnB;sCACD;;;;;;;;;;;;8BAOL,kMAAC;oBAAI,WAAU;8BACb,cAAA,kMAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,kMAAC,wIAAA,CAAA,OAAI;gCAEH,WAAU;0CAEV,cAAA,kMAAC;oCAAI,WAAU;;wCACZ;sDAED,kMAAC;4CACC,WAAU;4CACV,OAAO;gDACL,iBAAiB,QAAQ,YAAY,CAAC,QAAQ,CAAC,WAC3C,YACA,QAAQ,YAAY,CAAC,QAAQ,CAAC,UAC9B,YACA;4CACN;sDAEA,cAAA,kMAAC,0MAAA,CAAA,OAAI;gDACH,WAAU;gDACV,OAAO;oDACL,OAAO,QAAQ,YAAY,CAAC,QAAQ,CAAC,WACjC,YACA,QAAQ,YAAY,CAAC,QAAQ,CAAC,UAC9B,YACA;gDACN;;;;;;;;;;;sDAIJ,kMAAC;4CAAG,WAAU;sDACX,QAAQ,YAAY;;;;;;sDAGvB,kMAAC;4CAAI,WAAU;sDACb,cAAA,kMAAC;gDAAE,WAAU;0DAAiB,QAAQ,WAAW;;;;;;;;;;;sDAGnD,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,kMAAC;4DAAI,WAAU;;gEAAiG;gEAC5G,QAAQ,KAAK;;;;;;;;;;;;;8DAGnB,kMAAC,0IAAA,CAAA,SAAM;oDACL,WAAU;oDACV,SAAS;wDACP,mBAAmB,QAAQ,YAAY;wDACvC,iBAAiB;oDACnB;8DACD;;;;;;;;;;;;;;;;;;+BAjDA,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAoE/B"}}, {"offset": {"line": 1744, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1750, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/public/usePublicPackages.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport { PackageData } from \"@/app/types/counselor/package\";\r\n\r\nexport interface PublicPackagesState {\r\n  packages: { total: number; items: PackageData[] };\r\n  selectedPackage: PackageData | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchCounselorPackages: (counselorId: number) => Promise<void>;\r\n  fetchPackageById: (packageId: number) => Promise<void>;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const usePublicPackages = create<PublicPackagesState>((set) => ({\r\n  packages: { total: 0, items: [] },\r\n  selectedPackage: null,\r\n  loading: false,\r\n  error: null,\r\n\r\n  fetchCounselorPackages: async (counselorId) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(`/counselor/packages/${counselorId}/packages`);\r\n      set({ packages: response.data });\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.message || \"Error fetching counselor packages\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  fetchPackageById: async (packageId) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get<PackageData>(`/counselor/packages/${packageId}`);\r\n      set({ selectedPackage: response.data });\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.message || \"Error fetching package details\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAeO,MAAM,oBAAoB,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAuB,CAAC,MAAQ,CAAC;QACrE,UAAU;YAAE,OAAO;YAAG,OAAO,EAAE;QAAC;QAChC,iBAAiB;QACjB,SAAS;QACT,OAAO;QAEP,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,SAAS,CAAC;gBAClF,IAAI;oBAAE,UAAU,SAAS,IAAI;gBAAC;YAChC,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW;gBACvD,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB,OAAO;YACvB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAAc,CAAC,oBAAoB,EAAE,WAAW;gBACpF,IAAI;oBAAE,iBAAiB,SAAS,IAAI;gBAAC;YACvC,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW;gBACvD,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 1817, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1823, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-12 items-center justify-start border-b w-full overflow-x-auto\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap px-4 py-3 text-base font-medium text-gray-600 border-b-2 border-transparent transition-colors hover:text-gray-900\",\r\n      \"data-[state=active]:border-gray-900 data-[state=active]:text-gray-900\",\r\n      \"focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-4 ring-offset-background focus-visible:outline-none\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,yKAAc,IAAI;AAE/B,MAAM,yBAAW,mKAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,yKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,yKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,mKAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,yKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,+KACA,yEACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,yKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,mKAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,kMAAC,yKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,yKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 1870, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1876, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/student/usePackagePayments.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport { FirstSessionData } from \"./usePackages\";\r\n\r\nexport interface PackageCheckoutPayload {\r\n  package_id: number; // Still required by the backend\r\n  subscription_id: number; // Required - the ID of the created subscription\r\n  first_session?: FirstSessionData; // Optional\r\n  selected_service: string; // Still required by the backend\r\n}\r\n\r\nexport interface PackageCheckoutResponse {\r\n  checkout_url: string;\r\n  session_id: string;\r\n  payment_id: number;\r\n}\r\n\r\nexport interface PackagePaymentsState {\r\n  loading: boolean;\r\n  error: string | null;\r\n  createCheckoutSession: (data: PackageCheckoutPayload) => Promise<PackageCheckoutResponse>;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const usePackagePayments = create<PackagePaymentsState>((set) => ({\r\n  loading: false,\r\n  error: null,\r\n\r\n  createCheckoutSession: async (data) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.post<PackageCheckoutResponse>(\r\n        \"/payments/package/create-checkout-session\",\r\n        data\r\n      );\r\n      set({ loading: false });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage = error.response?.data?.message || \"Failed to create checkout session\";\r\n      set({ error: errorMessage, loading: false });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAyBO,MAAM,qBAAqB,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAwB,CAAC,MAAQ,CAAC;QACvE,SAAS;QACT,OAAO;QAEP,uBAAuB,OAAO;YAC5B,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,6CACA;gBAEF,IAAI;oBAAE,SAAS;gBAAM;gBACrB,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACtD,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 1913, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1919, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/student/usePackages.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nexport interface StudentPackageItem {\r\n  service_name: string;\r\n  hours: {\r\n    total: number;\r\n    used: number;\r\n    remaining: number;\r\n  };\r\n}\r\n\r\nexport interface StudentPackage {\r\n  id: number;\r\n  package: {\r\n    id: number;\r\n    title: string;\r\n    description: string;\r\n    counselor_name: string;\r\n    counselor_profile_picture: string;\r\n  };\r\n  start_date: string;\r\n  end_date: string | null;\r\n  status: 'pending' | 'active' | 'completed' | 'cancelled' | 'expired';\r\n  service_hours: {\r\n    [service_name: string]: {\r\n      total: number;\r\n      used: number;\r\n    };\r\n  };\r\n  sessions: {\r\n    id: number;\r\n    date: string;\r\n    start_time: string;\r\n    end_time: string;\r\n    status: string;\r\n    service_name: string;\r\n  }[];\r\n  progress?: {\r\n    [service_name: string]: {\r\n      total: number;\r\n      used: number;\r\n      remaining: number;\r\n    };\r\n  };\r\n}\r\n\r\nexport interface FirstSessionData {\r\n  event_name: string;\r\n  date: string;\r\n  start_time: string;\r\n  end_time: string;\r\n  student_email: string;\r\n}\r\n\r\nexport interface PackageSubscriptionPayload {\r\n  package_id: number;\r\n  selected_service: string;\r\n  first_session: FirstSessionData;\r\n  promo_code?: string;\r\n}\r\n\r\nexport interface SessionFromPackagePayload {\r\n  selected_service: string;\r\n  date: string;\r\n  start_time: string;\r\n  end_time: string;\r\n}\r\n\r\nexport interface PackagesState {\r\n  packages: StudentPackage[];\r\n  selectedPackage: StudentPackage | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  activePackages: StudentPackage[];\r\n  completedPackages: StudentPackage[];\r\n  fetchPackages: (status?: string) => Promise<void>;\r\n  fetchPackageById: (subscriptionId: number) => Promise<void>;\r\n  subscribeToPackage: (data: PackageSubscriptionPayload) => Promise<any>;\r\n  clearError: () => void;\r\n}\r\n\r\nconst usePackages = create<PackagesState>((set, get) => ({\r\n  packages: [],\r\n  selectedPackage: null,\r\n  loading: false,\r\n  error: null,\r\n  activePackages: [],\r\n  completedPackages: [],\r\n\r\n  fetchPackages: async (status = \"active\") => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.get<StudentPackage[]>(\r\n        `/student/packages/my-packages?status=${status}`\r\n      );\r\n\r\n      // Store packages in the appropriate state based on status\r\n      if (status === \"active\") {\r\n        set({\r\n          packages: response.data,\r\n          activePackages: response.data,\r\n          loading: false\r\n        });\r\n      } else if (status === \"completed\") {\r\n        set({\r\n          completedPackages: response.data,\r\n          loading: false\r\n        });\r\n      } else {\r\n        set({ packages: response.data, loading: false });\r\n      }\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.message || \"Failed to fetch packages\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchPackageById: async (subscriptionId) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.get<StudentPackage>(\r\n        `/student/packages/${subscriptionId}`\r\n      );\r\n      set({ selectedPackage: response.data, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.message || \"Failed to fetch package details\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  subscribeToPackage: async (data) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.post(\r\n        \"/student/packages/subscribe\",\r\n        data\r\n      );\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage = error.response?.data?.message || \"Failed to subscribe to package\";\r\n      set({ error: errorMessage, loading: false });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n\r\nexport default usePackages;\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAmFA,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QACvD,UAAU,EAAE;QACZ,iBAAiB;QACjB,SAAS;QACT,OAAO;QACP,gBAAgB,EAAE;QAClB,mBAAmB,EAAE;QAErB,eAAe,OAAO,SAAS,QAAQ;YACrC,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,qCAAqC,EAAE,QAAQ;gBAGlD,0DAA0D;gBAC1D,IAAI,WAAW,UAAU;oBACvB,IAAI;wBACF,UAAU,SAAS,IAAI;wBACvB,gBAAgB,SAAS,IAAI;wBAC7B,SAAS;oBACX;gBACF,OAAO,IAAI,WAAW,aAAa;oBACjC,IAAI;wBACF,mBAAmB,SAAS,IAAI;wBAChC,SAAS;oBACX;gBACF,OAAO;oBACL,IAAI;wBAAE,UAAU,SAAS,IAAI;wBAAE,SAAS;oBAAM;gBAChD;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;oBACxC,SAAS;gBACX;YACF;QACF;QAEA,kBAAkB,OAAO;YACvB,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,gBAAgB;gBAEvC,IAAI;oBAAE,iBAAiB,SAAS,IAAI;oBAAE,SAAS;gBAAM;YACvD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;oBACxC,SAAS;gBACX;YACF;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,+BACA;gBAEF,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACtD,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC;uCAEc"}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,mKAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,kMAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,yIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/public/useCounselors.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport {\r\n  CounselorPublicProfile,\r\n  CounselorListResponse,\r\n} from \"@/app/types/counselor\";\r\nimport { format } from \"date-fns\";\r\n\r\ninterface Filters {\r\n  service_type?: string[];\r\n  country?: string[];\r\n  university?: string[];\r\n  name?: string;\r\n}\r\n\r\ninterface ApiParams extends Filters {\r\n  page: number;\r\n  limit: number;\r\n  service_types?: string;\r\n  countries?: string;\r\n  universities?: string;\r\n  name?: string;\r\n}\r\n\r\ninterface CounselorsState {\r\n  loading: boolean;\r\n  profileLoading: boolean;\r\n  availabilityLoading: boolean;\r\n  datesLoading: boolean;\r\n  error: string | null;\r\n  counselors: CounselorPublicProfile[] | null;\r\n  counselorIds: number[];\r\n  filters: Filters;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  hasMore: boolean;\r\n  availableDates: string[];\r\n  timeSlots: string[];\r\n\r\n  fetchAllCounselors: () => Promise<void>;\r\n  fetchCounselorProfile: (userId: number) => Promise<CounselorPublicProfile>;\r\n  setFilters: (filters: Filters) => void;\r\n  fetchTopCounselors: () => Promise<CounselorPublicProfile[]>;\r\n  fetchTimeSlots: (\r\n    counselorUserId: number,\r\n    targetDate: Date,\r\n    timezone: string\r\n  ) => Promise<string[]>;\r\n  fetchAvailableDates: (\r\n    counselorUserId: number,\r\n    startDate: Date,\r\n    endDate: Date,\r\n    timezone: string\r\n  ) => Promise<string[]>;\r\n  loadMore: () => Promise<void>;\r\n  setLoading: (loading: boolean) => void;\r\nclearFilters: () => void;\r\n}\r\n\r\nexport const useCounselors = create<CounselorsState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      loading: false,\r\n      profileLoading: false,\r\n      availabilityLoading: false,\r\n      datesLoading: false,\r\n      error: null,\r\n      counselors: null,\r\n      counselorIds: [],\r\n      filters: {},\r\n      currentPage: 1,\r\n      totalPages: 1,\r\n      hasMore: false,\r\n      availableDates: [],\r\n      timeSlots: [],\r\n\r\n      setLoading: (loading) => {\r\n        set({ loading });\r\n      },\r\n      fetchAllCounselors: async () => {\r\n        try {\r\n          const { filters, currentPage } = get();\r\n\r\n          // Only set loading true if this is the first page\r\n          if (currentPage === 1) {\r\n            set({ loading: true, error: null });\r\n          }\r\n\r\n          // Format the parameters for the API call\r\n          const params: ApiParams = {\r\n            page: currentPage,\r\n            limit: 16,\r\n          };\r\n\r\n          // Only add filters that have a value\r\n          if (filters.service_type && filters.service_type.length > 0) {\r\n            params.service_types = filters.service_type.join(\",\");\r\n          }\r\n\r\n          if (filters.country && filters.country.length > 0) {\r\n            params.countries = filters.country.join(\",\");\r\n          }\r\n\r\n          if (filters.university && filters.university.length > 0) {\r\n            params.universities = filters.university.join(\",\");\r\n          }\r\n\r\n          // Only add name if it exists and is not empty\r\n          if (filters.name && filters.name.trim() !== \"\") {\r\n            params.name = filters.name.trim();\r\n          }\r\n\r\n          const { data } = await apiClient.get<CounselorListResponse>(\r\n            \"/counselor/profiles/public-profile/list\",\r\n            { params }\r\n          );\r\n\r\n          if (data.items && data.items.length > 0) {\r\n            set((state) => {\r\n              // For first page or filter changes, replace the list\r\n              const isFirstPage = currentPage === 1;\r\n\r\n              return {\r\n                counselors: isFirstPage\r\n                  ? data.items\r\n                  : [...(state.counselors || []), ...data.items],\r\n                counselorIds: isFirstPage\r\n                  ? data.items.map((c) => c.user_id)\r\n                  : [\r\n                      ...new Set([\r\n                        ...state.counselorIds,\r\n                        ...data.items.map((c) => c.user_id),\r\n                      ]),\r\n                    ],\r\n                totalPages: data.pages,\r\n                hasMore: data.has_next,\r\n                loading: false,\r\n                error: null,\r\n              };\r\n            });\r\n          } else {\r\n            set((state) => ({\r\n              ...state,\r\n              counselors: currentPage === 1 ? [] : state.counselors,\r\n              counselorIds: currentPage === 1 ? [] : state.counselorIds,\r\n              totalPages: currentPage === 1 ? 0 : state.totalPages,\r\n              hasMore: false,\r\n              loading: false,\r\n              error: currentPage === 1 ? \"No counselors found\" : null,\r\n            }));\r\n          }\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch counselors\",\r\n            loading: false,\r\n          });\r\n          console.error(\"Error fetching counselors:\", error);\r\n        }\r\n      },\r\n\r\n      fetchCounselorProfile: async (userId: number) => {\r\n        try {\r\n          set({ profileLoading: true, error: null });\r\n          const { data } = await apiClient.get<CounselorPublicProfile>(\r\n            `/counselor/profile/public-profile/user/${userId}`\r\n          );\r\n\r\n          // Update counselors state with the fetched profile\r\n          set((state) => {\r\n            // Create a new array with existing counselors\r\n            const updatedCounselors = [...(state.counselors || [])];\r\n\r\n            // Find if this counselor already exists in our array\r\n            const existingIndex = updatedCounselors.findIndex(\r\n              (c) => c.user_id === userId\r\n            );\r\n\r\n            // If counselor exists, update it, otherwise add it\r\n            if (existingIndex >= 0) {\r\n              updatedCounselors[existingIndex] = data;\r\n            } else {\r\n              updatedCounselors.push(data);\r\n            }\r\n\r\n            // Update counselorIds if needed\r\n            let updatedIds = [...state.counselorIds];\r\n            if (!updatedIds.includes(userId)) {\r\n              updatedIds.push(userId);\r\n            }\r\n\r\n            return {\r\n              counselors: updatedCounselors,\r\n              counselorIds: updatedIds,\r\n              profileLoading: false,\r\n            };\r\n          });\r\n\r\n          return data;\r\n        } catch (error) {\r\n          console.error(\"Error fetching counselor profile:\", error);\r\n          set({\r\n            error: \"Failed to fetch counselor profile\",\r\n            profileLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      fetchTopCounselors: async () => {\r\n        try {\r\n          set({ loading: true, error: null });\r\n          const { data } = await apiClient.get<CounselorListResponse>(\r\n            \"/counselor/profiles/public-profile/list\",\r\n            { params: { page: 1, limit: 8 } }\r\n          );\r\n\r\n          // Check if we have any items before updating state\r\n          if (data.items && data.items.length > 0) {\r\n            set({\r\n              counselors: data.items,\r\n              counselorIds: data.items.map((c) => c.user_id),\r\n              loading: false,\r\n            });\r\n            return data.items;\r\n          } else {\r\n            set({\r\n              counselors: [],\r\n              counselorIds: [],\r\n              loading: false,\r\n              error: \"No counselors found\",\r\n            });\r\n            return [];\r\n          }\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch top counselors\",\r\n            loading: false,\r\n            counselors: [],\r\n            counselorIds: [],\r\n          });\r\n          console.error(\"Error fetching top counselors:\", error);\r\n          return [];\r\n        }\r\n      },\r\n\r\n      fetchAvailableDates: async (\r\n        counselorUserId,\r\n        startDate,\r\n        endDate,\r\n        timezone\r\n      ) => {\r\n        try {\r\n          set({ datesLoading: true });\r\n          const response = await apiClient.get<string[]>(\r\n            `/counselor/availability/public/${counselorUserId}/dates`,\r\n            {\r\n              params: {\r\n                start_date: format(startDate, \"yyyy-MM-dd\"),\r\n                end_date: format(endDate, \"yyyy-MM-dd\"),\r\n                timezone,\r\n              },\r\n            }\r\n          );\r\n          set({ availableDates: response.data, datesLoading: false });\r\n          return response.data;\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch dates\",\r\n            datesLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      fetchTimeSlots: async (counselorUserId, targetDate, timezone) => {\r\n        try {\r\n          set({ availabilityLoading: true });\r\n          const response = await apiClient.get<string[]>(\r\n            `/counselor/availability/public/${counselorUserId}/slots`,\r\n            {\r\n              params: {\r\n                target_date: format(targetDate, \"yyyy-MM-dd\"),\r\n                timezone,\r\n              },\r\n            }\r\n          );\r\n          set({\r\n            timeSlots: response.data,\r\n            availabilityLoading: false,\r\n          });\r\n          return response.data;\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch time slots\",\r\n            availabilityLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      setFilters: (filters: Filters) => {\r\n        set({\r\n          filters,\r\n          currentPage: 1,\r\n        });\r\n      },\r\nclearFilters: () => {\r\n        set({\r\n          filters: {},\r\n          counselors: [],\r\n          currentPage: 1,\r\n          hasMore: false,\r\n        });\r\n      },\r\n\r\n      loadMore: async () => {\r\n        const { currentPage, hasMore } = get();\r\n        if (!hasMore) return;\r\n        set({ currentPage: currentPage + 1 });\r\n        await get().fetchAllCounselors();\r\n      },\r\n    }),\r\n    {\r\n      name: \"counselors-storage\",\r\n      partialize: (state) =>\r\n        Object.fromEntries(\r\n          Object.entries(state).filter(([key]) => [\"filters\"].includes(key))\r\n        ),\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AAFA;AACA;AAMA;AATA;;;;;AA8DO,MAAM,gBAAgB,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,SAAS;QACT,gBAAgB;QAChB,qBAAqB;QACrB,cAAc;QACd,OAAO;QACP,YAAY;QACZ,cAAc,EAAE;QAChB,SAAS,CAAC;QACV,aAAa;QACb,YAAY;QACZ,SAAS;QACT,gBAAgB,EAAE;QAClB,WAAW,EAAE;QAEb,YAAY,CAAC;YACX,IAAI;gBAAE;YAAQ;QAChB;QACA,oBAAoB;YAClB,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;gBAEjC,kDAAkD;gBAClD,IAAI,gBAAgB,GAAG;oBACrB,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK;gBACnC;gBAEA,yCAAyC;gBACzC,MAAM,SAAoB;oBACxB,MAAM;oBACN,OAAO;gBACT;gBAEA,qCAAqC;gBACrC,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;oBAC3D,OAAO,aAAa,GAAG,QAAQ,YAAY,CAAC,IAAI,CAAC;gBACnD;gBAEA,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG;oBACjD,OAAO,SAAS,GAAG,QAAQ,OAAO,CAAC,IAAI,CAAC;gBAC1C;gBAEA,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;oBACvD,OAAO,YAAY,GAAG,QAAQ,UAAU,CAAC,IAAI,CAAC;gBAChD;gBAEA,8CAA8C;gBAC9C,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,OAAO,IAAI;oBAC9C,OAAO,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI;gBACjC;gBAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,2CACA;oBAAE;gBAAO;gBAGX,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,IAAI,CAAC;wBACH,qDAAqD;wBACrD,MAAM,cAAc,gBAAgB;wBAEpC,OAAO;4BACL,YAAY,cACR,KAAK,KAAK,GACV;mCAAK,MAAM,UAAU,IAAI,EAAE;mCAAM,KAAK,KAAK;6BAAC;4BAChD,cAAc,cACV,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO,IAC/B;mCACK,IAAI,IAAI;uCACN,MAAM,YAAY;uCAClB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO;iCACnC;6BACF;4BACL,YAAY,KAAK,KAAK;4BACtB,SAAS,KAAK,QAAQ;4BACtB,SAAS;4BACT,OAAO;wBACT;oBACF;gBACF,OAAO;oBACL,IAAI,CAAC,QAAU,CAAC;4BACd,GAAG,KAAK;4BACR,YAAY,gBAAgB,IAAI,EAAE,GAAG,MAAM,UAAU;4BACrD,cAAc,gBAAgB,IAAI,EAAE,GAAG,MAAM,YAAY;4BACzD,YAAY,gBAAgB,IAAI,IAAI,MAAM,UAAU;4BACpD,SAAS;4BACT,SAAS;4BACT,OAAO,gBAAgB,IAAI,wBAAwB;wBACrD,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,SAAS;gBACX;gBACA,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;QAEA,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,gBAAgB;oBAAM,OAAO;gBAAK;gBACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,uCAAuC,EAAE,QAAQ;gBAGpD,mDAAmD;gBACnD,IAAI,CAAC;oBACH,8CAA8C;oBAC9C,MAAM,oBAAoB;2BAAK,MAAM,UAAU,IAAI,EAAE;qBAAE;oBAEvD,qDAAqD;oBACrD,MAAM,gBAAgB,kBAAkB,SAAS,CAC/C,CAAC,IAAM,EAAE,OAAO,KAAK;oBAGvB,mDAAmD;oBACnD,IAAI,iBAAiB,GAAG;wBACtB,iBAAiB,CAAC,cAAc,GAAG;oBACrC,OAAO;wBACL,kBAAkB,IAAI,CAAC;oBACzB;oBAEA,gCAAgC;oBAChC,IAAI,aAAa;2BAAI,MAAM,YAAY;qBAAC;oBACxC,IAAI,CAAC,WAAW,QAAQ,CAAC,SAAS;wBAChC,WAAW,IAAI,CAAC;oBAClB;oBAEA,OAAO;wBACL,YAAY;wBACZ,cAAc;wBACd,gBAAgB;oBAClB;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,IAAI;oBACF,OAAO;oBACP,gBAAgB;gBAClB;gBACA,MAAM;YACR;QACF;QAEA,oBAAoB;YAClB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,2CACA;oBAAE,QAAQ;wBAAE,MAAM;wBAAG,OAAO;oBAAE;gBAAE;gBAGlC,mDAAmD;gBACnD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,IAAI;wBACF,YAAY,KAAK,KAAK;wBACtB,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO;wBAC7C,SAAS;oBACX;oBACA,OAAO,KAAK,KAAK;gBACnB,OAAO;oBACL,IAAI;wBACF,YAAY,EAAE;wBACd,cAAc,EAAE;wBAChB,SAAS;wBACT,OAAO;oBACT;oBACA,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,SAAS;oBACT,YAAY,EAAE;oBACd,cAAc,EAAE;gBAClB;gBACA,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO,EAAE;YACX;QACF;QAEA,qBAAqB,OACnB,iBACA,WACA,SACA;YAEA,IAAI;gBACF,IAAI;oBAAE,cAAc;gBAAK;gBACzB,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,gBAAgB,MAAM,CAAC,EACzD;oBACE,QAAQ;wBACN,YAAY,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,WAAW;wBAC9B,UAAU,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,SAAS;wBAC1B;oBACF;gBACF;gBAEF,IAAI;oBAAE,gBAAgB,SAAS,IAAI;oBAAE,cAAc;gBAAM;gBACzD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,cAAc;gBAChB;gBACA,MAAM;YACR;QACF;QAEA,gBAAgB,OAAO,iBAAiB,YAAY;YAClD,IAAI;gBACF,IAAI;oBAAE,qBAAqB;gBAAK;gBAChC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,gBAAgB,MAAM,CAAC,EACzD;oBACE,QAAQ;wBACN,aAAa,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,YAAY;wBAChC;oBACF;gBACF;gBAEF,IAAI;oBACF,WAAW,SAAS,IAAI;oBACxB,qBAAqB;gBACvB;gBACA,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,qBAAqB;gBACvB;gBACA,MAAM;YACR;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBACF;gBACA,aAAa;YACf;QACF;QACN,cAAc;YACN,IAAI;gBACF,SAAS,CAAC;gBACV,YAAY,EAAE;gBACd,aAAa;gBACb,SAAS;YACX;QACF;QAEA,UAAU;YACR,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;YACjC,IAAI,CAAC,SAAS;YACd,IAAI;gBAAE,aAAa,cAAc;YAAE;YACnC,MAAM,MAAM,kBAAkB;QAChC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QACX,OAAO,WAAW,CAChB,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK;gBAAC;aAAU,CAAC,QAAQ,CAAC;AAEnE"}}, {"offset": {"line": 2311, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2317, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/student/usePromoCode.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\n\r\ninterface PromoCodeState {\r\n    loading: boolean;\r\n    error: string | null;\r\n    promoDetails: {\r\n        type: \"percentage\" | \"amount\" | \"fixed_price\";\r\n        amount?: number;\r\n        percentage?: number;\r\n    } | null;\r\n    verifyPromoCode: (\r\n        code: string,\r\n        counselorId: number,\r\n        original_amont: number\r\n    ) => Promise<void>;\r\n    clearPromoCode: () => void;\r\n}\r\n\r\nexport const usePromoCode = create<PromoCodeState>((set) => ({\r\n    loading: false,\r\n    error: null,\r\n    promoDetails: null,\r\n\r\n    verifyPromoCode: async (\r\n        code: string,\r\n        counselorId: number,\r\n        original_amount: number\r\n    ) => {\r\n        if (!code.trim()) {\r\n            set({ promoDetails: null, error: null, loading: false });\r\n            return;\r\n        }\r\n\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.get(\r\n                `/promo-codes/verify?code=${code}&requested_counselor_id=${counselorId}&original_amount=${original_amount}`\r\n            );\r\n            set({ promoDetails: response.data, error: null });\r\n        } catch (error: any) {\r\n            set({\r\n                error: error.response?.data?.detail || \"Invalid promo code\",\r\n                promoDetails: null,\r\n            });\r\n        } finally {\r\n            set({ loading: false });\r\n        }\r\n    },\r\n\r\n    clearPromoCode: () => {\r\n        set({ promoDetails: null, error: null });\r\n    },\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AADA;AAFA;;;AAqBO,MAAM,eAAe,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,MAAQ,CAAC;QACzD,SAAS;QACT,OAAO;QACP,cAAc;QAEd,iBAAiB,OACb,MACA,aACA;YAEA,IAAI,CAAC,KAAK,IAAI,IAAI;gBACd,IAAI;oBAAE,cAAc;oBAAM,OAAO;oBAAM,SAAS;gBAAM;gBACtD;YACJ;YAEA,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,yBAAyB,EAAE,KAAK,wBAAwB,EAAE,YAAY,iBAAiB,EAAE,iBAAiB;gBAE/G,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,OAAO;gBAAK;YACnD,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,cAAc;gBAClB;YACJ,SAAU;gBACN,IAAI;oBAAE,SAAS;gBAAM;YACzB;QACJ;QAEA,gBAAgB;YACZ,IAAI;gBAAE,cAAc;gBAAM,OAAO;YAAK;QAC1C;IACJ,CAAC"}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2372, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,mKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 2382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/booking-form/date-chooser.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  startOfMonth,\r\n  endOfMonth,\r\n  eachDayOfInterval,\r\n  addMonths,\r\n  subMonths,\r\n  isSameDay,\r\n  format,\r\n  getDay,\r\n  addDays,\r\n  isWithinInterval,\r\n} from \"date-fns\";\r\nimport React from \"react\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\n\r\ninterface DateChooserProps {\r\n  counselorId: number;\r\n  selectedDate: Date | null;\r\n  onDateSelect: (date: Date) => void;\r\n  datesLoading: boolean;\r\n  timezone: string;\r\n}\r\n\r\nexport function DateChooser({\r\n  counselorId,\r\n  selectedDate,\r\n  onDateSelect,\r\n  datesLoading,\r\n  timezone,\r\n}: DateChooserProps) {\r\n  const [currentDate, setCurrentDate] = React.useState(new Date());\r\n  const { fetchAvailableDates, availableDates } = useCounselors();\r\n\r\n  React.useEffect(() => {\r\n    const loadDates = async () => {\r\n      const start = startOfMonth(currentDate);\r\n      const end = endOfMonth(currentDate);\r\n      try {\r\n        await fetchAvailableDates(counselorId, start, end, timezone);\r\n      } catch (error) {\r\n        console.error(\"Failed to fetch dates:\", error);\r\n      }\r\n    };\r\n    loadDates();\r\n  }, [currentDate, counselorId, fetchAvailableDates, timezone]);\r\n\r\n  const getCalendarDays = () => {\r\n    const monthStart = startOfMonth(currentDate);\r\n    const monthEnd = endOfMonth(currentDate);\r\n\r\n    // Get the first day of the calendar (Monday of the week that includes the first day of the month)\r\n    // Note: getDay() returns 0 for Sunday, 1 for Monday, etc.\r\n    // We adjust to make Monday=0, Sunday=6 for our grid\r\n    const startDay = getDay(monthStart);\r\n    const firstCalendarDay = addDays(\r\n      monthStart,\r\n      startDay === 0 ? -6 : -(startDay - 1)\r\n    );\r\n\r\n    // Get the last day of the calendar (Sunday of the week that includes the last day of the month)\r\n    const endDay = getDay(monthEnd);\r\n    const lastCalendarDay = addDays(monthEnd, endDay === 0 ? 0 : 7 - endDay);\r\n\r\n    // Get all days in the calendar range\r\n    return eachDayOfInterval({ start: firstCalendarDay, end: lastCalendarDay });\r\n  };\r\n\r\n  const previousMonth = () => setCurrentDate(subMonths(currentDate, 1));\r\n  const nextMonth = () => setCurrentDate(addMonths(currentDate, 1));\r\n\r\n  const isAvailable = (date: Date) => {\r\n    return availableDates.includes(format(date, \"yyyy-MM-dd\"));\r\n  };\r\n\r\n  const isToday = (date: Date) => isSameDay(date, new Date());\r\n\r\n  const isCurrentMonth = (date: Date) => {\r\n    const monthStart = startOfMonth(currentDate);\r\n    const monthEnd = endOfMonth(currentDate);\r\n    return isWithinInterval(date, { start: monthStart, end: monthEnd });\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h3 className=\"text-lg font-medium text-gray-900\">\r\n          {format(currentDate, \"MMMM yyyy\")}\r\n        </h3>\r\n        <div className=\"flex gap-2\">\r\n          <button\r\n            onClick={previousMonth}\r\n            className=\"p-2 hover:bg-gray-100 rounded-full\"\r\n            disabled={datesLoading}\r\n          >\r\n            <ChevronLeft className=\"h-5 w-5 text-gray-600\" />\r\n          </button>\r\n          <button\r\n            onClick={nextMonth}\r\n            className=\"p-2 hover:bg-gray-100 rounded-full\"\r\n            disabled={datesLoading}\r\n          >\r\n            <ChevronRight className=\"h-5 w-5 text-gray-600\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {datesLoading ? (\r\n        <div className=\"grid grid-cols-7 gap-1 animate-pulse\">\r\n          {[...Array(35)].map((_, i) => (\r\n            <div key={i} className=\"h-10 bg-gray-200 rounded-md\" />\r\n          ))}\r\n        </div>\r\n      ) : (\r\n        <div className=\"grid grid-cols-7 gap-1 text-center text-sm\">\r\n          {[\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"].map((day) => (\r\n            <div key={day} className=\"py-2 text-gray-400 font-medium\">\r\n              {day.charAt(0)}\r\n            </div>\r\n          ))}\r\n          {getCalendarDays().map((day) => {\r\n            const dayStr = format(day, \"yyyy-MM-dd\");\r\n            const available = isCurrentMonth(day) && isAvailable(day);\r\n            const inCurrentMonth = isCurrentMonth(day);\r\n\r\n            return (\r\n              <button\r\n                key={dayStr}\r\n                onClick={() => available && onDateSelect(day)}\r\n                className={cn(\r\n                  \"py-2 rounded-md text-sm font-medium transition-colors\",\r\n                  !inCurrentMonth &&\r\n                    \"opacity-30 bg-gray-50 text-gray-400 cursor-not-allowed\",\r\n                  inCurrentMonth &&\r\n                    !available &&\r\n                    \"opacity-50 cursor-not-allowed\",\r\n                  isToday(day) && inCurrentMonth && \"ring-2 ring-blue-500\",\r\n                  selectedDate && isSameDay(day, selectedDate) && inCurrentMonth\r\n                    ? \"bg-[#8B141A] text-white\"\r\n                    : inCurrentMonth && available\r\n                    ? \"bg-green-100 hover:bg-green-200 text-green-800\"\r\n                    : inCurrentMonth\r\n                    ? \"bg-gray-100 text-gray-400\"\r\n                    : \"\"\r\n                )}\r\n                disabled={!available || !inCurrentMonth}\r\n              >\r\n                {inCurrentMonth ? format(day, \"d\") : \"\"}\r\n              </button>\r\n            );\r\n          })}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAaA;AACA;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;AAAA;AAFA;;;;;;;AA2BO,SAAS,YAAY,EAC1B,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,QAAQ,EACS;IACjB,MAAM,CAAC,aAAa,eAAe,GAAG,kKAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,IAAI;IACzD,MAAM,EAAE,mBAAmB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD;IAE5D,kKAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,YAAY;YAChB,MAAM,QAAQ,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;YAC3B,MAAM,MAAM,CAAA,GAAA,kJAAA,CAAA,aAAU,AAAD,EAAE;YACvB,IAAI;gBACF,MAAM,oBAAoB,aAAa,OAAO,KAAK;YACrD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;QACF;QACA;IACF,GAAG;QAAC;QAAa;QAAa;QAAqB;KAAS;IAE5D,MAAM,kBAAkB;QACtB,MAAM,aAAa,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;QAChC,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,aAAU,AAAD,EAAE;QAE5B,kGAAkG;QAClG,0DAA0D;QAC1D,oDAAoD;QACpD,MAAM,WAAW,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;QACxB,MAAM,mBAAmB,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EAC7B,YACA,aAAa,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC;QAGtC,gGAAgG;QAChG,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;QACtB,MAAM,kBAAkB,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,WAAW,IAAI,IAAI,IAAI;QAEjE,qCAAqC;QACrC,OAAO,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;YAAE,OAAO;YAAkB,KAAK;QAAgB;IAC3E;IAEA,MAAM,gBAAgB,IAAM,eAAe,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IAClE,MAAM,YAAY,IAAM,eAAe,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IAE9D,MAAM,cAAc,CAAC;QACnB,OAAO,eAAe,QAAQ,CAAC,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IAC9C;IAEA,MAAM,UAAU,CAAC,OAAe,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,IAAI;IAEpD,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE;QAChC,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,CAAA,GAAA,wJAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;YAAE,OAAO;YAAY,KAAK;QAAS;IACnE;IAEA,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAG,WAAU;kCACX,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,aAAa;;;;;;kCAEvB,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCACC,SAAS;gCACT,WAAU;gCACV,UAAU;0CAEV,cAAA,kMAAC,4NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,kMAAC;gCACC,SAAS;gCACT,WAAU;gCACV,UAAU;0CAEV,cAAA,kMAAC,8NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK7B,6BACC,kMAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,kMAAC;wBAAY,WAAU;uBAAb;;;;;;;;;qCAId,kMAAC;gBAAI,WAAU;;oBACZ;wBAAC;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;qBAAM,CAAC,GAAG,CAAC,CAAC,oBACtD,kMAAC;4BAAc,WAAU;sCACtB,IAAI,MAAM,CAAC;2BADJ;;;;;oBAIX,kBAAkB,GAAG,CAAC,CAAC;wBACtB,MAAM,SAAS,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,KAAK;wBAC3B,MAAM,YAAY,eAAe,QAAQ,YAAY;wBACrD,MAAM,iBAAiB,eAAe;wBAEtC,qBACE,kMAAC;4BAEC,SAAS,IAAM,aAAa,aAAa;4BACzC,WAAW,CAAA,GAAA,oHAAA,CAAA,KAAE,AAAD,EACV,yDACA,CAAC,kBACC,0DACF,kBACE,CAAC,aACD,iCACF,QAAQ,QAAQ,kBAAkB,wBAClC,gBAAgB,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,iBAAiB,iBAC5C,4BACA,kBAAkB,YAClB,mDACA,iBACA,8BACA;4BAEN,UAAU,CAAC,aAAa,CAAC;sCAExB,iBAAiB,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,KAAK,OAAO;2BApBhC;;;;;oBAuBX;;;;;;;;;;;;;AAKV"}}, {"offset": {"line": 2588, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2594, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/booking-form/time-chooser.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface TimeChooserProps {\r\n  slotsLoading?: boolean;\r\n  availableSlots: string[];\r\n  selectedTime: string | null;\r\n  onTimeSelect: (time: string) => void;\r\n  timezone: string;\r\n}\r\n\r\nexport function TimeChooser({\r\n  slotsLoading = false,\r\n  availableSlots,\r\n  selectedTime,\r\n  onTimeSelect,\r\n  timezone,\r\n}: TimeChooserProps) {\r\n  const formatSlot = (isoString: string) => {\r\n    const start = new Date(isoString);\r\n    return start\r\n      .toLocaleTimeString(\"en-US\", {\r\n        hour: \"numeric\",\r\n        minute: \"2-digit\",\r\n        hour12: true,\r\n        timeZone: timezone,\r\n      })\r\n      .toLowerCase();\r\n  };\r\n\r\n  if (slotsLoading) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        <div className=\"h-6 bg-gray-200 rounded animate-pulse w-48\" />\r\n        <div className=\"grid grid-cols-4 gap-4\">\r\n          {[...Array(8)].map((_, i) => (\r\n            <div key={i} className=\"h-12 bg-gray-200 rounded animate-pulse\" />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h3 className=\"text-sm font-medium text-gray-700\">\r\n          Available time slots\r\n        </h3>\r\n        <p className=\"text-xs text-gray-500\">Times shown in {timezone}</p>\r\n      </div>\r\n\r\n      <div className=\"grid md:grid-cols-4 grid-cols-3 gap-4\">\r\n        {availableSlots.length > 0 ? (\r\n          availableSlots.map((slot) => (\r\n            <button\r\n              key={slot}\r\n              onClick={() => onTimeSelect(slot)}\r\n              className={cn(\r\n                \"py-3 px-4 rounded-lg text-sm font-medium transition-colors\",\r\n                slot === selectedTime\r\n                  ? \"bg-[#8B141A] text-white\"\r\n                  : \"bg-green-100 hover:bg-green-200 text-green-800\"\r\n              )}\r\n            >\r\n              {formatSlot(slot)}\r\n            </button>\r\n          ))\r\n        ) : (\r\n          <div className=\"col-span-4 text-center text-gray-500\">\r\n            No available time slots for this date\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYO,SAAS,YAAY,EAC1B,eAAe,KAAK,EACpB,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,QAAQ,EACS;IACjB,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,IAAI,KAAK;QACvB,OAAO,MACJ,kBAAkB,CAAC,SAAS;YAC3B,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;QACZ,GACC,WAAW;IAChB;IAEA,IAAI,cAAc;QAChB,qBACE,kMAAC;YAAI,WAAU;;8BACb,kMAAC;oBAAI,WAAU;;;;;;8BACf,kMAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,kMAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;;;;;;;IAKpB;IAEA,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAGlD,kMAAC;wBAAE,WAAU;;4BAAwB;4BAAgB;;;;;;;;;;;;;0BAGvD,kMAAC;gBAAI,WAAU;0BACZ,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,qBAClB,kMAAC;wBAEC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAA,GAAA,oHAAA,CAAA,KAAE,AAAD,EACV,8DACA,SAAS,eACL,4BACA;kCAGL,WAAW;uBATP;;;;8CAaT,kMAAC;oBAAI,WAAU;8BAAuC;;;;;;;;;;;;;;;;;AAOhE"}}, {"offset": {"line": 2707, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2713, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/packages/purchase-confirmation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { But<PERSON> } from \"@/app/components/ui/button\";\r\nimport { PackageData } from \"@/app/types/counselor/package\";\r\nimport {\r\n  CheckCircle,\r\n  Clock,\r\n  Calendar,\r\n  AlertCircle,\r\n  AlertTriangle,\r\n} from \"lucide-react\";\r\nimport { usePackagePayments } from \"@/app/hooks/student/usePackagePayments\";\r\nimport usePackages from \"@/app/hooks/student/usePackages\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"react-toastify\";\r\nimport { Input } from \"@/app/components/ui/input\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport { usePromoCode } from \"@/app/hooks/student/usePromoCode\";\r\nimport { DateChooser } from \"../booking-form/date-chooser\";\r\nimport { TimeChooser } from \"../booking-form/time-chooser\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\n\r\ninterface PurchaseConfirmationProps {\r\n  pkg: PackageData;\r\n  counselor: CounselorPublicProfile;\r\n  selectedService: string;\r\n  onBack: () => void;\r\n  onComplete: () => void;\r\n}\r\n\r\nexport function PurchaseConfirmation({\r\n  pkg,\r\n  counselor,\r\n  selectedService,\r\n  onBack,\r\n  onComplete,\r\n}: PurchaseConfirmationProps) {\r\n  const router = useRouter();\r\n  const { createCheckoutSession, loading: paymentLoading } =\r\n    usePackagePayments();\r\n  const { subscribeToPackage, loading: subscriptionLoading } = usePackages();\r\n  const { userInfo } = useProfile();\r\n  const { fetchTimeSlots, availabilityLoading, timeSlots, datesLoading } =\r\n    useCounselors();\r\n  const {\r\n    verifyPromoCode,\r\n    promoDetails,\r\n    error: promoError,\r\n    loading: promoLoading,\r\n    clearPromoCode,\r\n  } = usePromoCode();\r\n\r\n  const loading =\r\n    paymentLoading ||\r\n    subscriptionLoading ||\r\n    availabilityLoading ||\r\n    datesLoading ||\r\n    promoLoading;\r\n\r\n  // Session scheduling states\r\n  const [step, setStep] = useState<\"date\" | \"time\" | \"details\">(\"date\");\r\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null);\r\n  const [selectedTime, setSelectedTime] = useState<string | null>(null);\r\n  const duration = 60; // Default 1 hour session duration\r\n\r\n  const [promoCode, setPromoCode] = useState(\"\");\r\n  const [finalPrice, setFinalPrice] = useState(pkg.total_price);\r\n  const [purchaseStatus, setPurchaseStatus] = useState<\r\n    \"idle\" | \"processing\" | \"redirecting\"\r\n  >(\"idle\");\r\n\r\n  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n\r\n  // Handle promo code validation\r\n  useEffect(() => {\r\n    const validatePromo = async () => {\r\n      if (promoCode.trim()) {\r\n        await verifyPromoCode(\r\n          promoCode,\r\n          counselor.counselor_id,\r\n          pkg.total_price\r\n        );\r\n      } else {\r\n        // Reset states when input is empty\r\n        setFinalPrice(pkg.total_price);\r\n        clearPromoCode();\r\n      }\r\n    };\r\n\r\n    const timeoutId = setTimeout(validatePromo, 500);\r\n    return () => clearTimeout(timeoutId);\r\n  }, [\r\n    promoCode,\r\n    counselor.counselor_id,\r\n    pkg.total_price,\r\n    verifyPromoCode,\r\n    clearPromoCode,\r\n  ]);\r\n\r\n  // Update final price when promo details change\r\n  useEffect(() => {\r\n    if (promoDetails) {\r\n      let newAmount = pkg.total_price;\r\n      if (promoDetails.type === \"percentage\" && promoDetails.percentage) {\r\n        newAmount = pkg.total_price * (1 - promoDetails.percentage / 100);\r\n      } else if (promoDetails.type === \"amount\" && promoDetails.amount) {\r\n        newAmount = Math.max(0, pkg.total_price - promoDetails.amount);\r\n      } else if (promoDetails.type === \"fixed_price\" && promoDetails.amount) {\r\n        newAmount = promoDetails.amount;\r\n      }\r\n      setFinalPrice(newAmount);\r\n    }\r\n  }, [promoDetails, pkg.total_price]);\r\n\r\n  // Handle promo code input change\r\n  const handlePromoCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setPromoCode(value);\r\n\r\n    if (!value.trim()) {\r\n      // Immediately clear promo details when input is empty\r\n      setFinalPrice(pkg.total_price);\r\n      clearPromoCode();\r\n    }\r\n  };\r\n\r\n  // Get discount text for display\r\n  const getDiscountText = () => {\r\n    if (!promoDetails) return null;\r\n    if (promoDetails.type === \"percentage\") {\r\n      return `${promoDetails.percentage}% off`;\r\n    }\r\n    return `$${promoDetails.amount} off`;\r\n  };\r\n\r\n  // Fetch time slots when date is selected\r\n  useEffect(() => {\r\n    const fetchSlots = async () => {\r\n      if (selectedDate && step === \"time\") {\r\n        try {\r\n          await fetchTimeSlots(counselor.user_id, selectedDate, userTimezone);\r\n          setSelectedTime(null);\r\n        } catch (error) {\r\n          console.error(\"Error fetching time slots:\", error);\r\n          toast.error(\"Failed to load available time slots\");\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchSlots();\r\n  }, [selectedDate, counselor.user_id, fetchTimeSlots, userTimezone, step]);\r\n\r\n  // Calculate total hours\r\n  const totalHours = pkg.package_items.reduce(\r\n    (acc, item) => acc + item.hours,\r\n    0\r\n  );\r\n\r\n  const handlePurchase = async () => {\r\n    try {\r\n      setPurchaseStatus(\"processing\");\r\n\r\n      // Create subscription first with the first session data\r\n      let subscriptionData;\r\n\r\n      if (selectedDate && selectedTime) {\r\n        // If date and time are selected, create a session\r\n        const startTime = new Date(selectedTime);\r\n        const endTime = new Date(startTime.getTime() + duration * 60 * 1000);\r\n\r\n        subscriptionData = {\r\n          package_id: pkg.id!,\r\n          selected_service: selectedService,\r\n          first_session: {\r\n            event_name: selectedService,\r\n            date: selectedDate.toISOString(),\r\n            start_time: startTime.toISOString(),\r\n            end_time: endTime.toISOString(),\r\n            student_email: userInfo?.email || \"\",\r\n          },\r\n        };\r\n      } else {\r\n        // If no date/time selected, provide empty values\r\n        subscriptionData = {\r\n          package_id: pkg.id!,\r\n          selected_service: selectedService,\r\n          first_session: {\r\n            event_name: selectedService,\r\n            date: \"\", // Empty string signals to the backend that no session should be created\r\n            start_time: \"\",\r\n            end_time: \"\",\r\n            student_email: userInfo?.email || \"\",\r\n          },\r\n        };\r\n      }\r\n\r\n      // Add promo code to subscription data if it exists and is valid\r\n      if (promoCode.trim() && promoDetails) {\r\n        (subscriptionData as any).promo_code = promoCode;\r\n      }\r\n\r\n      const subscriptionResponse = await subscribeToPackage(subscriptionData);\r\n      const subscriptionId = subscriptionResponse.id;\r\n\r\n      // Then create checkout session with the subscription ID\r\n      const checkoutData = {\r\n        subscription_id: subscriptionId,\r\n        package_id: pkg.id!,\r\n        selected_service: selectedService,\r\n      };\r\n\r\n      const checkoutResponse = await createCheckoutSession(checkoutData);\r\n\r\n      if (checkoutResponse.checkout_url) {\r\n        setPurchaseStatus(\"redirecting\");\r\n        // No success toast here - we'll show success on the success page after payment\r\n        setTimeout(() => {\r\n          router.push(checkoutResponse.checkout_url);\r\n          onComplete();\r\n        }, 1000);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in package purchase process:\", error);\r\n      setPurchaseStatus(\"idle\");\r\n    }\r\n  };\r\n\r\n  // Render different content based on the current step\r\n  const renderStepContent = () => {\r\n    if (step === \"date\") {\r\n      return (\r\n        <>\r\n          <div className=\"bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-start gap-3\">\r\n            <AlertCircle className=\"w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0\" />\r\n            <div>\r\n              <h3 className=\"font-medium text-blue-700\">\r\n                Schedule Your First Session\r\n              </h3>\r\n              <p className=\"text-sm text-blue-600 mt-1\">\r\n                Please select a date for your first session with the counselor.\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"border rounded-lg p-5 space-y-2\">\r\n            <h3 className=\"font-medium text-lg\">{pkg.title}</h3>\r\n            <p className=\"text-gray-600\">{pkg.description}</p>\r\n          </div>\r\n\r\n          <div className=\"border rounded-lg p-5 space-y-4\">\r\n            <h3 className=\"font-medium text-lg\">\r\n              Select Date for First Session\r\n            </h3>\r\n            <p className=\"text-gray-600\">\r\n              Choose a date for your first session with the counselor.\r\n            </p>\r\n            <DateChooser\r\n              counselorId={counselor.user_id}\r\n              selectedDate={selectedDate}\r\n              onDateSelect={setSelectedDate}\r\n              datesLoading={datesLoading}\r\n              timezone={userTimezone}\r\n            />\r\n          </div>\r\n        </>\r\n      );\r\n    } else if (step === \"time\") {\r\n      return (\r\n        <>\r\n          <div className=\"bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-start gap-3\">\r\n            <AlertCircle className=\"w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0\" />\r\n            <div>\r\n              <h3 className=\"font-medium text-blue-700\">\r\n                Schedule Your First Session\r\n              </h3>\r\n              <p className=\"text-sm text-blue-600 mt-1\">\r\n                Please select a time slot for your first session.\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"border rounded-lg p-5 space-y-2\">\r\n            <h3 className=\"font-medium text-lg\">{pkg.title}</h3>\r\n            <p className=\"text-gray-600\">{pkg.description}</p>\r\n          </div>\r\n\r\n          <div className=\"border rounded-lg p-5 space-y-4\">\r\n            <h3 className=\"font-medium text-lg\">\r\n              Select Time for First Session\r\n            </h3>\r\n            <p className=\"text-gray-600\">\r\n              Choose a time slot for your first session.\r\n            </p>\r\n            <TimeChooser\r\n              slotsLoading={availabilityLoading}\r\n              availableSlots={timeSlots}\r\n              selectedTime={selectedTime}\r\n              onTimeSelect={setSelectedTime}\r\n              timezone={userTimezone}\r\n            />\r\n          </div>\r\n        </>\r\n      );\r\n    }\r\n\r\n    // Last step is \"details\" (purchase confirmation)\r\n    return (\r\n      <>\r\n        <div className=\"bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-start gap-3\">\r\n          <AlertCircle className=\"w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0\" />\r\n          <div>\r\n            <h3 className=\"font-medium text-blue-700\">Confirm Your Purchase</h3>\r\n            <p className=\"text-sm text-blue-600 mt-1\">\r\n              Please review the package details before proceeding with your\r\n              purchase.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"border rounded-lg p-5 space-y-4\">\r\n          <h3 className=\"font-medium text-lg\">{pkg.title}</h3>\r\n          <p className=\"text-gray-600\">{pkg.description}</p>\r\n\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4\">\r\n            <div className=\"bg-gray-50 p-3 rounded-lg\">\r\n              <h4 className=\"text-sm text-gray-500 mb-1\">Total Hours</h4>\r\n              <p className=\"font-medium flex items-center gap-2\">\r\n                <Clock className=\"w-4 h-4 text-blue-500\" />\r\n                {totalHours} hours\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-green-50 p-4 rounded-lg mt-4\">\r\n            <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n              <div>\r\n                <h4 className=\"text-sm font-medium text-green-800 mb-1\">\r\n                  First Selected Service\r\n                </h4>\r\n                <p className=\"text-sm text-green-700 flex items-center gap-2\">\r\n                  <CheckCircle className=\"w-4 h-4 text-green-600\" />\r\n                  {selectedService}\r\n                </p>\r\n              </div>\r\n\r\n              {selectedDate && selectedTime && (\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-green-800 mb-1\">\r\n                    First Session Scheduled\r\n                  </h4>\r\n                  <p className=\"text-sm text-green-700 flex items-center gap-2\">\r\n                    <Calendar className=\"w-4 h-4 text-green-600\" />\r\n                    {selectedDate.toLocaleDateString()} at{\" \"}\r\n                    {new Date(selectedTime).toLocaleTimeString([], {\r\n                      hour: \"2-digit\",\r\n                      minute: \"2-digit\",\r\n                    })}\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </>\r\n    );\r\n  };\r\n\r\n  // Determine which buttons to show based on the current step\r\n  const renderButtons = () => {\r\n    if (step === \"date\") {\r\n      return (\r\n        <div className=\"flex justify-between items-center pt-4 border-t\">\r\n          <Button variant=\"outline\" onClick={onBack} disabled={loading}>\r\n            Back\r\n          </Button>\r\n\r\n          <Button\r\n            onClick={() => setStep(\"time\")}\r\n            disabled={!selectedDate || loading}\r\n            className=\"min-w-[150px]\"\r\n          >\r\n            Continue\r\n          </Button>\r\n        </div>\r\n      );\r\n    } else if (step === \"time\") {\r\n      return (\r\n        <div className=\"flex justify-between items-center pt-4 border-t\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => setStep(\"date\")}\r\n            disabled={loading}\r\n          >\r\n            Back\r\n          </Button>\r\n\r\n          <Button\r\n            onClick={() => setStep(\"details\")}\r\n            disabled={!selectedTime || loading}\r\n            className=\"min-w-[150px]\"\r\n          >\r\n            Continue to Checkout\r\n          </Button>\r\n        </div>\r\n      );\r\n    } else if (step === \"details\") {\r\n      return (\r\n        <div className=\"flex justify-between items-center pt-4 border-t\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => setStep(\"time\")}\r\n            disabled={loading}\r\n          >\r\n            Back\r\n          </Button>\r\n\r\n          <Button\r\n            onClick={handlePurchase}\r\n            disabled={loading || purchaseStatus !== \"idle\"}\r\n            className=\"min-w-[150px]\"\r\n          >\r\n            {purchaseStatus === \"processing\"\r\n              ? \"Processing...\"\r\n              : purchaseStatus === \"redirecting\"\r\n              ? \"Redirecting...\"\r\n              : \"Confirm Purchase\"}\r\n          </Button>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    // Default buttons\r\n    return (\r\n      <div className=\"flex justify-between items-center pt-4 border-t\">\r\n        <Button variant=\"outline\" onClick={onBack} disabled={loading}>\r\n          Back\r\n        </Button>\r\n\r\n        <Button\r\n          onClick={handlePurchase}\r\n          disabled={loading || purchaseStatus !== \"idle\"}\r\n          className=\"min-w-[150px]\"\r\n        >\r\n          {purchaseStatus === \"processing\"\r\n            ? \"Processing...\"\r\n            : purchaseStatus === \"redirecting\"\r\n            ? \"Redirecting...\"\r\n            : \"Confirm Purchase\"}\r\n        </Button>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {renderStepContent()}\r\n      {step === \"details\" && (\r\n        <>\r\n          <div className=\"border rounded-lg p-5\">\r\n            <h3 className=\"font-medium mb-4\">Payment Details</h3>\r\n\r\n            {/* Price Display */}\r\n            {promoDetails ? (\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex justify-between items-center text-gray-500\">\r\n                  <span>Original price</span>\r\n                  <span className=\"line-through\">\r\n                    ${pkg.total_price.toFixed(2)}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span className=\"font-medium text-gray-900\">Final price</span>\r\n                  <span className=\"text-xl font-semibold text-green-600\">\r\n                    ${finalPrice.toFixed(2)}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"font-medium text-gray-900\">Price</span>\r\n                <span className=\"text-xl font-semibold text-gray-900\">\r\n                  ${pkg.total_price.toFixed(2)}\r\n                </span>\r\n              </div>\r\n            )}\r\n\r\n            {/* Promo Code Section */}\r\n            {pkg.total_price > 0 && (\r\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n                <details className=\"group\">\r\n                  <summary className=\"flex items-center gap-1 text-sm text-blue-600 cursor-pointer hover:text-blue-800\">\r\n                    <span>Have a promo code?</span>\r\n                    <svg\r\n                      className=\"w-4 h-4 transition-transform group-open:rotate-180\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M19 9l-7 7-7-7\"\r\n                      />\r\n                    </svg>\r\n                  </summary>\r\n\r\n                  <div className=\"mt-3 p-3 bg-gray-50 rounded-lg\">\r\n                    <div className=\"relative\">\r\n                      <Input\r\n                        type=\"text\"\r\n                        value={promoCode}\r\n                        onChange={handlePromoCodeChange}\r\n                        placeholder=\"Enter promo code\"\r\n                        className=\"w-full\"\r\n                      />\r\n                      {promoLoading && (\r\n                        <div className=\"absolute right-3 top-1/2 -translate-y-1/2\">\r\n                          <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {promoCode.trim() && !promoLoading && promoError && (\r\n                      <div className=\"flex items-center gap-2 text-red-500 text-sm mt-2\">\r\n                        <AlertTriangle className=\"w-4 h-4\" />\r\n                        {promoError}\r\n                      </div>\r\n                    )}\r\n\r\n                    {promoCode.trim() && !promoLoading && promoDetails && (\r\n                      <div className=\"flex items-center gap-2 text-green-600 text-sm mt-2\">\r\n                        <CheckCircle className=\"w-4 h-4\" />\r\n                        Promo code applied! {getDiscountText()}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </details>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      {renderButtons()}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AATA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;;;;;;;;;;AAgCO,SAAS,qBAAqB,EACnC,GAAG,EACH,SAAS,EACT,eAAe,EACf,MAAM,EACN,UAAU,EACgB;IAC1B,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,qBAAqB,EAAE,SAAS,cAAc,EAAE,GACtD,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD;IACnB,MAAM,EAAE,kBAAkB,EAAE,SAAS,mBAAmB,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,UAAW,AAAD;IACvE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,SAAS,EAAE,YAAY,EAAE,GACpE,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD;IACd,MAAM,EACJ,eAAe,EACf,YAAY,EACZ,OAAO,UAAU,EACjB,SAAS,YAAY,EACrB,cAAc,EACf,GAAG,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD;IAEf,MAAM,UACJ,kBACA,uBACA,uBACA,gBACA;IAEF,4BAA4B;IAC5B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAA+B;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,WAAW,IAAI,kCAAkC;IAEvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,WAAW;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAEjD;IAEF,MAAM,eAAe,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;IAErE,+BAA+B;IAC/B,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI,UAAU,IAAI,IAAI;gBACpB,MAAM,gBACJ,WACA,UAAU,YAAY,EACtB,IAAI,WAAW;YAEnB,OAAO;gBACL,mCAAmC;gBACnC,cAAc,IAAI,WAAW;gBAC7B;YACF;QACF;QAEA,MAAM,YAAY,WAAW,eAAe;QAC5C,OAAO,IAAM,aAAa;IAC5B,GAAG;QACD;QACA,UAAU,YAAY;QACtB,IAAI,WAAW;QACf;QACA;KACD;IAED,+CAA+C;IAC/C,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,IAAI,YAAY,IAAI,WAAW;YAC/B,IAAI,aAAa,IAAI,KAAK,gBAAgB,aAAa,UAAU,EAAE;gBACjE,YAAY,IAAI,WAAW,GAAG,CAAC,IAAI,aAAa,UAAU,GAAG,GAAG;YAClE,OAAO,IAAI,aAAa,IAAI,KAAK,YAAY,aAAa,MAAM,EAAE;gBAChE,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI,WAAW,GAAG,aAAa,MAAM;YAC/D,OAAO,IAAI,aAAa,IAAI,KAAK,iBAAiB,aAAa,MAAM,EAAE;gBACrE,YAAY,aAAa,MAAM;YACjC;YACA,cAAc;QAChB;IACF,GAAG;QAAC;QAAc,IAAI,WAAW;KAAC;IAElC,iCAAiC;IACjC,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,aAAa;QAEb,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,sDAAsD;YACtD,cAAc,IAAI,WAAW;YAC7B;QACF;IACF;IAEA,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc,OAAO;QAC1B,IAAI,aAAa,IAAI,KAAK,cAAc;YACtC,OAAO,GAAG,aAAa,UAAU,CAAC,KAAK,CAAC;QAC1C;QACA,OAAO,CAAC,CAAC,EAAE,aAAa,MAAM,CAAC,IAAI,CAAC;IACtC;IAEA,yCAAyC;IACzC,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI,gBAAgB,SAAS,QAAQ;gBACnC,IAAI;oBACF,MAAM,eAAe,UAAU,OAAO,EAAE,cAAc;oBACtD,gBAAgB;gBAClB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF;QACF;QAEA;IACF,GAAG;QAAC;QAAc,UAAU,OAAO;QAAE;QAAgB;QAAc;KAAK;IAExE,wBAAwB;IACxB,MAAM,aAAa,IAAI,aAAa,CAAC,MAAM,CACzC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAC/B;IAGF,MAAM,iBAAiB;QACrB,IAAI;YACF,kBAAkB;YAElB,wDAAwD;YACxD,IAAI;YAEJ,IAAI,gBAAgB,cAAc;gBAChC,kDAAkD;gBAClD,MAAM,YAAY,IAAI,KAAK;gBAC3B,MAAM,UAAU,IAAI,KAAK,UAAU,OAAO,KAAK,WAAW,KAAK;gBAE/D,mBAAmB;oBACjB,YAAY,IAAI,EAAE;oBAClB,kBAAkB;oBAClB,eAAe;wBACb,YAAY;wBACZ,MAAM,aAAa,WAAW;wBAC9B,YAAY,UAAU,WAAW;wBACjC,UAAU,QAAQ,WAAW;wBAC7B,eAAe,UAAU,SAAS;oBACpC;gBACF;YACF,OAAO;gBACL,iDAAiD;gBACjD,mBAAmB;oBACjB,YAAY,IAAI,EAAE;oBAClB,kBAAkB;oBAClB,eAAe;wBACb,YAAY;wBACZ,MAAM;wBACN,YAAY;wBACZ,UAAU;wBACV,eAAe,UAAU,SAAS;oBACpC;gBACF;YACF;YAEA,gEAAgE;YAChE,IAAI,UAAU,IAAI,MAAM,cAAc;gBACnC,iBAAyB,UAAU,GAAG;YACzC;YAEA,MAAM,uBAAuB,MAAM,mBAAmB;YACtD,MAAM,iBAAiB,qBAAqB,EAAE;YAE9C,wDAAwD;YACxD,MAAM,eAAe;gBACnB,iBAAiB;gBACjB,YAAY,IAAI,EAAE;gBAClB,kBAAkB;YACpB;YAEA,MAAM,mBAAmB,MAAM,sBAAsB;YAErD,IAAI,iBAAiB,YAAY,EAAE;gBACjC,kBAAkB;gBAClB,+EAA+E;gBAC/E,WAAW;oBACT,OAAO,IAAI,CAAC,iBAAiB,YAAY;oBACzC;gBACF,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,kBAAkB;QACpB;IACF;IAEA,qDAAqD;IACrD,MAAM,oBAAoB;QACxB,IAAI,SAAS,QAAQ;YACnB,qBACE;;kCACE,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,4NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,kMAAC;;kDACC,kMAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAG1C,kMAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAM9C,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAG,WAAU;0CAAuB,IAAI,KAAK;;;;;;0CAC9C,kMAAC;gCAAE,WAAU;0CAAiB,IAAI,WAAW;;;;;;;;;;;;kCAG/C,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAG,WAAU;0CAAsB;;;;;;0CAGpC,kMAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAG7B,kMAAC,qLAAA,CAAA,cAAW;gCACV,aAAa,UAAU,OAAO;gCAC9B,cAAc;gCACd,cAAc;gCACd,cAAc;gCACd,UAAU;;;;;;;;;;;;;;QAKpB,OAAO,IAAI,SAAS,QAAQ;YAC1B,qBACE;;kCACE,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,4NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,kMAAC;;kDACC,kMAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAG1C,kMAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAM9C,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAG,WAAU;0CAAuB,IAAI,KAAK;;;;;;0CAC9C,kMAAC;gCAAE,WAAU;0CAAiB,IAAI,WAAW;;;;;;;;;;;;kCAG/C,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAG,WAAU;0CAAsB;;;;;;0CAGpC,kMAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAG7B,kMAAC,qLAAA,CAAA,cAAW;gCACV,cAAc;gCACd,gBAAgB;gCAChB,cAAc;gCACd,cAAc;gCACd,UAAU;;;;;;;;;;;;;;QAKpB;QAEA,iDAAiD;QACjD,qBACE;;8BACE,kMAAC;oBAAI,WAAU;;sCACb,kMAAC,4NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,kMAAC;;8CACC,kMAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,kMAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;8BAO9C,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAG,WAAU;sCAAuB,IAAI,KAAK;;;;;;sCAC9C,kMAAC;4BAAE,WAAU;sCAAiB,IAAI,WAAW;;;;;;sCAE7C,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,kMAAC;wCAAE,WAAU;;0DACX,kMAAC,4MAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB;4CAAW;;;;;;;;;;;;;;;;;;sCAKlB,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;;0DACC,kMAAC;gDAAG,WAAU;0DAA0C;;;;;;0DAGxD,kMAAC;gDAAE,WAAU;;kEACX,kMAAC,mOAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB;;;;;;;;;;;;;oCAIJ,gBAAgB,8BACf,kMAAC;;0DACC,kMAAC;gDAAG,WAAU;0DAA0C;;;;;;0DAGxD,kMAAC;gDAAE,WAAU;;kEACX,kMAAC,kNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,aAAa,kBAAkB;oDAAG;oDAAI;oDACtC,IAAI,KAAK,cAAc,kBAAkB,CAAC,EAAE,EAAE;wDAC7C,MAAM;wDACN,QAAQ;oDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASlB;IAEA,4DAA4D;IAC5D,MAAM,gBAAgB;QACpB,IAAI,SAAS,QAAQ;YACnB,qBACE,kMAAC;gBAAI,WAAU;;kCACb,kMAAC,0IAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAQ,UAAU;kCAAS;;;;;;kCAI9D,kMAAC,0IAAA,CAAA,SAAM;wBACL,SAAS,IAAM,QAAQ;wBACvB,UAAU,CAAC,gBAAgB;wBAC3B,WAAU;kCACX;;;;;;;;;;;;QAKP,OAAO,IAAI,SAAS,QAAQ;YAC1B,qBACE,kMAAC;gBAAI,WAAU;;kCACb,kMAAC,0IAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,QAAQ;wBACvB,UAAU;kCACX;;;;;;kCAID,kMAAC,0IAAA,CAAA,SAAM;wBACL,SAAS,IAAM,QAAQ;wBACvB,UAAU,CAAC,gBAAgB;wBAC3B,WAAU;kCACX;;;;;;;;;;;;QAKP,OAAO,IAAI,SAAS,WAAW;YAC7B,qBACE,kMAAC;gBAAI,WAAU;;kCACb,kMAAC,0IAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,QAAQ;wBACvB,UAAU;kCACX;;;;;;kCAID,kMAAC,0IAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,WAAW,mBAAmB;wBACxC,WAAU;kCAET,mBAAmB,eAChB,kBACA,mBAAmB,gBACnB,mBACA;;;;;;;;;;;;QAIZ;QAEA,kBAAkB;QAClB,qBACE,kMAAC;YAAI,WAAU;;8BACb,kMAAC,0IAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,SAAS;oBAAQ,UAAU;8BAAS;;;;;;8BAI9D,kMAAC,0IAAA,CAAA,SAAM;oBACL,SAAS;oBACT,UAAU,WAAW,mBAAmB;oBACxC,WAAU;8BAET,mBAAmB,eAChB,kBACA,mBAAmB,gBACnB,mBACA;;;;;;;;;;;;IAIZ;IAEA,qBACE,kMAAC;QAAI,WAAU;;YACZ;YACA,SAAS,2BACR;0BACE,cAAA,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAG,WAAU;sCAAmB;;;;;;wBAGhC,6BACC,kMAAC;4BAAI,WAAU;;8CACb,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;sDAAK;;;;;;sDACN,kMAAC;4CAAK,WAAU;;gDAAe;gDAC3B,IAAI,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;8CAG9B,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,kMAAC;4CAAK,WAAU;;gDAAuC;gDACnD,WAAW,OAAO,CAAC;;;;;;;;;;;;;;;;;;iDAK3B,kMAAC;4BAAI,WAAU;;8CACb,kMAAC;oCAAK,WAAU;8CAA4B;;;;;;8CAC5C,kMAAC;oCAAK,WAAU;;wCAAsC;wCAClD,IAAI,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;wBAM/B,IAAI,WAAW,GAAG,mBACjB,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC;gCAAQ,WAAU;;kDACjB,kMAAC;wCAAQ,WAAU;;0DACjB,kMAAC;0DAAK;;;;;;0DACN,kMAAC;gDACC,WAAU;gDACV,MAAK;gDACL,SAAQ;gDACR,QAAO;0DAEP,cAAA,kMAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;kDAKR,kMAAC;wCAAI,WAAU;;0DACb,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,yIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO;wDACP,UAAU;wDACV,aAAY;wDACZ,WAAU;;;;;;oDAEX,8BACC,kMAAC;wDAAI,WAAU;kEACb,cAAA,kMAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;4CAKpB,UAAU,IAAI,MAAM,CAAC,gBAAgB,4BACpC,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,gOAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDACxB;;;;;;;4CAIJ,UAAU,IAAI,MAAM,CAAC,gBAAgB,8BACpC,kMAAC;gDAAI,WAAU;;kEACb,kMAAC,mOAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAY;oDACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWtC;;;;;;;AAGP"}}, {"offset": {"line": 3697, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3703, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/packages/package-details-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle,\r\n  DialogDescription,\r\n  DialogFooter,\r\n} from \"@/app/components/ui/dialog\";\r\nimport { <PERSON><PERSON> } from \"@/app/components/ui/button\";\r\nimport { PackageData } from \"@/app/types/counselor/package\";\r\nimport { Clock } from \"lucide-react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>onte<PERSON>,\r\n  <PERSON><PERSON><PERSON>ist,\r\n  TabsTrigger,\r\n} from \"@/app/components/ui/tabs\";\r\nimport { PurchaseConfirmation } from \"./purchase-confirmation\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\n\r\ninterface PackageDetailsDialogProps {\r\n  pkg: PackageData;\r\n  counselor: CounselorPublicProfile;\r\n  onClose: () => void;\r\n}\r\n\r\nexport function PackageDetailsDialog({\r\n  pkg,\r\n  counselor,\r\n  onClose,\r\n}: PackageDetailsDialogProps) {\r\n  const [currentStep, setCurrentStep] = useState<\r\n    \"details\" | \"service-selection\" | \"confirmation\" | \"booking\"\r\n  >(\"details\");\r\n  const [selectedService, setSelectedService] = useState<string>(\"\");\r\n  const { userInfo } = useProfile();\r\n\r\n  const isCounselor = userInfo?.userType === \"counselor\";\r\n\r\n  const totalHours = pkg.package_items.reduce(\r\n    (acc, item) => acc + item.hours,\r\n    0\r\n  );\r\n\r\n  const handlePurchaseClick = () => {\r\n    if (isCounselor) {\r\n      return;\r\n    }\r\n    setCurrentStep(\"service-selection\");\r\n  };\r\n\r\n  return (\r\n    <Dialog open={!!pkg} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto overflow-x-hidden w-[95vw] p-4 md:p-6\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-xl\">Purchase Package</DialogTitle>\r\n        </DialogHeader>\r\n        {currentStep === \"details\" ? (\r\n          <>\r\n            <Tabs defaultValue=\"overview\" className=\"w-full\">\r\n              <div className=\"flex justify-center mb-6\">\r\n                <TabsList className=\"w-full max-w-md\">\r\n                  <TabsTrigger value=\"overview\" className=\"flex-1\">\r\n                    Overview\r\n                  </TabsTrigger>\r\n                  <TabsTrigger value=\"services\" className=\"flex-1\">\r\n                    Services & Hours\r\n                  </TabsTrigger>\r\n                </TabsList>\r\n              </div>\r\n\r\n              <TabsContent value=\"overview\" className=\"space-y-4\">\r\n                <div className=\"space-y-4\">\r\n                  <h3 className=\"font-medium\">{pkg.title}</h3>\r\n                  <p className=\"text-gray-600\">{pkg.description}</p>\r\n                </div>\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <h4 className=\"text-sm text-gray-500 mb-1\">Total Hours</h4>\r\n                    <p className=\"font-medium flex items-center gap-2\">\r\n                      <Clock className=\"w-4 h-4 text-blue-500\" />\r\n                      {totalHours} hours\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <h4 className=\"text-sm text-gray-500 mb-1\">Price</h4>\r\n                    <p className=\"font-medium text-xl\">${pkg.total_price}</p>\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"services\" className=\"space-y-4\">\r\n                <div className=\"space-y-4\">\r\n                  <h3 className=\"font-medium\">Included Services</h3>\r\n                  <div className=\"grid gap-3\">\r\n                    {pkg.package_items.map((service, idx) => (\r\n                      <div key={idx} className=\"border rounded-lg p-4\">\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <h4 className=\"font-medium\">\r\n                            {service.service_name}\r\n                          </h4>\r\n                          <span className=\"text-sm font-medium bg-blue-100 text-blue-800 px-3 py-1 rounded-full\">\r\n                            {service.hours} hours\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </TabsContent>\r\n            </Tabs>\r\n\r\n            <DialogFooter className=\"mt-8 pt-6 border-t\">\r\n              <div className=\"w-full flex flex-col-reverse sm:flex-row items-center justify-between gap-6\">\r\n                <Button variant=\"outline\" onClick={onClose}>\r\n                  Close\r\n                </Button>\r\n\r\n                <div className=\"w-full sm:w-auto flex flex-col sm:flex-row items-center gap-6\">\r\n                  <div className=\"text-center sm:text-right\">\r\n                    <p className=\"text-sm text-gray-500\">Total Price</p>\r\n                    <p className=\"text-2xl font-bold\">${pkg.total_price}</p>\r\n                  </div>\r\n\r\n                  <Button\r\n                    disabled={isCounselor}\r\n                    onClick={handlePurchaseClick}\r\n                    className=\"w-full sm:w-auto px-8\"\r\n                    size=\"lg\"\r\n                  >\r\n                    Continue to Purchase\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </DialogFooter>\r\n          </>\r\n        ) : currentStep === \"service-selection\" ? (\r\n          <>\r\n            <DialogHeader>\r\n              <DialogTitle className=\"text-xl\">\r\n                Select Your First Service\r\n              </DialogTitle>\r\n              <DialogDescription className=\"text-base mt-2\">\r\n                Choose which service you'd like to start with for your package\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n\r\n            <div className=\"py-6 space-y-6\">\r\n              <div className=\"grid gap-4\">\r\n                {pkg.package_items.map((service) => (\r\n                  <div\r\n                    key={service.service_name}\r\n                    className={`p-4 border rounded-lg cursor-pointer transition-all ${\r\n                      selectedService === service.service_name\r\n                        ? \"border-blue-500 bg-blue-50\"\r\n                        : \"hover:border-gray-400\"\r\n                    }`}\r\n                    onClick={() => setSelectedService(service.service_name)}\r\n                  >\r\n                    <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <div\r\n                          className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${\r\n                            selectedService === service.service_name\r\n                              ? \"border-blue-500\"\r\n                              : \"border-gray-300\"\r\n                          }`}\r\n                        >\r\n                          {selectedService === service.service_name && (\r\n                            <div className=\"w-3 h-3 rounded-full bg-blue-500\"></div>\r\n                          )}\r\n                        </div>\r\n                        <span className=\"font-medium\">\r\n                          {service.service_name}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <DialogFooter className=\"mt-6 pt-6 border-t\">\r\n              <div className=\"w-full flex flex-col-reverse sm:flex-row items-center justify-between gap-6\">\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => setCurrentStep(\"details\")}\r\n                >\r\n                  Back\r\n                </Button>\r\n\r\n                <Button\r\n                  onClick={() => setCurrentStep(\"confirmation\")}\r\n                  className=\"w-full sm:w-auto px-8\"\r\n                  size=\"lg\"\r\n                  disabled={!selectedService}\r\n                >\r\n                  Continue\r\n                </Button>\r\n              </div>\r\n            </DialogFooter>\r\n          </>\r\n        ) : currentStep === \"confirmation\" ? (\r\n          <PurchaseConfirmation\r\n            pkg={pkg}\r\n            counselor={counselor}\r\n            selectedService={selectedService}\r\n            onBack={() => setCurrentStep(\"service-selection\")}\r\n            onComplete={() => {\r\n              setCurrentStep(\"booking\");\r\n              setTimeout(() => onClose(), 2000);\r\n            }}\r\n          />\r\n        ) : currentStep === \"booking\" ? (\r\n          <div className=\"py-8 text-center space-y-4\">\r\n            <div className=\"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto\"></div>\r\n            <h3 className=\"text-xl font-medium\">Purchasing Package</h3>\r\n            <p className=\"text-gray-500\">\r\n              Please wait while we process your purchase...\r\n            </p>\r\n          </div>\r\n        ) : null}\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AAGA;AAMA;AAEA;AATA;AAbA;;;;;;;;;AA8BO,SAAS,qBAAqB,EACnC,GAAG,EACH,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAE3C;IACF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;IAE9B,MAAM,cAAc,UAAU,aAAa;IAE3C,MAAM,aAAa,IAAI,aAAa,CAAC,MAAM,CACzC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAC/B;IAGF,MAAM,sBAAsB;QAC1B,IAAI,aAAa;YACf;QACF;QACA,eAAe;IACjB;IAEA,qBACE,kMAAC,0IAAA,CAAA,SAAM;QAAC,MAAM,CAAC,CAAC;QAAK,cAAc;kBACjC,cAAA,kMAAC,0IAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,kMAAC,0IAAA,CAAA,eAAY;8BACX,cAAA,kMAAC,0IAAA,CAAA,cAAW;wBAAC,WAAU;kCAAU;;;;;;;;;;;gBAElC,gBAAgB,0BACf;;sCACE,kMAAC,wIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAW,WAAU;;8CACtC,kMAAC;oCAAI,WAAU;8CACb,cAAA,kMAAC,wIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,kMAAC,wIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAW,WAAU;0DAAS;;;;;;0DAGjD,kMAAC,wIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAW,WAAU;0DAAS;;;;;;;;;;;;;;;;;8CAMrD,kMAAC,wIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;oDAAG,WAAU;8DAAe,IAAI,KAAK;;;;;;8DACtC,kMAAC;oDAAE,WAAU;8DAAiB,IAAI,WAAW;;;;;;;;;;;;sDAE/C,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,kMAAC;4DAAE,WAAU;;8EACX,kMAAC,4MAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB;gEAAW;;;;;;;;;;;;;8DAIhB,kMAAC;oDAAI,WAAU;;sEACb,kMAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,kMAAC;4DAAE,WAAU;;gEAAsB;gEAAE,IAAI,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;8CAK1D,kMAAC,wIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,kMAAC;wCAAI,WAAU;;0DACb,kMAAC;gDAAG,WAAU;0DAAc;;;;;;0DAC5B,kMAAC;gDAAI,WAAU;0DACZ,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC/B,kMAAC;wDAAc,WAAU;kEACvB,cAAA,kMAAC;4DAAI,WAAU;;8EACb,kMAAC;oEAAG,WAAU;8EACX,QAAQ,YAAY;;;;;;8EAEvB,kMAAC;oEAAK,WAAU;;wEACb,QAAQ,KAAK;wEAAC;;;;;;;;;;;;;uDANX;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAgBpB,kMAAC,0IAAA,CAAA,eAAY;4BAAC,WAAU;sCACtB,cAAA,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,0IAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;kDAAS;;;;;;kDAI5C,kMAAC;wCAAI,WAAU;;0DACb,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,kMAAC;wDAAE,WAAU;;4DAAqB;4DAAE,IAAI,WAAW;;;;;;;;;;;;;0DAGrD,kMAAC,0IAAA,CAAA,SAAM;gDACL,UAAU;gDACV,SAAS;gDACT,WAAU;gDACV,MAAK;0DACN;;;;;;;;;;;;;;;;;;;;;;;;mCAOP,gBAAgB,oCAClB;;sCACE,kMAAC,0IAAA,CAAA,eAAY;;8CACX,kMAAC,0IAAA,CAAA,cAAW;oCAAC,WAAU;8CAAU;;;;;;8CAGjC,kMAAC,0IAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAAiB;;;;;;;;;;;;sCAKhD,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC;gCAAI,WAAU;0CACZ,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC,wBACtB,kMAAC;wCAEC,WAAW,CAAC,oDAAoD,EAC9D,oBAAoB,QAAQ,YAAY,GACpC,+BACA,yBACJ;wCACF,SAAS,IAAM,mBAAmB,QAAQ,YAAY;kDAEtD,cAAA,kMAAC;4CAAI,WAAU;sDACb,cAAA,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;wDACC,WAAW,CAAC,+DAA+D,EACzE,oBAAoB,QAAQ,YAAY,GACpC,oBACA,mBACJ;kEAED,oBAAoB,QAAQ,YAAY,kBACvC,kMAAC;4DAAI,WAAU;;;;;;;;;;;kEAGnB,kMAAC;wDAAK,WAAU;kEACb,QAAQ,YAAY;;;;;;;;;;;;;;;;;uCAtBtB,QAAQ,YAAY;;;;;;;;;;;;;;;sCA+BjC,kMAAC,0IAAA,CAAA,eAAY;4BAAC,WAAU;sCACtB,cAAA,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,0IAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe;kDAC/B;;;;;;kDAID,kMAAC,0IAAA,CAAA,SAAM;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAU;wCACV,MAAK;wCACL,UAAU,CAAC;kDACZ;;;;;;;;;;;;;;;;;;mCAML,gBAAgB,+BAClB,kMAAC,uLAAA,CAAA,uBAAoB;oBACnB,KAAK;oBACL,WAAW;oBACX,iBAAiB;oBACjB,QAAQ,IAAM,eAAe;oBAC7B,YAAY;wBACV,eAAe;wBACf,WAAW,IAAM,WAAW;oBAC9B;;;;;2BAEA,gBAAgB,0BAClB,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAI,WAAU;;;;;;sCACf,kMAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,kMAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;2BAI7B;;;;;;;;;;;;AAIZ"}}, {"offset": {"line": 4229, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4235, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/packages/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { usePublicPackages } from \"@/app/hooks/public/usePublicPackages\";\r\nimport { PackageData } from \"@/app/types/counselor/package\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { PackageDetailsDialog } from \"./package-details-dialog\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ninterface PublicPackagesProps {\r\n  counselor: CounselorPublicProfile;\r\n}\r\n\r\nexport default function PublicPackages({ counselor }: PublicPackagesProps) {\r\n  const { packages, loading, fetchCounselorPackages } = usePublicPackages();\r\n  const [selectedPackage, setSelectedPackage] = useState<PackageData | null>(\r\n    null\r\n  );\r\n  const { userInfo } = useProfile();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    fetchCounselorPackages(counselor.user_id);\r\n  }, [counselor, fetchCounselorPackages]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div\r\n        id=\"packages\"\r\n        className=\"bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n      >\r\n        <h2 className=\"text-xl font-semibold text-blue-950 mb-6\">Packages</h2>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {[1, 2, 3].map((i) => (\r\n            <div\r\n              key={i}\r\n              className=\"flex flex-col border rounded-xl p-6 hover:shadow-md transition-shadow duration-200 h-[500px] relative animate-pulse\"\r\n            >\r\n              <div className=\"h-7 bg-gray-200 rounded w-3/4 mb-2\"></div>\r\n              <div className=\"h-4 bg-gray-200 rounded w-full mb-4\"></div>\r\n              <div className=\"space-y-2 mb-6\">\r\n                {[1, 2].map((j) => (\r\n                  <div\r\n                    key={j}\r\n                    className=\"h-10 bg-blue-50 rounded-lg w-full\"\r\n                  ></div>\r\n                ))}\r\n              </div>\r\n              <div className=\"mb-6\">\r\n                <div className=\"h-5 bg-gray-200 rounded w-1/3 mb-2\"></div>\r\n                <div className=\"space-y-2\">\r\n                  {[1, 2, 3].map((k) => (\r\n                    <div key={k} className=\"flex items-start gap-2\">\r\n                      <div className=\"w-4 h-4 bg-green-200 rounded-full mt-1\"></div>\r\n                      <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n              <div className=\"absolute bottom-6 left-6 right-6\">\r\n                <div className=\"flex items-center mb-4\">\r\n                  <div className=\"w-4 h-4 bg-gray-200 rounded-full mr-1\"></div>\r\n                  <div className=\"h-4 bg-gray-200 rounded w-1/3\"></div>\r\n                </div>\r\n                <div className=\"flex justify-between items-end pt-4 border-t\">\r\n                  <div>\r\n                    <div className=\"h-4 bg-gray-200 rounded w-16 mb-1\"></div>\r\n                    <div className=\"h-8 bg-gray-200 rounded w-24\"></div>\r\n                  </div>\r\n                  <div className=\"h-10 bg-gray-200 rounded w-32\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      id=\"packages\"\r\n      className=\"bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n    >\r\n      <h2 className=\"text-xl font-semibold text-blue-950 mb-6\">Packages</h2>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        {packages.items.length === 0 ? (\r\n          <p className=\"text-[13px] text-gray-500\">\r\n            No packages available, This counselor hasn't created any service\r\n            packages yet\r\n          </p>\r\n        ) : (\r\n          packages.items.map((pkg) => (\r\n            <div\r\n              key={pkg.id}\r\n              className=\"flex flex-col border rounded-xl p-6 hover:shadow-md transition-shadow duration-200 h-[330px] relative\"\r\n            >\r\n              <h3 className=\"text-xl font-semibold mb-2\">{pkg.title}</h3>\r\n              <p className=\"text-gray-600 mb-4 line-clamp-2\">\r\n                {pkg.description}\r\n              </p>\r\n\r\n              <div className=\"mb-6 space-y-2\">\r\n                {pkg.package_items.slice(0, 2).map((service, idx) => (\r\n                  <div\r\n                    key={idx}\r\n                    className=\"bg-blue-50 px-3 py-2 rounded-lg text-blue-800 font-medium flex justify-between\"\r\n                  >\r\n                    <span>{service.service_name}</span>\r\n                    <span>{service.hours} hours</span>\r\n                  </div>\r\n                ))}\r\n                {pkg.package_items.length > 2 && (\r\n                  <div className=\"text-sm text-blue-600 font-medium px-3 py-2\">\r\n                    + {pkg.package_items.length - 2} more service\r\n                    {pkg.package_items.length - 2 > 1 ? \"s\" : \"\"}\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"absolute bottom-6 left-6 right-6\">\r\n                <div className=\"flex justify-between items-end pt-4 border-t\">\r\n                  <div>\r\n                    <p className=\"text-sm text-gray-500 mb-1\">Price</p>\r\n                    <p className=\"text-2xl font-medium\">${pkg.total_price}</p>\r\n                  </div>\r\n                  <Button\r\n                    onClick={() => {\r\n                      if (!userInfo) {\r\n                        router.push(\r\n                          `/auth/login?redirect=/profile/${counselor.user_id}`\r\n                        );\r\n                        return;\r\n                      }\r\n                      setSelectedPackage(pkg);\r\n                    }}\r\n                  >\r\n                    View Details\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))\r\n        )}\r\n      </div>\r\n\r\n      {selectedPackage && (\r\n        <PackageDetailsDialog\r\n          pkg={selectedPackage}\r\n          counselor={counselor}\r\n          onClose={() => setSelectedPackage(null)}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AATA;;;;;;;;AAee,SAAS,eAAe,EAAE,SAAS,EAAuB;IACvE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EACnD;IAEF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,uBAAuB,UAAU,OAAO;IAC1C,GAAG;QAAC;QAAW;KAAuB;IAEtC,IAAI,SAAS;QACX,qBACE,kMAAC;YACC,IAAG;YACH,WAAU;;8BAEV,kMAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,kMAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,kMAAC;4BAEC,WAAU;;8CAEV,kMAAC;oCAAI,WAAU;;;;;;8CACf,kMAAC;oCAAI,WAAU;;;;;;8CACf,kMAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,kBACX,kMAAC;4CAEC,WAAU;2CADL;;;;;;;;;;8CAKX,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;4CAAI,WAAU;;;;;;sDACf,kMAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,kBACd,kMAAC;oDAAY,WAAU;;sEACrB,kMAAC;4DAAI,WAAU;;;;;;sEACf,kMAAC;4DAAI,WAAU;;;;;;;mDAFP;;;;;;;;;;;;;;;;8CAOhB,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;oDAAI,WAAU;;;;;;8DACf,kMAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;;sEACC,kMAAC;4DAAI,WAAU;;;;;;sEACf,kMAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,kMAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;2BAlCd;;;;;;;;;;;;;;;;IA0CjB;IAEA,qBACE,kMAAC;QACC,IAAG;QACH,WAAU;;0BAEV,kMAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,kMAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,CAAC,MAAM,KAAK,kBACzB,kMAAC;oBAAE,WAAU;8BAA4B;;;;;2BAKzC,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,oBAClB,kMAAC;wBAEC,WAAU;;0CAEV,kMAAC;gCAAG,WAAU;0CAA8B,IAAI,KAAK;;;;;;0CACrD,kMAAC;gCAAE,WAAU;0CACV,IAAI,WAAW;;;;;;0CAGlB,kMAAC;gCAAI,WAAU;;oCACZ,IAAI,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,oBAC3C,kMAAC;4CAEC,WAAU;;8DAEV,kMAAC;8DAAM,QAAQ,YAAY;;;;;;8DAC3B,kMAAC;;wDAAM,QAAQ,KAAK;wDAAC;;;;;;;;2CAJhB;;;;;oCAOR,IAAI,aAAa,CAAC,MAAM,GAAG,mBAC1B,kMAAC;wCAAI,WAAU;;4CAA8C;4CACxD,IAAI,aAAa,CAAC,MAAM,GAAG;4CAAE;4CAC/B,IAAI,aAAa,CAAC,MAAM,GAAG,IAAI,IAAI,MAAM;;;;;;;;;;;;;0CAKhD,kMAAC;gCAAI,WAAU;0CACb,cAAA,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;;8DACC,kMAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,kMAAC;oDAAE,WAAU;;wDAAuB;wDAAE,IAAI,WAAW;;;;;;;;;;;;;sDAEvD,kMAAC,0IAAA,CAAA,SAAM;4CACL,SAAS;gDACP,IAAI,CAAC,UAAU;oDACb,OAAO,IAAI,CACT,CAAC,8BAA8B,EAAE,UAAU,OAAO,EAAE;oDAEtD;gDACF;gDACA,mBAAmB;4CACrB;sDACD;;;;;;;;;;;;;;;;;;uBA1CA,IAAI,EAAE;;;;;;;;;;YAoDlB,iCACC,kMAAC,2LAAA,CAAA,uBAAoB;gBACnB,KAAK;gBACL,WAAW;gBACX,SAAS,IAAM,mBAAmB;;;;;;;;;;;;AAK5C"}}, {"offset": {"line": 4628, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4634, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/utils/uni-logo.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst CLIENT_ID = \"1idDNCRTv8QuIQpUJ7P\";\r\n\r\nexport async function getUniversityLogoUrl(\r\n  universityName: string\r\n): Promise<string> {\r\n  try {\r\n    const encodedName = encodeURIComponent(universityName);\r\n\r\n    const response = await axios.get(\r\n      `http://universities.hipolabs.com/search?name=${encodedName}`\r\n    );\r\n\r\n    if (response.data.length === 0) {\r\n      throw new Error(\"University not found!\");\r\n    }\r\n\r\n    const domain = response.data[0].domains[0];\r\n\r\n    const brandfetchUrl = `https://cdn.brandfetch.io/${domain}/w/400/h/400?c=${CLIENT_ID}`;\r\n\r\n    return brandfetchUrl;\r\n  } catch (error) {\r\n    throw new Error(`<PERSON>rror fetching university logo: ${error}`);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY;AAEX,eAAe,qBACpB,cAAsB;IAEtB,IAAI;QACF,MAAM,cAAc,mBAAmB;QAEvC,MAAM,WAAW,MAAM,6IAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,CAAC,6CAA6C,EAAE,aAAa;QAG/D,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,GAAG;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,SAAS,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;QAE1C,MAAM,gBAAgB,CAAC,0BAA0B,EAAE,OAAO,eAAe,EAAE,WAAW;QAEtF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,OAAO;IAC5D;AACF"}}, {"offset": {"line": 4654, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4660, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/Education.tsx"], "sourcesContent": ["\"use client\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { getUniversityLogoUrl } from \"@/app/utils/uni-logo\";\r\nimport { GraduationCap } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nconst capitalizeFirstLetter = (str: string) => {\r\n  if (!str) return str;\r\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\r\n};\r\n\r\ninterface EducationProps {\r\n  profileData: CounselorPublicProfile;\r\n}\r\n\r\nexport default function Education({ profileData }: EducationProps) {\r\n  // State to store logo URLs for each university\r\n  const [universityLogos, setUniversityLogos] = useState<\r\n    Record<string, string>\r\n  >({});\r\n\r\n  useEffect(() => {\r\n    const fetchUniversityLogos = async () => {\r\n      if (profileData.education && profileData.education.length > 0) {\r\n        // Create an object to store all logo URLs\r\n        const logoUrls: Record<string, string> = {};\r\n\r\n        // Fetch logo for each university\r\n        for (const edu of profileData.education) {\r\n          try {\r\n            const logoUrl = await getUniversityLogoUrl(edu.university_name);\r\n            logoUrls[edu.university_name] = logoUrl;\r\n          } catch (error) {\r\n            console.error(\r\n              `Failed to fetch logo for ${edu.university_name}:`,\r\n              error\r\n            );\r\n            // Don't set anything in logoUrls for this university, will use fallback\r\n          }\r\n        }\r\n\r\n        setUniversityLogos(logoUrls);\r\n      }\r\n    };\r\n\r\n    fetchUniversityLogos();\r\n  }, [profileData.education]);\r\n\r\n  if (!profileData.education || profileData.education.length === 0) {\r\n    return (\r\n      <div\r\n        id=\"education\"\r\n        className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n      >\r\n        <h2 className=\"text-[15px] font-medium text-blue-950 mb-3\">\r\n          Education\r\n        </h2>\r\n        <p className=\"text-[13px] text-gray-500\">\r\n          No education history available.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      id=\"education\"\r\n      className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n    >\r\n      <h2 className=\"text-[15px] font-medium text-blue-950 mb-6\">Education</h2>\r\n      <div className=\"space-y-6\">\r\n        {profileData.education.map((edu, index) => (\r\n          <div key={index} className=\"flex gap-4\">\r\n            <div className=\"flex-shrink-0 w-16 h-16 flex items-center justify-center bg-gray-100 rounded-md\">\r\n              {universityLogos[edu.university_name] ? (\r\n                <img\r\n                  src={universityLogos[edu.university_name]}\r\n                  alt={edu.university_name}\r\n                  className=\"w-16 h-16 object-contain border border-gray-200 rounded-md overflow-hidden shadow-sm transition-transform group-hover:scale-105\"\r\n                  onError={(e) => {\r\n                    // If image fails to load, replace with icon\r\n                    e.currentTarget.style.display = \"none\";\r\n                    const parent = e.currentTarget.parentElement;\r\n                    if (parent) {\r\n                      const icon = document.createElement(\"div\");\r\n                      icon.className =\r\n                        \"flex items-center justify-center w-full h-full\";\r\n                      icon.innerHTML =\r\n                        '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"text-gray-400\"><path d=\"M22 10v6M2 10l10-5 10 5-10 5z\"/><path d=\"M6 12v5c0 2 2 3 6 3s6-1 6-3v-5\"/></svg>';\r\n                      parent.appendChild(icon);\r\n                    }\r\n                  }}\r\n                />\r\n              ) : (\r\n                <GraduationCap className=\"w-8 h-8 text-gray-400\" />\r\n              )}\r\n            </div>\r\n            <div className=\"flex-grow space-y-2\">\r\n              <h3 className=\"text-[15px] font-medium text-blue-950\">\r\n                {edu.university_name}\r\n              </h3>\r\n              <p className=\"text-[13px] text-gray-500\">\r\n                {capitalizeFirstLetter(edu.degree)} in {edu.major}\r\n              </p>\r\n              <p className=\"text-[13px] text-gray-500\">\r\n                {edu.start_date} - {edu.end_date}\r\n              </p>\r\n              {edu.grade && (\r\n                <p className=\"text-[13px] text-gray-500\">Grade: {edu.grade}</p>\r\n              )}\r\n              {edu.accomplishments && (\r\n                <p className=\"text-[13px] text-gray-500 mt-2\">\r\n                  {edu.accomplishments}\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AADA;AAHA;;;;;AAMA,MAAM,wBAAwB,CAAC;IAC7B,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAMe,SAAS,UAAU,EAAE,WAAW,EAAkB;IAC/D,+CAA+C;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAEnD,CAAC;IAEH,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI,YAAY,SAAS,IAAI,YAAY,SAAS,CAAC,MAAM,GAAG,GAAG;gBAC7D,0CAA0C;gBAC1C,MAAM,WAAmC,CAAC;gBAE1C,iCAAiC;gBACjC,KAAK,MAAM,OAAO,YAAY,SAAS,CAAE;oBACvC,IAAI;wBACF,MAAM,UAAU,MAAM,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,eAAe;wBAC9D,QAAQ,CAAC,IAAI,eAAe,CAAC,GAAG;oBAClC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CACX,CAAC,yBAAyB,EAAE,IAAI,eAAe,CAAC,CAAC,CAAC,EAClD;oBAEF,wEAAwE;oBAC1E;gBACF;gBAEA,mBAAmB;YACrB;QACF;QAEA;IACF,GAAG;QAAC,YAAY,SAAS;KAAC;IAE1B,IAAI,CAAC,YAAY,SAAS,IAAI,YAAY,SAAS,CAAC,MAAM,KAAK,GAAG;QAChE,qBACE,kMAAC;YACC,IAAG;YACH,WAAU;;8BAEV,kMAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAG3D,kMAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;IAK/C;IAEA,qBACE,kMAAC;QACC,IAAG;QACH,WAAU;;0BAEV,kMAAC;gBAAG,WAAU;0BAA6C;;;;;;0BAC3D,kMAAC;gBAAI,WAAU;0BACZ,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC/B,kMAAC;wBAAgB,WAAU;;0CACzB,kMAAC;gCAAI,WAAU;0CACZ,eAAe,CAAC,IAAI,eAAe,CAAC,iBACnC,kMAAC;oCACC,KAAK,eAAe,CAAC,IAAI,eAAe,CAAC;oCACzC,KAAK,IAAI,eAAe;oCACxB,WAAU;oCACV,SAAS,CAAC;wCACR,4CAA4C;wCAC5C,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wCAChC,MAAM,SAAS,EAAE,aAAa,CAAC,aAAa;wCAC5C,IAAI,QAAQ;4CACV,MAAM,OAAO,SAAS,aAAa,CAAC;4CACpC,KAAK,SAAS,GACZ;4CACF,KAAK,SAAS,GACZ;4CACF,OAAO,WAAW,CAAC;wCACrB;oCACF;;;;;yDAGF,kMAAC,gOAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAG7B,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAG,WAAU;kDACX,IAAI,eAAe;;;;;;kDAEtB,kMAAC;wCAAE,WAAU;;4CACV,sBAAsB,IAAI,MAAM;4CAAE;4CAAK,IAAI,KAAK;;;;;;;kDAEnD,kMAAC;wCAAE,WAAU;;4CACV,IAAI,UAAU;4CAAC;4CAAI,IAAI,QAAQ;;;;;;;oCAEjC,IAAI,KAAK,kBACR,kMAAC;wCAAE,WAAU;;4CAA4B;4CAAQ,IAAI,KAAK;;;;;;;oCAE3D,IAAI,eAAe,kBAClB,kMAAC;wCAAE,WAAU;kDACV,IAAI,eAAe;;;;;;;;;;;;;uBAxClB;;;;;;;;;;;;;;;;AAiDpB"}}, {"offset": {"line": 4857, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4863, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/Experience.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { Briefcase } from \"lucide-react\";\r\n\r\ninterface ExperienceProps {\r\n  profileData: CounselorPublicProfile;\r\n}\r\n\r\nexport default function Experience({ profileData }: ExperienceProps) {\r\n  if (\r\n    !profileData.professional_experience ||\r\n    profileData.professional_experience.length === 0\r\n  ) {\r\n    return (\r\n      <div\r\n        id=\"experience\"\r\n        className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n      >\r\n        <h2 className=\"text-[15px] font-medium text-blue-950 mb-3\">\r\n          Experience\r\n        </h2>\r\n        <p className=\"text-[13px] text-gray-500\">\r\n          No professional experience available.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      id=\"experience\"\r\n      className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n    >\r\n      <h2 className=\"text-[15px] font-medium text-blue-950 mb-6\">Experience</h2>\r\n      <div className=\"space-y-6\">\r\n        {profileData.professional_experience.map((exp, index) => (\r\n          <div key={index} className=\"flex gap-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <div className=\"w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center\">\r\n                <Briefcase className=\"w-5 h-5 text-blue-600\" />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex-grow space-y-2\">\r\n              <h2 className=\"text-[15px] font-medium text-blue-950\">\r\n                {exp.company_name}\r\n              </h2>\r\n              <p className=\"text-[15px] font-medium text-blue-950\">\r\n                {exp.role}\r\n              </p>\r\n              <p className=\"text-[13px] text-gray-500\">\r\n                {exp.start_date} - {exp.end_date}\r\n              </p>\r\n              {exp.experience_description && (\r\n                <p className=\"text-[13px] text-gray-500 mt-2\">\r\n                  {exp.experience_description}\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {profileData.has_mentored_before &&\r\n        profileData.experience_description && (\r\n          <div className=\"mt-8 pt-6 border-t border-gray-200\">\r\n            <h3 className=\"text-[15px] font-medium text-blue-950 mb-3\">\r\n              Mentoring Experience\r\n            </h3>\r\n            <p className=\"text-[13px] text-gray-500\">\r\n              {profileData.experience_description}\r\n            </p>\r\n          </div>\r\n        )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AASe,SAAS,WAAW,EAAE,WAAW,EAAmB;IACjE,IACE,CAAC,YAAY,uBAAuB,IACpC,YAAY,uBAAuB,CAAC,MAAM,KAAK,GAC/C;QACA,qBACE,kMAAC;YACC,IAAG;YACH,WAAU;;8BAEV,kMAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAG3D,kMAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;IAK/C;IAEA,qBACE,kMAAC;QACC,IAAG;QACH,WAAU;;0BAEV,kMAAC;gBAAG,WAAU;0BAA6C;;;;;;0BAC3D,kMAAC;gBAAI,WAAU;0BACZ,YAAY,uBAAuB,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC7C,kMAAC;wBAAgB,WAAU;;0CACzB,kMAAC;gCAAI,WAAU;0CACb,cAAA,kMAAC;oCAAI,WAAU;8CACb,cAAA,kMAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAG,WAAU;kDACX,IAAI,YAAY;;;;;;kDAEnB,kMAAC;wCAAE,WAAU;kDACV,IAAI,IAAI;;;;;;kDAEX,kMAAC;wCAAE,WAAU;;4CACV,IAAI,UAAU;4CAAC;4CAAI,IAAI,QAAQ;;;;;;;oCAEjC,IAAI,sBAAsB,kBACzB,kMAAC;wCAAE,WAAU;kDACV,IAAI,sBAAsB;;;;;;;;;;;;;uBAlBzB;;;;;;;;;;YA0Bb,YAAY,mBAAmB,IAC9B,YAAY,sBAAsB,kBAChC,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAG3D,kMAAC;wBAAE,WAAU;kCACV,YAAY,sBAAsB;;;;;;;;;;;;;;;;;;AAMjD"}}, {"offset": {"line": 5026, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5032, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/student/useSessions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { NotesPayload, SessionsState } from \"@/app/types/student/sessions\";\r\n\r\nexport const useSessions = create<SessionsState>((set, get) => ({\r\n    loading: false,\r\n    error: null,\r\n    sessions: { items: [], total: null },\r\n    currentNotes: null,\r\n\r\n    fetchSessions: async (timezone, params) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.get(\r\n                `/student/sessions/my-sessions?timezone=${timezone}`,\r\n                {\r\n                    params,\r\n                }\r\n            );\r\n            set({\r\n                sessions: {\r\n                    items: response.data.items || [],\r\n                    total: response.data.total || 0,\r\n                },\r\n                loading: false,\r\n            });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail || \"Failed to fetch sessions\",\r\n                loading: false,\r\n                sessions: { items: [], total: 0 },\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    fetchSessionNotes: async (sessionId: number) => {\r\n        if (!sessionId) return null;\r\n\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.get(\r\n                `/sessions/${sessionId}/notes`\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to fetch session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    createSessionNotes: async (sessionId: number, data: NotesPayload) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.post(\r\n                `/sessions/${sessionId}/notes`,\r\n                data\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to create session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    updateSessionNotes: async (sessionId: number, data: NotesPayload) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.put(\r\n                `/sessions/${sessionId}/notes`,\r\n                data\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to update session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    deleteSessionNotes: async (sessionId: number) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            await apiClient.delete(`/sessions/${sessionId}/notes`);\r\n            set({ currentNotes: null, loading: false });\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to delete session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    clearError: () => {\r\n        set({ error: null });\r\n    },\r\n\r\n    createSession: async (counselorId: number, data) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.post(\r\n                `/student/sessions/counselor/${counselorId}/sessions`,\r\n                {\r\n                    session: {\r\n                        service_type: data.service_type,\r\n                        description: data.description,\r\n                        date: data.date,\r\n                        start_time: data.start_time,\r\n                        end_time: data.end_time,\r\n                    },\r\n                    payment: {\r\n                        amount: data.amount,\r\n                        promo_code: data.promo_code,\r\n                    },\r\n                }\r\n            );\r\n            set({ loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail || \"Failed to create session\",\r\n                loading: false,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AADA;AAFA;;;AAMO,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QAC5D,SAAS;QACT,OAAO;QACP,UAAU;YAAE,OAAO,EAAE;YAAE,OAAO;QAAK;QACnC,cAAc;QAEd,eAAe,OAAO,UAAU;YAC5B,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,uCAAuC,EAAE,UAAU,EACpD;oBACI;gBACJ;gBAEJ,IAAI;oBACA,UAAU;wBACN,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;wBAChC,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI;oBAClC;oBACA,SAAS;gBACb;gBACA,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACpC,SAAS;oBACT,UAAU;wBAAE,OAAO,EAAE;wBAAE,OAAO;oBAAE;gBACpC;gBACA,MAAM;YACV;QACJ;QAEA,mBAAmB,OAAO;YACtB,IAAI,CAAC,WAAW,OAAO;YAEvB,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;gBAElC,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO,WAAmB;YAC1C,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,IAAI,CACjC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAC9B;gBAEJ,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO,WAAmB;YAC1C,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAC9B;gBAEJ,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO;YACvB,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,wHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;gBACrD,IAAI;oBAAE,cAAc;oBAAM,SAAS;gBAAM;YAC7C,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,YAAY;YACR,IAAI;gBAAE,OAAO;YAAK;QACtB;QAEA,eAAe,OAAO,aAAqB;YACvC,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,IAAI,CACjC,CAAC,4BAA4B,EAAE,YAAY,SAAS,CAAC,EACrD;oBACI,SAAS;wBACL,cAAc,KAAK,YAAY;wBAC/B,aAAa,KAAK,WAAW;wBAC7B,MAAM,KAAK,IAAI;wBACf,YAAY,KAAK,UAAU;wBAC3B,UAAU,KAAK,QAAQ;oBAC3B;oBACA,SAAS;wBACL,QAAQ,KAAK,MAAM;wBACnB,YAAY,KAAK,UAAU;oBAC/B;gBACJ;gBAEJ,IAAI;oBAAE,SAAS;gBAAM;gBACrB,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACpC,SAAS;gBACb;gBACA,MAAM;YACV;QACJ;IACJ,CAAC"}}, {"offset": {"line": 5198, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5204, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/student/usePayments.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport { PaymentState, PaymentFilters } from \"@/app/types/student/payments\";\r\n\r\nexport const usePayments = create<PaymentState>((set, get) => ({\r\n  loading: false,\r\n  error: null,\r\n  stats: null,\r\n  sessions: [],\r\n  pagination: {\r\n    total: 0,\r\n    page: 1,\r\n    size: 10,\r\n    hasMore: false,\r\n  },\r\n  filters: {\r\n    page: 1,\r\n    page_size: 10,\r\n  },\r\n\r\n  fetchStats: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/payments/student/payment-stats\");\r\n      set({ stats: response.data, loading: false });\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to fetch payment stats\";\r\n      set({ error: errorMessage, loading: false });\r\n      toast.error(errorMessage);\r\n    }\r\n  },\r\n\r\n  fetchSessions: async (filters?: PaymentFilters) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const currentFilters = { ...get().filters, ...filters };\r\n      set({ filters: currentFilters });\r\n\r\n      const queryParams = new URLSearchParams();\r\n      if (currentFilters.service_type)\r\n        queryParams.append(\"service_type\", currentFilters.service_type);\r\n      if (currentFilters.page)\r\n        queryParams.append(\"page\", currentFilters.page.toString());\r\n      if (currentFilters.page_size)\r\n        queryParams.append(\"page_size\", currentFilters.page_size.toString());\r\n\r\n      const response = await apiClient.get(\r\n        `/payments/student/payment-history?${queryParams}`\r\n      );\r\n\r\n      set({\r\n        sessions: response.data.items,\r\n        pagination: {\r\n          total: response.data.total,\r\n          page: response.data.page,\r\n          size: response.data.size,\r\n          hasMore: response.data.has_more,\r\n        },\r\n        loading: false,\r\n      });\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to fetch payment sessions\";\r\n      set({ error: errorMessage, loading: false });\r\n      toast.error(errorMessage);\r\n    }\r\n  },\r\n\r\n  updateFilters: (newFilters: Partial<PaymentFilters>) => {\r\n    const currentFilters = get().filters;\r\n    const updatedFilters = { ...currentFilters, ...newFilters };\r\n    set({ filters: updatedFilters });\r\n    get().fetchSessions(updatedFilters);\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n\r\n  createCheckoutSession: async (sessionId: number) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const { data } = await apiClient.post(\r\n        \"/payments/session/create-checkout-session\",\r\n        {\r\n          session_id: sessionId,\r\n        }\r\n      );\r\n      set({ loading: false });\r\n      return data;\r\n    } catch (error: any) {\r\n      set({\r\n        error:\r\n          error.response?.data?.detail || \"Failed to create checkout session\",\r\n        loading: false,\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AACA;AAFA;AAFA;;;;AAOO,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAgB,CAAC,KAAK,MAAQ,CAAC;QAC7D,SAAS;QACT,OAAO;QACP,OAAO;QACP,UAAU,EAAE;QACZ,YAAY;YACV,OAAO;YACP,MAAM;YACN,MAAM;YACN,SAAS;QACX;QACA,SAAS;YACP,MAAM;YACN,WAAW;QACb;QAEA,YAAY;YACV,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,OAAO,SAAS,IAAI;oBAAE,SAAS;gBAAM;YAC7C,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,iBAAiB;oBAAE,GAAG,MAAM,OAAO;oBAAE,GAAG,OAAO;gBAAC;gBACtD,IAAI;oBAAE,SAAS;gBAAe;gBAE9B,MAAM,cAAc,IAAI;gBACxB,IAAI,eAAe,YAAY,EAC7B,YAAY,MAAM,CAAC,gBAAgB,eAAe,YAAY;gBAChE,IAAI,eAAe,IAAI,EACrB,YAAY,MAAM,CAAC,QAAQ,eAAe,IAAI,CAAC,QAAQ;gBACzD,IAAI,eAAe,SAAS,EAC1B,YAAY,MAAM,CAAC,aAAa,eAAe,SAAS,CAAC,QAAQ;gBAEnE,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kCAAkC,EAAE,aAAa;gBAGpD,IAAI;oBACF,UAAU,SAAS,IAAI,CAAC,KAAK;oBAC7B,YAAY;wBACV,OAAO,SAAS,IAAI,CAAC,KAAK;wBAC1B,MAAM,SAAS,IAAI,CAAC,IAAI;wBACxB,MAAM,SAAS,IAAI,CAAC,IAAI;wBACxB,SAAS,SAAS,IAAI,CAAC,QAAQ;oBACjC;oBACA,SAAS;gBACX;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,2JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,eAAe,CAAC;YACd,MAAM,iBAAiB,MAAM,OAAO;YACpC,MAAM,iBAAiB;gBAAE,GAAG,cAAc;gBAAE,GAAG,UAAU;YAAC;YAC1D,IAAI;gBAAE,SAAS;YAAe;YAC9B,MAAM,aAAa,CAAC;QACtB;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;QAEpC,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,wHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,6CACA;oBACE,YAAY;gBACd;gBAEF,IAAI;oBAAE,SAAS;gBAAM;gBACrB,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OACE,MAAM,QAAQ,EAAE,MAAM,UAAU;oBAClC,SAAS;gBACX;gBACA,MAAM;YACR;QACF;IACF,CAAC"}}, {"offset": {"line": 5322, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5328, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/booking-form/details-form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface DetailsFormProps {\r\n  counselor: CounselorPublicProfile | null;\r\n  selectedService: string;\r\n  serviceDetails: string;\r\n  onServiceChange: (service: string) => void;\r\n}\r\n\r\nexport function DetailsForm({\r\n  counselor,\r\n  selectedService,\r\n  serviceDetails,\r\n  onServiceChange,\r\n}: DetailsFormProps) {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"space-y-2\">\r\n        <h3 className=\"text-lg font-medium\">Provide Details</h3>\r\n        <p className=\"text-sm text-gray-500\">\r\n          Please provide your specific details below.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        <div className=\"space-y-2\">\r\n          <label className=\"text-sm font-medium\">\r\n            Select Service <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          {counselor?.services.length === 0 ? (\r\n            <p>No services listed</p>\r\n          ) : (\r\n            <select\r\n              value={selectedService}\r\n              onChange={(e) => onServiceChange(e.target.value)}\r\n              className=\"w-full p-2 border rounded-md bg-white\"\r\n            >\r\n              <option value=\"\">Select a service</option>\r\n              {counselor?.services.map((service) => (\r\n                <option\r\n                  key={service.id}\r\n                  value={service.service_type}\r\n                  className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                >\r\n                  {service.service_type}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          )}\r\n        </div>\r\n\r\n        <div\r\n          className={cn(\r\n            \"space-y-2 transition-all\",\r\n            serviceDetails ? \"h-min opacity-100\" : \"h-0 opacity-0\"\r\n          )}\r\n        >\r\n          <label className=\"text-sm font-medium\">Service Details:</label>\r\n          <textarea\r\n            value={serviceDetails}\r\n            readOnly\r\n            className=\"w-full p-2 border rounded-md h-32 bg-gray-50\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAYO,SAAS,YAAY,EAC1B,SAAS,EACT,eAAe,EACf,cAAc,EACd,eAAe,EACE;IACjB,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAG,WAAU;kCAAsB;;;;;;kCACpC,kMAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAM,WAAU;;oCAAsB;kDACtB,kMAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;4BAE/C,WAAW,SAAS,WAAW,kBAC9B,kMAAC;0CAAE;;;;;qDAEH,kMAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,kMAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,WAAW,SAAS,IAAI,CAAC,wBACxB,kMAAC;4CAEC,OAAO,QAAQ,YAAY;4CAC3B,WAAU;sDAET,QAAQ,YAAY;2CAJhB,QAAQ,EAAE;;;;;;;;;;;;;;;;;kCAWzB,kMAAC;wBACC,WAAW,CAAA,GAAA,oHAAA,CAAA,KAAE,AAAD,EACV,4BACA,iBAAiB,sBAAsB;;0CAGzC,kMAAC;gCAAM,WAAU;0CAAsB;;;;;;0CACvC,kMAAC;gCACC,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtB"}}, {"offset": {"line": 5468, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5474, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/skeletons.tsx"], "sourcesContent": ["import { Card } from \"@components/ui/card\";\r\nimport { Skeleton } from \"@components/ui/skeleton\";\r\nimport { Button } from \"@components/ui/button\";\r\n\r\nexport default function ProfileSkeleton() {\r\n  return (\r\n    <>\r\n      <HeaderSkeleton />\r\n      <OverviewSkeleton />\r\n      <ServicesSkeleton />\r\n      <EducationSkeleton />\r\n      <ExperienceSkeleton />\r\n    </>\r\n  );\r\n}\r\n\r\nfunction ServicesSkeleton() {\r\n  return (\r\n    <div className=\"grid grid-cols-3 gap-6 skeleton-fade\">\r\n      {[1, 2, 3].map((i) => (\r\n        <Card key={i} className=\"p-6 shadow-sm border-[#E2E8F0]\">\r\n          <Skeleton className=\"w-8 h-8 mb-4\" />\r\n          <Skeleton className=\"h-5 w-32 mb-2\" />\r\n          <Skeleton className=\"h-4 w-full mb-4\" />\r\n          <Skeleton className=\"h-4 w-20 mb-1\" />\r\n          <Skeleton className=\"h-5 w-16 mb-4\" />\r\n          <div className=\"flex gap-3\">\r\n            <Skeleton className=\"h-9 flex-1\" />\r\n            <Skeleton className=\"h-9 flex-1\" />\r\n          </div>\r\n        </Card>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction HeaderSkeleton() {\r\n  return (\r\n    <div className=\"flex items-start gap-4 mb-10 mt-20 skeleton-fade\">\r\n      <Skeleton className=\"w-[76px] h-[76px] rounded-full\" />\r\n      <div className=\"flex-1\">\r\n        <div className=\"flex items-center gap-2 mb-1\">\r\n          <Skeleton className=\"h-5 w-32\" />\r\n          <Skeleton className=\"w-5 h-5 rounded-sm\" />\r\n        </div>\r\n        <Skeleton className=\"h-4 w-3/4 mb-1\" />\r\n        <Skeleton className=\"h-4 w-1/2\" />\r\n      </div>\r\n      <div className=\"flex items-center gap-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Skeleton className=\"w-2 h-2 rounded-full mt-1\" />\r\n          <div className=\"flex flex-col\">\r\n            <Skeleton className=\"h-4 w-48 mb-1\" />\r\n            <Skeleton className=\"h-4 w-40\" />\r\n          </div>\r\n        </div>\r\n        <Button disabled className=\"bg-[#0F172A] text-white h-9 rounded-[6px]\">\r\n          Message\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction FeedbackSkeleton() {\r\n  return (\r\n    <div className=\"w-full max-w-6xl mx-auto p-4\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Skeleton className=\"h-6 w-32\" />\r\n          <Skeleton className=\"h-4 w-24\" />\r\n        </div>\r\n        <Skeleton className=\"h-4 w-16\" />\r\n      </div>\r\n\r\n      <div className=\"grid md:grid-cols-2 gap-6\">\r\n        {[1, 2].map((i) => (\r\n          <Card key={i} className=\"p-4\">\r\n            <div className=\"flex items-start gap-3\">\r\n              <Skeleton className=\"h-10 w-10 rounded-full\" />\r\n              <div className=\"flex-1\">\r\n                <div className=\"flex justify-between items-start\">\r\n                  <div>\r\n                    <Skeleton className=\"h-5 w-32 mb-2\" />\r\n                    <Skeleton className=\"h-4 w-24\" />\r\n                  </div>\r\n                  <Skeleton className=\"h-4 w-24\" />\r\n                </div>\r\n                <Skeleton className=\"h-4 w-full mt-4\" />\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function BookingSkeleton() {\r\n  return (\r\n    <div className=\"w-full max-w-2xl mx-auto bg-white p-6\">\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <div className=\"space-y-1\">\r\n            <Skeleton className=\"h-6 w-32\" />\r\n            <Skeleton className=\"h-4 w-48\" />\r\n          </div>\r\n          <Skeleton className=\"h-4 w-16\" />\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Skeleton className=\"h-5 w-40\" />\r\n            <Skeleton className=\"h-4 w-64\" />\r\n          </div>\r\n\r\n          <div className=\"space-y-3\">\r\n            <Skeleton className=\"h-10 w-full\" />\r\n            <Skeleton className=\"h-24 w-full\" />\r\n            <Skeleton className=\"h-32 w-full\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex justify-between pt-4\">\r\n          <Skeleton className=\"h-9 w-24\" />\r\n          <Skeleton className=\"h-9 w-24\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function BookingServiceSkeleton() {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {[1, 2, 3].map((i) => (\r\n        <div key={i} className=\"p-4 border rounded-lg space-y-3\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <Skeleton className=\"h-5 w-32\" />\r\n            <Skeleton className=\"h-5 w-20\" />\r\n          </div>\r\n          <Skeleton className=\"h-4 w-3/4\" />\r\n          <Skeleton className=\"h-4 w-1/2\" />\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function BookingDateSkeleton() {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <Skeleton className=\"h-5 w-40\" />\r\n        <Skeleton className=\"h-5 w-24\" />\r\n      </div>\r\n      <div className=\"grid grid-cols-7 gap-2\">\r\n        {[...Array(35)].map((_, i) => (\r\n          <Skeleton key={i} className=\"h-10 w-full rounded-md\" />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function BookingTimeSkeleton() {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <Skeleton className=\"h-5 w-48 mb-6\" />\r\n      <div className=\"grid grid-cols-3 gap-4\">\r\n        {[...Array(6)].map((_, i) => (\r\n          <Skeleton key={i} className=\"h-12 w-full rounded-lg\" />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function DocumentsSkeleton() {\r\n  return (\r\n    <Card className=\"p-4 sm:p-6\">\r\n      <Skeleton className=\"h-4 w-28 mb-6\" />\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n        {[1, 2].map((i) => (\r\n          <div\r\n            key={i}\r\n            className=\"flex items-center gap-4 p-4 bg-[#F8FAFC] rounded-[6px]\"\r\n          >\r\n            <Skeleton className=\"w-10 h-10 rounded-[6px]\" />\r\n            <div className=\"space-y-2\">\r\n              <Skeleton className=\"h-4 w-32\" />\r\n              <Skeleton className=\"h-3 w-16\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </Card>\r\n  );\r\n}\r\n\r\nfunction EducationSkeleton() {\r\n  return (\r\n    <Card className=\"p-6\">\r\n      <Skeleton className=\"h-4 w-28 mb-6\" />\r\n      <div className=\"space-y-6\">\r\n        {[1, 2].map((i) => (\r\n          <div key={i} className=\"flex gap-4\">\r\n            <Skeleton className=\"w-12 h-12 rounded-[6px]\" />\r\n            <div className=\"flex-1 space-y-2\">\r\n              <Skeleton className=\"h-4 w-48\" />\r\n              <Skeleton className=\"h-4 w-full\" />\r\n              <Skeleton className=\"h-4 w-32\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </Card>\r\n  );\r\n}\r\n\r\nfunction ExperienceSkeleton() {\r\n  return (\r\n    <Card className=\"p-6\">\r\n      <Skeleton className=\"h-4 w-28 mb-6\" />\r\n      <div className=\"flex gap-4\">\r\n        <Skeleton className=\"w-12 h-12 rounded-[6px]\" />\r\n        <div className=\"flex-1 space-y-2\">\r\n          <Skeleton className=\"h-4 w-32\" />\r\n          <Skeleton className=\"h-4 w-48\" />\r\n          <Skeleton className=\"h-4 w-40\" />\r\n        </div>\r\n      </div>\r\n    </Card>\r\n  );\r\n}\r\n\r\nfunction OverviewSkeleton() {\r\n  return (\r\n    <Card className=\"p-6\">\r\n      <div className=\"space-y-4\">\r\n        <Skeleton className=\"h-4 w-24\" />\r\n        <div className=\"space-y-2\">\r\n          <Skeleton className=\"h-4 w-full\" />\r\n          <Skeleton className=\"h-4 w-3/4\" />\r\n          <Skeleton className=\"h-4 w-1/2\" />\r\n        </div>\r\n      </div>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE;;0BACE,kMAAC;;;;;0BACD,kMAAC;;;;;0BACD,kMAAC;;;;;0BACD,kMAAC;;;;;0BACD,kMAAC;;;;;;;AAGP;AAEA,SAAS;IACP,qBACE,kMAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,kBACd,kMAAC,wIAAA,CAAA,OAAI;gBAAS,WAAU;;kCACtB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;eARb;;;;;;;;;;AAcnB;AAEA,SAAS;IACP,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC,4IAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAEtB,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,4IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,kMAAC,4IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAGxB,kMAAC,0IAAA,CAAA,SAAM;wBAAC,QAAQ;wBAAC,WAAU;kCAA4C;;;;;;;;;;;;;;;;;;AAM/E;AAEA,SAAS;IACP,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAGtB,kMAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,kBACX,kMAAC,wIAAA,CAAA,OAAI;wBAAS,WAAU;kCACtB,cAAA,kMAAC;4BAAI,WAAU;;8CACb,kMAAC,4IAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;;sEACC,kMAAC,4IAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,kMAAC,4IAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;8DAEtB,kMAAC,4IAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,kMAAC,4IAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;uBAXf;;;;;;;;;;;;;;;;AAmBrB;AAEO,SAAS;IACd,qBACE,kMAAC;QAAI,WAAU;kBACb,cAAA,kMAAC;YAAI,WAAU;;8BACb,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAI,WAAU;;8CACb,kMAAC,4IAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,kMAAC,4IAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,kMAAC,4IAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAGtB,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAI,WAAU;;8CACb,kMAAC,4IAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,kMAAC,4IAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAGtB,kMAAC;4BAAI,WAAU;;8CACb,kMAAC,4IAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,kMAAC,4IAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,kMAAC,4IAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAIxB,kMAAC;oBAAI,WAAU;;sCACb,kMAAC,4IAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,kMAAC,4IAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;AAEO,SAAS;IACd,qBACE,kMAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,kBACd,kMAAC;gBAAY,WAAU;;kCACrB,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;eANZ;;;;;;;;;;AAWlB;AAEO,SAAS;IACd,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAI,WAAU;;kCACb,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAEtB,kMAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,kMAAC,4IAAA,CAAA,WAAQ;wBAAS,WAAU;uBAAb;;;;;;;;;;;;;;;;AAKzB;AAEO,SAAS;IACd,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC,4IAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,kMAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,kMAAC,4IAAA,CAAA,WAAQ;wBAAS,WAAU;uBAAb;;;;;;;;;;;;;;;;AAKzB;AAEO,SAAS;IACd,qBACE,kMAAC,wIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,kMAAC,4IAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,kMAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,kBACX,kMAAC;wBAEC,WAAU;;0CAEV,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,4IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,kMAAC,4IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;uBANjB;;;;;;;;;;;;;;;;AAajB;AAEA,SAAS;IACP,qBACE,kMAAC,wIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,kMAAC,4IAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,kMAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,kBACX,kMAAC;wBAAY,WAAU;;0CACrB,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,4IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,kMAAC,4IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,kMAAC,4IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;uBALd;;;;;;;;;;;;;;;;AAYpB;AAEA,SAAS;IACP,qBACE,kMAAC,wIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,kMAAC,4IAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,kMAAC;gBAAI,WAAU;;kCACb,kMAAC,4IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,kMAAC;wBAAI,WAAU;;0CACb,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,kMAAC,4IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAK9B;AAEA,SAAS;IACP,qBACE,kMAAC,wIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,kMAAC;YAAI,WAAU;;8BACb,kMAAC,4IAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,kMAAC;oBAAI,WAAU;;sCACb,kMAAC,4IAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,kMAAC,4IAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,kMAAC,4IAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B"}}, {"offset": {"line": 6404, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6410, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/booking-form/booking-confirmation.tsx"], "sourcesContent": ["\"use client\";\r\nimport { Calendar, Clock, CreditCard } from \"lucide-react\";\r\nimport type { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport Image from \"next/image\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { usePromoCode } from \"@/app/hooks/student/usePromoCode\";\r\n\r\ninterface BookingConfirmationProps {\r\n  counselor: CounselorPublicProfile;\r\n  selectedService: string;\r\n  selectedDate: Date;\r\n  sessionTime: { start_time: Date; end_time: Date };\r\n  amount: number;\r\n  timezone: string;\r\n  onPromoCodeValidated?: (code: string | null, finalAmount: number) => void;\r\n}\r\n\r\nexport function BookingConfirmation({\r\n  counselor,\r\n  selectedService,\r\n  selectedDate,\r\n  sessionTime,\r\n  amount,\r\n  timezone,\r\n  onPromoCodeValidated,\r\n}: BookingConfirmationProps) {\r\n  const [promoCode, setPromoCode] = useState(\"\");\r\n  const { verifyPromoCode, promoDetails, error, loading, clearPromoCode } =\r\n    usePromoCode();\r\n  const [finalAmount, setFinalAmount] = useState(amount);\r\n\r\n  useEffect(() => {\r\n    const validatePromo = async () => {\r\n      if (promoCode.trim()) {\r\n        await verifyPromoCode(promoCode, counselor.counselor_id, amount);\r\n      } else {\r\n        // Reset states when input is empty\r\n        setFinalAmount(amount);\r\n        onPromoCodeValidated?.(null, amount);\r\n        clearPromoCode();\r\n      }\r\n    };\r\n\r\n    const timeoutId = setTimeout(validatePromo, 500);\r\n    return () => clearTimeout(timeoutId);\r\n  }, [promoCode, counselor.counselor_id, amount]);\r\n\r\n  useEffect(() => {\r\n    if (promoDetails) {\r\n      let newAmount = amount;\r\n      if (promoDetails.type === \"percentage\" && promoDetails.percentage) {\r\n        newAmount = amount * (1 - promoDetails.percentage / 100);\r\n      } else if (promoDetails.type === \"amount\" && promoDetails.amount) {\r\n        newAmount = Math.max(0, amount - promoDetails.amount);\r\n      } else if (promoDetails.type === \"fixed_price\" && promoDetails.amount) {\r\n        newAmount = promoDetails.amount;\r\n      }\r\n      setFinalAmount(newAmount);\r\n      onPromoCodeValidated?.(promoCode, newAmount);\r\n    }\r\n  }, [promoDetails, amount]);\r\n\r\n  const handlePromoCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setPromoCode(value);\r\n\r\n    if (!value.trim()) {\r\n      // Immediately clear promo details when input is empty\r\n      setFinalAmount(amount);\r\n      onPromoCodeValidated?.(null, amount);\r\n      clearPromoCode();\r\n    }\r\n  };\r\n\r\n  const getDiscountText = () => {\r\n    if (!promoDetails) return null;\r\n    if (promoDetails.type === \"percentage\") {\r\n      return `${promoDetails.percentage}% off`;\r\n    }\r\n    return `$${promoDetails.amount} off`;\r\n  };\r\n\r\n  const formatDate = (date: Date) => {\r\n    return date.toLocaleDateString(\"en-US\", {\r\n      weekday: \"long\",\r\n      year: \"numeric\",\r\n      month: \"long\",\r\n      day: \"numeric\",\r\n    });\r\n  };\r\n\r\n  const formatTime = (date: Date) => {\r\n    return date.toLocaleTimeString([], {\r\n      hour: \"2-digit\",\r\n      minute: \"2-digit\",\r\n    });\r\n  };\r\n\r\n  const service = counselor.services.find(\r\n    (s) => s.service_type === selectedService\r\n  )!;\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100\">\r\n      <div className=\"p-6 space-y-6\">\r\n        <div className=\"flex items-start gap-4\">\r\n          <div className=\"relative w-16 h-16 rounded-full overflow-hidden flex-shrink-0\">\r\n            <Image\r\n              src={\r\n                counselor.profile_picture_url ||\r\n                generatePlaceholder(counselor.first_name, counselor.last_name)\r\n              }\r\n              alt={counselor.first_name}\r\n              fill\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <h4 className=\"text-lg font-medium text-gray-900\">\r\n              {counselor.first_name}\r\n            </h4>\r\n            <p className=\"text-sm text-gray-500\">{\"Counselor\"}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid gap-4 md:grid-cols-2\">\r\n          <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <Calendar className=\"w-5 h-5 text-[#0F1C2D]\" />\r\n              <span className=\"font-medium text-gray-700\">Date</span>\r\n            </div>\r\n            <p className=\"text-gray-800\">{formatDate(selectedDate)}</p>\r\n          </div>\r\n\r\n          <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <Clock className=\"w-5 h-5 text-[#0F1C2D]\" />\r\n              <span className=\"font-medium text-gray-700\">Time</span>\r\n            </div>\r\n            <p className=\"text-gray-800\">\r\n              {formatTime(sessionTime.start_time)} -{\" \"}\r\n              {formatTime(sessionTime.end_time)}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 mt-1\">Timezone: {timezone}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n          <div className=\"flex items-center gap-2 mb-2\">\r\n            <CreditCard className=\"w-5 h-5 text-[#0F1C2D]\" />\r\n            <span className=\"font-medium text-gray-700\">Service Details</span>\r\n          </div>\r\n          <div className=\"flex justify-between items-start\">\r\n            <div>\r\n              <p className=\"text-gray-800 font-medium\">\r\n                {service.service_type}\r\n              </p>\r\n              <p className=\"text-sm text-gray-600 mt-1\">\r\n                {service.description}\r\n              </p>\r\n            </div>\r\n            <div className=\"text-right\">\r\n              <p className=\"text-xl font-semibold text-[#0F1C2D]\">${amount}</p>\r\n              <p className=\"text-xs text-gray-500\">\r\n                {service.service_type.toLowerCase().includes(\"15 minutes\")\r\n                  ? \"15 Minute Session\"\r\n                  : \"1 hour session\"}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500\">\r\n          <p className=\"text-sm text-blue-800\">\r\n            By clicking \"Book Session\", a session will get booked with{\" \"}\r\n            {counselor.first_name} {counselor.last_name} based on the details\r\n            you provided.{\" \"}\r\n            {amount !== 0 && \"And you will be redirected to payment.\"}\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div className=\"p-4 border-t border-gray-100\">\r\n        <div className=\"space-y-4\">\r\n          {/* Price Display */}\r\n          <div>\r\n            <h3 className=\"font-medium mb-4\">Payment Details</h3>\r\n            {promoDetails ? (\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex justify-between items-center text-gray-500\">\r\n                  <span>Original price</span>\r\n                  <span className=\"line-through\">${amount.toFixed(2)}</span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span className=\"font-medium text-gray-900\">Final price</span>\r\n                  <span className=\"text-xl font-semibold text-green-600\">\r\n                    ${finalAmount.toFixed(2)}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"font-medium text-gray-900\">Price</span>\r\n                <span className=\"text-xl font-semibold text-gray-900\">\r\n                  ${amount.toFixed(2)}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Promo Code Section */}\r\n          {amount > 0 && (\r\n            <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n              <details className=\"group\">\r\n                <summary className=\"flex items-center gap-1 text-sm text-blue-600 cursor-pointer hover:text-blue-800\">\r\n                  <span>Have a promo code?</span>\r\n                  <svg\r\n                    className=\"w-4 h-4 transition-transform group-open:rotate-180\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M19 9l-7 7-7-7\"\r\n                    />\r\n                  </svg>\r\n                </summary>\r\n\r\n                <div className=\"mt-3 p-3 bg-gray-50 rounded-lg\">\r\n                  <div className=\"relative\">\r\n                    <input\r\n                      type=\"text\"\r\n                      value={promoCode}\r\n                      onChange={handlePromoCodeChange}\r\n                      className=\"w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder:text-gray-400\"\r\n                      placeholder=\"Enter promo code\"\r\n                    />\r\n                    {loading && (\r\n                      <div className=\"absolute right-3 top-1/2 -translate-y-1/2\">\r\n                        <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full\" />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {promoCode.trim() && !loading && error && (\r\n                    <div className=\"flex items-center gap-2 text-red-500 text-sm mt-2\">\r\n                      <svg\r\n                        className=\"w-4 h-4\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        stroke=\"currentColor\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                        />\r\n                      </svg>\r\n                      {error}\r\n                    </div>\r\n                  )}\r\n\r\n                  {promoCode.trim() && !loading && promoDetails && (\r\n                    <div className=\"flex items-center gap-2 text-green-600 text-sm mt-2\">\r\n                      <svg\r\n                        className=\"w-4 h-4\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        stroke=\"currentColor\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M5 13l4 4L19 7\"\r\n                        />\r\n                      </svg>\r\n                      Promo code applied! {getDiscountText()}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </details>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAHA;AAFA;AAAA;AAAA;AADA;;;;;;;AAkBO,SAAS,oBAAoB,EAClC,SAAS,EACT,eAAe,EACf,YAAY,EACZ,WAAW,EACX,MAAM,EACN,QAAQ,EACR,oBAAoB,EACK;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,GACrE,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD;IACb,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI,UAAU,IAAI,IAAI;gBACpB,MAAM,gBAAgB,WAAW,UAAU,YAAY,EAAE;YAC3D,OAAO;gBACL,mCAAmC;gBACnC,eAAe;gBACf,uBAAuB,MAAM;gBAC7B;YACF;QACF;QAEA,MAAM,YAAY,WAAW,eAAe;QAC5C,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAW,UAAU,YAAY;QAAE;KAAO;IAE9C,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,IAAI,YAAY;YAChB,IAAI,aAAa,IAAI,KAAK,gBAAgB,aAAa,UAAU,EAAE;gBACjE,YAAY,SAAS,CAAC,IAAI,aAAa,UAAU,GAAG,GAAG;YACzD,OAAO,IAAI,aAAa,IAAI,KAAK,YAAY,aAAa,MAAM,EAAE;gBAChE,YAAY,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,MAAM;YACtD,OAAO,IAAI,aAAa,IAAI,KAAK,iBAAiB,aAAa,MAAM,EAAE;gBACrE,YAAY,aAAa,MAAM;YACjC;YACA,eAAe;YACf,uBAAuB,WAAW;QACpC;IACF,GAAG;QAAC;QAAc;KAAO;IAEzB,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,aAAa;QAEb,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,sDAAsD;YACtD,eAAe;YACf,uBAAuB,MAAM;YAC7B;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc,OAAO;QAC1B,IAAI,aAAa,IAAI,KAAK,cAAc;YACtC,OAAO,GAAG,aAAa,UAAU,CAAC,KAAK,CAAC;QAC1C;QACA,OAAO,CAAC,CAAC,EAAE,aAAa,MAAM,CAAC,IAAI,CAAC;IACtC;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YACjC,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,UAAU,UAAU,QAAQ,CAAC,IAAI,CACrC,CAAC,IAAM,EAAE,YAAY,KAAK;IAG5B,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAI,WAAU;0CACb,cAAA,kMAAC,iLAAA,CAAA,UAAK;oCACJ,KACE,UAAU,mBAAmB,IAC7B,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,UAAU,EAAE,UAAU,SAAS;oCAE/D,KAAK,UAAU,UAAU;oCACzB,IAAI;oCACJ,WAAU;;;;;;;;;;;0CAId,kMAAC;;kDACC,kMAAC;wCAAG,WAAU;kDACX,UAAU,UAAU;;;;;;kDAEvB,kMAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAI1C,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAI,WAAU;;0DACb,kMAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,kMAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAE9C,kMAAC;wCAAE,WAAU;kDAAiB,WAAW;;;;;;;;;;;;0CAG3C,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAI,WAAU;;0DACb,kMAAC,4MAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,kMAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAE9C,kMAAC;wCAAE,WAAU;;4CACV,WAAW,YAAY,UAAU;4CAAE;4CAAG;4CACtC,WAAW,YAAY,QAAQ;;;;;;;kDAElC,kMAAC;wCAAE,WAAU;;4CAA6B;4CAAW;;;;;;;;;;;;;;;;;;;kCAIzD,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAI,WAAU;;kDACb,kMAAC,0NAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,kMAAC;wCAAK,WAAU;kDAA4B;;;;;;;;;;;;0CAE9C,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;;0DACC,kMAAC;gDAAE,WAAU;0DACV,QAAQ,YAAY;;;;;;0DAEvB,kMAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;;;;;;kDAGxB,kMAAC;wCAAI,WAAU;;0DACb,kMAAC;gDAAE,WAAU;;oDAAuC;oDAAE;;;;;;;0DACtD,kMAAC;gDAAE,WAAU;0DACV,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACzC,sBACA;;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,kMAAC;wBAAI,WAAU;kCACb,cAAA,kMAAC;4BAAE,WAAU;;gCAAwB;gCACwB;gCAC1D,UAAU,UAAU;gCAAC;gCAAE,UAAU,SAAS;gCAAC;gCAC9B;gCACb,WAAW,KAAK;;;;;;;;;;;;;;;;;;0BAIvB,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAI,WAAU;;sCAEb,kMAAC;;8CACC,kMAAC;oCAAG,WAAU;8CAAmB;;;;;;gCAChC,6BACC,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;8DAAK;;;;;;8DACN,kMAAC;oDAAK,WAAU;;wDAAe;wDAAE,OAAO,OAAO,CAAC;;;;;;;;;;;;;sDAElD,kMAAC;4CAAI,WAAU;;8DACb,kMAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,kMAAC;oDAAK,WAAU;;wDAAuC;wDACnD,YAAY,OAAO,CAAC;;;;;;;;;;;;;;;;;;yDAK5B,kMAAC;oCAAI,WAAU;;sDACb,kMAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,kMAAC;4CAAK,WAAU;;gDAAsC;gDAClD,OAAO,OAAO,CAAC;;;;;;;;;;;;;;;;;;;wBAOxB,SAAS,mBACR,kMAAC;4BAAI,WAAU;sCACb,cAAA,kMAAC;gCAAQ,WAAU;;kDACjB,kMAAC;wCAAQ,WAAU;;0DACjB,kMAAC;0DAAK;;;;;;0DACN,kMAAC;gDACC,WAAU;gDACV,MAAK;gDACL,SAAQ;gDACR,QAAO;0DAEP,cAAA,kMAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;kDAKR,kMAAC;wCAAI,WAAU;;0DACb,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;oDAEb,yBACC,kMAAC;wDAAI,WAAU;kEACb,cAAA,kMAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;4CAKpB,UAAU,IAAI,MAAM,CAAC,WAAW,uBAC/B,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;wDACC,WAAU;wDACV,MAAK;wDACL,SAAQ;wDACR,QAAO;kEAEP,cAAA,kMAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,aAAa;4DACb,GAAE;;;;;;;;;;;oDAGL;;;;;;;4CAIJ,UAAU,IAAI,MAAM,CAAC,WAAW,8BAC/B,kMAAC;gDAAI,WAAU;;kEACb,kMAAC;wDACC,WAAU;wDACV,MAAK;wDACL,SAAQ;wDACR,QAAO;kEAEP,cAAA,kMAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,aAAa;4DACb,GAAE;;;;;;;;;;;oDAEA;oDACe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3C"}}, {"offset": {"line": 7073, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7079, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/booking-form/booking-progress.tsx"], "sourcesContent": ["\"use client\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Check<PERSON>ir<PERSON>, Loader2, CreditCard } from \"lucide-react\";\r\n\r\ninterface BookingProgressProps {\r\n  status: \"idle\" | \"booking\" | \"redirecting\";\r\n}\r\n\r\nexport function BookingProgress({ status }: BookingProgressProps) {\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center py-10\">\r\n      <div className=\"relative w-24 h-24 mb-6\">\r\n        <div className=\"absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"absolute inset-0 rounded-full border-4 border-t-[#0F1C2D] border-r-transparent border-b-transparent border-l-transparent animate-spin\"></div>\r\n          <Loader2 className=\"w-12 h-12 text-[#0F1C2D] animate-spin\" />\r\n        </div>\r\n      </div>\r\n\r\n      <h3 className=\"text-xl font-semibold text-gray-900 mb-2 text-center\">\r\n        {window.location.pathname.includes(\"/packages\")\r\n          ? \"Purchasing Your Package...\"\r\n          : \"Booking Your Session...\"}\r\n      </h3>\r\n\r\n      <p className=\"text-gray-600 text-center max-w-md mb-6\">\r\n        {status === \"booking\"\r\n          ? window.location.pathname.includes(\"/packages\")\r\n            ? \"Please wait while we process your package purchase.\"\r\n            : \"Please wait while we process your booking request.\"\r\n          : \"You're being redirected to the payment page.\"}\r\n      </p>\r\n\r\n      <div className=\"w-full max-w-md\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-0 flex items-center\">\r\n            <div className=\"w-full border-t border-gray-300\"></div>\r\n          </div>\r\n          <div className=\"relative flex justify-center text-sm\">\r\n            <span className=\"px-2 bg-white text-gray-500\">Progress</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-6 space-y-6\">\r\n          <div className=\"flex items-center\">\r\n            <div\r\n              className={cn(\r\n                \"flex-shrink-0 h-8 w-8 rounded-full  flex items-center justify-center\",\r\n                status === \"booking\" ? \"bg-white\" : \"bg-[#0F1C2D]\"\r\n              )}\r\n            >\r\n              {status === \"booking\" ? (\r\n                <Loader2 className=\"h-5 w-5 text-gray-500 animate-spin\" />\r\n              ) : (\r\n                <CheckCircle className=\"h-5 w-5 text-white\" />\r\n              )}\r\n            </div>\r\n            <div className={cn(\"ml-4\", status === \"booking\" && \"opacity-40\")}>\r\n              <p className=\"text-sm font-medium text-gray-900\">\r\n                {window.location.pathname.includes(\"/packages\")\r\n                  ? \"Package Details Verified\"\r\n                  : \"Session Details Verified\"}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500\">\r\n                {window.location.pathname.includes(\"/packages\")\r\n                  ? \"Your selected package and service are confirmed\"\r\n                  : \"Your selected time and service are confirmed\"}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {status === \"redirecting\" && (\r\n        <div className=\"mt-8 text-center\">\r\n          <p className=\"text-sm text-gray-500 animate-pulse\">\r\n            Redirecting to payment gateway...\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAFA;;;;AAQO,SAAS,gBAAgB,EAAE,MAAM,EAAwB;IAC9D,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BAAI,WAAU;;;;;;sCACf,kMAAC,yNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIvB,kMAAC;gBAAG,WAAU;0BACX,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAC/B,+BACA;;;;;;0BAGN,kMAAC;gBAAE,WAAU;0BACV,WAAW,YACR,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAChC,wDACA,uDACF;;;;;;0BAGN,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAI,WAAU;0CACb,cAAA,kMAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,kMAAC;gCAAI,WAAU;0CACb,cAAA,kMAAC;oCAAK,WAAU;8CAA8B;;;;;;;;;;;;;;;;;kCAIlD,kMAAC;wBAAI,WAAU;kCACb,cAAA,kMAAC;4BAAI,WAAU;;8CACb,kMAAC;oCACC,WAAW,CAAA,GAAA,oHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,YAAY,aAAa;8CAGrC,WAAW,0BACV,kMAAC,yNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,kMAAC,mOAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAG3B,kMAAC;oCAAI,WAAW,CAAA,GAAA,oHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,WAAW,aAAa;;sDACjD,kMAAC;4CAAE,WAAU;sDACV,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAC/B,6BACA;;;;;;sDAEN,kMAAC;4CAAE,WAAU;sDACV,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAC/B,oDACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOb,WAAW,+BACV,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAE,WAAU;8BAAsC;;;;;;;;;;;;;;;;;AAO7D"}}, {"offset": {"line": 7270, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7276, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/booking-form/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { X } from \"lucide-react\";\r\nimport { DialogClose } from \"@components/ui/dialog\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport { useSessions } from \"@/app/hooks/student/useSessions\";\r\nimport { usePayments } from \"@/app/hooks/student/usePayments\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport type { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { DetailsForm } from \"./details-form\";\r\nimport { DateChooser } from \"./date-chooser\";\r\nimport { TimeChooser } from \"./time-chooser\";\r\nimport { BookingServiceSkeleton } from \"../skeletons\";\r\nimport { BookingConfirmation } from \"./booking-confirmation\";\r\nimport { BookingProgress } from \"./booking-progress\";\r\nimport { useEffect, useMemo, useState } from \"react\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\n\r\ninterface BookingFormProps {\r\n    counselor: CounselorPublicProfile;\r\n    counselor_user_id: number;\r\n    selectedService: string;\r\n    setSelectedService: (serviceType: string) => void;\r\n}\r\n\r\nexport default function BookingForm({\r\n    counselor,\r\n    counselor_user_id,\r\n    selectedService,\r\n    setSelectedService,\r\n}: BookingFormProps) {\r\n    const router = useRouter();\r\n    const { fetchTimeSlots, availabilityLoading, timeSlots, datesLoading } =\r\n        useCounselors();\r\n    const { createSession } = useSessions();\r\n    const { createCheckoutSession } = usePayments();\r\n    const { userInfo } = useProfile();\r\n\r\n    const [step, setStep] = useState(1);\r\n    const [selectedDate, setSelectedDate] = useState<Date | null>(null);\r\n    const [selectedTime, setSelectedTime] = useState<string | null>(null);\r\n    const [serviceDetails, setServiceDetails] = useState(\"\");\r\n    const [isSubmitting, setIsSubmitting] = useState(false);\r\n    const [isLoadingServices, setIsLoadingServices] = useState(true);\r\n    const [bookingStatus, setBookingStatus] = useState<\r\n        \"idle\" | \"booking\" | \"redirecting\"\r\n    >(\"idle\");\r\n    const [promoCode, setPromoCode] = useState<string | null>(null);\r\n    const [finalAmount, setFinalAmount] = useState<number>(0);\r\n\r\n    useEffect(() => {\r\n        if (counselor?.services) {\r\n            setIsLoadingServices(false);\r\n        }\r\n    }, [counselor?.services]);\r\n\r\n    const isStepValid = useMemo(() => {\r\n        if (userInfo?.userType !== \"student\") return false;\r\n        switch (step) {\r\n            case 1:\r\n                return selectedService !== \"\";\r\n            case 2:\r\n                return selectedDate !== null;\r\n            case 3:\r\n                return selectedTime !== null;\r\n            case 4:\r\n                return true; // Confirmation step is always valid\r\n            default:\r\n                return false;\r\n        }\r\n    }, [step, selectedService, selectedDate, selectedTime]);\r\n\r\n    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n\r\n    const handleServiceChange = (serviceType: string) => {\r\n        setSelectedService(serviceType);\r\n    };\r\n\r\n    useEffect(() => {\r\n        // Find service by service_type, handling both regular and custom types\r\n        const service = counselor.services.find(\r\n            (s) => s.service_type === selectedService\r\n        );\r\n        setServiceDetails(service?.description || \"\");\r\n    }, [selectedService, counselor.services]);\r\n\r\n    const getSessionAmount = () => {\r\n        return counselor.services.find(\r\n            (s) => s.service_type === selectedService\r\n        )!.price;\r\n    };\r\n\r\n    const getSessionDuration = () => {\r\n        const selectedServiceObject = counselor.services.find(\r\n            (s) => s.service_type === selectedService\r\n        );\r\n        const is15Minute = selectedServiceObject!.service_type\r\n            .toLowerCase()\r\n            .includes(\"15 minutes\");\r\n        if (!selectedTime) return null;\r\n        const start_time = new Date(selectedTime);\r\n        const end_time = new Date(\r\n            start_time.getTime() + (is15Minute ? 15 : 60) * 60 * 1000\r\n        );\r\n\r\n        return { start_time, end_time };\r\n    };\r\n\r\n    useEffect(() => {\r\n        const fetchSlots = async () => {\r\n            if (selectedDate) {\r\n                try {\r\n                    await fetchTimeSlots(\r\n                        counselor_user_id,\r\n                        selectedDate,\r\n                        userTimezone\r\n                    );\r\n                    setSelectedTime(null);\r\n                } catch (error) {\r\n                    console.error(\"Failed to fetch slots:\", error);\r\n                }\r\n            }\r\n        };\r\n\r\n        fetchSlots();\r\n    }, [selectedDate, counselor_user_id, fetchTimeSlots, userTimezone]);\r\n\r\n    const handlePromoCodeValidated = (code: string | null, amount: number) => {\r\n        setPromoCode(code);\r\n        setFinalAmount(amount);\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        if (!counselor || !selectedDate || !selectedTime || !selectedService)\r\n            return;\r\n\r\n        try {\r\n            setIsSubmitting(true);\r\n            setBookingStatus(\"booking\");\r\n            setStep(5);\r\n\r\n            const { start_time, end_time } = getSessionDuration()!;\r\n\r\n            // Always send original amount, not the discounted amount\r\n            const originalAmount = getSessionAmount();\r\n\r\n            const session = await createSession(counselor_user_id, {\r\n                service_type: selectedService,\r\n                description: serviceDetails,\r\n                date: selectedDate.toISOString(),\r\n                start_time: start_time.toISOString(),\r\n                end_time: end_time.toISOString(),\r\n                amount: originalAmount, // Send original amount\r\n                promo_code: promoCode, // Backend will handle the discount\r\n            });\r\n\r\n            const checkoutSession = await createCheckoutSession(session.id);\r\n\r\n            if (checkoutSession.checkout_url) {\r\n                setBookingStatus(\"redirecting\");\r\n                setTimeout(\r\n                    () => router.push(checkoutSession.checkout_url),\r\n                    1500\r\n                );\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Booking failed:\", error);\r\n            setBookingStatus(\"idle\");\r\n            setStep(4);\r\n        } finally {\r\n            setIsSubmitting(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"w-full max-w-2xl mx-auto bg-white p-6\">\r\n            <div className=\"space-y-6\">\r\n                <div className=\"flex justify-between items-center\">\r\n                    <div className=\"space-y-1\">\r\n                        <h2 className=\"text-xl font-medium text-[#111827]\">\r\n                            {step === 1\r\n                                ? \"Select Service\"\r\n                                : step === 2 || step === 3\r\n                                ? \"Select available time\"\r\n                                : step === 4\r\n                                ? \"Confirm Booking\"\r\n                                : \"Processing Booking\"}\r\n                        </h2>\r\n                        <p className=\"text-sm text-gray-500\">\r\n                            {step === 2 || step === 3\r\n                                ? \"Please select your preferred date and time\"\r\n                                : step === 4\r\n                                ? \"Please review your booking details before confirming\"\r\n                                : step === 5\r\n                                ? \"Please wait while we process your booking\"\r\n                                : \"\"}\r\n                        </p>\r\n                    </div>\r\n                    <span className=\"text-md text-gray-500\">Step {step}/5</span>\r\n                </div>\r\n\r\n                {step === 1 &&\r\n                    (isLoadingServices ? (\r\n                        <BookingServiceSkeleton />\r\n                    ) : (\r\n                        <DetailsForm\r\n                            counselor={counselor}\r\n                            selectedService={selectedService}\r\n                            serviceDetails={serviceDetails}\r\n                            onServiceChange={handleServiceChange}\r\n                        />\r\n                    ))}\r\n\r\n                {step === 2 && (\r\n                    <>\r\n                        <DateChooser\r\n                            counselorId={counselor_user_id}\r\n                            selectedDate={selectedDate!}\r\n                            onDateSelect={setSelectedDate}\r\n                            datesLoading={datesLoading}\r\n                            timezone={userTimezone}\r\n                        />\r\n                    </>\r\n                )}\r\n\r\n                {step === 3 && (\r\n                    <>\r\n                        <TimeChooser\r\n                            slotsLoading={availabilityLoading}\r\n                            availableSlots={timeSlots}\r\n                            selectedTime={selectedTime}\r\n                            onTimeSelect={setSelectedTime}\r\n                            timezone={userTimezone}\r\n                        />\r\n                    </>\r\n                )}\r\n\r\n                {step === 4 && (\r\n                    <BookingConfirmation\r\n                        counselor={counselor}\r\n                        selectedService={selectedService}\r\n                        selectedDate={selectedDate!}\r\n                        sessionTime={getSessionDuration()!}\r\n                        amount={getSessionAmount()}\r\n                        timezone={userTimezone}\r\n                        onPromoCodeValidated={handlePromoCodeValidated}\r\n                    />\r\n                )}\r\n\r\n                {step === 5 && <BookingProgress status={bookingStatus} />}\r\n\r\n                <div className=\"flex justify-end pt-6 space-x-3\">\r\n                    {/* Back Button - shows for steps 2-4 */}\r\n                    {step === 2 && (\r\n                        <DialogClose asChild className=\"mr-auto\">\r\n                            <button className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100\">\r\n                                Cancel{\" \"}\r\n                                <X className=\"w-4 h-4 ml-1.5 inline-block\" />\r\n                            </button>\r\n                        </DialogClose>\r\n                    )}\r\n                    {step > 1 && step < 5 && (\r\n                        <button\r\n                            onClick={() => setStep(step - 1)}\r\n                            className=\"ml-auto px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100\"\r\n                        >\r\n                            ← Back\r\n                        </button>\r\n                    )}\r\n\r\n                    {/* Continue/Submit Button - shows on all steps except final */}\r\n                    {step < 4 && (\r\n                        <button\r\n                            onClick={() => setStep(step + 1)}\r\n                            disabled={!isStepValid}\r\n                            className=\"px-6 py-2 text-sm font-medium text-white bg-[#0F1C2D] rounded-lg hover:bg-[#0F1C2D]/90 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                        >\r\n                            Continue →\r\n                        </button>\r\n                    )}\r\n\r\n                    {/* Book Session Button - only on confirmation step */}\r\n                    {step === 4 && (\r\n                        <button\r\n                            onClick={handleSubmit}\r\n                            disabled={isSubmitting}\r\n                            className=\"px-6 py-2 text-sm font-medium text-white bg-[#0F1C2D] rounded-lg hover:bg-[#0F1C2D]/90 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                        >\r\n                            {isSubmitting ? \"Processing...\" : \"Book Session\"}\r\n                        </button>\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AALA;AAFA;;;;;;;;;;;;;;;;AAyBe,SAAS,YAAY,EAChC,SAAS,EACT,iBAAiB,EACjB,eAAe,EACf,kBAAkB,EACH;IACf,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,SAAS,EAAE,YAAY,EAAE,GAClE,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD;IAChB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;IACpC,MAAM,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;IAC5C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;IAE9B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAE/C;IACF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,WAAW,UAAU;YACrB,qBAAqB;QACzB;IACJ,GAAG;QAAC,WAAW;KAAS;IAExB,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,UAAU,aAAa,WAAW,OAAO;QAC7C,OAAQ;YACJ,KAAK;gBACD,OAAO,oBAAoB;YAC/B,KAAK;gBACD,OAAO,iBAAiB;YAC5B,KAAK;gBACD,OAAO,iBAAiB;YAC5B,KAAK;gBACD,OAAO,MAAM,oCAAoC;YACrD;gBACI,OAAO;QACf;IACJ,GAAG;QAAC;QAAM;QAAiB;QAAc;KAAa;IAEtD,MAAM,eAAe,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;IAErE,MAAM,sBAAsB,CAAC;QACzB,mBAAmB;IACvB;IAEA,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACN,uEAAuE;QACvE,MAAM,UAAU,UAAU,QAAQ,CAAC,IAAI,CACnC,CAAC,IAAM,EAAE,YAAY,KAAK;QAE9B,kBAAkB,SAAS,eAAe;IAC9C,GAAG;QAAC;QAAiB,UAAU,QAAQ;KAAC;IAExC,MAAM,mBAAmB;QACrB,OAAO,UAAU,QAAQ,CAAC,IAAI,CAC1B,CAAC,IAAM,EAAE,YAAY,KAAK,iBAC3B,KAAK;IACZ;IAEA,MAAM,qBAAqB;QACvB,MAAM,wBAAwB,UAAU,QAAQ,CAAC,IAAI,CACjD,CAAC,IAAM,EAAE,YAAY,KAAK;QAE9B,MAAM,aAAa,sBAAuB,YAAY,CACjD,WAAW,GACX,QAAQ,CAAC;QACd,IAAI,CAAC,cAAc,OAAO;QAC1B,MAAM,aAAa,IAAI,KAAK;QAC5B,MAAM,WAAW,IAAI,KACjB,WAAW,OAAO,KAAK,CAAC,aAAa,KAAK,EAAE,IAAI,KAAK;QAGzD,OAAO;YAAE;YAAY;QAAS;IAClC;IAEA,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa;YACf,IAAI,cAAc;gBACd,IAAI;oBACA,MAAM,eACF,mBACA,cACA;oBAEJ,gBAAgB;gBACpB,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,0BAA0B;gBAC5C;YACJ;QACJ;QAEA;IACJ,GAAG;QAAC;QAAc;QAAmB;QAAgB;KAAa;IAElE,MAAM,2BAA2B,CAAC,MAAqB;QACnD,aAAa;QACb,eAAe;IACnB;IAEA,MAAM,eAAe;QACjB,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,iBACjD;QAEJ,IAAI;YACA,gBAAgB;YAChB,iBAAiB;YACjB,QAAQ;YAER,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;YAEjC,yDAAyD;YACzD,MAAM,iBAAiB;YAEvB,MAAM,UAAU,MAAM,cAAc,mBAAmB;gBACnD,cAAc;gBACd,aAAa;gBACb,MAAM,aAAa,WAAW;gBAC9B,YAAY,WAAW,WAAW;gBAClC,UAAU,SAAS,WAAW;gBAC9B,QAAQ;gBACR,YAAY;YAChB;YAEA,MAAM,kBAAkB,MAAM,sBAAsB,QAAQ,EAAE;YAE9D,IAAI,gBAAgB,YAAY,EAAE;gBAC9B,iBAAiB;gBACjB,WACI,IAAM,OAAO,IAAI,CAAC,gBAAgB,YAAY,GAC9C;YAER;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,mBAAmB;YACjC,iBAAiB;YACjB,QAAQ;QACZ,SAAU;YACN,gBAAgB;QACpB;IACJ;IAEA,qBACI,kMAAC;QAAI,WAAU;kBACX,cAAA,kMAAC;YAAI,WAAU;;8BACX,kMAAC;oBAAI,WAAU;;sCACX,kMAAC;4BAAI,WAAU;;8CACX,kMAAC;oCAAG,WAAU;8CACT,SAAS,IACJ,mBACA,SAAS,KAAK,SAAS,IACvB,0BACA,SAAS,IACT,oBACA;;;;;;8CAEV,kMAAC;oCAAE,WAAU;8CACR,SAAS,KAAK,SAAS,IAClB,+CACA,SAAS,IACT,yDACA,SAAS,IACT,8CACA;;;;;;;;;;;;sCAGd,kMAAC;4BAAK,WAAU;;gCAAwB;gCAAM;gCAAK;;;;;;;;;;;;;gBAGtD,SAAS,KACN,CAAC,kCACG,kMAAC,4JAAA,CAAA,yBAAsB;;;;yCAEvB,kMAAC,qLAAA,CAAA,cAAW;oBACR,WAAW;oBACX,iBAAiB;oBACjB,gBAAgB;oBAChB,iBAAiB;;;;;wBAExB;gBAEJ,SAAS,mBACN;8BACI,cAAA,kMAAC,qLAAA,CAAA,cAAW;wBACR,aAAa;wBACb,cAAc;wBACd,cAAc;wBACd,cAAc;wBACd,UAAU;;;;;;;gBAKrB,SAAS,mBACN;8BACI,cAAA,kMAAC,qLAAA,CAAA,cAAW;wBACR,cAAc;wBACd,gBAAgB;wBAChB,cAAc;wBACd,cAAc;wBACd,UAAU;;;;;;;gBAKrB,SAAS,mBACN,kMAAC,6LAAA,CAAA,sBAAmB;oBAChB,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,aAAa;oBACb,QAAQ;oBACR,UAAU;oBACV,sBAAsB;;;;;;gBAI7B,SAAS,mBAAK,kMAAC,yLAAA,CAAA,kBAAe;oBAAC,QAAQ;;;;;;8BAExC,kMAAC;oBAAI,WAAU;;wBAEV,SAAS,mBACN,kMAAC,0IAAA,CAAA,cAAW;4BAAC,OAAO;4BAAC,WAAU;sCAC3B,cAAA,kMAAC;gCAAO,WAAU;;oCAAsF;oCAC7F;kDACP,kMAAC,oMAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIxB,OAAO,KAAK,OAAO,mBAChB,kMAAC;4BACG,SAAS,IAAM,QAAQ,OAAO;4BAC9B,WAAU;sCACb;;;;;;wBAMJ,OAAO,mBACJ,kMAAC;4BACG,SAAS,IAAM,QAAQ,OAAO;4BAC9B,UAAU,CAAC;4BACX,WAAU;sCACb;;;;;;wBAMJ,SAAS,mBACN,kMAAC;4BACG,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAO9D"}}, {"offset": {"line": 7619, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7625, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/RecommendedCounselorCard.tsx"], "sourcesContent": ["import { GraduationCap } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport * as CountryFlags from \"country-flag-icons/react/3x2\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\nconst getCountryFlag = (countryCode: string | null = \"US\") => {\r\n  const code = (countryCode || \"US\").toUpperCase();\r\n  const FlagComponent = (CountryFlags as any)[code];\r\n  return FlagComponent ? <FlagComponent /> : <CountryFlags.US />;\r\n};\r\n\r\nexport function RecommendedCounselorCard({\r\n  counselor,\r\n}: {\r\n  counselor: CounselorPublicProfile;\r\n}) {\r\n  return (\r\n    <Link href={`/profile/${counselor.user_id}`} className=\"block\">\r\n      <div className=\"overflow-hidden rounded-lg flex bg-white border transition-all duration-200 hover:shadow-md group relative h-32\">\r\n        <div className=\"relative w-32 flex-shrink-0\">\r\n          <Image\r\n            src={\r\n              counselor.profile_picture_url ||\r\n              generatePlaceholder(counselor.first_name, counselor.last_name)\r\n            }\r\n            alt={`${counselor.first_name} ${counselor.last_name}`}\r\n            width={128}\r\n            height={128}\r\n            className=\"h-full w-full object-cover group-hover:opacity-95 transition-opacity\"\r\n          />\r\n          <div className=\"absolute top-2 left-2 w-5 h-4 rounded-sm overflow-hidden shadow-md\">\r\n            {getCountryFlag(counselor.country_of_residence)}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"p-3 flex-1 min-w-0\">\r\n          <div className=\"flex flex-col justify-between h-full\">\r\n            <div>\r\n              <h3 className=\"text-base font-medium group-hover:text-[#9C0E22] transition-colors truncate\">\r\n                {counselor.first_name} {counselor.last_name}\r\n              </h3>\r\n              {counselor.education && counselor.education.length > 0 && (\r\n                <div className=\"flex items-center gap-1.5 text-sm text-gray-700 mt-1\">\r\n                  <GraduationCap className=\"w-4 h-4 text-blue-950 flex-shrink-0\" />\r\n                  <p className=\"truncate\">\r\n                    {counselor.education[0].university_name ||\r\n                      counselor.education[0].major}\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"text-sm text-gray-500\">\r\n              Starting at ${counselor.hourly_rate}/hr\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAGA;AADA;AAHA;AADA;;;;;;;AAOA,MAAM,iBAAiB,CAAC,cAA6B,IAAI;IACvD,MAAM,OAAO,CAAC,eAAe,IAAI,EAAE,WAAW;IAC9C,MAAM,gBAAgB,AAAC,yKAAoB,CAAC,KAAK;IACjD,OAAO,8BAAgB,kMAAC;;;;6BAAmB,kMAAC,0KAAa,EAAE;;;;;AAC7D;AAEO,SAAS,yBAAyB,EACvC,SAAS,EAGV;IACC,qBACE,kMAAC,2KAAA,CAAA,UAAI;QAAC,MAAM,CAAC,SAAS,EAAE,UAAU,OAAO,EAAE;QAAE,WAAU;kBACrD,cAAA,kMAAC;YAAI,WAAU;;8BACb,kMAAC;oBAAI,WAAU;;sCACb,kMAAC,iLAAA,CAAA,UAAK;4BACJ,KACE,UAAU,mBAAmB,IAC7B,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,UAAU,EAAE,UAAU,SAAS;4BAE/D,KAAK,GAAG,UAAU,UAAU,CAAC,CAAC,EAAE,UAAU,SAAS,EAAE;4BACrD,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,kMAAC;4BAAI,WAAU;sCACZ,eAAe,UAAU,oBAAoB;;;;;;;;;;;;8BAIlD,kMAAC;oBAAI,WAAU;8BACb,cAAA,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;;kDACC,kMAAC;wCAAG,WAAU;;4CACX,UAAU,UAAU;4CAAC;4CAAE,UAAU,SAAS;;;;;;;oCAE5C,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,MAAM,GAAG,mBACnD,kMAAC;wCAAI,WAAU;;0DACb,kMAAC,gOAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,kMAAC;gDAAE,WAAU;0DACV,UAAU,SAAS,CAAC,EAAE,CAAC,eAAe,IACrC,UAAU,SAAS,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;;;;;;;0CAKtC,kMAAC;gCAAI,WAAU;;oCAAwB;oCACvB,UAAU,WAAW;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD"}}, {"offset": {"line": 7773, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7779, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/RecommendedCounselors.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { RecommendedCounselorCard } from \"./RecommendedCounselorCard\";\r\n\r\ninterface RecommendedCounselorsProps {\r\n  currentCounselorId: number;\r\n  allCounselors: CounselorPublicProfile[] | null;\r\n}\r\n\r\nexport function RecommendedCounselors({\r\n  currentCounselorId,\r\n  allCounselors,\r\n}: RecommendedCounselorsProps) {\r\n  const [recommendedCounselors, setRecommendedCounselors] = useState<\r\n    CounselorPublicProfile[]\r\n  >([]);\r\n\r\n  useEffect(() => {\r\n    if (!allCounselors) return;\r\n    // Filter out the current counselor\r\n    const otherCounselors = allCounselors.filter(\r\n      (counselor) => counselor.user_id !== currentCounselorId\r\n    );\r\n\r\n    // Randomly select 4 counselors\r\n    const shuffled = [...otherCounselors].sort(() => Math.random() - 0.5);\r\n    setRecommendedCounselors(shuffled.slice(0, 4));\r\n  }, [currentCounselorId, allCounselors]);\r\n\r\n  if (recommendedCounselors.length === 0) return null;\r\n\r\n  return (\r\n    <div className=\"mt-8 border-t pt-8\">\r\n      <h2 className=\"text-xl font-medium mb-4\">Other Popular Counselors</h2>\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n        {recommendedCounselors.map((counselor) => (\r\n          <RecommendedCounselorCard\r\n            key={counselor.user_id}\r\n            counselor={counselor}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAOO,SAAS,sBAAsB,EACpC,kBAAkB,EAClB,aAAa,EACc;IAC3B,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAE/D,EAAE;IAEJ,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QACpB,mCAAmC;QACnC,MAAM,kBAAkB,cAAc,MAAM,CAC1C,CAAC,YAAc,UAAU,OAAO,KAAK;QAGvC,+BAA+B;QAC/B,MAAM,WAAW;eAAI;SAAgB,CAAC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;QACjE,yBAAyB,SAAS,KAAK,CAAC,GAAG;IAC7C,GAAG;QAAC;QAAoB;KAAc;IAEtC,IAAI,sBAAsB,MAAM,KAAK,GAAG,OAAO;IAE/C,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC;gBAAG,WAAU;0BAA2B;;;;;;0BACzC,kMAAC;gBAAI,WAAU;0BACZ,sBAAsB,GAAG,CAAC,CAAC,0BAC1B,kMAAC,2KAAA,CAAA,2BAAwB;wBAEvB,WAAW;uBADN,UAAU,OAAO;;;;;;;;;;;;;;;;AAOlC"}}, {"offset": {"line": 7836, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7842, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/hooks/public/useCounselorReviews.tsx"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\n\r\nexport interface ReviewResponse {\r\n  id: number;\r\n  student_name: string;\r\n  student_profile_picture?: string;\r\n  rating: number;\r\n  received_expected_service: boolean;\r\n  comment: string;\r\n  additional_feedback?: string;\r\n  created_at: string; // ISO date string\r\n}\r\n\r\nexport interface CounselorReviewsResponse {\r\n  average_rating: number;\r\n  total_reviews: number;\r\n  reviews: ReviewResponse[];\r\n  page: number;\r\n  page_size: number;\r\n  total_pages: number;\r\n  has_next: boolean;\r\n  has_prev: boolean;\r\n}\r\n\r\ninterface CounselorReviewsState {\r\n  reviewsData: CounselorReviewsResponse | null;\r\n\r\n  isLoading: boolean;\r\n  error: string | null;\r\n\r\n  fetchReviews: (\r\n    counselorId: number,\r\n    page?: number,\r\n    pageSize?: number\r\n  ) => Promise<void>;\r\n  setPage: (page: number) => Promise<void>;\r\n  reset: () => void;\r\n\r\n  currentCounselorId: number | null;\r\n}\r\n\r\nexport const useCounselorReviews = create<CounselorReviewsState>(\r\n  (set, get) => ({\r\n    // Initial state\r\n    reviewsData: null,\r\n    isLoading: false,\r\n    error: null,\r\n    currentCounselorId: null,\r\n\r\n    // Actions\r\n    fetchReviews: async (counselorId, page = 1, pageSize = 10) => {\r\n      set({ isLoading: true, error: null, currentCounselorId: counselorId });\r\n\r\n      try {\r\n        const response = await apiClient.get<CounselorReviewsResponse>(\r\n          `/counselors/${counselorId}/reviews`,\r\n          { params: { page, page_size: pageSize } }\r\n        );\r\n\r\n        set({\r\n          reviewsData: response.data,\r\n          isLoading: false,\r\n        });\r\n      } catch (error) {\r\n        set({\r\n          error:\r\n            error instanceof Error ? error.message : \"Failed to fetch reviews\",\r\n          isLoading: false,\r\n        });\r\n      }\r\n    },\r\n\r\n    setPage: async (page) => {\r\n      const { currentCounselorId, reviewsData } = get();\r\n      if (!currentCounselorId || !reviewsData) return;\r\n\r\n      await get().fetchReviews(currentCounselorId, page, reviewsData.page_size);\r\n    },\r\n\r\n    reset: () => {\r\n      set({\r\n        reviewsData: null,\r\n        isLoading: false,\r\n        error: null,\r\n        currentCounselorId: null,\r\n      });\r\n    },\r\n  })\r\n);\r\n"], "names": [], "mappings": ";;;AACA;AADA;;;AA0CO,MAAM,sBAAsB,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EACtC,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,OAAO;QACP,oBAAoB;QAEpB,UAAU;QACV,cAAc,OAAO,aAAa,OAAO,CAAC,EAAE,WAAW,EAAE;YACvD,IAAI;gBAAE,WAAW;gBAAM,OAAO;gBAAM,oBAAoB;YAAY;YAEpE,IAAI;gBACF,MAAM,WAAW,MAAM,wHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,YAAY,EAAE,YAAY,QAAQ,CAAC,EACpC;oBAAE,QAAQ;wBAAE;wBAAM,WAAW;oBAAS;gBAAE;gBAG1C,IAAI;oBACF,aAAa,SAAS,IAAI;oBAC1B,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAC3C,WAAW;gBACb;YACF;QACF;QAEA,SAAS,OAAO;YACd,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,GAAG;YAC5C,IAAI,CAAC,sBAAsB,CAAC,aAAa;YAEzC,MAAM,MAAM,YAAY,CAAC,oBAAoB,MAAM,YAAY,SAAS;QAC1E;QAEA,OAAO;YACL,IAAI;gBACF,aAAa;gBACb,WAAW;gBACX,OAAO;gBACP,oBAAoB;YACtB;QACF;IACF,CAAC"}}, {"offset": {"line": 7894, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7900, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/public/profile/Reviews.tsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport { format } from \"date-fns\";\r\nimport { useCounselorReviews } from \"@/app/hooks/public/useCounselorReviews\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\ninterface CounselorReviewsProps {\r\n  counselorId: number;\r\n  maxReviews?: number;\r\n}\r\n\r\nexport const CounselorReviews: React.FC<CounselorReviewsProps> = ({\r\n  counselorId,\r\n  maxReviews = 8,\r\n}) => {\r\n  const { fetchReviews, reviewsData, isLoading, error, setPage, reset } =\r\n    useCounselorReviews();\r\n\r\n  useEffect(() => {\r\n    // Calculate optimal page size based on maxReviews\r\n    fetchReviews(counselorId, 1, maxReviews);\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      reset();\r\n    };\r\n  }, [counselorId, maxReviews, fetchReviews, reset]);\r\n\r\n  if (isLoading && !reviewsData) {\r\n    return <div className=\"flex justify-center p-8\">Loading reviews...</div>;\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"text-red-500 p-4\">Error loading reviews: {error}</div>\r\n    );\r\n  }\r\n\r\n  if (!reviewsData || reviewsData.total_reviews === 0) {\r\n    return (\r\n      <div\r\n        id=\"reviews\"\r\n        className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n      >\r\n        <h2 className=\"text-[15px] font-medium text-blue-950 mb-3\">Reviews</h2>\r\n        <p className=\"text-[13px] text-gray-500\">\r\n          No reviews available for this counselor.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      id=\"reviews\"\r\n      className=\"w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6\"\r\n    >\r\n      {/* Reviews Header */}\r\n      <div className=\"flex items-center justify-between mb-2\">\r\n        <h2 className=\"text-[15px] font-medium text-blue-950 mb-6\">Reviews</h2>\r\n        <div className=\"flex items-center\">\r\n          <div className=\"flex items-center mr-2\">\r\n            {[1, 2, 3, 4, 5].map((star) => (\r\n              <svg\r\n                key={star}\r\n                className={`w-5 h-5 ${\r\n                  star <= Math.round(reviewsData.average_rating)\r\n                    ? \"text-yellow-400\"\r\n                    : \"text-gray-300\"\r\n                }`}\r\n                fill=\"currentColor\"\r\n                viewBox=\"0 0 20 20\"\r\n              >\r\n                <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\r\n              </svg>\r\n            ))}\r\n          </div>\r\n          <span className=\"text-lg font-[500]\">\r\n            {reviewsData.average_rating.toFixed(1)}\r\n          </span>\r\n          <span className=\"text-gray-500 ml-2\">\r\n            {reviewsData.total_reviews} review(s)\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Reviews Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n        {reviewsData.reviews.slice(0, maxReviews).map((review) => (\r\n          <div\r\n            key={review.id}\r\n            className=\"bg-white border rounded-lg hover:scale-[100.7%] transition-all  cursor-pointer p-4 flex\"\r\n          >\r\n            {/* Student Avatar */}\r\n            <div className=\"mr-4 flex-shrink-0\">\r\n              <img\r\n                src={\r\n                  review.student_profile_picture ||\r\n                  generatePlaceholder(\r\n                    review.student_name.split(/s+/)[0],\r\n                    review.student_name.split(/s+/)[1]\r\n                  )\r\n                }\r\n                alt={review.student_name}\r\n                className=\"w-12 h-12 rounded-full object-cover\"\r\n              />\r\n            </div>\r\n\r\n            {/* Review Content */}\r\n            <div className=\"flex-1\">\r\n              <div className=\"font-medium mb-1\">{review.student_name}</div>\r\n              {/* Star Rating */}\r\n              <div className=\"flex mb-2\">\r\n                {[1, 2, 3, 4, 5].map((star) => (\r\n                  <svg\r\n                    key={star}\r\n                    className={`w-4 h-4 ${\r\n                      star <= review.rating\r\n                        ? \"text-yellow-400\"\r\n                        : \"text-gray-300\"\r\n                    }`}\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                  >\r\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\r\n                  </svg>\r\n                ))}\r\n              </div>\r\n\r\n              {/* Review Comment */}\r\n              <p className=\"text-gray-700 mb-2 line-clamp-3\">\r\n                {review.comment}\r\n              </p>\r\n\r\n              {/* Review Date */}\r\n              <p className=\"text-gray-500 text-sm\">\r\n                {format(new Date(review.created_at), \"PPP\")}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Pagination (if showing all reviews) */}\r\n      {reviewsData.total_pages > 1 && (\r\n        <div className=\"flex justify-center mt-6\">\r\n          <nav className=\"flex items-center\">\r\n            <button\r\n              onClick={() => setPage(reviewsData.page - 1)}\r\n              disabled={!reviewsData.has_prev}\r\n              className={`px-3 py-1 rounded-l border ${\r\n                !reviewsData.has_prev\r\n                  ? \"bg-gray-100 text-gray-400\"\r\n                  : \"bg-white text-blue-500 hover:bg-blue-50\"\r\n              }`}\r\n            >\r\n              Previous\r\n            </button>\r\n\r\n            <div className=\"px-4 py-1 border-t border-b\">\r\n              Page {reviewsData.page} of {reviewsData.total_pages}\r\n            </div>\r\n\r\n            <button\r\n              onClick={() => setPage(reviewsData.page + 1)}\r\n              disabled={!reviewsData.has_next}\r\n              className={`px-3 py-1 rounded-r border ${\r\n                !reviewsData.has_next\r\n                  ? \"bg-gray-100 text-gray-400\"\r\n                  : \"bg-white text-blue-500 hover:bg-blue-50\"\r\n              }`}\r\n            >\r\n              Next\r\n            </button>\r\n          </nav>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAFA;;;;;;AASO,MAAM,mBAAoD,CAAC,EAChE,WAAW,EACX,aAAa,CAAC,EACf;IACC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,GACnE,CAAA,GAAA,sJAAA,CAAA,sBAAmB,AAAD;IAEpB,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,kDAAkD;QAClD,aAAa,aAAa,GAAG;QAE7B,mBAAmB;QACnB,OAAO;YACL;QACF;IACF,GAAG;QAAC;QAAa;QAAY;QAAc;KAAM;IAEjD,IAAI,aAAa,CAAC,aAAa;QAC7B,qBAAO,kMAAC;YAAI,WAAU;sBAA0B;;;;;;IAClD;IAEA,IAAI,OAAO;QACT,qBACE,kMAAC;YAAI,WAAU;;gBAAmB;gBAAwB;;;;;;;IAE9D;IAEA,IAAI,CAAC,eAAe,YAAY,aAAa,KAAK,GAAG;QACnD,qBACE,kMAAC;YACC,IAAG;YACH,WAAU;;8BAEV,kMAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAC3D,kMAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;IAK/C;IAEA,qBACE,kMAAC;QACC,IAAG;QACH,WAAU;;0BAGV,kMAAC;gBAAI,WAAU;;kCACb,kMAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAC3D,kMAAC;wBAAI,WAAU;;0CACb,kMAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,kMAAC;wCAEC,WAAW,CAAC,QAAQ,EAClB,QAAQ,KAAK,KAAK,CAAC,YAAY,cAAc,IACzC,oBACA,iBACJ;wCACF,MAAK;wCACL,SAAQ;kDAER,cAAA,kMAAC;4CAAK,GAAE;;;;;;uCATH;;;;;;;;;;0CAaX,kMAAC;gCAAK,WAAU;0CACb,YAAY,cAAc,CAAC,OAAO,CAAC;;;;;;0CAEtC,kMAAC;gCAAK,WAAU;;oCACb,YAAY,aAAa;oCAAC;;;;;;;;;;;;;;;;;;;0BAMjC,kMAAC;gBAAI,WAAU;0BACZ,YAAY,OAAO,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC,uBAC7C,kMAAC;wBAEC,WAAU;;0CAGV,kMAAC;gCAAI,WAAU;0CACb,cAAA,kMAAC;oCACC,KACE,OAAO,uBAAuB,IAC9B,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAChB,OAAO,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAClC,OAAO,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oCAGtC,KAAK,OAAO,YAAY;oCACxB,WAAU;;;;;;;;;;;0CAKd,kMAAC;gCAAI,WAAU;;kDACb,kMAAC;wCAAI,WAAU;kDAAoB,OAAO,YAAY;;;;;;kDAEtD,kMAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,kMAAC;gDAEC,WAAW,CAAC,QAAQ,EAClB,QAAQ,OAAO,MAAM,GACjB,oBACA,iBACJ;gDACF,MAAK;gDACL,SAAQ;0DAER,cAAA,kMAAC;oDAAK,GAAE;;;;;;+CATH;;;;;;;;;;kDAeX,kMAAC;wCAAE,WAAU;kDACV,OAAO,OAAO;;;;;;kDAIjB,kMAAC;wCAAE,WAAU;kDACV,CAAA,GAAA,8JAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,UAAU,GAAG;;;;;;;;;;;;;uBA9CpC,OAAO,EAAE;;;;;;;;;;YAsDnB,YAAY,WAAW,GAAG,mBACzB,kMAAC;gBAAI,WAAU;0BACb,cAAA,kMAAC;oBAAI,WAAU;;sCACb,kMAAC;4BACC,SAAS,IAAM,QAAQ,YAAY,IAAI,GAAG;4BAC1C,UAAU,CAAC,YAAY,QAAQ;4BAC/B,WAAW,CAAC,2BAA2B,EACrC,CAAC,YAAY,QAAQ,GACjB,8BACA,2CACJ;sCACH;;;;;;sCAID,kMAAC;4BAAI,WAAU;;gCAA8B;gCACrC,YAAY,IAAI;gCAAC;gCAAK,YAAY,WAAW;;;;;;;sCAGrD,kMAAC;4BACC,SAAS,IAAM,QAAQ,YAAY,IAAI,GAAG;4BAC1C,UAAU,CAAC,YAAY,QAAQ;4BAC/B,WAAW,CAAC,2BAA2B,EACrC,CAAC,YAAY,QAAQ,GACjB,8BACA,2CACJ;sCACH;;;;;;;;;;;;;;;;;;;;;;;AAQb"}}, {"offset": {"line": 8207, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8213, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/(landing)/profile/[id]/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, DialogContent, DialogTitle } from \"@components/ui/dialog\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { ProfileHeader } from \"@components/public/profile/ProfileHeader\";\r\nimport { Overview } from \"@components/public/profile/Overview\";\r\nimport { Services } from \"@/app/components/public/profile/Services\";\r\nimport PublicPackages from \"@/app/components/public/profile/packages\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport { useParams, useRouter } from \"next/navigation\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport Education from \"@/app/components/public/profile/Education\";\r\nimport Experience from \"@/app/components/public/profile/Experience\";\r\nimport BookingForm from \"@/app/components/public/profile/booking-form\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport ProfileSkeleton from \"@/app/components/public/profile/skeletons\";\r\nimport { RecommendedCounselors } from \"@/app/components/public/profile/RecommendedCounselors\";\r\nimport { CounselorReviews } from \"@/app/components/public/profile/Reviews\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport default function ProfilePage() {\r\n  const [isBookingOpen, setIsBookingOpen] = useState(false);\r\n  const { id } = useParams();\r\n  const { fetchCounselorProfile, counselors, profileLoading } = useCounselors();\r\n  const [selectedService, setSelectedService] = useState(\"\");\r\n  const { userInfo } = useProfile();\r\n  const router = useRouter();\r\n\r\n  const [profileData, setProfileData] = useState<CounselorPublicProfile | null>(\r\n    null\r\n  );\r\n  const [error, setError] = useState(false);\r\n\r\n  const handleBookingClick = () => {\r\n    if (!userInfo) {\r\n      router.push(`/auth/login?redirect=/profile/${id}`);\r\n      return;\r\n    }\r\n    setIsBookingOpen(true);\r\n  };\r\n\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const loadProfile = async () => {\r\n      if (!id) return;\r\n\r\n      try {\r\n        setError(false);\r\n        // Always fetch fresh data to ensure we have complete profile\r\n        const data = await fetchCounselorProfile(Number(id));\r\n\r\n        // Only update state if component is still mounted\r\n        if (isMounted) {\r\n          setProfileData(data);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to load counselor profile:\", error);\r\n        if (isMounted) {\r\n          setError(true);\r\n        }\r\n      }\r\n    };\r\n\r\n    loadProfile();\r\n\r\n    // Cleanup function to prevent state updates on unmounted component\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [id, fetchCounselorProfile]);\r\n\r\n  // Removed redundant check since we now have more robust error handling below\r\n\r\n  // Show loading state while profile is being fetched\r\n  if (profileLoading || !profileData) {\r\n    return (\r\n      <div className=\"container max-w-7xl mx-auto px-3 sm:px-5 my-12 md:my-20 space-y-4 md:space-y-6 font-clash-display\">\r\n        <ProfileSkeleton />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if profile could not be loaded\r\n  if (!profileData && error) {\r\n    return (\r\n      <div className=\"text-center py-12 space-y-8\">\r\n        <h1 className=\"text-2xl md:text-3xl font-medium\">No profile found</h1>\r\n        <Link href=\"/explore\">\r\n          <Button className=\"inline-flex items-center gap-2\">\r\n            Explore Counselors <ArrowRight className=\"w-5 h-5\" />\r\n          </Button>\r\n        </Link>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Safeguard - wait until we have profile data\r\n  if (!profileData) {\r\n    return (\r\n      <div className=\"container max-w-7xl mx-auto px-3 sm:px-5 my-12 md:my-20 space-y-4 md:space-y-6 font-clash-display\">\r\n        <ProfileSkeleton />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"container max-w-7xl mx-auto px-3 sm:px-5 my-12 md:my-20 space-y-4 md:space-y-6 font-clash-display\">\r\n      <ProfileHeader\r\n        counselorUserId={Number(id)}\r\n        profileData={profileData}\r\n        setIsBookingOpen={handleBookingClick}\r\n      />\r\n\r\n      <Overview profileData={profileData} />\r\n      <Services\r\n        setIsBookingOpen={handleBookingClick}\r\n        profileData={profileData}\r\n        setSelectedService={setSelectedService}\r\n      />\r\n      <PublicPackages counselor={profileData} />\r\n      <Education profileData={profileData} />\r\n      <Experience profileData={profileData} />\r\n      <CounselorReviews counselorId={Number(profileData.user_id)} />\r\n\r\n      <RecommendedCounselors\r\n        currentCounselorId={Number(id)}\r\n        allCounselors={counselors}\r\n      />\r\n\r\n      <Dialog open={isBookingOpen} onOpenChange={setIsBookingOpen}>\r\n        <DialogContent className=\"max-w-2xl p-0 max-h-[90vh] overflow-y-auto\">\r\n          <DialogTitle className=\"text-2xl font-medium p-4\">\r\n            Book Service\r\n          </DialogTitle>\r\n          <BookingForm\r\n            counselor={profileData}\r\n            counselor_user_id={Number(id)}\r\n            selectedService={selectedService}\r\n            setSelectedService={setSelectedService}\r\n          />\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAVA;AAOA;AAjBA;;;;;;;;;;;;;;;;;;;;AAsBO,MAAM,UAAU;AAER,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,qBAAqB,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD;IAC1E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAC3C;IAEF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU;YACb,OAAO,IAAI,CAAC,CAAC,8BAA8B,EAAE,IAAI;YACjD;QACF;QACA,iBAAiB;IACnB;IAEA,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;QAEhB,MAAM,cAAc;YAClB,IAAI,CAAC,IAAI;YAET,IAAI;gBACF,SAAS;gBACT,6DAA6D;gBAC7D,MAAM,OAAO,MAAM,sBAAsB,OAAO;gBAEhD,kDAAkD;gBAClD,IAAI,WAAW;oBACb,eAAe;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,IAAI,WAAW;oBACb,SAAS;gBACX;YACF;QACF;QAEA;QAEA,mEAAmE;QACnE,OAAO;YACL,YAAY;QACd;IACF,GAAG;QAAC;QAAI;KAAsB;IAE9B,6EAA6E;IAE7E,oDAAoD;IACpD,IAAI,kBAAkB,CAAC,aAAa;QAClC,qBACE,kMAAC;YAAI,WAAU;sBACb,cAAA,kMAAC,4JAAA,CAAA,UAAe;;;;;;;;;;IAGtB;IAEA,kDAAkD;IAClD,IAAI,CAAC,eAAe,OAAO;QACzB,qBACE,kMAAC;YAAI,WAAU;;8BACb,kMAAC;oBAAG,WAAU;8BAAmC;;;;;;8BACjD,kMAAC,2KAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,kMAAC,0IAAA,CAAA,SAAM;wBAAC,WAAU;;4BAAiC;0CAC9B,kMAAC,0NAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKnD;IAEA,8CAA8C;IAC9C,IAAI,CAAC,aAAa;QAChB,qBACE,kMAAC;YAAI,WAAU;sBACb,cAAA,kMAAC,4JAAA,CAAA,UAAe;;;;;;;;;;IAGtB;IAEA,qBACE,kMAAC;QAAI,WAAU;;0BACb,kMAAC,gKAAA,CAAA,gBAAa;gBACZ,iBAAiB,OAAO;gBACxB,aAAa;gBACb,kBAAkB;;;;;;0BAGpB,kMAAC,2JAAA,CAAA,WAAQ;gBAAC,aAAa;;;;;;0BACvB,kMAAC,2JAAA,CAAA,WAAQ;gBACP,kBAAkB;gBAClB,aAAa;gBACb,oBAAoB;;;;;;0BAEtB,kMAAC,oKAAA,CAAA,UAAc;gBAAC,WAAW;;;;;;0BAC3B,kMAAC,4JAAA,CAAA,UAAS;gBAAC,aAAa;;;;;;0BACxB,kMAAC,6JAAA,CAAA,UAAU;gBAAC,aAAa;;;;;;0BACzB,kMAAC,0JAAA,CAAA,mBAAgB;gBAAC,aAAa,OAAO,YAAY,OAAO;;;;;;0BAEzD,kMAAC,wKAAA,CAAA,wBAAqB;gBACpB,oBAAoB,OAAO;gBAC3B,eAAe;;;;;;0BAGjB,kMAAC,0IAAA,CAAA,SAAM;gBAAC,MAAM;gBAAe,cAAc;0BACzC,cAAA,kMAAC,0IAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,kMAAC,0IAAA,CAAA,cAAW;4BAAC,WAAU;sCAA2B;;;;;;sCAGlD,kMAAC,2KAAA,CAAA,UAAW;4BACV,WAAW;4BACX,mBAAmB,OAAO;4BAC1B,iBAAiB;4BACjB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;AAMhC"}}, {"offset": {"line": 8483, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/server/base-server.ts"], "sourcesContent": ["import type { __ApiPreviewProps } from './api-utils'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { Params } from './request/params'\nimport {\n  type FallbackRouteParams,\n  getFallbackRouteParams,\n} from './request/fallback-params'\nimport type { NextConfig, NextConfigComplete } from './config-shared'\nimport type {\n  NextParsedUrlQuery,\n  NextUrlWithParsedQuery,\n  RequestMeta,\n} from './request-meta'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from './render'\nimport type {\n  RenderOptsPartial as AppRenderOptsPartial,\n  ServerOnInstrumentationRequestError,\n} from './app-render/types'\nimport {\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ServerComponentsHmrCache,\n  type ResponseCacheBase,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n  CachedRouteKind,\n  type CachedRedirectValue,\n} from './response-cache'\nimport type { UrlWithParsedQuery } from 'url'\nimport {\n  NormalizeError,\n  DecodeError,\n  normalizeRepeatedSlashes,\n  MissingStaticPage,\n} from '../shared/lib/utils'\nimport type { PreviewData } from '../types'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport type {\n  ManifestRewriteRoute,\n  ManifestRoute,\n  PrerenderManifest,\n} from '../build'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type {\n  AppPageRouteHandlerContext,\n  AppPageRouteModule,\n} from './route-modules/app-page/module'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { AppRouteRouteHandlerContext } from './route-modules/app-route/module'\nimport type {\n  Server as HTTPServer,\n  IncomingMessage,\n  ServerResponse as HTTPServerResponse,\n} from 'http'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport type { TLSSocket } from 'tls'\nimport type { PathnameNormalizer } from './normalizers/request/pathname-normalizer'\nimport type { InstrumentationModule } from './instrumentation/types'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { formatHostname } from './lib/format-hostname'\nimport { getRedirectStatus } from '../lib/redirect-status'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_PATHS_MANIFEST,\n  NEXT_BUILTIN_DOCUMENT,\n  PAGES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport {\n  formatRevalidate,\n  type Revalidate,\n  type ExpireTime,\n} from './lib/revalidate'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage } from './utils'\nimport { isBot } from '../shared/lib/router/utils/is-bot'\nimport RenderResult from './render-result'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport * as Log from '../build/output/log'\nimport { getUtils } from './server-utils'\nimport isError, { getProperError } from '../lib/is-error'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  removeRequestMeta,\n  setRequestMeta,\n} from './request-meta'\nimport { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { getHostname } from '../shared/lib/get-hostname'\nimport { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport {\n  RSC_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_URL,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n} from '../client/components/app-router-headers'\nimport type {\n  MatchOptions,\n  RouteMatcherManager,\n} from './route-matcher-managers/route-matcher-manager'\nimport { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer'\nimport { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager'\nimport { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider'\nimport { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider'\nimport { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider'\nimport { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider'\nimport { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader'\nimport { getTracer, isBubbledError, SpanKind } from './lib/trace/tracer'\nimport { BaseServerSpan } from './lib/trace/constants'\nimport { I18NProvider } from './lib/i18n-provider'\nimport { sendResponse } from './send-response'\nimport {\n  fromNodeOutgoingHttpHeaders,\n  normalizeNextQueryParam,\n  toNodeOutgoingHttpHeaders,\n} from './web/utils'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  MATCHED_PATH_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_RESUME_HEADER,\n} from '../lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from './web/spec-extension/adapters/next-request'\nimport { matchNextDataPathname } from './lib/match-next-data-pathname'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { decodePathParams } from './lib/router-utils/decode-path-params'\nimport { RSCPathnameNormalizer } from './normalizers/request/rsc'\nimport { stripFlightHeaders } from './app-render/strip-flight-headers'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n  isPagesRouteModule,\n} from './route-modules/checks'\nimport { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc'\nimport { NextDataPathnameNormalizer } from './normalizers/request/next-data'\nimport { getIsServerAction } from './lib/server-action-request-meta'\nimport { isInterceptionRouteAppPath } from './lib/interception-routes'\nimport { toRoute } from './lib/to-route'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers'\nimport { patchSetHeaderWithCookieSupport } from './lib/patch-set-header'\nimport { checkIsAppPPREnabled } from './lib/experimental/ppr'\nimport {\n  getBuiltinRequestContext,\n  type WaitUntil,\n} from './after/builtin-request-context'\nimport { ENCODED_TAGS } from './stream-utils/encodedTags'\nimport { NextRequestHint } from './web/adapter'\nimport { getRevalidateReason } from './instrumentation/utils'\nimport { RouteKind } from './route-kind'\nimport type { RouteModule } from './route-modules/route-module'\nimport { FallbackMode, parseFallbackField } from '../lib/fallback'\nimport { toResponseCacheEntry } from './response-cache/utils'\nimport { scheduleOnNextTick } from '../lib/scheduler'\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: NextParsedUrlQuery\n}\n\nexport interface MiddlewareRoutingItem {\n  page: string\n  match: MiddlewareRouteMatch\n  matchers?: MiddlewareMatcher[]\n}\n\nexport type RouteHandler<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = (\n  req: ServerRequest,\n  res: ServerResponse,\n  parsedUrl: NextUrlWithParsedQuery\n) => PromiseLike<boolean> | boolean\n\n/**\n * The normalized route manifest is the same as the route manifest, but with\n * the rewrites normalized to the object shape that the router expects.\n */\nexport type NormalizedRouteManifest = {\n  readonly dynamicRoutes: ReadonlyArray<ManifestRoute>\n  readonly rewrites: {\n    readonly beforeFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly afterFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly fallback: ReadonlyArray<ManifestRewriteRoute>\n  }\n}\n\nexport interface Options {\n  /**\n   * Object containing the configuration next.config.js\n   */\n  conf: NextConfig\n  /**\n   * Set to false when the server was created by Next.js\n   */\n  customServer?: boolean\n  /**\n   * Tells if Next.js is running in dev mode\n   */\n  dev?: boolean\n  /**\n   * Enables the experimental testing mode.\n   */\n  experimentalTestProxy?: boolean\n\n  /**\n   * Whether or not the dev server is running in experimental HTTPS mode\n   */\n  experimentalHttpsServer?: boolean\n  /**\n   * Where the Next project is located\n   */\n  dir?: string\n  /**\n   * Tells if Next.js is at the platform-level\n   */\n  minimalMode?: boolean\n  /**\n   * Hide error messages containing server information\n   */\n  quiet?: boolean\n  /**\n   * The hostname the server is running behind\n   */\n  hostname?: string\n  /**\n   * The port the server is running behind\n   */\n  port?: number\n  /**\n   * The HTTP Server that Next.js is running behind\n   */\n  httpServer?: HTTPServer\n}\n\nexport type RenderOpts = PagesRenderOptsPartial & AppRenderOptsPartial\n\nexport type LoadedRenderOpts = RenderOpts &\n  LoadComponentsReturnType &\n  RequestLifecycleOpts\n\nexport type RequestLifecycleOpts = {\n  waitUntil: ((promise: Promise<any>) => void) | undefined\n  onClose: (callback: () => void) => void\n  onAfterTaskError: ((error: unknown) => void) | undefined\n}\n\ntype BaseRenderOpts = RenderOpts & {\n  poweredByHeader: boolean\n  generateEtags: boolean\n  previewProps: __ApiPreviewProps\n}\n\n/**\n * The public interface for rendering with the server programmatically. This\n * would typically only allow the base request or response to extend it, but\n * because this can be programmatically accessed, we assume that it could also\n * be the base Node.js request and response types.\n */\nexport interface BaseRequestHandler<\n  ServerRequest extends BaseNextRequest | IncomingMessage = BaseNextRequest,\n  ServerResponse extends\n    | BaseNextResponse\n    | HTTPServerResponse = BaseNextResponse,\n> {\n  (\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery | undefined\n  ): Promise<void> | void\n}\n\nexport type RequestContext<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = {\n  req: ServerRequest\n  res: ServerResponse\n  pathname: string\n  query: NextParsedUrlQuery\n  renderOpts: RenderOpts\n}\n\nexport class NoFallbackError extends Error {}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  type: 'html' | 'json' | 'rsc'\n  body: RenderResult\n  revalidate?: Revalidate | undefined\n}\n\nexport type NextEnabledDirectories = {\n  readonly pages: boolean\n  readonly app: boolean\n}\n\nexport default abstract class Server<\n  ServerOptions extends Options = Options,\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> {\n  public readonly hostname?: string\n  public readonly fetchHostname?: string\n  public readonly port?: number\n  protected readonly dir: string\n  protected readonly quiet: boolean\n  protected readonly nextConfig: NextConfigComplete\n  protected readonly distDir: string\n  protected readonly publicDir: string\n  protected readonly hasStaticDir: boolean\n  protected readonly pagesManifest?: PagesManifest\n  protected readonly appPathsManifest?: PagesManifest\n  protected readonly buildId: string\n  protected readonly minimalMode: boolean\n  protected readonly renderOpts: BaseRenderOpts\n  protected readonly serverOptions: Readonly<ServerOptions>\n  protected readonly appPathRoutes?: Record<string, string[]>\n  protected readonly clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  protected interceptionRoutePatterns: RegExp[]\n  protected nextFontManifest?: DeepReadonly<NextFontManifest>\n  protected instrumentation: InstrumentationModule | undefined\n  private readonly responseCache: ResponseCacheBase\n\n  protected abstract getPublicDir(): string\n  protected abstract getHasStaticDir(): boolean\n  protected abstract getPagesManifest(): PagesManifest | undefined\n  protected abstract getAppPathsManifest(): PagesManifest | undefined\n  protected abstract getBuildId(): string\n  protected abstract getinterceptionRoutePatterns(): RegExp[]\n\n  protected readonly enabledDirectories: NextEnabledDirectories\n  protected abstract getEnabledDirectories(dev: boolean): NextEnabledDirectories\n\n  protected readonly experimentalTestProxy?: boolean\n\n  protected abstract findPageComponents(params: {\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure?: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null>\n  protected abstract getPrerenderManifest(): DeepReadonly<PrerenderManifest>\n  protected abstract getNextFontManifest():\n    | DeepReadonly<NextFontManifest>\n    | undefined\n  protected abstract attachRequestMeta(\n    req: ServerRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ): void\n  protected abstract hasPage(pathname: string): Promise<boolean>\n\n  protected abstract sendRenderResult(\n    req: ServerRequest,\n    res: ServerResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      revalidate: Revalidate | undefined\n      expireTime: ExpireTime | undefined\n    }\n  ): Promise<void>\n\n  protected abstract runApi(\n    req: ServerRequest,\n    res: ServerResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean>\n\n  protected abstract renderHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult>\n\n  protected abstract getIncrementalCache(options: {\n    requestHeaders: Record<string, undefined | string | string[]>\n    requestProtocol: 'http' | 'https'\n  }): Promise<import('./lib/incremental-cache').IncrementalCache>\n\n  protected abstract getResponseCache(options: {\n    dev: boolean\n  }): ResponseCacheBase\n\n  protected getServerComponentsHmrCache():\n    | ServerComponentsHmrCache\n    | undefined {\n    return this.nextConfig.experimental.serverComponentsHmrCache\n      ? (globalThis as any).__serverComponentsHmrCache\n      : undefined\n  }\n\n  protected abstract loadEnvConfig(params: {\n    dev: boolean\n    forceReload?: boolean\n  }): void\n\n  // TODO-APP: (wyattjoh): Make protected again. Used for turbopack in route-resolver.ts right now.\n  public readonly matchers: RouteMatcherManager\n  protected readonly i18nProvider?: I18NProvider\n  protected readonly localeNormalizer?: LocaleRouteNormalizer\n\n  protected readonly normalizers: {\n    readonly rsc: RSCPathnameNormalizer | undefined\n    readonly prefetchRSC: PrefetchRSCPathnameNormalizer | undefined\n    readonly data: NextDataPathnameNormalizer | undefined\n  }\n\n  private readonly isAppPPREnabled: boolean\n\n  /**\n   * This is used to persist cache scopes across\n   * prefetch -> full route requests for dynamic IO\n   * it's only fully used in dev\n   */\n\n  public constructor(options: ServerOptions) {\n    const {\n      dir = '.',\n      quiet = false,\n      conf,\n      dev = false,\n      minimalMode = false,\n      customServer = true,\n      hostname,\n      port,\n      experimentalTestProxy,\n    } = options\n\n    this.experimentalTestProxy = experimentalTestProxy\n    this.serverOptions = options\n\n    this.dir =\n      process.env.NEXT_RUNTIME === 'edge' ? dir : require('path').resolve(dir)\n\n    this.quiet = quiet\n    this.loadEnvConfig({ dev })\n\n    // TODO: should conf be normalized to prevent missing\n    // values from causing issues as this can be user provided\n    this.nextConfig = conf as NextConfigComplete\n    this.hostname = hostname\n    if (this.hostname) {\n      // we format the hostname so that it can be fetched\n      this.fetchHostname = formatHostname(this.hostname)\n    }\n    this.port = port\n    this.distDir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? this.nextConfig.distDir\n        : require('path').join(this.dir, this.nextConfig.distDir)\n    this.publicDir = this.getPublicDir()\n    this.hasStaticDir = !minimalMode && this.getHasStaticDir()\n\n    this.i18nProvider = this.nextConfig.i18n?.locales\n      ? new I18NProvider(this.nextConfig.i18n)\n      : undefined\n\n    // Configure the locale normalizer, it's used for routes inside `pages/`.\n    this.localeNormalizer = this.i18nProvider\n      ? new LocaleRouteNormalizer(this.i18nProvider)\n      : undefined\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n    } = this.nextConfig\n\n    this.buildId = this.getBuildId()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] =\n      minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE\n\n    this.enabledDirectories = this.getEnabledDirectories(dev)\n\n    this.isAppPPREnabled =\n      this.enabledDirectories.app &&\n      checkIsAppPPREnabled(this.nextConfig.experimental.ppr)\n\n    this.normalizers = {\n      // We should normalize the pathname from the RSC prefix only in minimal\n      // mode as otherwise that route is not exposed external to the server as\n      // we instead only rely on the headers.\n      rsc:\n        this.enabledDirectories.app && this.minimalMode\n          ? new RSCPathnameNormalizer()\n          : undefined,\n      prefetchRSC:\n        this.isAppPPREnabled && this.minimalMode\n          ? new PrefetchRSCPathnameNormalizer()\n          : undefined,\n      data: this.enabledDirectories.pages\n        ? new NextDataPathnameNormalizer(this.buildId)\n        : undefined,\n    }\n\n    this.nextFontManifest = this.getNextFontManifest()\n\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n    }\n\n    this.renderOpts = {\n      supportsDynamicResponse: true,\n      trailingSlash: this.nextConfig.trailingSlash,\n      deploymentId: this.nextConfig.deploymentId,\n      strictNextHead: this.nextConfig.experimental.strictNextHead ?? true,\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      buildId: this.buildId,\n      generateEtags,\n      previewProps: this.getPrerenderManifest().preview,\n      customServer: customServer === true ? true : undefined,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: this.nextConfig.images,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      nextConfigOutput: this.nextConfig.output,\n      nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      serverComponents: this.enabledDirectories.app,\n      cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      enableTainting: this.nextConfig.experimental.taint,\n      crossOrigin: this.nextConfig.crossOrigin\n        ? this.nextConfig.crossOrigin\n        : undefined,\n      largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(publicRuntimeConfig).length > 0\n          ? publicRuntimeConfig\n          : undefined,\n\n      // @ts-expect-error internal field not publicly exposed\n      isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,\n      experimental: {\n        expireTime: this.nextConfig.expireTime,\n        clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,\n        dynamicIO: this.nextConfig.experimental.dynamicIO ?? false,\n        inlineCss: this.nextConfig.experimental.inlineCss ?? false,\n        authInterrupts: !!this.nextConfig.experimental.authInterrupts,\n      },\n      onInstrumentationRequestError:\n        this.instrumentationOnRequestError.bind(this),\n      reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,\n    }\n\n    // Initialize next/config with the environment configuration\n    setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.pagesManifest = this.getPagesManifest()\n    this.appPathsManifest = this.getAppPathsManifest()\n    this.appPathRoutes = this.getAppPathRoutes()\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // Configure the routes.\n    this.matchers = this.getRouteMatchers()\n\n    // Start route compilation. We don't wait for the routes to finish loading\n    // because we use the `waitTillReady` promise below in `handleRequest` to\n    // wait. Also we can't `await` in the constructor.\n    void this.matchers.reload()\n\n    this.setAssetPrefix(assetPrefix)\n    this.responseCache = this.getResponseCache({ dev })\n  }\n\n  protected reloadMatchers() {\n    return this.matchers.reload()\n  }\n\n  private handleRSCRequest: RouteHandler<ServerRequest, ServerResponse> = (\n    req,\n    _res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname) return false\n\n    if (this.normalizers.prefetchRSC?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n    } else if (this.normalizers.rsc?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.rsc.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a RSC request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n    } else if (req.headers['x-now-route-matches']) {\n      // If we didn't match, return with the flight headers stripped. If in\n      // minimal mode we didn't match based on the path, this can't be a RSC\n      // request. This is because Vercel only sends this header during\n      // revalidation requests and we want the cache to instead depend on the\n      // request path for flight information.\n      stripFlightHeaders(req.headers)\n\n      return false\n    } else if (req.headers[RSC_HEADER.toLowerCase()] === '1') {\n      addRequestMeta(req, 'isRSCRequest', true)\n\n      if (req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] === '1') {\n        addRequestMeta(req, 'isPrefetchRSCRequest', true)\n      }\n    } else {\n      // Otherwise just return without doing anything.\n      return false\n    }\n\n    if (req.url) {\n      const parsed = parseUrl(req.url)\n      parsed.pathname = parsedUrl.pathname\n      req.url = formatUrl(parsed)\n    }\n\n    return false\n  }\n\n  private handleNextDataRequest: RouteHandler<ServerRequest, ServerResponse> =\n    async (req, res, parsedUrl) => {\n      const middleware = this.getMiddleware()\n      const params = matchNextDataPathname(parsedUrl.pathname)\n\n      // ignore for non-next data URLs\n      if (!params || !params.path) {\n        return false\n      }\n\n      if (params.path[0] !== this.buildId) {\n        // Ignore if its a middleware request when we aren't on edge.\n        if (\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          getRequestMeta(req, 'middlewareInvoke')\n        ) {\n          return false\n        }\n\n        // Make sure to 404 if the buildId isn't correct\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // remove buildId from URL\n      params.path.shift()\n\n      const lastParam = params.path[params.path.length - 1]\n\n      // show 404 if it doesn't end with .json\n      if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // re-create page's pathname\n      let pathname = `/${params.path.join('/')}`\n      pathname = getRouteFromAssetPath(pathname, '.json')\n\n      // ensure trailing slash is normalized per config\n      if (middleware) {\n        if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {\n          pathname += '/'\n        }\n        if (\n          !this.nextConfig.trailingSlash &&\n          pathname.length > 1 &&\n          pathname.endsWith('/')\n        ) {\n          pathname = pathname.substring(0, pathname.length - 1)\n        }\n      }\n\n      if (this.i18nProvider) {\n        // Remove the port from the hostname if present.\n        const hostname = req?.headers.host?.split(':', 1)[0].toLowerCase()\n\n        const domainLocale = this.i18nProvider.detectDomainLocale(hostname)\n        const defaultLocale =\n          domainLocale?.defaultLocale ?? this.i18nProvider.config.defaultLocale\n\n        const localePathResult = this.i18nProvider.analyze(pathname)\n\n        // If the locale is detected from the path, we need to remove it\n        // from the pathname.\n        if (localePathResult.detectedLocale) {\n          pathname = localePathResult.pathname\n        }\n\n        // Update the query with the detected locale and default locale.\n        parsedUrl.query.__nextLocale = localePathResult.detectedLocale\n        parsedUrl.query.__nextDefaultLocale = defaultLocale\n\n        // If the locale is not detected from the path, we need to mark that\n        // it was not inferred from default.\n        if (!localePathResult.detectedLocale) {\n          delete parsedUrl.query.__nextInferredLocaleFromDefault\n        }\n\n        // If no locale was detected and we don't have middleware, we need\n        // to render a 404 page.\n        if (!localePathResult.detectedLocale && !middleware) {\n          parsedUrl.query.__nextLocale = defaultLocale\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n      }\n\n      parsedUrl.pathname = pathname\n      parsedUrl.query.__nextDataReq = '1'\n\n      return false\n    }\n\n  protected handleNextImageRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallRenderRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallMiddlewareRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    // Create a new manifest loader that get's the manifests from the server.\n    const manifestLoader = new ServerManifestLoader((name) => {\n      switch (name) {\n        case PAGES_MANIFEST:\n          return this.getPagesManifest() ?? null\n        case APP_PATHS_MANIFEST:\n          return this.getAppPathsManifest() ?? null\n        default:\n          return null\n      }\n    })\n\n    // Configure the matchers and handlers.\n    const matchers: RouteMatcherManager = new DefaultRouteMatcherManager()\n\n    // Match pages under `pages/`.\n    matchers.push(\n      new PagesRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // Match api routes under `pages/api/`.\n    matchers.push(\n      new PagesAPIRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // If the app directory is enabled, then add the app matchers and handlers.\n    if (this.enabledDirectories.app) {\n      // Match app pages under `app/`.\n      matchers.push(\n        new AppPageRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n      matchers.push(\n        new AppRouteRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    const [err, req, ctx] = args\n\n    if (this.instrumentation) {\n      try {\n        await this.instrumentation.onRequestError?.(\n          err,\n          {\n            path: req.url || '',\n            method: req.method || 'GET',\n            // Normalize middleware headers and other server request headers\n            headers:\n              req instanceof NextRequestHint\n                ? Object.fromEntries(req.headers.entries())\n                : req.headers,\n          },\n          ctx\n        )\n      } catch (handlerErr) {\n        // Log the soft error and continue, since errors can thrown from react stream handler\n        console.error('Error in instrumentation.onRequestError:', handlerErr)\n      }\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    Log.error(err)\n  }\n\n  public async handleRequest(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    await this.prepare()\n    const method = req.method.toUpperCase()\n\n    const tracer = getTracer()\n    return tracer.withPropagatedContext(req.headers, () => {\n      return tracer.trace(\n        BaseServerSpan.handleRequest,\n        {\n          spanName: `${method} ${req.url}`,\n          kind: SpanKind.SERVER,\n          attributes: {\n            'http.method': method,\n            'http.target': req.url,\n          },\n        },\n        async (span) =>\n          this.handleRequestImpl(req, res, parsedUrl).finally(() => {\n            if (!span) return\n\n            const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n            span.setAttributes({\n              'http.status_code': res.statusCode,\n              'next.rsc': isRSCRequest,\n            })\n\n            const rootSpanAttributes = tracer.getRootSpanAttributes()\n            // We were unable to get attributes, probably OTEL is not enabled\n            if (!rootSpanAttributes) return\n\n            if (\n              rootSpanAttributes.get('next.span_type') !==\n              BaseServerSpan.handleRequest\n            ) {\n              console.warn(\n                `Unexpected root span type '${rootSpanAttributes.get(\n                  'next.span_type'\n                )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n              )\n              return\n            }\n\n            const route = rootSpanAttributes.get('next.route')\n            if (route) {\n              const name = isRSCRequest\n                ? `RSC ${method} ${route}`\n                : `${method} ${route}`\n\n              span.setAttributes({\n                'next.route': route,\n                'http.route': route,\n                'next.span_name': name,\n              })\n              span.updateName(name)\n            } else {\n              span.updateName(\n                isRSCRequest\n                  ? `RSC ${method} ${req.url}`\n                  : `${method} ${req.url}`\n              )\n            }\n          })\n      )\n    })\n  }\n\n  private async handleRequestImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      // Wait for the matchers to be ready.\n      await this.matchers.waitTillReady()\n\n      // ensure cookies set in middleware are merged and\n      // not overridden by API routes/getServerSideProps\n      patchSetHeaderWithCookieSupport(\n        req,\n        isNodeNextResponse(res) ? res.originalResponse : res\n      )\n\n      const urlParts = (req.url || '').split('?', 1)\n      const urlNoQuery = urlParts[0]\n\n      // this normalizes repeated slashes in the path e.g. hello//world ->\n      // hello/world or backslashes to forward slashes, this does not\n      // handle trailing slash as that is handled the same as a next.config.js\n      // redirect\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.redirect(cleanUrl, 308).body(cleanUrl).send()\n        return\n      }\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        if (!req.url) {\n          throw new Error('Invariant: url can not be undefined')\n        }\n\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (!parsedUrl.pathname) {\n        throw new Error(\"Invariant: pathname can't be empty\")\n      }\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = Object.fromEntries(\n          new URLSearchParams(parsedUrl.query)\n        )\n      }\n\n      // Update the `x-forwarded-*` headers.\n      const { originalRequest = null } = isNodeNextRequest(req) ? req : {}\n      const xForwardedProto = originalRequest?.headers['x-forwarded-proto']\n      const isHttps = xForwardedProto\n        ? xForwardedProto === 'https'\n        : !!(originalRequest?.socket as TLSSocket)?.encrypted\n\n      req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname\n      req.headers['x-forwarded-port'] ??= this.port\n        ? this.port.toString()\n        : isHttps\n          ? '443'\n          : '80'\n      req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http'\n      req.headers['x-forwarded-for'] ??= originalRequest?.socket?.remoteAddress\n\n      // Validate that if i18n isn't configured or the passed parameters are not\n      // valid it should be removed from the query.\n      if (!this.i18nProvider?.validateQuery(parsedUrl.query)) {\n        delete parsedUrl.query.__nextLocale\n        delete parsedUrl.query.__nextDefaultLocale\n        delete parsedUrl.query.__nextInferredLocaleFromDefault\n      }\n\n      // This should be done before any normalization of the pathname happens as\n      // it captures the initial URL.\n      this.attachRequestMeta(req, parsedUrl)\n\n      let finished = await this.handleRSCRequest(req, res, parsedUrl)\n      if (finished) return\n\n      const domainLocale = this.i18nProvider?.detectDomainLocale(\n        getHostname(parsedUrl, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || this.nextConfig.i18n?.defaultLocale\n      parsedUrl.query.__nextDefaultLocale = defaultLocale\n\n      const url = parseUrlUtil(req.url.replace(/^\\/+/, '/'))\n      const pathnameInfo = getNextPathnameInfo(url.pathname, {\n        nextConfig: this.nextConfig,\n        i18nProvider: this.i18nProvider,\n      })\n      url.pathname = pathnameInfo.pathname\n\n      if (pathnameInfo.basePath) {\n        req.url = removePathPrefix(req.url!, this.nextConfig.basePath)\n      }\n\n      const useMatchedPathHeader =\n        this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string'\n\n      // TODO: merge handling with invokePath\n      if (useMatchedPathHeader) {\n        try {\n          if (this.enabledDirectories.app) {\n            // ensure /index path is normalized for prerender\n            // in minimal mode\n            if (req.url.match(/^\\/index($|\\?)/)) {\n              req.url = req.url.replace(/^\\/index/, '/')\n            }\n            parsedUrl.pathname =\n              parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname\n          }\n\n          // x-matched-path is the source of truth, it tells what page\n          // should be rendered because we don't process rewrites in minimalMode\n          let { pathname: matchedPath } = new URL(\n            req.headers[MATCHED_PATH_HEADER] as string,\n            'http://localhost'\n          )\n\n          let { pathname: urlPathname } = new URL(req.url, 'http://localhost')\n\n          // For ISR the URL is normalized to the prerenderPath so if\n          // it's a data request the URL path will be the data URL,\n          // basePath is already stripped by this point\n          if (this.normalizers.data?.match(urlPathname)) {\n            parsedUrl.query.__nextDataReq = '1'\n          }\n          // In minimal mode, if PPR is enabled, then we should check to see if\n          // the request should be a resume request.\n          else if (\n            this.isAppPPREnabled &&\n            this.minimalMode &&\n            req.headers[NEXT_RESUME_HEADER] === '1' &&\n            req.method === 'POST'\n          ) {\n            // Decode the postponed state from the request body, it will come as\n            // an array of buffers, so collect them and then concat them to form\n            // the string.\n            const body: Array<Buffer> = []\n            for await (const chunk of req.body) {\n              body.push(chunk)\n            }\n            const postponed = Buffer.concat(body).toString('utf8')\n\n            addRequestMeta(req, 'postponed', postponed)\n          }\n\n          matchedPath = this.normalize(matchedPath)\n          const normalizedUrlPath = this.stripNextDataPath(urlPathname)\n\n          // Perform locale detection and normalization.\n          const localeAnalysisResult = this.i18nProvider?.analyze(matchedPath, {\n            defaultLocale,\n          })\n\n          // The locale result will be defined even if the locale was not\n          // detected for the request because it will be inferred from the\n          // default locale.\n          if (localeAnalysisResult) {\n            parsedUrl.query.__nextLocale = localeAnalysisResult.detectedLocale\n\n            // If the detected locale was inferred from the default locale, we\n            // need to modify the metadata on the request to indicate that.\n            if (localeAnalysisResult.inferredFromDefault) {\n              parsedUrl.query.__nextInferredLocaleFromDefault = '1'\n            } else {\n              delete parsedUrl.query.__nextInferredLocaleFromDefault\n            }\n          }\n\n          // TODO: check if this is needed any more?\n          matchedPath = denormalizePagePath(matchedPath)\n\n          let srcPathname = matchedPath\n          let pageIsDynamic = isDynamicRoute(srcPathname)\n\n          if (!pageIsDynamic) {\n            const match = await this.matchers.match(srcPathname, {\n              i18n: localeAnalysisResult,\n            })\n\n            // Update the source pathname to the matched page's pathname.\n            if (match) {\n              srcPathname = match.definition.pathname\n              // The page is dynamic if the params are defined.\n              pageIsDynamic = typeof match.params !== 'undefined'\n            }\n          }\n\n          // The rest of this function can't handle i18n properly, so ensure we\n          // restore the pathname with the locale information stripped from it\n          // now that we're done matching if we're using i18n.\n          if (localeAnalysisResult) {\n            matchedPath = localeAnalysisResult.pathname\n          }\n\n          const utils = getUtils({\n            pageIsDynamic,\n            page: srcPathname,\n            i18n: this.nextConfig.i18n,\n            basePath: this.nextConfig.basePath,\n            rewrites: this.getRoutesManifest()?.rewrites || {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes,\n          })\n\n          // Ensure parsedUrl.pathname includes locale before processing\n          // rewrites or they won't match correctly.\n          if (defaultLocale && !pathnameInfo.locale) {\n            parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`\n          }\n\n          const pathnameBeforeRewrite = parsedUrl.pathname\n          const rewriteParams = utils.handleRewrites(req, parsedUrl)\n          const rewriteParamKeys = Object.keys(rewriteParams)\n          const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname\n\n          if (didRewrite && parsedUrl.pathname) {\n            addRequestMeta(req, 'rewroteURL', parsedUrl.pathname)\n          }\n          const routeParamKeys = new Set<string>()\n\n          for (const key of Object.keys(parsedUrl.query)) {\n            const value = parsedUrl.query[key]\n\n            normalizeNextQueryParam(key, (normalizedKey) => {\n              if (!parsedUrl) return // typeguard\n\n              parsedUrl.query[normalizedKey] = value\n              routeParamKeys.add(normalizedKey)\n              delete parsedUrl.query[key]\n            })\n          }\n\n          // interpolate dynamic params and normalize URL if needed\n          if (pageIsDynamic) {\n            let params: ParsedUrlQuery | false = {}\n\n            let paramsResult = utils.normalizeDynamicRouteParams(\n              parsedUrl.query\n            )\n\n            // for prerendered ISR paths we attempt parsing the route\n            // params from the URL directly as route-matches may not\n            // contain the correct values due to the filesystem path\n            // matching before the dynamic route has been matched\n            if (\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(normalizedUrlPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(normalizedUrlPath)\n\n              if (matcherParams) {\n                utils.normalizeDynamicRouteParams(matcherParams)\n                Object.assign(paramsResult.params, matcherParams)\n                paramsResult.hasValidParams = true\n              }\n            }\n\n            // if an action request is bypassing a prerender and we\n            // don't have the params in the URL since it was prerendered\n            // and matched during handle: 'filesystem' rather than dynamic route\n            // resolving we need to parse the params from the matched-path.\n            // Note: this is similar to above case but from match-path instead\n            // of from the request URL since a rewrite could cause that to not\n            // match the src pathname\n            if (\n              // we can have a collision with /index and a top-level /[slug]\n              matchedPath !== '/index' &&\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(matchedPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(matchedPath)\n\n              if (matcherParams) {\n                const curParamsResult =\n                  utils.normalizeDynamicRouteParams(matcherParams)\n\n                if (curParamsResult.hasValidParams) {\n                  Object.assign(params, matcherParams)\n                  paramsResult = curParamsResult\n                }\n              }\n            }\n\n            if (paramsResult.hasValidParams) {\n              params = paramsResult.params\n            }\n\n            if (\n              req.headers['x-now-route-matches'] &&\n              isDynamicRoute(matchedPath) &&\n              !paramsResult.hasValidParams\n            ) {\n              const opts: Record<string, string> = {}\n              const routeParams = utils.getParamsFromRouteMatches(\n                req,\n                opts,\n                parsedUrl.query.__nextLocale || ''\n              )\n\n              // If this returns a locale, it means that the locale was detected\n              // from the pathname.\n              if (opts.locale) {\n                parsedUrl.query.__nextLocale = opts.locale\n\n                // As the locale was parsed from the pathname, we should mark\n                // that the locale was not inferred as the default.\n                delete parsedUrl.query.__nextInferredLocaleFromDefault\n              }\n              paramsResult = utils.normalizeDynamicRouteParams(\n                routeParams,\n                true\n              )\n\n              if (paramsResult.hasValidParams) {\n                params = paramsResult.params\n              }\n            }\n\n            // handle the actual dynamic route name being requested\n            if (\n              utils.defaultRouteMatches &&\n              normalizedUrlPath === srcPathname &&\n              !paramsResult.hasValidParams &&\n              !utils.normalizeDynamicRouteParams({ ...params }, true)\n                .hasValidParams\n            ) {\n              params = utils.defaultRouteMatches\n\n              // Mark that the default route matches were set on the request\n              // during routing.\n              addRequestMeta(req, 'didSetDefaultRouteMatches', true)\n            }\n\n            if (params) {\n              matchedPath = utils.interpolateDynamicPath(srcPathname, params)\n              req.url = utils.interpolateDynamicPath(req.url!, params)\n            }\n          }\n\n          if (pageIsDynamic || didRewrite) {\n            utils.normalizeVercelUrl(req, true, [\n              ...rewriteParamKeys,\n              ...Object.keys(utils.defaultRouteRegex?.groups || {}),\n            ])\n          }\n          for (const key of routeParamKeys) {\n            delete parsedUrl.query[key]\n          }\n          parsedUrl.pathname = matchedPath\n          url.pathname = parsedUrl.pathname\n          finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n          if (finished) return\n        } catch (err) {\n          if (err instanceof DecodeError || err instanceof NormalizeError) {\n            res.statusCode = 400\n            return this.renderError(null, req, res, '/_error', {})\n          }\n          throw err\n        }\n      }\n\n      addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n      if (pathnameInfo.locale) {\n        req.url = formatUrl(url)\n        addRequestMeta(req, 'didStripLocale', true)\n      }\n\n      // If we aren't in minimal mode or there is no locale in the query\n      // string, add the locale to the query string.\n      if (!this.minimalMode || !parsedUrl.query.__nextLocale) {\n        // If the locale is in the pathname, add it to the query string.\n        if (pathnameInfo.locale) {\n          parsedUrl.query.__nextLocale = pathnameInfo.locale\n        }\n        // If the default locale is available, add it to the query string and\n        // mark it as inferred rather than implicit.\n        else if (defaultLocale) {\n          parsedUrl.query.__nextLocale = defaultLocale\n          parsedUrl.query.__nextInferredLocaleFromDefault = '1'\n        }\n      }\n\n      // set incremental cache to request meta so it can\n      // be passed down for edge functions and the fetch disk\n      // cache can be leveraged locally\n      if (\n        !(this.serverOptions as any).webServerConfig &&\n        !getRequestMeta(req, 'incrementalCache')\n      ) {\n        let protocol: 'http:' | 'https:' = 'https:'\n\n        try {\n          const parsedFullUrl = new URL(\n            getRequestMeta(req, 'initURL') || '/',\n            'http://n'\n          )\n          protocol = parsedFullUrl.protocol as 'https:' | 'http:'\n        } catch {}\n\n        const incrementalCache = await this.getIncrementalCache({\n          requestHeaders: Object.assign({}, req.headers),\n          requestProtocol: protocol.substring(0, protocol.length - 1) as\n            | 'http'\n            | 'https',\n        })\n\n        const _globalThis: typeof globalThis & {\n          __nextCacheHandlers?: Record<\n            string,\n            import('./lib/cache-handlers/types').CacheHandler\n          >\n        } = globalThis\n\n        if (_globalThis.__nextCacheHandlers) {\n          const expiredTags: string[] =\n            (req.headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER] as string)?.split(\n              ','\n            ) || []\n\n          for (const handler of Object.values(\n            _globalThis.__nextCacheHandlers\n          )) {\n            if (typeof handler.receiveExpiredTags === 'function') {\n              await handler.receiveExpiredTags(...expiredTags)\n            }\n          }\n        }\n\n        incrementalCache.resetRequestCache()\n        addRequestMeta(req, 'incrementalCache', incrementalCache)\n        ;(globalThis as any).__incrementalCache = incrementalCache\n      }\n\n      // set server components HMR cache to request meta so it can be passed\n      // down for edge functions\n      if (!getRequestMeta(req, 'serverComponentsHmrCache')) {\n        addRequestMeta(\n          req,\n          'serverComponentsHmrCache',\n          this.getServerComponentsHmrCache()\n        )\n      }\n\n      // when invokePath is specified we can short short circuit resolving\n      // we only honor this header if we are inside of a render worker to\n      // prevent external users coercing the routing path\n      const invokePath = getRequestMeta(req, 'invokePath')\n      const useInvokePath =\n        !useMatchedPathHeader &&\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        invokePath\n\n      if (useInvokePath) {\n        const invokeStatus = getRequestMeta(req, 'invokeStatus')\n        if (invokeStatus) {\n          const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n          if (invokeQuery) {\n            Object.assign(parsedUrl.query, invokeQuery)\n          }\n\n          res.statusCode = invokeStatus\n          let err: Error | null = getRequestMeta(req, 'invokeError') || null\n\n          return this.renderError(err, req, res, '/_error', parsedUrl.query)\n        }\n\n        const parsedMatchedPath = new URL(invokePath || '/', 'http://n')\n        const invokePathnameInfo = getNextPathnameInfo(\n          parsedMatchedPath.pathname,\n          {\n            nextConfig: this.nextConfig,\n            parseData: false,\n          }\n        )\n\n        if (invokePathnameInfo.locale) {\n          parsedUrl.query.__nextLocale = invokePathnameInfo.locale\n        }\n\n        if (parsedUrl.pathname !== parsedMatchedPath.pathname) {\n          parsedUrl.pathname = parsedMatchedPath.pathname\n          addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname)\n        }\n        const normalizeResult = normalizeLocalePath(\n          removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''),\n          this.nextConfig.i18n?.locales || []\n        )\n\n        if (normalizeResult.detectedLocale) {\n          parsedUrl.query.__nextLocale = normalizeResult.detectedLocale\n        }\n        parsedUrl.pathname = normalizeResult.pathname\n\n        for (const key of Object.keys(parsedUrl.query)) {\n          if (!key.startsWith('__next') && !key.startsWith('_next')) {\n            delete parsedUrl.query[key]\n          }\n        }\n        const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n        if (invokeQuery) {\n          Object.assign(parsedUrl.query, invokeQuery)\n        }\n\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        await this.handleCatchallRenderRequest(req, res, parsedUrl)\n        return\n      }\n\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        getRequestMeta(req, 'middlewareInvoke')\n      ) {\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        finished = await this.handleCatchallMiddlewareRequest(\n          req,\n          res,\n          parsedUrl\n        )\n        if (finished) return\n\n        const err = new Error()\n        ;(err as any).result = {\n          response: new Response(null, {\n            headers: {\n              'x-middleware-next': '1',\n            },\n          }),\n        }\n        ;(err as any).bubble = true\n        throw err\n      }\n\n      // This wasn't a request via the matched path or the invoke path, so\n      // prepare for a legacy run by removing the base path.\n\n      // ensure we strip the basePath when not using an invoke header\n      if (!useMatchedPathHeader && pathnameInfo.basePath) {\n        parsedUrl.pathname = removePathPrefix(\n          parsedUrl.pathname,\n          pathnameInfo.basePath\n        )\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError ||\n        err instanceof NormalizeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (\n        this.minimalMode ||\n        this.renderOpts.dev ||\n        (isBubbledError(err) && err.bubble)\n      ) {\n        throw err\n      }\n      this.logError(getProperError(err))\n      res.statusCode = 500\n      res.body('Internal Server Error').send()\n    }\n  }\n\n  /**\n   * Normalizes a pathname without attaching any metadata from any matched\n   * normalizer.\n   *\n   * @param pathname the pathname to normalize\n   * @returns the normalized pathname\n   */\n  private normalize = (pathname: string) => {\n    const normalizers: Array<PathnameNormalizer> = []\n\n    if (this.normalizers.data) {\n      normalizers.push(this.normalizers.data)\n    }\n\n    // We have to put the prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.prefetchRSC) {\n      normalizers.push(this.normalizers.prefetchRSC)\n    }\n\n    if (this.normalizers.rsc) {\n      normalizers.push(this.normalizers.rsc)\n    }\n\n    for (const normalizer of normalizers) {\n      if (!normalizer.match(pathname)) continue\n\n      return normalizer.normalize(pathname, true)\n    }\n\n    return pathname\n  }\n\n  private normalizeAndAttachMetadata: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = async (req, res, url) => {\n    let finished = await this.handleNextImageRequest(req, res, url)\n    if (finished) return true\n\n    if (this.enabledDirectories.pages) {\n      finished = await this.handleNextDataRequest(req, res, url)\n      if (finished) return true\n    }\n\n    return false\n  }\n\n  /**\n   * @internal - this method is internal to Next.js and should not be used directly by end-users\n   */\n  public getRequestHandlerWithMetadata(\n    meta: RequestMeta\n  ): BaseRequestHandler<ServerRequest, ServerResponse> {\n    const handler = this.getRequestHandler()\n    return (req, res, parsedUrl) => {\n      setRequestMeta(req, meta)\n      return handler(req, res, parsedUrl)\n    }\n  }\n\n  public getRequestHandler(): BaseRequestHandler<\n    ServerRequest,\n    ServerResponse\n  > {\n    return this.handleRequest.bind(this)\n  }\n\n  protected abstract handleUpgrade(\n    req: ServerRequest,\n    socket: any,\n    head?: any\n  ): Promise<void>\n\n  public setAssetPrefix(prefix?: string): void {\n    this.renderOpts.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n  }\n\n  protected prepared: boolean = false\n  protected preparedPromise: Promise<void> | null = null\n  /**\n   * Runs async initialization of server.\n   * It is idempotent, won't fire underlying initialization more than once.\n   */\n  public async prepare(): Promise<void> {\n    if (this.prepared) return\n\n    if (this.preparedPromise === null) {\n      // Get instrumentation module\n      this.instrumentation = await this.loadInstrumentationModule()\n      this.preparedPromise = this.prepareImpl().then(() => {\n        this.prepared = true\n        this.preparedPromise = null\n      })\n    }\n    return this.preparedPromise\n  }\n  protected async prepareImpl(): Promise<void> {}\n  protected async loadInstrumentationModule(): Promise<any> {}\n\n  public async close(): Promise<void> {}\n\n  protected getAppPathRoutes(): Record<string, string[]> {\n    const appPathRoutes: Record<string, string[]> = {}\n\n    Object.keys(this.appPathsManifest || {}).forEach((entry) => {\n      const normalizedPath = normalizeAppPath(entry)\n      if (!appPathRoutes[normalizedPath]) {\n        appPathRoutes[normalizedPath] = []\n      }\n      appPathRoutes[normalizedPath].push(entry)\n    })\n    return appPathRoutes\n  }\n\n  protected async run(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.run, async () =>\n      this.runImpl(req, res, parsedUrl)\n    )\n  }\n\n  private async runImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.handleCatchallRenderRequest(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.pipe, async () =>\n      this.pipeImpl(fn, partialContext)\n    )\n  }\n\n  private async pipeImpl(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    const isBotRequest = isBot(partialContext.req.headers['user-agent'] || '')\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: !isBotRequest,\n        isBot: !!isBotRequest,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const originalStatus = res.statusCode\n    const { body, type } = payload\n    let { revalidate } = payload\n    if (!res.sent) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n\n      // In dev, we should not cache pages for any reason.\n      if (dev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        revalidate = undefined\n      }\n\n      await this.sendRenderResult(req, res, {\n        result: body,\n        type,\n        generateEtags,\n        poweredByHeader,\n        revalidate,\n        expireTime: this.nextConfig.expireTime,\n      })\n      res.statusCode = originalStatus\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<string | null> {\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: false,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return null\n    }\n    return payload.body.toUnchunkedString()\n  }\n\n  public async render(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.render, async () =>\n      this.renderImpl(req, res, pathname, query, parsedUrl, internalRender)\n    )\n  }\n\n  protected getWaitUntil(): WaitUntil | undefined {\n    const builtinRequestContext = getBuiltinRequestContext()\n    if (builtinRequestContext) {\n      // the platform provided a request context.\n      // use the `waitUntil` from there, whether actually present or not --\n      // if not present, `after` will error.\n\n      // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.\n      return builtinRequestContext.waitUntil\n    }\n\n    if (this.minimalMode) {\n      // we're built for a serverless environment, and `waitUntil` is not available,\n      // but using a noop would likely lead to incorrect behavior,\n      // because we have no way of keeping the invocation alive.\n      // return nothing, and `after` will error if used.\n      //\n      // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.\n      //\n      // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using \"@next/request-context\",\n      // so we won't get here.\n      return undefined\n    }\n\n    return this.getInternalWaitUntil()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil | undefined {\n    return undefined\n  }\n\n  private async renderImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.renderOpts.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !internalRender &&\n      !this.minimalMode &&\n      !query.__nextDataReq &&\n      (req.url?.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && req.url!.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getStaticPaths({\n    pathname,\n  }: {\n    pathname: string\n    requestHeaders: import('./lib/incremental-cache').IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname]?.fallback\n\n    return {\n      // `staticPaths` is intentionally set to `undefined` as it should've\n      // been caught when checking disk data.\n      staticPaths: undefined,\n      fallbackMode: parseFallbackField(fallbackField),\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    requestContext: RequestContext<ServerRequest, ServerResponse>,\n    findComponentsResult: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponseWithComponents,\n      async () =>\n        this.renderToResponseWithComponentsImpl(\n          requestContext,\n          findComponentsResult\n        )\n    )\n  }\n\n  protected pathCouldBeIntercepted(resolvedPathname: string): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      this.interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  protected setVaryHeader(\n    req: ServerRequest,\n    res: ServerResponse,\n    isAppPath: boolean,\n    resolvedPathname: string\n  ): void {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    let addedNextUrlToVary = false\n\n    if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      res.setHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`)\n      addedNextUrlToVary = true\n    } else if (isAppPath || isRSCRequest) {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      res.setHeader('vary', baseVaryHeader)\n    }\n\n    if (!addedNextUrlToVary) {\n      // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.\n      // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.\n      delete req.headers[NEXT_URL]\n    }\n  }\n\n  private async renderToResponseWithComponentsImpl(\n    {\n      req,\n      res,\n      pathname,\n      renderOpts: opts,\n    }: RequestContext<ServerRequest, ServerResponse>,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    const isErrorPathname = pathname === '/_error'\n    const is404Page =\n      pathname === '/404' || (isErrorPathname && res.statusCode === 404)\n    const is500Page =\n      pathname === '/500' || (isErrorPathname && res.statusCode === 500)\n    const isAppPath = components.isAppPath === true\n\n    const hasServerProps = !!components.getServerSideProps\n    let hasGetStaticPaths = !!components.getStaticPaths\n    const isServerAction = getIsServerAction(req)\n    const hasGetInitialProps = !!components.Component?.getInitialProps\n    let isSSG = !!components.getStaticProps\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname\n\n    this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname)\n\n    let staticPaths: string[] | undefined\n    let fallbackMode: FallbackMode | undefined\n    let hasFallback = false\n\n    const isDynamic = isDynamicRoute(components.page)\n\n    const prerenderManifest = this.getPrerenderManifest()\n\n    if (isAppPath && isDynamic) {\n      const pathsResult = await this.getStaticPaths({\n        pathname,\n        page: components.page,\n        isAppPath,\n        requestHeaders: req.headers,\n      })\n\n      staticPaths = pathsResult.staticPaths\n      fallbackMode = pathsResult.fallbackMode\n      hasFallback = typeof fallbackMode !== 'undefined'\n\n      if (this.nextConfig.output === 'export') {\n        const page = components.page\n        if (!staticPaths) {\n          throw new Error(\n            `Page \"${page}\" is missing exported function \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n\n        const resolvedWithoutSlash = removeTrailingSlash(resolvedUrlPathname)\n        if (!staticPaths.includes(resolvedWithoutSlash)) {\n          throw new Error(\n            `Page \"${page}\" is missing param \"${resolvedWithoutSlash}\" in \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n      }\n\n      if (hasFallback) {\n        hasGetStaticPaths = true\n      }\n    }\n\n    if (\n      hasFallback ||\n      staticPaths?.includes(resolvedUrlPathname) ||\n      // this signals revalidation in deploy environments\n      // TODO: make this more generic\n      req.headers['x-now-route-matches']\n    ) {\n      isSSG = true\n    } else if (!this.renderOpts.dev) {\n      isSSG ||= !!prerenderManifest.routes[toRoute(pathname)]\n    }\n\n    // Toggle whether or not this is a Data request\n    const isNextDataRequest =\n      !!(\n        query.__nextDataReq ||\n        (req.headers['x-nextjs-data'] &&\n          (this.serverOptions as any).webServerConfig)\n      ) &&\n      (isSSG || hasServerProps)\n\n    /**\n     * If true, this indicates that the request being made is for an app\n     * prefetch request.\n     */\n    const isPrefetchRSCRequest =\n      getRequestMeta(req, 'isPrefetchRSCRequest') ?? false\n\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    // when we are handling a middleware prefetch and it doesn't\n    // resolve to a static data route we bail early to avoid\n    // unexpected SSR invocations\n    if (\n      !isSSG &&\n      req.headers['x-middleware-prefetch'] &&\n      !(is404Page || pathname === '/_error')\n    ) {\n      res.setHeader(MATCHED_PATH_HEADER, pathname)\n      res.setHeader('x-middleware-skip', '1')\n      res.setHeader(\n        'cache-control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n      res.body('{}').send()\n      return null\n    }\n\n    delete query.__nextDataReq\n\n    // normalize req.url for SSG paths as it is not exposed\n    // to getStaticProps and the asPath should not expose /_next/data\n    if (\n      isSSG &&\n      this.minimalMode &&\n      req.headers[MATCHED_PATH_HEADER] &&\n      req.url.startsWith('/_next/data')\n    ) {\n      req.url = this.stripNextDataPath(req.url)\n    }\n\n    if (\n      !!req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${query.__nextLocale ? `/${query.__nextLocale}` : ''}${pathname}`\n      )\n    }\n\n    let routeModule: RouteModule | undefined\n    if (components.routeModule) {\n      routeModule = components.routeModule\n    }\n\n    /**\n     * If the route being rendered is an app page, and the ppr feature has been\n     * enabled, then the given route _could_ support PPR.\n     */\n    const couldSupportPPR: boolean =\n      this.isAppPPREnabled &&\n      typeof routeModule !== 'undefined' &&\n      isAppPageRouteModule(routeModule)\n\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n      typeof query.__nextppronly !== 'undefined' &&\n      couldSupportPPR\n\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery =\n      hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled: boolean =\n      couldSupportPPR &&\n      ((\n        prerenderManifest.routes[pathname] ??\n        prerenderManifest.dynamicRoutes[pathname]\n      )?.renderingMode === 'PARTIALLY_STATIC' ||\n        // Ideally we'd want to check the appConfig to see if this page has PPR\n        // enabled or not, but that would require plumbing the appConfig through\n        // to the server during development. We assume that the page supports it\n        // but only during development.\n        (hasDebugStaticShellQuery &&\n          (this.renderOpts.dev === true ||\n            this.experimentalTestProxy === true)))\n\n    const isDebugStaticShell: boolean =\n      hasDebugStaticShellQuery && isRoutePPREnabled\n\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses =\n      isDebugStaticShell && this.renderOpts.dev === true\n\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled\n      ? getRequestMeta(req, 'postponed')\n      : undefined\n\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest =\n      isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader =\n      req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()]\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isNextDataRequest && !isRSCRequest) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.slice(1), 10)\n    }\n\n    if (\n      // Server actions can use non-GET/HEAD methods.\n      !isServerAction &&\n      // Resume can use non-GET/HEAD methods.\n      !minimalPostponed &&\n      !is404Page &&\n      !is500Page &&\n      pathname !== '/_error' &&\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      (typeof components.Component === 'string' || isSSG)\n    ) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      await this.renderError(null, req, res, pathname)\n      return null\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        type: 'html',\n        // TODO: Static pages should be serialized as RenderResult\n        body: RenderResult.fromStatic(components.Component),\n      }\n    }\n\n    // Ensure that if the `amp` query parameter is falsy that we remove it from\n    // the query object. This ensures it won't be found by the `in` operator.\n    if ('amp' in query && !query.amp) delete query.amp\n\n    if (opts.supportsDynamicResponse === true) {\n      const isBotRequest = isBot(req.headers['user-agent'] || '')\n      const isSupportedDocument =\n        typeof components.Document?.getInitialProps !== 'function' ||\n        // The built-in `Document` component also supports dynamic HTML for concurrent mode.\n        NEXT_BUILTIN_DOCUMENT in components.Document\n\n      // Disable dynamic HTML in cases that we know it won't be generated,\n      // so that we can continue generating a cache key when possible.\n      // TODO-APP: should the first render for a dynamic app path\n      // be static so we can collect revalidate and populate the\n      // cache if there are no dynamic data requirements\n      opts.supportsDynamicResponse =\n        !isSSG && !isBotRequest && !query.amp && isSupportedDocument\n      opts.isBot = isBotRequest\n    }\n\n    // In development, we always want to generate dynamic HTML.\n    if (!isNextDataRequest && isAppPath && opts.dev) {\n      opts.supportsDynamicResponse = true\n    }\n\n    const defaultLocale = isSSG\n      ? this.nextConfig.i18n?.defaultLocale\n      : query.__nextDefaultLocale\n\n    const locale = query.__nextLocale\n    const locales = this.nextConfig.i18n?.locales\n\n    let previewData: PreviewData\n    let isPreviewMode = false\n\n    if (hasServerProps || isSSG || isAppPath) {\n      // For the edge runtime, we don't support preview mode in SSG.\n      if (process.env.NEXT_RUNTIME !== 'edge') {\n        const { tryGetPreviewData } =\n          require('./api-utils/node/try-get-preview-data') as typeof import('./api-utils/node/try-get-preview-data')\n        previewData = tryGetPreviewData(\n          req,\n          res,\n          this.renderOpts.previewProps,\n          !!this.nextConfig.experimental.multiZoneDraftMode\n        )\n        isPreviewMode = previewData !== false\n      }\n    }\n\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (\n      isAppPath &&\n      !opts.dev &&\n      !isPreviewMode &&\n      isSSG &&\n      isRSCRequest &&\n      !isDynamicRSCRequest &&\n      (!isEdgeRuntime(opts.runtime) ||\n        (this.serverOptions as any).webServerConfig)\n    ) {\n      stripFlightHeaders(req.headers)\n    }\n\n    let isOnDemandRevalidate = false\n    let revalidateOnlyGenerated = false\n\n    if (isSSG) {\n      ;({ isOnDemandRevalidate, revalidateOnlyGenerated } =\n        checkIsOnDemandRevalidate(req, this.renderOpts.previewProps))\n    }\n\n    if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {\n      // the url value is already correct when the matched-path header is set\n      resolvedUrlPathname = urlPathname\n    }\n\n    urlPathname = removeTrailingSlash(urlPathname)\n    resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname)\n    if (this.localeNormalizer) {\n      resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname)\n    }\n\n    const handleRedirect = (pageData: any) => {\n      const redirect = {\n        destination: pageData.pageProps.__N_REDIRECT,\n        statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n        basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n      }\n      const statusCode = getRedirectStatus(redirect)\n      const { basePath } = this.nextConfig\n\n      if (\n        basePath &&\n        redirect.basePath !== false &&\n        redirect.destination.startsWith('/')\n      ) {\n        redirect.destination = `${basePath}${redirect.destination}`\n      }\n\n      if (redirect.destination.startsWith('/')) {\n        redirect.destination = normalizeRepeatedSlashes(redirect.destination)\n      }\n\n      res\n        .redirect(redirect.destination, statusCode)\n        .body(redirect.destination)\n        .send()\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isNextDataRequest) {\n      resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname)\n      urlPathname = this.stripNextDataPath(urlPathname)\n    }\n\n    let ssgCacheKey: string | null = null\n    if (\n      !isPreviewMode &&\n      isSSG &&\n      !opts.supportsDynamicResponse &&\n      !isServerAction &&\n      !minimalPostponed &&\n      !isDynamicRSCRequest\n    ) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${\n        (pathname === '/' || resolvedUrlPathname === '/') && locale\n          ? ''\n          : resolvedUrlPathname\n      }${query.amp ? '.amp' : ''}`\n    }\n\n    if ((is404Page || is500Page) && isSSG) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${pathname}${\n        query.amp ? '.amp' : ''\n      }`\n    }\n\n    if (ssgCacheKey) {\n      ssgCacheKey = decodePathParams(ssgCacheKey)\n\n      // ensure /index and / is normalized to one key\n      ssgCacheKey =\n        ssgCacheKey === '/index' && pathname === '/' ? '/' : ssgCacheKey\n    }\n    let protocol: 'http:' | 'https:' = 'https:'\n\n    try {\n      const parsedFullUrl = new URL(\n        getRequestMeta(req, 'initURL') || '/',\n        'http://n'\n      )\n      protocol = parsedFullUrl.protocol as 'https:' | 'http:'\n    } catch {}\n\n    // use existing incrementalCache instance if available\n    const incrementalCache: import('./lib/incremental-cache').IncrementalCache =\n      (globalThis as any).__incrementalCache ||\n      (await this.getIncrementalCache({\n        requestHeaders: Object.assign({}, req.headers),\n        requestProtocol: protocol.substring(0, protocol.length - 1) as\n          | 'http'\n          | 'https',\n      }))\n\n    // TODO: investigate, this is not safe across multiple concurrent requests\n    incrementalCache.resetRequestCache()\n\n    type RendererContext = {\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }\n    type Renderer = (\n      context: RendererContext\n    ) => Promise<ResponseCacheEntry | null>\n\n    const doRender: Renderer = async ({ postponed, fallbackRouteParams }) => {\n      // In development, we always want to generate dynamic HTML.\n      let supportsDynamicResponse: boolean =\n        // If we're in development, we always support dynamic HTML, unless it's\n        // a data request, in which case we only produce static HTML.\n        (!isNextDataRequest && opts.dev === true) ||\n        // If this is not SSG or does not have static paths, then it supports\n        // dynamic HTML.\n        (!isSSG && !hasGetStaticPaths) ||\n        // If this request has provided postponed data, it supports dynamic\n        // HTML.\n        typeof postponed === 'string' ||\n        // If this is a dynamic RSC request, then this render supports dynamic\n        // HTML (it's dynamic).\n        isDynamicRSCRequest\n\n      const origQuery = parseUrl(req.url || '', true).query\n\n      // clear any dynamic route params so they aren't in\n      // the resolvedUrl\n      if (opts.params) {\n        Object.keys(opts.params).forEach((key) => {\n          delete origQuery[key]\n        })\n      }\n      const hadTrailingSlash =\n        urlPathname !== '/' && this.nextConfig.trailingSlash\n\n      const resolvedUrl = formatUrl({\n        pathname: `${resolvedUrlPathname}${hadTrailingSlash ? '/' : ''}`,\n        // make sure to only add query values from original URL\n        query: origQuery,\n      })\n      const renderOpts: LoadedRenderOpts = {\n        ...components,\n        ...opts,\n        ...(isAppPath\n          ? {\n              incrementalCache,\n              // This is a revalidation request if the request is for a static\n              // page and it is not being resumed from a postponed render and\n              // it is not a dynamic RSC request then it is a revalidation\n              // request.\n              isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n              serverActions: this.nextConfig.experimental.serverActions,\n            }\n          : {}),\n        isNextDataRequest,\n        resolvedUrl,\n        locale,\n        locales,\n        defaultLocale,\n        multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,\n        // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n        // and not the resolved URL to prevent a hydration mismatch on\n        // asPath\n        resolvedAsPath:\n          hasServerProps || hasGetInitialProps\n            ? formatUrl({\n                // we use the original URL pathname less the _next/data prefix if\n                // present\n                pathname: `${urlPathname}${hadTrailingSlash ? '/' : ''}`,\n                query: origQuery,\n              })\n            : resolvedUrl,\n        experimental: {\n          ...opts.experimental,\n          isRoutePPREnabled,\n        },\n        supportsDynamicResponse,\n        isOnDemandRevalidate,\n        isDraftMode: isPreviewMode,\n        isServerAction,\n        postponed,\n        waitUntil: this.getWaitUntil(),\n        onClose: res.onClose.bind(res),\n        onAfterTaskError: undefined,\n        // only available in dev\n        setAppIsrStatus: (this as any).setAppIsrStatus,\n      }\n\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        supportsDynamicResponse = false\n        renderOpts.nextExport = true\n        renderOpts.supportsDynamicResponse = false\n        renderOpts.isStaticGeneration = true\n        renderOpts.isRevalidate = true\n        renderOpts.isDebugStaticShell = isDebugStaticShell\n        renderOpts.isDebugDynamicAccesses = isDebugDynamicAccesses\n      }\n\n      // Legacy render methods will return a render result that needs to be\n      // served by the server.\n      let result: RenderResult\n\n      if (routeModule) {\n        if (isAppRouteRouteModule(routeModule)) {\n          if (\n            // The type check here ensures that `req` is correctly typed, and the\n            // environment variable check provides dead code elimination.\n            process.env.NEXT_RUNTIME === 'edge' ||\n            !isNodeNextRequest(req) ||\n            !isNodeNextResponse(res)\n          ) {\n            throw new Error(\n              'Invariant: App Route Route Modules cannot be used in the edge runtime'\n            )\n          }\n\n          const context: AppRouteRouteHandlerContext = {\n            params: opts.params,\n            prerenderManifest,\n            renderOpts: {\n              experimental: {\n                dynamicIO: renderOpts.experimental.dynamicIO,\n                authInterrupts: renderOpts.experimental.authInterrupts,\n              },\n              supportsDynamicResponse,\n              incrementalCache,\n              cacheLifeProfiles: this.nextConfig.experimental?.cacheLife,\n              isRevalidate: isSSG,\n              waitUntil: this.getWaitUntil(),\n              onClose: res.onClose.bind(res),\n              onAfterTaskError: undefined,\n              onInstrumentationRequestError:\n                this.renderOpts.onInstrumentationRequestError,\n              buildId: this.renderOpts.buildId,\n            },\n          }\n\n          try {\n            const request = NextRequestAdapter.fromNodeNextRequest(\n              req,\n              signalFromNodeResponse(res.originalResponse)\n            )\n\n            const response = await routeModule.handle(request, context)\n\n            ;(req as any).fetchMetrics = (\n              context.renderOpts as any\n            ).fetchMetrics\n\n            const cacheTags = context.renderOpts.collectedTags\n\n            // If the request is for a static response, we can cache it so long\n            // as it's not edge.\n            if (isSSG) {\n              const blob = await response.blob()\n\n              // Copy the headers from the response.\n              const headers = toNodeOutgoingHttpHeaders(response.headers)\n\n              if (cacheTags) {\n                headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n              }\n\n              if (!headers['content-type'] && blob.type) {\n                headers['content-type'] = blob.type\n              }\n\n              const revalidate =\n                typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n                context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n                  ? false\n                  : context.renderOpts.collectedRevalidate\n\n              // Create the cache entry for the response.\n              const cacheEntry: ResponseCacheEntry = {\n                value: {\n                  kind: CachedRouteKind.APP_ROUTE,\n                  status: response.status,\n                  body: Buffer.from(await blob.arrayBuffer()),\n                  headers,\n                },\n                revalidate,\n                isFallback: false,\n              }\n\n              return cacheEntry\n            }\n\n            // Send the response now that we have copied it into the cache.\n            await sendResponse(\n              req,\n              res,\n              response,\n              context.renderOpts.pendingWaitUntil\n            )\n            return null\n          } catch (err) {\n            await this.instrumentationOnRequestError(err, req, {\n              routerKind: 'App Router',\n              routePath: pathname,\n              routeType: 'route',\n              revalidateReason: getRevalidateReason(renderOpts),\n            })\n\n            // If this is during static generation, throw the error again.\n            if (isSSG) throw err\n\n            Log.error(err)\n\n            // Otherwise, send a 500 response.\n            await sendResponse(req, res, new Response(null, { status: 500 }))\n\n            return null\n          }\n        } else if (\n          isPagesRouteModule(routeModule) ||\n          isAppPageRouteModule(routeModule)\n        ) {\n          // An OPTIONS request to a page handler is invalid.\n          if (req.method === 'OPTIONS' && !is404Page) {\n            await sendResponse(req, res, new Response(null, { status: 400 }))\n            return null\n          }\n\n          if (isPagesRouteModule(routeModule)) {\n            // Due to the way we pass data by mutating `renderOpts`, we can't extend\n            // the object here but only updating its `clientReferenceManifest` and\n            // `nextFontManifest` properties.\n            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n            renderOpts.nextFontManifest = this.nextFontManifest\n            renderOpts.clientReferenceManifest =\n              components.clientReferenceManifest\n\n            const request = isNodeNextRequest(req) ? req.originalRequest : req\n            const response = isNodeNextResponse(res)\n              ? res.originalResponse\n              : res\n\n            // Call the built-in render method on the module.\n            try {\n              result = await routeModule.render(\n                // TODO: fix this type\n                // @ts-expect-error - preexisting accepted this\n                request,\n                response,\n                {\n                  page: pathname,\n                  params: opts.params,\n                  query,\n                  renderOpts,\n                }\n              )\n            } catch (err) {\n              await this.instrumentationOnRequestError(err, req, {\n                routerKind: 'Pages Router',\n                routePath: pathname,\n                routeType: 'render',\n                revalidateReason: getRevalidateReason({\n                  isRevalidate: isSSG,\n                  isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n                }),\n              })\n              throw err\n            }\n          } else {\n            const module = components.routeModule as AppPageRouteModule\n\n            // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n            // object here but only updating its `nextFontManifest` field.\n            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n            renderOpts.nextFontManifest = this.nextFontManifest\n\n            const context: AppPageRouteHandlerContext = {\n              page: is404Page ? '/404' : pathname,\n              params: opts.params,\n              query,\n              fallbackRouteParams,\n              renderOpts,\n              serverComponentsHmrCache: this.getServerComponentsHmrCache(),\n            }\n\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't s prefetch or a server action,\n            // we should seed the resume data cache.\n            if (\n              this.nextConfig.experimental.dynamicIO &&\n              this.renderOpts.dev &&\n              !isPrefetchRSCRequest &&\n              !isServerAction\n            ) {\n              const warmup = await module.warmup(req, res, context)\n\n              // If the warmup is successful, we should use the resume data\n              // cache from the warmup.\n              if (warmup.metadata.devRenderResumeDataCache) {\n                renderOpts.devRenderResumeDataCache =\n                  warmup.metadata.devRenderResumeDataCache\n              }\n            }\n\n            // Call the built-in render method on the module.\n            result = await module.render(req, res, context)\n          }\n        } else {\n          throw new Error('Invariant: Unknown route module type')\n        }\n      } else {\n        // If we didn't match a page, we should fallback to using the legacy\n        // render method.\n        result = await this.renderHTML(req, res, pathname, query, renderOpts)\n      }\n\n      const { metadata } = result\n\n      const {\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isAppPath &&\n        isSSG &&\n        metadata.revalidate === 0 &&\n        !this.renderOpts.dev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${urlPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      // Based on the metadata, we can determine what kind of cache result we\n      // should return.\n\n      // Handle `isNotFound`.\n      if ('isNotFound' in metadata && metadata.isNotFound) {\n        return {\n          value: null,\n          revalidate: metadata.revalidate,\n          isFallback: false,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isRedirect`.\n      if (metadata.isRedirect) {\n        return {\n          value: {\n            kind: CachedRouteKind.REDIRECT,\n            props: metadata.pageData ?? metadata.flightData,\n          } satisfies CachedRedirectValue,\n          revalidate: metadata.revalidate,\n          isFallback: false,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isNull`.\n      if (result.isNull) {\n        return null\n      }\n\n      // We now have a valid HTML result that we can return to the user.\n      if (isAppPath) {\n        return {\n          value: {\n            kind: CachedRouteKind.APP_PAGE,\n            html: result,\n            headers,\n            rscData: metadata.flightData,\n            postponed: metadata.postponed,\n            status: res.statusCode,\n            segmentData: metadata.segmentData,\n          } satisfies CachedAppPageValue,\n          revalidate: metadata.revalidate,\n          isFallback: !!fallbackRouteParams,\n        } satisfies ResponseCacheEntry\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.PAGES,\n          html: result,\n          pageData: metadata.pageData ?? metadata.flightData,\n          headers,\n          status: isAppPath ? res.statusCode : undefined,\n        } satisfies CachedPageValue,\n        revalidate: metadata.revalidate,\n        isFallback: query.__nextFallback === 'true',\n      }\n    }\n\n    let responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n    }): Promise<ResponseCacheEntry | null> => {\n      const isProduction = !this.renderOpts.dev\n      const didRespond = hasResolved || res.sent\n\n      // If we haven't found the static paths for the route, then do it now.\n      if (!staticPaths && isDynamic) {\n        if (hasGetStaticPaths) {\n          const pathsResult = await this.getStaticPaths({\n            pathname,\n            requestHeaders: req.headers,\n            isAppPath,\n            page: components.page,\n          })\n\n          staticPaths = pathsResult.staticPaths\n          fallbackMode = pathsResult.fallbackMode\n        } else {\n          staticPaths = undefined\n          fallbackMode = FallbackMode.NOT_FOUND\n        }\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (\n        fallbackMode === FallbackMode.PRERENDER &&\n        isBot(req.headers['user-agent'] || '')\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !this.minimalMode\n      ) {\n        await this.render404(req, res)\n        return null\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // We use `ssgCacheKey` here as it is normalized to match the encoding\n      // from getStaticPaths along with including the locale.\n      //\n      // We use the `resolvedUrlPathname` for the development case when this\n      // is an app path since it doesn't include locale information.\n      //\n      // We decode the `resolvedUrlPathname` to correctly match the app path\n      // with prerendered paths.\n      let staticPathKey = ssgCacheKey\n      if (!staticPathKey && opts.dev && isAppPath) {\n        staticPathKey = decodePathParams(resolvedUrlPathname)\n      }\n      if (staticPathKey && query.amp) {\n        staticPathKey = staticPathKey.replace(/\\.amp$/, '')\n      }\n\n      const isPageIncludedInStaticPaths =\n        staticPathKey && staticPaths?.includes(staticPathKey)\n\n      // When experimental compile is used, no pages have been prerendered,\n      // so they should all be blocking.\n\n      // @ts-expect-error internal field\n      if (this.nextConfig.experimental.isExperimentalCompile) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // When we did not respond from cache, we need to choose to block on\n      // rendering or return a skeleton.\n      //\n      // - Data requests always block.\n      // - Blocking mode fallback always blocks.\n      // - Preview mode toggles all pages to be resolved in a blocking manner.\n      // - Non-dynamic pages should block (though this is an impossible\n      //   case in production).\n      // - Dynamic pages should return their skeleton if not defined in\n      //   getStaticPaths, then finish the data request on the client-side.\n      //\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        !this.minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isPreviewMode &&\n        isDynamic &&\n        (isProduction || !staticPaths || !isPageIncludedInStaticPaths)\n      ) {\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || (staticPaths && staticPaths?.length > 0)) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        // If this is a pages router page.\n        if (isPagesRouteModule(components.routeModule) && !isNextDataRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? (locale ? `/${locale}${pathname}` : pathname) : null,\n            // This is the response generator for the fallback shell.\n            async ({\n              previousCacheEntry: previousFallbackCacheEntry = null,\n            }) => {\n              // For the pages router, fallbacks cannot be revalidated or\n              // generated in production. In the case of a missing fallback,\n              // we return null, but if it's being revalidated, we just return\n              // the previous fallback cache entry. This preserves the previous\n              // behavior.\n              if (isProduction) {\n                return toResponseCacheEntry(previousFallbackCacheEntry)\n              }\n\n              // For the pages router, fallbacks can only be generated on\n              // demand in development, so if we're not in production, and we\n              // aren't a app path, then just add the __nextFallback query\n              // and render.\n              query.__nextFallback = 'true'\n\n              // We pass `undefined` and `null` as it doesn't apply to the pages\n              // router.\n              return doRender({\n                postponed: undefined,\n                fallbackRouteParams: null,\n              })\n            },\n            {\n              routeKind: RouteKind.PAGES,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n        // If this is a app router page, PPR is enabled, and PFPR is also\n        // enabled, then we should use the fallback renderer.\n        else if (\n          isRoutePPREnabled &&\n          isAppPageRouteModule(components.routeModule) &&\n          !isRSCRequest\n        ) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? pathname : null,\n            // This is the response generator for the fallback shell.\n            async () =>\n              doRender({\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                fallbackRouteParams:\n                  // If we're in production of we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(pathname)\n                    : null,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n\n        // If the fallback response was set to null, then we should return null.\n        if (fallbackResponse === null) return null\n\n        // Otherwise, if we did get a fallback response, we should return it.\n        if (fallbackResponse) {\n          // Remove the revalidate from the response to prevent it from being\n          // used in the surrounding cache.\n          delete fallbackResponse.revalidate\n\n          return fallbackResponse\n        }\n      }\n\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          revalidate: 1,\n          isFallback: false,\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        isDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'didSetDefaultRouteMatches') ||\n          isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      const result = await doRender({\n        postponed,\n        fallbackRouteParams,\n      })\n      if (!result) return null\n\n      return {\n        ...result,\n        revalidate: result.revalidate,\n      }\n    }\n\n    const cacheEntry = await this.responseCache.get(\n      ssgCacheKey,\n      responseGenerator,\n      {\n        routeKind:\n          // If the route module is not defined, we can assume it's a page being\n          // rendered and thus check isAppPath.\n          routeModule?.definition.kind ??\n          (isAppPath ? RouteKind.APP_PAGE : RouteKind.PAGES),\n        incrementalCache,\n        isOnDemandRevalidate,\n        isPrefetch: req.headers.purpose === 'prefetch',\n        isRoutePPREnabled,\n      }\n    )\n\n    if (isPrefetchRSCRequest && typeof segmentPrefetchHeader === 'string') {\n      // This is a prefetch request issued by the client Segment Cache. These\n      // should never reach the application layer (lambda). We should either\n      // respond from the cache (HIT) or respond with 204 No Content (MISS).\n      if (\n        cacheEntry !== null &&\n        // This is always true at runtime but is needed to refine the type\n        // of cacheEntry.value to CachedAppPageValue, because the outer\n        // ResponseCacheEntry is not a discriminated union.\n        cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n        cacheEntry.value.segmentData\n      ) {\n        const matchedSegment = cacheEntry.value.segmentData.get(\n          segmentPrefetchHeader\n        )\n        if (matchedSegment !== undefined) {\n          // Cache hit\n          return {\n            type: 'rsc',\n            body: RenderResult.fromStatic(matchedSegment),\n            // TODO: Eventually this should use revalidate time of the\n            // individual segment, not the whole page.\n            revalidate: cacheEntry.revalidate,\n          }\n        }\n      }\n\n      // Cache miss. Either a cache entry for this route has not been generated,\n      // or there's no match for the requested segment. Regardless, respond with\n      // a 204 No Content. We don't bother to respond with 404 in cases where\n      // the segment does not exist, because these requests are only issued by\n      // the client cache.\n      // TODO: If this is a request for the route tree (the special /_tree\n      // segment), we should *always* respond with a tree, even if PPR\n      // is disabled.\n      res.statusCode = 204\n      if (isRoutePPREnabled) {\n        // Set a header to indicate that PPR is enabled for this route. This\n        // lets the client distinguish between a regular cache miss and a cache\n        // miss due to PPR being disabled.\n        // NOTE: Theoretically, when PPR is enabled, there should *never* be\n        // a cache miss because we should generate a fallback route. So this\n        // is mostly defensive.\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n      return {\n        type: 'rsc',\n        body: RenderResult.fromStatic(''),\n        revalidate: cacheEntry?.revalidate,\n      }\n    }\n\n    if (isPreviewMode) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    if (!cacheEntry) {\n      if (ssgCacheKey && !(isOnDemandRevalidate && revalidateOnlyGenerated)) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n      return null\n    }\n\n    // If we're not in minimal mode and the cache entry that was returned was a\n    // app page fallback, then we need to kick off the dynamic shell generation.\n    if (\n      ssgCacheKey &&\n      !this.minimalMode &&\n      isRoutePPREnabled &&\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      cacheEntry.isFallback &&\n      !isOnDemandRevalidate &&\n      // When we're debugging the fallback shell, we don't want to regenerate\n      // the route shell.\n      !isDebugFallbackShell &&\n      process.env.DISABLE_ROUTE_SHELL_GENERATION !== 'true'\n    ) {\n      scheduleOnNextTick(async () => {\n        try {\n          await this.responseCache.get(\n            ssgCacheKey,\n            () =>\n              doRender({\n                // We're an on-demand request, so we don't need to pass in the\n                // fallbackRouteParams.\n                fallbackRouteParams: null,\n                postponed: undefined,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isOnDemandRevalidate: true,\n              isPrefetch: false,\n              isRoutePPREnabled: true,\n            }\n          )\n        } catch (err) {\n          console.error('Error occurred while rendering dynamic shell', err)\n        }\n      })\n    }\n\n    const didPostpone =\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      typeof cacheEntry.value.postponed === 'string'\n\n    if (\n      isSSG &&\n      // We don't want to send a cache header for requests that contain dynamic\n      // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n      // request, then we should set the cache header.\n      !isDynamicRSCRequest &&\n      (!didPostpone || isPrefetchRSCRequest)\n    ) {\n      if (!this.minimalMode) {\n        // set x-nextjs-cache header to match the header\n        // we set for the image-optimizer\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n      // Set a header used by the client router to signal the response is static\n      // and should respect the `static` cache staleTime value.\n      res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n    }\n\n    const { value: cachedData } = cacheEntry\n\n    // If the cache value is an image, we should error early.\n    if (cachedData?.kind === CachedRouteKind.IMAGE) {\n      throw new Error('invariant SSG should not return an image cache value')\n    }\n\n    // Coerce the revalidate parameter from the render.\n    let revalidate: Revalidate | undefined\n\n    // If this is a resume request in minimal mode it is streamed with dynamic\n    // content and should not be cached.\n    if (minimalPostponed) {\n      revalidate = 0\n    }\n\n    // If this is in minimal mode and this is a flight request that isn't a\n    // prefetch request while PPR is enabled, it cannot be cached as it contains\n    // dynamic content.\n    else if (\n      this.minimalMode &&\n      isRSCRequest &&\n      !isPrefetchRSCRequest &&\n      isRoutePPREnabled\n    ) {\n      revalidate = 0\n    } else if (!this.renderOpts.dev || (hasServerProps && !isNextDataRequest)) {\n      // If this is a preview mode request, we shouldn't cache it\n      if (isPreviewMode) {\n        revalidate = 0\n      }\n\n      // If this isn't SSG, then we should set change the header only if it is\n      // not set already.\n      else if (!isSSG) {\n        if (!res.getHeader('Cache-Control')) {\n          revalidate = 0\n        }\n      }\n\n      // If we are rendering the 404 page we derive the cache-control\n      // revalidate period from the value that trigged the not found\n      // to be rendered. So if `getStaticProps` returns\n      // { notFound: true, revalidate 60 } the revalidate period should\n      // be 60 but if a static asset 404s directly it should have a revalidate\n      // period of 0 so that it doesn't get cached unexpectedly by a CDN\n      else if (is404Page) {\n        const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n        revalidate =\n          typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate\n      } else if (is500Page) {\n        revalidate = 0\n      }\n\n      // If the cache entry has a revalidate value that's a number, use it.\n      else if (typeof cacheEntry.revalidate === 'number') {\n        if (cacheEntry.revalidate < 1) {\n          throw new Error(\n            `Invalid revalidate configuration provided: ${cacheEntry.revalidate} < 1`\n          )\n        }\n\n        revalidate = cacheEntry.revalidate\n      }\n      // Otherwise if the revalidate value is false, then we should use the cache\n      // time of one year.\n      else if (cacheEntry.revalidate === false) {\n        revalidate = CACHE_ONE_YEAR\n      }\n    }\n\n    cacheEntry.revalidate = revalidate\n\n    // If there's a callback for `onCacheEntry`, call it with the cache entry\n    // and the revalidate options.\n    const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n    if (onCacheEntry) {\n      const finished = await onCacheEntry(\n        {\n          ...cacheEntry,\n          // TODO: remove this when upstream doesn't\n          // always expect this value to be \"PAGE\"\n          value: {\n            ...cacheEntry.value,\n            kind:\n              cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n                ? 'PAGE'\n                : cacheEntry.value?.kind,\n          },\n        },\n        {\n          url: getRequestMeta(req, 'initURL'),\n        }\n      )\n      if (finished) {\n        // TODO: maybe we have to end the request?\n        return null\n      }\n    }\n\n    if (!cachedData) {\n      // add revalidate metadata before rendering 404 page\n      // so that we can use this as source of truth for the\n      // cache-control header instead of what the 404 page returns\n      // for the revalidate value\n      addRequestMeta(req, 'notFoundRevalidate', cacheEntry.revalidate)\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        typeof cacheEntry.revalidate !== 'undefined' &&\n        !res.getHeader('Cache-Control')\n      ) {\n        res.setHeader(\n          'Cache-Control',\n          formatRevalidate({\n            revalidate: cacheEntry.revalidate,\n            expireTime: this.nextConfig.expireTime,\n          })\n        )\n      }\n      if (isNextDataRequest) {\n        res.statusCode = 404\n        res.body('{\"notFound\":true}').send()\n        return null\n      }\n\n      if (this.renderOpts.dev) {\n        query.__nextNotFoundSrcPage = pathname\n      }\n      await this.render404(req, res, { pathname, query }, false)\n      return null\n    } else if (cachedData.kind === CachedRouteKind.REDIRECT) {\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        typeof cacheEntry.revalidate !== 'undefined' &&\n        !res.getHeader('Cache-Control')\n      ) {\n        res.setHeader(\n          'Cache-Control',\n          formatRevalidate({\n            revalidate: cacheEntry.revalidate,\n            expireTime: this.nextConfig.expireTime,\n          })\n        )\n      }\n\n      if (isNextDataRequest) {\n        return {\n          type: 'json',\n          body: RenderResult.fromStatic(\n            // @TODO: Handle flight data.\n            JSON.stringify(cachedData.props)\n          ),\n          revalidate: cacheEntry.revalidate,\n        }\n      } else {\n        await handleRedirect(cachedData.props)\n        return null\n      }\n    } else if (cachedData.kind === CachedRouteKind.APP_ROUTE) {\n      const headers = fromNodeOutgoingHttpHeaders(cachedData.headers)\n\n      if (!(this.minimalMode && isSSG)) {\n        headers.delete(NEXT_CACHE_TAGS_HEADER)\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        typeof cacheEntry.revalidate !== 'undefined' &&\n        !res.getHeader('Cache-Control') &&\n        !headers.get('Cache-Control')\n      ) {\n        headers.set(\n          'Cache-Control',\n          formatRevalidate({\n            revalidate: cacheEntry.revalidate,\n            expireTime: this.nextConfig.expireTime,\n          })\n        )\n      }\n\n      await sendResponse(\n        req,\n        res,\n        new Response(cachedData.body, {\n          headers,\n          status: cachedData.status || 200,\n        })\n      )\n      return null\n    } else if (cachedData.kind === CachedRouteKind.APP_PAGE) {\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!this.minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      if (\n        this.minimalMode &&\n        isSSG &&\n        cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      ) {\n        res.setHeader(\n          NEXT_CACHE_TAGS_HEADER,\n          cachedData.headers[NEXT_CACHE_TAGS_HEADER] as string\n        )\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isPreviewMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return {\n            type: 'rsc',\n            body: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            revalidate: isDynamicRSCRequest ? 0 : cacheEntry.revalidate,\n          }\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(cachedData.rscData),\n          revalidate: cacheEntry.revalidate,\n        }\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || this.minimalMode) {\n        return {\n          type: 'html',\n          body,\n          revalidate: cacheEntry.revalidate,\n        }\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return { type: 'html', body, revalidate: 0 }\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        postponed: cachedData.postponed,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return {\n        type: 'html',\n        body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        revalidate: 0,\n      }\n    } else if (isNextDataRequest) {\n      return {\n        type: 'json',\n        body: RenderResult.fromStatic(JSON.stringify(cachedData.pageData)),\n        revalidate: cacheEntry.revalidate,\n      }\n    } else {\n      return {\n        type: 'html',\n        body: cachedData.html,\n        revalidate: cacheEntry.revalidate,\n      }\n    }\n  }\n\n  private stripNextDataPath(path: string, stripLocale = true) {\n    if (path.includes(this.buildId)) {\n      const splitPath = path.substring(\n        path.indexOf(this.buildId) + this.buildId.length\n      )\n\n      path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n    }\n\n    if (this.localeNormalizer && stripLocale) {\n      return this.localeNormalizer.normalize(path)\n    }\n    return path\n  }\n\n  // map the route to the actual bundle name\n  protected getOriginalAppPaths(route: string) {\n    if (this.enabledDirectories.app) {\n      const originalAppPath = this.appPathRoutes?.[route]\n\n      if (!originalAppPath) {\n        return null\n      }\n\n      return originalAppPath\n    }\n    return null\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const { query, pathname } = ctx\n\n    const appPaths = this.getOriginalAppPaths(pathname)\n    const isAppPath = Array.isArray(appPaths)\n\n    let page = pathname\n    if (isAppPath) {\n      // the last item in the array is the root page, if there are parallel routes\n      page = appPaths[appPaths.length - 1]\n    }\n\n    const result = await this.findPageComponents({\n      page,\n      query,\n      params: ctx.renderOpts.params || {},\n      isAppPath,\n      sriEnabled: !!this.nextConfig.experimental.sri?.algorithm,\n      appPaths,\n      // Ensuring for loading page component routes is done via the matcher.\n      shouldEnsure: false,\n    })\n    if (result) {\n      getTracer().setRootSpanAttribute('next.route', pathname)\n      try {\n        return await this.renderToResponseWithComponents(ctx, result)\n      } catch (err) {\n        const isNoFallbackError = err instanceof NoFallbackError\n\n        if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n          throw err\n        }\n      }\n    }\n    return false\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponse,\n      {\n        spanName: `rendering page`,\n        attributes: {\n          'next.route': ctx.pathname,\n        },\n      },\n      async () => {\n        return this.renderToResponseImpl(ctx)\n      }\n    )\n  }\n\n  protected abstract getMiddleware(): MiddlewareRoutingItem | undefined\n  protected abstract getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null>\n  protected abstract getRoutesManifest(): NormalizedRouteManifest | undefined\n\n  private async renderToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    const { res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback = !!query._nextBubbleNoFallback\n    delete query[NEXT_RSC_UNION_QUERY]\n    delete query._nextBubbleNoFallback\n\n    const options: MatchOptions = {\n      i18n: this.i18nProvider?.fromQuery(pathname, query),\n    }\n\n    try {\n      for await (const match of this.matchers.matchAll(pathname, options)) {\n        // when a specific invoke-output is meant to be matched\n        // ensure a prior dynamic route/page doesn't take priority\n        const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput')\n        if (\n          !this.minimalMode &&\n          typeof invokeOutput === 'string' &&\n          isDynamicRoute(invokeOutput || '') &&\n          invokeOutput !== match.definition.pathname\n        ) {\n          continue\n        }\n\n        const result = await this.renderPageComponent(\n          {\n            ...ctx,\n            pathname: match.definition.pathname,\n            renderOpts: {\n              ...ctx.renderOpts,\n              params: match.params,\n            },\n          },\n          bubbleNoFallback\n        )\n        if (result !== false) return result\n      }\n\n      // currently edge functions aren't receiving the x-matched-path\n      // header so we need to fallback to matching the current page\n      // when we weren't able to match via dynamic route to handle\n      // the rewrite case\n      // @ts-expect-error extended in child class web-server\n      if (this.serverOptions.webServerConfig) {\n        // @ts-expect-error extended in child class web-server\n        ctx.pathname = this.serverOptions.webServerConfig.page\n        const result = await this.renderPageComponent(ctx, bubbleNoFallback)\n        if (result !== false) return result\n      }\n    } catch (error) {\n      const err = getProperError(error)\n\n      if (error instanceof MissingStaticPage) {\n        console.error(\n          'Invariant: failed to load static page',\n          JSON.stringify(\n            {\n              page,\n              url: ctx.req.url,\n              matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],\n              initUrl: getRequestMeta(ctx.req, 'initURL'),\n              didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),\n              rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL'),\n            },\n            null,\n            2\n          )\n        )\n        throw err\n      }\n\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError || err instanceof NormalizeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n\n      // if pages/500 is present we still need to trigger\n      // /_error `getInitialProps` to allow reporting error\n      if (await this.hasPage('/500')) {\n        ctx.query.__nextCustomErrorRender = '1'\n        await this.renderErrorToResponse(ctx, err)\n        delete ctx.query.__nextCustomErrorRender\n      }\n\n      const isWrappedError = err instanceof WrappedBuildError\n\n      if (!isWrappedError) {\n        if (\n          (this.minimalMode && process.env.NEXT_RUNTIME !== 'edge') ||\n          this.renderOpts.dev\n        ) {\n          if (isError(err)) err.page = page\n          throw err\n        }\n        this.logError(getProperError(err))\n      }\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? (err as WrappedBuildError).innerError : err\n      )\n      return response\n    }\n\n    if (\n      this.getMiddleware() &&\n      !!ctx.req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${query.__nextLocale ? `/${query.__nextLocale}` : ''}${pathname}`\n      )\n      res.statusCode = 200\n      res.setHeader('content-type', 'application/json')\n      res.body('{}')\n      res.send()\n      return null\n    }\n\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return getTracer().trace(BaseServerSpan.renderToHTML, async () => {\n      return this.renderToHTMLImpl(req, res, pathname, query)\n    })\n  }\n\n  private async renderToHTMLImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.renderError, async () => {\n      return this.renderErrorImpl(err, req, res, pathname, query, setHeaders)\n    })\n  }\n\n  private async renderErrorImpl(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    Log.warn(\n      `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(BaseServerSpan.renderErrorToResponse, async () => {\n      return this.renderErrorToResponseImpl(ctx, err)\n    })\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.\n    // Since favicon.ico is automatically requested by the browser.\n    if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic(''),\n      }\n    }\n    const { res, query } = ctx\n\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      if (is404) {\n        if (this.enabledDirectories.app) {\n          // Use the not-found entry in app directory\n          result = await this.findPageComponents({\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n            query,\n            params: {},\n            isAppPath: true,\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n\n        if (!result && (await this.hasPage('/404'))) {\n          result = await this.findPageComponents({\n            page: '/404',\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 404 route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (\n        !ctx.query.__nextCustomErrorRender &&\n        !result &&\n        STATIC_STATUS_PAGES.includes(statusPage)\n      ) {\n        // skip ensuring /500 in dev mode as it isn't used and the\n        // dev overlay is used instead\n        if (statusPage !== '/500' || !this.renderOpts.dev) {\n          result = await this.findPageComponents({\n            page: statusPage,\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 500\n            // route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n        }\n      }\n\n      if (!result) {\n        result = await this.findPageComponents({\n          page: '/_error',\n          query,\n          params: {},\n          isAppPath: false,\n          // Ensuring can't be done here because you never \"match\" an error\n          // route.\n          shouldEnsure: true,\n          url: ctx.req.url,\n        })\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      if (!result) {\n        // this can occur when a project directory has been moved/deleted\n        // which is handled in the parent process in development\n        if (this.renderOpts.dev) {\n          return {\n            type: 'html',\n            // wait for dev-server to restart before refreshing\n            body: RenderResult.fromStatic(\n              `\n              <pre>missing required error components, refreshing...</pre>\n              <script>\n                async function check() {\n                  const res = await fetch(location.href).catch(() => ({}))\n\n                  if (res.status === 200) {\n                    location.reload()\n                  } else {\n                    setTimeout(check, 1000)\n                  }\n                }\n                check()\n              </script>`\n            ),\n          }\n        }\n\n        throw new WrappedBuildError(\n          new Error('missing required error components')\n        )\n      }\n\n      // If the page has a route module, use it for the new match. If it doesn't\n      // have a route module, remove the match.\n      if (result.components.routeModule) {\n        addRequestMeta(ctx.req, 'match', {\n          definition: result.components.routeModule.definition,\n          params: undefined,\n        })\n      } else {\n        removeRequestMeta(ctx.req, 'match')\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (error) {\n      const renderToHtmlError = getProperError(error)\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents(\n        ctx.req.url\n      )\n\n      if (fallbackComponents) {\n        // There was an error, so use it's definition from the route module\n        // to add the match to the request.\n        addRequestMeta(ctx.req, 'match', {\n          definition: fallbackComponents.routeModule!.definition,\n          params: undefined,\n        })\n\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic('Internal Server Error'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async render404(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: Pick<NextUrlWithParsedQuery, 'pathname' | 'query'>,\n    setHeaders = true\n  ): Promise<void> {\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url!, true)\n\n    if (this.nextConfig.i18n) {\n      query.__nextLocale ||= this.nextConfig.i18n.defaultLocale\n      query.__nextDefaultLocale ||= this.nextConfig.i18n.defaultLocale\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n}\n"], "names": ["getFallbackRouteParams", "CachedRouteKind", "NormalizeError", "DecodeError", "normalizeRepeatedSlashes", "MissingStaticPage", "format", "formatUrl", "parse", "parseUrl", "formatHostname", "getRedirectStatus", "isEdgeRuntime", "APP_PATHS_MANIFEST", "NEXT_BUILTIN_DOCUMENT", "PAGES_MANIFEST", "STATIC_STATUS_PAGES", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "isDynamicRoute", "checkIsOnDemandRevalidate", "setConfig", "formatRevalidate", "execOnce", "isBlockedPage", "isBot", "RenderResult", "removeTrailingSlash", "denormalizePagePath", "Log", "getUtils", "isError", "getProperError", "addRequestMeta", "getRequestMeta", "removeRequestMeta", "setRequestMeta", "removePathPrefix", "normalizeAppPath", "getHostname", "parseUrlUtil", "getNextPathnameInfo", "RSC_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_URL", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_IS_PRERENDER_HEADER", "LocaleRouteNormalizer", "DefaultRouteMatcherManager", "AppPageRouteMatcherProvider", "AppRouteRouteMatcherProvider", "PagesAPIRouteMatcherProvider", "PagesRouteMatcherProvider", "ServerManifestLoader", "getTracer", "isBubbledError", "SpanKind", "BaseServerSpan", "I18NProvider", "sendResponse", "fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "toNodeOutgoingHttpHeaders", "CACHE_ONE_YEAR", "INFINITE_CACHE", "MATCHED_PATH_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_TAGS_HEADER", "NEXT_RESUME_HEADER", "normalizeLocalePath", "NextRequestAdapter", "signalFromNodeResponse", "matchNextDataPathname", "getRouteFromAssetPath", "decodePathParams", "RSCPathnameNormalizer", "stripFlightHeaders", "isAppPageRouteModule", "isAppRouteRouteModule", "isPagesRouteModule", "PrefetchRSCPathnameNormalizer", "NextDataPathnameNormalizer", "getIsServerAction", "isInterceptionRouteAppPath", "toRoute", "isNodeNextRequest", "isNodeNextResponse", "patchSetHeaderWithCookieSupport", "checkIsAppPPREnabled", "getBuiltinRequestContext", "ENCODED_TAGS", "NextRequestHint", "getRevalidateReason", "RouteKind", "FallbackMode", "parseFallbackField", "toResponseCacheEntry", "scheduleOnNextTick", "NoFallbackError", "Error", "WrappedBuildError", "constructor", "innerError", "Server", "getServerComponentsHmrCache", "nextConfig", "experimental", "serverComponentsHmrCache", "globalThis", "__serverComponentsHmrCache", "undefined", "options", "handleRSCRequest", "req", "_res", "parsedUrl", "pathname", "normalizers", "prefetchRSC", "match", "normalize", "headers", "toLowerCase", "rsc", "url", "parsed", "handleNextDataRequest", "res", "middleware", "getMiddleware", "params", "path", "buildId", "process", "env", "NEXT_RUNTIME", "render404", "shift", "lastPara<PERSON>", "length", "endsWith", "join", "trailingSlash", "substring", "i18nProvider", "hostname", "host", "split", "domainLocale", "detectDomainLocale", "defaultLocale", "config", "localePathResult", "analyze", "detectedLocale", "query", "__next<PERSON><PERSON><PERSON>", "__nextDefaultLocale", "__nextInferredLocaleFromDefault", "__nextDataReq", "handleNextImageRequest", "handleCatchallRenderRequest", "handleCatchallMiddlewareRequest", "data", "push", "normalizer", "normalizeAndAttachMetadata", "finished", "enabledDirectories", "pages", "prepared", "preparedPromise", "customErrorNo404Warn", "warn", "dir", "quiet", "conf", "dev", "minimalMode", "customServer", "port", "experimentalTestProxy", "serverOptions", "require", "resolve", "loadEnvConfig", "fetchHostname", "distDir", "publicDir", "getPublicDir", "hasStaticDir", "getHasStaticDir", "i18n", "locales", "localeNormalizer", "serverRuntimeConfig", "publicRuntimeConfig", "assetPrefix", "generateEtags", "getBuildId", "minimalModeKey", "NEXT_PRIVATE_MINIMAL_MODE", "getEnabledDirectories", "isAppPPREnabled", "app", "ppr", "nextFontManifest", "getNextFontManifest", "NEXT_DEPLOYMENT_ID", "deploymentId", "renderOpts", "supportsDynamicResponse", "strictNextHead", "poweredByHeader", "canonicalBase", "amp", "previewProps", "getPrerenderManifest", "preview", "ampOptimizerConfig", "optimizer", "basePath", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "domains", "serverComponents", "cacheLifeProfiles", "cacheLife", "enableTainting", "taint", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "isExperimentalCompile", "expireTime", "clientTraceMetadata", "dynamicIO", "inlineCss", "authInterrupts", "onInstrumentationRequestError", "instrumentationOnRequestError", "bind", "reactMaxHeadersLength", "pagesManifest", "getPagesManifest", "appPathsManifest", "getAppPathsManifest", "appPathRoutes", "getAppPathRoutes", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "matchers", "getRouteMatchers", "reload", "setAssetPrefix", "responseCache", "getResponseCache", "reloadMatchers", "manifest<PERSON><PERSON>der", "name", "args", "err", "ctx", "instrumentation", "onRequestError", "method", "fromEntries", "entries", "handlerErr", "console", "error", "logError", "handleRequest", "prepare", "toUpperCase", "tracer", "withPropagatedContext", "trace", "spanName", "kind", "SERVER", "attributes", "span", "handleRequestImpl", "finally", "isRSCRequest", "setAttributes", "statusCode", "rootSpanAttributes", "getRootSpanAttributes", "get", "route", "updateName", "originalRequest", "waitTillReady", "originalResponse", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "redirect", "body", "send", "URLSearchParams", "xForwardedProto", "isHttps", "socket", "encrypted", "toString", "remoteAddress", "validate<PERSON><PERSON>y", "attachRequestMeta", "replace", "pathnameInfo", "useMatchedPathHeader", "<PERSON><PERSON><PERSON>", "URL", "urlPathname", "chunk", "postponed", "<PERSON><PERSON><PERSON>", "concat", "normalizedUrlPath", "stripNextDataPath", "localeAnalysisResult", "inferredFromDefault", "srcPathname", "pageIsDynamic", "definition", "utils", "page", "rewrites", "getRoutesManifest", "beforeFiles", "afterFiles", "fallback", "caseSensitive", "caseSensitiveRoutes", "locale", "pathnameBeforeRewrite", "rewriteParams", "handleRewrites", "rewriteParamKeys", "didRewrite", "routeParamKeys", "Set", "key", "value", "normalizedKey", "add", "paramsResult", "normalizeDynamicRouteParams", "hasValidParams", "matcherParams", "dynamicRouteMatcher", "assign", "curParamsResult", "opts", "routeParams", "getParamsFromRouteMatches", "defaultRouteMatches", "interpolateDynamicPath", "normalizeVercelUrl", "defaultRouteRegex", "groups", "renderError", "Boolean", "webServerConfig", "protocol", "parsedFullUrl", "incrementalCache", "getIncrementalCache", "requestHeaders", "requestProtocol", "_globalThis", "__next<PERSON>ache<PERSON>and<PERSON>", "expiredTags", "handler", "values", "receiveExpiredTags", "resetRequestCache", "__incrementalCache", "invoke<PERSON><PERSON>", "useInvokePath", "invoke<PERSON>tatus", "invoke<PERSON><PERSON>y", "parsedMatchedPath", "invokePathnameInfo", "parseData", "normalizeResult", "startsWith", "result", "response", "Response", "bubble", "run", "code", "getRequestHandlerWithMetadata", "meta", "getRequestHandler", "prefix", "loadInstrumentationModule", "prepareImpl", "then", "close", "for<PERSON>ach", "entry", "normalizedPath", "runImpl", "pipe", "fn", "partialContext", "pipeImpl", "isBotRequest", "payload", "originalStatus", "type", "revalidate", "sent", "<PERSON><PERSON><PERSON><PERSON>", "sendRenderResult", "getStaticHTML", "toUnchunkedString", "render", "internalRender", "renderImpl", "getWaitUntil", "builtinRequestContext", "waitUntil", "getInternalWaitUntil", "hasPage", "renderToResponse", "getStaticPaths", "fallback<PERSON><PERSON>", "dynamicRoutes", "staticPaths", "fallbackMode", "renderToResponseWithComponents", "requestContext", "findComponentsResult", "renderToResponseWithComponentsImpl", "pathCouldBeIntercepted", "resolvedPathname", "some", "regexp", "test", "set<PERSON>aryH<PERSON>er", "isAppPath", "baseVaryHeader", "addedNextUrlToVary", "components", "prerenderManifest", "cacheEntry", "isErrorPathname", "is404Page", "is500Page", "hasServerProps", "getServerSideProps", "hasGetStaticPaths", "isServerAction", "hasGetInitialProps", "Component", "getInitialProps", "isSSG", "getStaticProps", "resolvedUrlPathname", "<PERSON><PERSON><PERSON><PERSON>", "isDynamic", "pathsResult", "resolvedWithoutSlash", "includes", "routes", "isNextDataRequest", "isPrefetchRSCRequest", "routeModule", "couldSupportPPR", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "isDynamicRSCRequest", "segmentPrefetchHeader", "parseInt", "slice", "fromStatic", "isSupportedDocument", "Document", "previewData", "isPreviewMode", "tryGetPreviewData", "multiZoneDraftMode", "runtime", "isOnDemandRevalidate", "revalidateOnlyGenerated", "handleRedirect", "pageData", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "ssgCacheKey", "doR<PERSON>", "fallbackRouteParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hadTrailingSlash", "resolvedUrl", "isRevalidate", "serverActions", "resolvedAsPath", "isDraftMode", "onClose", "onAfterTaskError", "setAppIsrStatus", "nextExport", "isStaticGeneration", "context", "request", "fromNodeNextRequest", "handle", "fetchMetrics", "cacheTags", "collectedTags", "blob", "collectedRevalidate", "APP_ROUTE", "status", "from", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON>", "pendingWaitUntil", "routerKind", "routePath", "routeType", "revalidateReason", "clientReferenceManifest", "module", "warmup", "metadata", "devRenderResumeDataCache", "renderHTML", "fetchTags", "staticBailoutInfo", "description", "stack", "message", "indexOf", "isNotFound", "isRedirect", "REDIRECT", "props", "flightData", "isNull", "APP_PAGE", "html", "rscData", "segmentData", "PAGES", "__<PERSON><PERSON><PERSON><PERSON>", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "NOT_FOUND", "PRERENDER", "BLOCKING_STATIC_RENDER", "isStale", "static<PERSON><PERSON><PERSON><PERSON>", "isPageIncludedInStaticPaths", "fallbackResponse", "previousFallbackCacheEntry", "routeKind", "isPrefetch", "purpose", "matchedSegment", "DISABLE_ROUTE_SHELL_GENERATION", "didPostpone", "isMiss", "cachedData", "IMAGE", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "onCacheEntry", "__nextNotFoundSrcPage", "JSON", "stringify", "delete", "set", "Array", "isArray", "v", "append<PERSON><PERSON>er", "chain", "ReadableStream", "start", "controller", "enqueue", "CLOSED", "BODY_AND_HTML", "transformer", "TransformStream", "readable", "pipeTo", "writable", "catch", "abort", "e", "stripLocale", "splitPath", "getOriginalAppPaths", "originalAppPath", "renderPageComponent", "bubbleNoFallback", "appPaths", "findPageComponents", "sriEnabled", "sri", "algorithm", "shouldEnsure", "setRootSpanAttribute", "isNoFallbackError", "renderToResponseImpl", "_nextBubbleNoFallback", "fromQuery", "matchAll", "invokeOutput", "initUrl", "rewroteUrl", "renderErrorToResponse", "__nextCustomErrorRender", "isWrappedError", "renderToHTML", "renderToHTMLImpl", "setHeaders", "renderErrorImpl", "renderErrorToResponseImpl", "is404", "using404Page", "statusPage", "NODE_ENV", "maybeFallbackError", "renderToHtmlError", "fallbackComponents", "getFallbackErrorComponents", "renderErrorToHTML"], "mappings": ";;;;;AAIA,SAEEA,sBAAsB,QACjB,4BAA2B;AAalC,SAOEC,eAAe,QAEV,mBAAkB;AAEzB,SACEC,cAAc,EACdC,WAAW,EACXC,wBAAwB,EACxBC,iBAAiB,QACZ,sBAAqB;AA2B5B,SAASC,UAAUC,SAAS,EAAEC,SAASC,QAAQ,QAAQ,MAAK;AAC5D,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SACEC,kBAAkB,EAClBC,qBAAqB,EACrBC,cAAc,EACdC,mBAAmB,EACnBC,0BAA0B,EAC1BC,gCAAgC,QAC3B,0BAAyB;AAChC,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,yBAAyB,QAAQ,cAAa;AACvD,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SACEC,gBAAgB,QAGX,mBAAkB;AAEzB,SAASE,aAAa,QAAQ,UAAS;AACvC,SAASC,KAAK,QAAQ,oCAAmC;AACzD,OAAOC,kBAAkB,kBAAiB;AAC1C,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,QAAQ,QAAQ,iBAAgB;AACzC,OAAOC,WAAWC,cAAc,QAAQ,kBAAiB;AACzD,SACEC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,cAAc,QACT,iBAAgB;AACvB,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SAASC,WAAW,QAAQ,6BAA4B;AACxD,SAAS9B,YAAY+B,YAAY,QAAQ,uCAAsC;AAC/E,SAASC,mBAAmB,QAAQ,oDAAmD;AACvF,SACEC,UAAU,EACVC,oBAAoB,EACpBC,2BAA2B,EAC3BC,mCAAmC,EACnCC,wBAAwB,EACxBC,QAAQ,EACRC,6BAA6B,EAC7BC,wBAAwB,QACnB,0CAAyC;AAKhD,SAASC,qBAAqB,QAAQ,wCAAuC;AAC7E,SAASC,0BAA0B,QAAQ,yDAAwD;AACnG,SAASC,2BAA2B,QAAQ,4DAA2D;AACvG,SAASC,4BAA4B,QAAQ,6DAA4D;AACzG,SAASC,4BAA4B,QAAQ,6DAA4D;AACzG,SAASC,yBAAyB,QAAQ,yDAAwD;AAClG,SAASC,oBAAoB,QAAQ,4EAA2E;AAChH,SAASC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,qBAAoB;AACxE,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,YAAY,QAAQ,sBAAqB;AAClD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SACEC,2BAA2B,EAC3BC,uBAAuB,EACvBC,yBAAyB,QACpB,cAAa;AACpB,SACEC,cAAc,EACdC,cAAc,EACdC,mBAAmB,EACnBC,kCAAkC,EAClCC,sBAAsB,EACtBC,kBAAkB,QACb,mBAAkB;AACzB,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SACEC,kBAAkB,EAClBC,sBAAsB,QACjB,6CAA4C;AACnD,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,OAAOC,2BAA2B,uDAAsD;AACxF,SAASC,gBAAgB,QAAQ,wCAAuC;AACxE,SAASC,qBAAqB,QAAQ,4BAA2B;AACjE,SAASC,kBAAkB,QAAQ,oCAAmC;AACtE,SACEC,oBAAoB,EACpBC,qBAAqB,EACrBC,kBAAkB,QACb,yBAAwB;AAC/B,SAASC,6BAA6B,QAAQ,qCAAoC;AAClF,SAASC,0BAA0B,QAAQ,kCAAiC;AAC5E,SAASC,iBAAiB,QAAQ,mCAAkC;AACpE,SAASC,0BAA0B,QAAQ,4BAA2B;AACtE,SAASC,OAAO,QAAQ,iBAAgB;AAExC,SAASC,iBAAiB,EAAEC,kBAAkB,QAAQ,sBAAqB;AAC3E,SAASC,+BAA+B,QAAQ,yBAAwB;AACxE,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SACEC,wBAAwB,QAEnB,kCAAiC;AACxC,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SAASC,eAAe,QAAQ,gBAAe;AAC/C,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,SAAS,QAAQ,eAAc;AAExC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;AAClE,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SAASC,kBAAkB,QAAQ,mBAAkB;;AAi5BvBqO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9wBvB,MAAMpO,wBAAwBC;AAAO;AAIrC,MAAMC,0BAA0BD;IAGrCE,YAAYC,UAAiB,CAAE;QAC7B,KAAK;QACL,IAAI,CAACA,UAAU,GAAGA;IACpB;AACF;AAae,MAAeC;IAkGlBC,8BAEI;QACZ,OAAO,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,wBAAwB,GACvDC,WAAmBC,0BAA0B,GAC9CC;IACN;IAoBA;;;;GAIC,GAED,YAAmBC,OAAsB,CAAE;YAsCrB,uBAiEE,mCAQL;aAyDXC,gBAAAA,GAAgE,CACtEC,KACAC,MACAC;gBAII,+BAWO;YAbX,IAAI,CAACA,UAAUC,QAAQ,EAAE,OAAO;YAEhC,IAAA,CAAI,gCAAA,IAAI,CAACC,WAAW,CAACC,WAAW,KAAA,OAAA,KAAA,IAA5B,8BAA8BC,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC3DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACC,WAAW,CAACE,SAAS,CACzDL,UAAUC,QAAQ,EAClB;gBAGF,iDAAiD;gBACjDH,IAAIQ,OAAO,iMAAClF,aAAAA,CAAWmF,WAAW,GAAG,GAAG;gBACxCT,IAAIQ,OAAO,iMAAChF,8BAAAA,CAA4BiF,WAAW,GAAG,GAAG;gBACzD5F,8LAAAA,EAAemF,KAAK,gBAAgB;6LACpCnF,iBAAAA,EAAemF,KAAK,wBAAwB;YAC9C,OAAO,IAAA,CAAI,wBAAA,IAAI,CAACI,WAAW,CAACM,GAAG,KAAA,OAAA,KAAA,IAApB,sBAAsBJ,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC1DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACM,GAAG,CAACH,SAAS,CACjDL,UAAUC,QAAQ,EAClB;gBAGF,qCAAqC;gBACrCH,IAAIQ,OAAO,iMAAClF,aAAAA,CAAWmF,WAAW,GAAG,GAAG;6LACxC5F,iBAAAA,EAAemF,KAAK,gBAAgB;YACtC,OAAO,IAAIA,IAAIQ,OAAO,CAAC,sBAAsB,EAAE;gBAC7C,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,uEAAuE;gBACvE,uCAAuC;yNACvC7C,qBAAAA,EAAmBqC,IAAIQ,OAAO;gBAE9B,OAAO;YACT,OAAO,IAAIR,IAAIQ,OAAO,iMAAClF,aAAAA,CAAWmF,WAAW,GAAG,KAAK,KAAK;gBACxD5F,8LAAAA,EAAemF,KAAK,gBAAgB;gBAEpC,IAAIA,IAAIQ,OAAO,iMAAChF,8BAAAA,CAA4BiF,WAAW,GAAG,KAAK,KAAK;iMAClE5F,iBAAAA,EAAemF,KAAK,wBAAwB;gBAC9C;YACF,OAAO;gBACL,gDAAgD;gBAChD,OAAO;YACT;YAEA,IAAIA,IAAIW,GAAG,EAAE;gBACX,MAAMC,wLAASvH,QAAAA,EAAS2G,IAAIW,GAAG;gBAC/BC,OAAOT,QAAQ,GAAGD,UAAUC,QAAQ;gBACpCH,IAAIW,GAAG,kLAAGxH,SAAAA,EAAUyH;YACtB;YAEA,OAAO;QACT;aAEQC,qBAAAA,GACN,OAAOb,KAAKc,KAAKZ;YACf,MAAMa,aAAa,IAAI,CAACC,aAAa;YACrC,MAAMC,+MAAS1D,wBAAAA,EAAsB2C,UAAUC,QAAQ;YAEvD,gCAAgC;YAChC,IAAI,CAACc,UAAU,CAACA,OAAOC,IAAI,EAAE;gBAC3B,OAAO;YACT;YAEA,IAAID,OAAOC,IAAI,CAAC,EAAE,KAAK,IAAI,CAACC,OAAO,EAAE;gBACnC,6DAA6D;gBAC7D,IACEC,QAAQC,GAAG,CAACC,YAAY,qBAAK,uLAC7BxG,iBAAAA,EAAekF,KAAK,qBACpB;;gBAEF;gBAEA,gDAAgD;gBAChD,MAAM,IAAI,CAACuB,SAAS,CAACvB,KAAKc,KAAKZ;gBAC/B,OAAO;YACT;YAEA,0BAA0B;YAC1Be,OAAOC,IAAI,CAACM,KAAK;YAEjB,MAAMC,YAAYR,OAAOC,IAAI,CAACD,OAAOC,IAAI,CAACQ,MAAM,GAAG,EAAE;YAErD,wCAAwC;YACxC,IAAI,OAAOD,cAAc,YAAY,CAACA,UAAUE,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,CAACJ,SAAS,CAACvB,KAAKc,KAAKZ;gBAC/B,OAAO;YACT;YAEA,4BAA4B;YAC5B,IAAIC,WAAW,CAAC,CAAC,EAAEc,OAAOC,IAAI,CAACU,IAAI,CAAC,MAAM;YAC1CzB,wOAAW3C,UAAAA,EAAsB2C,UAAU;YAE3C,iDAAiD;YACjD,IAAIY,YAAY;gBACd,IAAI,IAAI,CAACvB,UAAU,CAACqC,aAAa,IAAI,CAAC1B,SAASwB,QAAQ,CAAC,MAAM;oBAC5DxB,YAAY;gBACd;gBACA,IACE,CAAC,IAAI,CAACX,UAAU,CAACqC,aAAa,IAC9B1B,SAASuB,MAAM,GAAG,KAClBvB,SAASwB,QAAQ,CAAC,MAClB;oBACAxB,WAAWA,SAAS2B,SAAS,CAAC,GAAG3B,SAASuB,MAAM,GAAG;gBACrD;YACF;YAEA,IAAI,IAAI,CAACK,YAAY,EAAE;oBAEJ/B;gBADjB,gDAAgD;gBAChD,MAAMgC,WAAWhC,OAAAA,OAAAA,KAAAA,IAAAA,CAAAA,oBAAAA,IAAKQ,OAAO,CAACyB,IAAI,KAAA,OAAA,KAAA,IAAjBjC,kBAAmBkC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACzB,WAAW;gBAEhE,MAAM0B,eAAe,IAAI,CAACJ,YAAY,CAACK,kBAAkB,CAACJ;gBAC1D,MAAMK,gBACJF,CAAAA,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcE,aAAa,KAAI,IAAI,CAACN,YAAY,CAACO,MAAM,CAACD,aAAa;gBAEvE,MAAME,mBAAmB,IAAI,CAACR,YAAY,CAACS,OAAO,CAACrC;gBAEnD,gEAAgE;gBAChE,qBAAqB;gBACrB,IAAIoC,iBAAiBE,cAAc,EAAE;oBACnCtC,WAAWoC,iBAAiBpC,QAAQ;gBACtC;gBAEA,gEAAgE;gBAChED,UAAUwC,KAAK,CAACC,YAAY,GAAGJ,iBAAiBE,cAAc;gBAC9DvC,UAAUwC,KAAK,CAACE,mBAAmB,GAAGP;gBAEtC,oEAAoE;gBACpE,oCAAoC;gBACpC,IAAI,CAACE,iBAAiBE,cAAc,EAAE;oBACpC,OAAOvC,UAAUwC,KAAK,CAACG,+BAA+B;gBACxD;gBAEA,kEAAkE;gBAClE,wBAAwB;gBACxB,IAAI,CAACN,iBAAiBE,cAAc,IAAI,CAAC1B,YAAY;oBACnDb,UAAUwC,KAAK,CAACC,YAAY,GAAGN;oBAC/B,MAAM,IAAI,CAACd,SAAS,CAACvB,KAAKc,KAAKZ;oBAC/B,OAAO;gBACT;YACF;YAEAA,UAAUC,QAAQ,GAAGA;YACrBD,UAAUwC,KAAK,CAACI,aAAa,GAAG;YAEhC,OAAO;QACT;aAEQC,sBAAAA,GAGN,IAAM;aAEAC,2BAAAA,GAGN,IAAM;aAEAC,+BAAAA,GAGN,IAAM;QAmuBV;;;;;;GAMC,GAAA,IAAA,CACO1C,SAAAA,GAAY,CAACJ;YACnB,MAAMC,cAAyC,EAAE;YAEjD,IAAI,IAAI,CAACA,WAAW,CAAC8C,IAAI,EAAE;gBACzB9C,YAAY+C,IAAI,CAAC,IAAI,CAAC/C,WAAW,CAAC8C,IAAI;YACxC;YAEA,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,IAAI,CAAC9C,WAAW,CAACC,WAAW,EAAE;gBAChCD,YAAY+C,IAAI,CAAC,IAAI,CAAC/C,WAAW,CAACC,WAAW;YAC/C;YAEA,IAAI,IAAI,CAACD,WAAW,CAACM,GAAG,EAAE;gBACxBN,YAAY+C,IAAI,CAAC,IAAI,CAAC/C,WAAW,CAACM,GAAG;YACvC;YAEA,KAAK,MAAM0C,cAAchD,YAAa;gBACpC,IAAI,CAACgD,WAAW9C,KAAK,CAACH,WAAW;gBAEjC,OAAOiD,WAAW7C,SAAS,CAACJ,UAAU;YACxC;YAEA,OAAOA;QACT;aAEQkD,0BAAAA,GAGJ,OAAOrD,KAAKc,KAAKH;YACnB,IAAI2C,WAAW,MAAM,IAAI,CAACP,sBAAsB,CAAC/C,KAAKc,KAAKH;YAC3D,IAAI2C,UAAU,OAAO;YAErB,IAAI,IAAI,CAACC,kBAAkB,CAACC,KAAK,EAAE;gBACjCF,WAAW,MAAM,IAAI,CAACzC,qBAAqB,CAACb,KAAKc,KAAKH;gBACtD,IAAI2C,UAAU,OAAO;YACvB;YAEA,OAAO;QACT;aAgCUG,QAAAA,GAAoB;aACpBC,eAAAA,GAAwC;aA8rE1CC,oBAAAA,GAAuBxJ,qLAAAA,EAAS;YACtCM,sKAAImJ,IAAI,CACN,CAAC,iNAAiN,CAAC;QAEvN;QA9zGE,MAAM,EACJC,MAAM,GAAG,EACTC,QAAQ,KAAK,EACbC,IAAI,EACJC,MAAM,KAAK,EACXC,cAAc,KAAK,EACnBC,eAAe,IAAI,EACnBlC,QAAQ,EACRmC,IAAI,EACJC,qBAAqB,EACtB,GAAGtE;QAEJ,IAAI,CAACsE,qBAAqB,GAAGA;QAC7B,IAAI,CAACC,aAAa,GAAGvE;QAErB,IAAI,CAAC+D,GAAG,GACNzC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAASuC,MAAMS,QAAQ,QAAQC,OAAO,CAACV;QAEtE,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACU,aAAa,CAAC;YAAER;QAAI;QAEzB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAACxE,UAAU,GAAGuE;QAClB,IAAI,CAAC/B,QAAQ,GAAGA;QAChB,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB,mDAAmD;YACnD,IAAI,CAACyC,aAAa,0LAAGnL,iBAAAA,EAAe,IAAI,CAAC0I,QAAQ;QACnD;QACA,IAAI,CAACmC,IAAI,GAAGA;QACZ,IAAI,CAACO,OAAO,GACVtD,QAAQC,GAAG,CAACC,YAAY,KAAK,UACzB,IAAI,CAAC9B,UAAU,CAACkF,OAAO,GACvBJ,QAAQ,QAAQ1C,IAAI,CAAC,IAAI,CAACiC,GAAG,EAAE,IAAI,CAACrE,UAAU,CAACkF,OAAO;QAC5D,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;QAClC,IAAI,CAACC,YAAY,GAAG,CAACZ,eAAe,IAAI,CAACa,eAAe;QAExD,IAAI,CAAC/C,YAAY,GAAG,CAAA,CAAA,wBAAA,IAAI,CAACvC,UAAU,CAACuF,IAAI,KAAA,OAAA,KAAA,IAApB,sBAAsBC,OAAO,IAC7C,IAAIvI,gMAAAA,CAAa,IAAI,CAAC+C,UAAU,CAACuF,IAAI,IACrClF;QAEJ,yEAAyE;QACzE,IAAI,CAACoF,gBAAgB,GAAG,IAAI,CAAClD,YAAY,GACrC,yMAAIjG,yBAAAA,CAAsB,IAAI,CAACiG,YAAY,IAC3ClC;QAEJ,6CAA6C;QAC7C,2DAA2D;QAC3D,MAAM,EACJqF,sBAAsB,CAAC,CAAC,EACxBC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACd,GAAG,IAAI,CAAC7F,UAAU;QAEnB,IAAI,CAAC2B,OAAO,GAAG,IAAI,CAACmE,UAAU;QAC9B,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAClBtB,eAAe,CAAC,CAAC7C,QAAQC,GAAG,CAACmE,yBAAyB;QAExD,IAAI,CAACjC,kBAAkB,GAAG,IAAI,CAACkC,qBAAqB,CAACzB;QAErD,IAAI,CAAC0B,eAAe,GAClB,IAAI,CAACnC,kBAAkB,CAACoC,GAAG,4LAC3BpH,uBAAAA,EAAqB,IAAI,CAACiB,UAAU,CAACC,YAAY,CAACmG,GAAG;QAEvD,IAAI,CAACxF,WAAW,GAAG;YACjB,uEAAuE;YACvE,wEAAwE;YACxE,uCAAuC;YACvCM,KACE,IAAI,CAAC6C,kBAAkB,CAACoC,GAAG,IAAI,IAAI,CAAC1B,WAAW,GAC3C,0LAAIvG,yBAAAA,KACJmC;YACNQ,aACE,IAAI,CAACqF,eAAe,IAAI,IAAI,CAACzB,WAAW,GACpC,uMAAIlG,gCAAAA,KACJ8B;YACNqD,MAAM,IAAI,CAACK,kBAAkB,CAACC,KAAK,GAC/B,oMAAIxF,6BAAAA,CAA2B,IAAI,CAACmD,OAAO,IAC3CtB;QACN;QAEA,IAAI,CAACgG,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAEhD,IAAI1E,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;QAEzC;QAEA,IAAI,CAAC2E,UAAU,GAAG;YAChBC,yBAAyB;YACzBrE,eAAe,IAAI,CAACrC,UAAU,CAACqC,aAAa;YAC5CmE,cAAc,IAAI,CAACxG,UAAU,CAACwG,YAAY;YAC1CG,gBAAgB,IAAI,CAAC3G,UAAU,CAACC,YAAY,CAAC0G,cAAc,IAAI;YAC/DC,iBAAiB,IAAI,CAAC5G,UAAU,CAAC4G,eAAe;YAChDC,eAAe,IAAI,CAAC7G,UAAU,CAAC8G,GAAG,CAACD,aAAa,IAAI;YACpDlF,SAAS,IAAI,CAACA,OAAO;YACrBkE;YACAkB,cAAc,IAAI,CAACC,oBAAoB,GAAGC,OAAO;YACjDvC,cAAcA,iBAAiB,OAAO,OAAOrE;YAC7C6G,kBAAkB,EAAA,CAAE,oCAAA,IAAI,CAAClH,UAAU,CAACC,YAAY,CAAC6G,GAAG,KAAA,OAAA,KAAA,IAAhC,kCAAkCK,SAAS;YAC/DC,UAAU,IAAI,CAACpH,UAAU,CAACoH,QAAQ;YAClCC,QAAQ,IAAI,CAACrH,UAAU,CAACqH,MAAM;YAC9BC,aAAa,IAAI,CAACtH,UAAU,CAACC,YAAY,CAACqH,WAAW;YACrDC,kBAAkB,IAAI,CAACvH,UAAU,CAACwH,MAAM;YACxCC,mBAAmB,IAAI,CAACzH,UAAU,CAACC,YAAY,CAACwH,iBAAiB;YACjEC,yBACE,IAAI,CAAC1H,UAAU,CAACC,YAAY,CAACyH,uBAAuB;YACtDC,aAAa,EAAA,CAAE,yBAAA,IAAI,CAAC3H,UAAU,CAACuF,IAAI,KAAA,OAAA,KAAA,IAApB,uBAAsBqC,OAAO;YAC5C1C,SAAS,IAAI,CAACA,OAAO;YACrB2C,kBAAkB,IAAI,CAAC9D,kBAAkB,CAACoC,GAAG;YAC7C2B,mBAAmB,IAAI,CAAC9H,UAAU,CAACC,YAAY,CAAC8H,SAAS;YACzDC,gBAAgB,IAAI,CAAChI,UAAU,CAACC,YAAY,CAACgI,KAAK;YAClDC,aAAa,IAAI,CAAClI,UAAU,CAACkI,WAAW,GACpC,IAAI,CAAClI,UAAU,CAACkI,WAAW,GAC3B7H;YACJ8H,oBAAoB,IAAI,CAACnI,UAAU,CAACC,YAAY,CAACkI,kBAAkB;YACnE,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAAC3C,qBAAqBzD,MAAM,GAAG,IACtCyD,sBACAtF;YAEN,uDAAuD;YACvDkI,uBAAuB,IAAI,CAACvI,UAAU,CAACC,YAAY,CAACsI,qBAAqB;YACzEtI,cAAc;gBACZuI,YAAY,IAAI,CAACxI,UAAU,CAACwI,UAAU;gBACtCC,qBAAqB,IAAI,CAACzI,UAAU,CAACC,YAAY,CAACwI,mBAAmB;gBACrEC,WAAW,IAAI,CAAC1I,UAAU,CAACC,YAAY,CAACyI,SAAS,IAAI;gBACrDC,WAAW,IAAI,CAAC3I,UAAU,CAACC,YAAY,CAAC0I,SAAS,IAAI;gBACrDC,gBAAgB,CAAC,CAAC,IAAI,CAAC5I,UAAU,CAACC,YAAY,CAAC2I,cAAc;YAC/D;YACAC,+BACE,IAAI,CAACC,6BAA6B,CAACC,IAAI,CAAC,IAAI;YAC9CC,uBAAuB,IAAI,CAAChJ,UAAU,CAACgJ,qBAAqB;QAC9D;QAEA,4DAA4D;0MAC5DvO,YAAAA,EAAU;YACRiL;YACAC;QACF;QAEA,IAAI,CAACsD,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,wBAAwB;QACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;QAErC,0EAA0E;QAC1E,yEAAyE;QACzE,kDAAkD;QAClD,KAAK,IAAI,CAACD,QAAQ,CAACE,MAAM;QAEzB,IAAI,CAACC,cAAc,CAAChE;QACpB,IAAI,CAACiE,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAAEtF;QAAI;IACnD;IAEUuF,iBAAiB;QACzB,OAAO,IAAI,CAACN,QAAQ,CAACE,MAAM;IAC7B;IAuKUD,mBAAwC;QAChD,yEAAyE;QACzE,MAAMM,iBAAiB,6PAAIpN,uBAAAA,CAAqB,CAACqN;YAC/C,OAAQA;gBACN,+LAAK9P,iBAAAA;oBACH,OAAO,IAAI,CAAC+O,gBAAgB,MAAM;gBACpC,+LAAKjP,qBAAAA;oBACH,OAAO,IAAI,CAACmP,mBAAmB,MAAM;gBACvC;oBACE,OAAO;YACX;QACF;QAEA,uCAAuC;QACvC,MAAMK,WAAgC,oOAAIlN,6BAAAA;QAE1C,8BAA8B;QAC9BkN,SAAS9F,IAAI,CACX,oOAAIhH,4BAAAA,CACF,IAAI,CAACuI,OAAO,EACZ8E,gBACA,IAAI,CAACzH,YAAY;QAIrB,uCAAuC;QACvCkH,SAAS9F,IAAI,CACX,2OAAIjH,+BAAAA,CACF,IAAI,CAACwI,OAAO,EACZ8E,gBACA,IAAI,CAACzH,YAAY;QAIrB,2EAA2E;QAC3E,IAAI,IAAI,CAACwB,kBAAkB,CAACoC,GAAG,EAAE;YAC/B,gCAAgC;YAChCsD,SAAS9F,IAAI,CACX,0OAAInH,8BAAAA,CAA4B,IAAI,CAAC0I,OAAO,EAAE8E;YAEhDP,SAAS9F,IAAI,CACX,2OAAIlH,+BAAAA,CAA6B,IAAI,CAACyI,OAAO,EAAE8E;QAEnD;QAEA,OAAOP;IACT;IAEA,MAAgBX,8BACd,GAAGoB,IAAqD,EACxD;QACA,MAAM,CAACC,KAAK3J,KAAK4J,IAAI,GAAGF;QAExB,IAAI,IAAI,CAACG,eAAe,EAAE;YACxB,IAAI;gBACF,MAAA,CAAM,IAAI,CAACA,eAAe,CAACC,cAAc,IAAA,OAAA,KAAA,IAAnC,IAAI,CAACD,eAAe,CAACC,cAAc,CAAA,IAAA,CAAnC,IAAI,CAACD,eAAe,EACxBF,KACA;oBACEzI,MAAMlB,IAAIW,GAAG,IAAI;oBACjBoJ,QAAQ/J,IAAI+J,MAAM,IAAI;oBACtB,gEAAgE;oBAChEvJ,SACER,sLAAetB,mBAAAA,GACXmJ,OAAOmC,WAAW,CAAChK,IAAIQ,OAAO,CAACyJ,OAAO,MACtCjK,IAAIQ,OAAO;gBACnB,GACAoJ,IAAAA;YAEJ,EAAE,OAAOM,YAAY;gBACnB,qFAAqF;gBACrFC,QAAQC,KAAK,CAAC,4CAA4CF;YAC5D;QACF;IACF;IAEOG,SAASV,GAAU,EAAQ;QAChC,IAAI,IAAI,CAAC7F,KAAK,EAAE;QAChBrJ,sKAAI2P,KAAK,CAACT;IACZ;IAEA,MAAaW,cACXtK,GAAkB,EAClBc,GAAmB,EACnBZ,SAAkC,EACnB;QACf,MAAM,IAAI,CAACqK,OAAO;QAClB,MAAMR,SAAS/J,IAAI+J,MAAM,CAACS,WAAW;QAErC,MAAMC,6LAASpO,YAAAA;QACf,OAAOoO,OAAOC,qBAAqB,CAAC1K,IAAIQ,OAAO,EAAE;YAC/C,OAAOiK,OAAOE,KAAK,oLACjBnO,iBAAAA,CAAe8N,aAAa,EAC5B;gBACEM,UAAU,GAAGb,OAAO,CAAC,EAAE/J,IAAIW,GAAG,EAAE;gBAChCkK,sLAAMtO,WAAAA,CAASuO,MAAM;gBACrBC,YAAY;oBACV,eAAehB;oBACf,eAAe/J,IAAIW,GAAG;gBACxB;YACF,GACA,OAAOqK,OACL,IAAI,CAACC,iBAAiB,CAACjL,KAAKc,KAAKZ,WAAWgL,OAAO,CAAC;oBAClD,IAAI,CAACF,MAAM;oBAEX,MAAMG,4LAAerQ,iBAAAA,EAAekF,KAAK,mBAAmB;oBAC5DgL,KAAKI,aAAa,CAAC;wBACjB,oBAAoBtK,IAAIuK,UAAU;wBAClC,YAAYF;oBACd;oBAEA,MAAMG,qBAAqBb,OAAOc,qBAAqB;oBACvD,iEAAiE;oBACjE,IAAI,CAACD,oBAAoB;oBAEzB,IACEA,mBAAmBE,GAAG,CAAC,sBACvBhP,oMAAAA,CAAe8N,aAAa,EAC5B;wBACAH,QAAQvG,IAAI,CACV,CAAC,2BAA2B,EAAE0H,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;wBAE1E;oBACF;oBAEA,MAAMC,QAAQH,mBAAmBE,GAAG,CAAC;oBACrC,IAAIC,OAAO;wBACT,MAAMhC,OAAO0B,eACT,CAAC,IAAI,EAAEpB,OAAO,CAAC,EAAE0B,OAAO,GACxB,GAAG1B,OAAO,CAAC,EAAE0B,OAAO;wBAExBT,KAAKI,aAAa,CAAC;4BACjB,cAAcK;4BACd,cAAcA;4BACd,kBAAkBhC;wBACpB;wBACAuB,KAAKU,UAAU,CAACjC;oBAClB,OAAO;wBACLuB,KAAKU,UAAU,CACbP,eACI,CAAC,IAAI,EAAEpB,OAAO,CAAC,EAAE/J,IAAIW,GAAG,EAAE,GAC1B,GAAGoJ,OAAO,CAAC,EAAE/J,IAAIW,GAAG,EAAE;oBAE9B;gBACF;QAEN;IACF;IAEA,MAAcsK,kBACZjL,GAAkB,EAClBc,GAAmB,EACnBZ,SAAkC,EACnB;QACf,IAAI;gBAiDKyL,yBAS4BA,0BAI9B,oBAagB,qBAKY;YA/EjC,qCAAqC;YACrC,MAAM,IAAI,CAAC1C,QAAQ,CAAC2C,aAAa;YAEjC,kDAAkD;YAClD,kDAAkD;uMAClDtN,kCAAAA,EACE0B,0LACA3B,qBAAAA,EAAmByC,OAAOA,IAAI+K,gBAAgB,GAAG/K;YAGnD,MAAMgL,WAAY9L,CAAAA,IAAIW,GAAG,IAAI,EAAC,EAAGuB,KAAK,CAAC,KAAK;YAC5C,MAAM6J,aAAaD,QAAQ,CAAC,EAAE;YAE9B,oEAAoE;YACpE,+DAA+D;YAC/D,wEAAwE;YACxE,WAAW;YACX,IAAIC,cAAAA,OAAAA,KAAAA,IAAAA,WAAYzL,KAAK,CAAC,cAAc;gBAClC,MAAM0L,qLAAWhT,2BAAAA,EAAyBgH,IAAIW,GAAG;gBACjDG,IAAImL,QAAQ,CAACD,UAAU,KAAKE,IAAI,CAACF,UAAUG,IAAI;gBAC/C;YACF;YAEA,sCAAsC;YACtC,IAAI,CAACjM,aAAa,OAAOA,cAAc,UAAU;gBAC/C,IAAI,CAACF,IAAIW,GAAG,EAAE;oBACZ,MAAM,IAAIzB,MAAM;gBAClB;gBAEAgB,2LAAY7G,QAAAA,EAAS2G,IAAIW,GAAG,EAAG;YACjC;YAEA,IAAI,CAACT,UAAUC,QAAQ,EAAE;gBACvB,MAAM,IAAIjB,MAAM;YAClB;YAEA,iFAAiF;YACjF,IAAI,OAAOgB,UAAUwC,KAAK,KAAK,UAAU;gBACvCxC,UAAUwC,KAAK,GAAGmF,OAAOmC,WAAW,CAClC,IAAIoC,gBAAgBlM,UAAUwC,KAAK;YAEvC;YAEA,sCAAsC;YACtC,MAAM,EAAEiJ,kBAAkB,IAAI,EAAE,wLAAGvN,oBAAAA,EAAkB4B,OAAOA,MAAM,CAAC;YACnE,MAAMqM,kBAAkBV,mBAAAA,OAAAA,KAAAA,IAAAA,gBAAiBnL,OAAO,CAAC,oBAAoB;YACrE,MAAM8L,UAAUD,kBACZA,oBAAoB,UACpB,CAAC,CAAA,CAAEV,mBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,0BAAAA,gBAAiBY,MAAM,KAAA,OAAA,KAAA,IAAvBZ,wBAAuCa,SAAS;YAEvDxM,IAAIQ,OAAO,CAAC,mBAAmB,KAAKR,IAAIQ,OAAO,CAAC,OAAO,IAAI,IAAI,CAACwB,QAAQ;YACxEhC,IAAIQ,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAAC2D,IAAI,GACzC,IAAI,CAACA,IAAI,CAACsI,QAAQ,KAClBH,UACE,QACA;YACNtM,IAAIQ,OAAO,CAAC,oBAAoB,KAAK8L,UAAU,UAAU;YACzDtM,IAAIQ,OAAO,CAAC,kBAAkB,KAAKmL,mBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,2BAAAA,gBAAiBY,MAAM,KAAA,OAAA,KAAA,IAAvBZ,yBAAyBe,aAAa;YAEzE,0EAA0E;YAC1E,6CAA6C;YAC7C,IAAI,CAAA,CAAA,CAAC,qBAAA,IAAI,CAAC3K,YAAY,KAAA,OAAA,KAAA,IAAjB,mBAAmB4K,aAAa,CAACzM,UAAUwC,KAAK,CAAA,GAAG;gBACtD,OAAOxC,UAAUwC,KAAK,CAACC,YAAY;gBACnC,OAAOzC,UAAUwC,KAAK,CAACE,mBAAmB;gBAC1C,OAAO1C,UAAUwC,KAAK,CAACG,+BAA+B;YACxD;YAEA,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAI,CAAC+J,iBAAiB,CAAC5M,KAAKE;YAE5B,IAAIoD,WAAW,MAAM,IAAI,CAACvD,gBAAgB,CAACC,KAAKc,KAAKZ;YACrD,IAAIoD,UAAU;YAEd,MAAMnB,eAAAA,CAAe,sBAAA,IAAI,CAACJ,YAAY,KAAA,OAAA,KAAA,IAAjB,oBAAmBK,kBAAkB,qLACxDjH,cAAAA,EAAY+E,WAAWF,IAAIQ,OAAO;YAGpC,MAAM6B,gBACJF,CAAAA,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcE,aAAa,KAAA,CAAA,CAAI,wBAAA,IAAI,CAAC7C,UAAU,CAACuF,IAAI,KAAA,OAAA,KAAA,IAApB,sBAAsB1C,aAAa;YACpEnC,UAAUwC,KAAK,CAACE,mBAAmB,GAAGP;YAEtC,MAAM1B,0MAAMvF,WAAAA,EAAa4E,IAAIW,GAAG,CAACkM,OAAO,CAAC,QAAQ;YACjD,MAAMC,sOAAezR,sBAAAA,EAAoBsF,IAAIR,QAAQ,EAAE;gBACrDX,YAAY,IAAI,CAACA,UAAU;gBAC3BuC,cAAc,IAAI,CAACA,YAAY;YACjC;YACApB,IAAIR,QAAQ,GAAG2M,aAAa3M,QAAQ;YAEpC,IAAI2M,aAAalG,QAAQ,EAAE;gBACzB5G,IAAIW,GAAG,mNAAG1F,mBAAAA,EAAiB+E,IAAIW,GAAG,EAAG,IAAI,CAACnB,UAAU,CAACoH,QAAQ;YAC/D;YAEA,MAAMmG,uBACJ,IAAI,CAAC9I,WAAW,IAAI,OAAOjE,IAAIQ,OAAO,CAACxD,sLAAAA,CAAoB,KAAK;YAElE,uCAAuC;YACvC,IAAI+P,sBAAsB;gBACxB,IAAI;wBAuBE,wBA2ByB,qBAkDjB;oBAnGZ,IAAI,IAAI,CAACxJ,kBAAkB,CAACoC,GAAG,EAAE;wBAC/B,iDAAiD;wBACjD,kBAAkB;wBAClB,IAAI3F,IAAIW,GAAG,CAACL,KAAK,CAAC,mBAAmB;4BACnCN,IAAIW,GAAG,GAAGX,IAAIW,GAAG,CAACkM,OAAO,CAAC,YAAY;wBACxC;wBACA3M,UAAUC,QAAQ,GAChBD,UAAUC,QAAQ,KAAK,WAAW,MAAMD,UAAUC,QAAQ;oBAC9D;oBAEA,4DAA4D;oBAC5D,sEAAsE;oBACtE,IAAI,EAAEA,UAAU6M,WAAW,EAAE,GAAG,IAAIC,IAClCjN,IAAIQ,OAAO,iKAACxD,sBAAAA,CAAoB,EAChC;oBAGF,IAAI,EAAEmD,UAAU+M,WAAW,EAAE,GAAG,IAAID,IAAIjN,IAAIW,GAAG,EAAE;oBAEjD,2DAA2D;oBAC3D,yDAAyD;oBACzD,6CAA6C;oBAC7C,IAAA,CAAI,yBAAA,IAAI,CAACP,WAAW,CAAC8C,IAAI,KAAA,OAAA,KAAA,IAArB,uBAAuB5C,KAAK,CAAC4M,cAAc;wBAC7ChN,UAAUwC,KAAK,CAACI,aAAa,GAAG;oBAClC,OAGK,IACH,IAAI,CAAC4C,eAAe,IACpB,IAAI,CAACzB,WAAW,IAChBjE,IAAIQ,OAAO,iKAACrD,qBAAAA,CAAmB,KAAK,OACpC6C,IAAI+J,MAAM,KAAK,QACf;wBACA,oEAAoE;wBACpE,oEAAoE;wBACpE,cAAc;wBACd,MAAMmC,OAAsB,EAAE;wBAC9B,WAAW,MAAMiB,SAASnN,IAAIkM,IAAI,CAAE;4BAClCA,KAAK/I,IAAI,CAACgK;wBACZ;wBACA,MAAMC,2IAAYC,CAAOC,MAAM,CAACpB,MAAMO,QAAQ,CAAC;qMAE/C5R,iBAAAA,EAAemF,KAAK,aAAaoN;oBACnC;oBAEAJ,cAAc,IAAI,CAACzM,SAAS,CAACyM;oBAC7B,MAAMO,oBAAoB,IAAI,CAACC,iBAAiB,CAACN;oBAEjD,8CAA8C;oBAC9C,MAAMO,uBAAAA,CAAuB,sBAAA,IAAI,CAAC1L,YAAY,KAAA,OAAA,KAAA,IAAjB,oBAAmBS,OAAO,CAACwK,aAAa;wBACnE3K;oBACF;oBAEA,+DAA+D;oBAC/D,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAIoL,sBAAsB;wBACxBvN,UAAUwC,KAAK,CAACC,YAAY,GAAG8K,qBAAqBhL,cAAc;wBAElE,kEAAkE;wBAClE,+DAA+D;wBAC/D,IAAIgL,qBAAqBC,mBAAmB,EAAE;4BAC5CxN,UAAUwC,KAAK,CAACG,+BAA+B,GAAG;wBACpD,OAAO;4BACL,OAAO3C,UAAUwC,KAAK,CAACG,+BAA+B;wBACxD;oBACF;oBAEA,0CAA0C;oBAC1CmK,8NAAcxS,sBAAAA,EAAoBwS;oBAElC,IAAIW,cAAcX;oBAClB,IAAIY,qNAAgB7T,iBAAAA,EAAe4T;oBAEnC,IAAI,CAACC,eAAe;wBAClB,MAAMtN,QAAQ,MAAM,IAAI,CAAC2I,QAAQ,CAAC3I,KAAK,CAACqN,aAAa;4BACnD5I,MAAM0I;wBACR;wBAEA,6DAA6D;wBAC7D,IAAInN,OAAO;4BACTqN,cAAcrN,MAAMuN,UAAU,CAAC1N,QAAQ;4BACvC,iDAAiD;4BACjDyN,gBAAgB,OAAOtN,MAAMW,MAAM,KAAK;wBAC1C;oBACF;oBAEA,qEAAqE;oBACrE,oEAAoE;oBACpE,oDAAoD;oBACpD,IAAIwM,sBAAsB;wBACxBT,cAAcS,qBAAqBtN,QAAQ;oBAC7C;oBAEA,MAAM2N,qLAAQpT,WAAAA,EAAS;wBACrBkT;wBACAG,MAAMJ;wBACN5I,MAAM,IAAI,CAACvF,UAAU,CAACuF,IAAI;wBAC1B6B,UAAU,IAAI,CAACpH,UAAU,CAACoH,QAAQ;wBAClCoH,UAAU,CAAA,CAAA,0BAAA,IAAI,CAACC,iBAAiB,EAAA,KAAA,OAAA,KAAA,IAAtB,wBAA0BD,QAAQ,KAAI;4BAC9CE,aAAa,EAAE;4BACfC,YAAY,EAAE;4BACdC,UAAU,EAAE;wBACd;wBACAC,eAAe,CAAC,CAAC,IAAI,CAAC7O,UAAU,CAACC,YAAY,CAAC6O,mBAAmB;oBACnE;oBAEA,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIjM,iBAAiB,CAACyK,aAAayB,MAAM,EAAE;wBACzCrO,UAAUC,QAAQ,GAAG,CAAC,CAAC,EAAEkC,gBAAgBnC,UAAUC,QAAQ,EAAE;oBAC/D;oBAEA,MAAMqO,wBAAwBtO,UAAUC,QAAQ;oBAChD,MAAMsO,gBAAgBX,MAAMY,cAAc,CAAC1O,KAAKE;oBAChD,MAAMyO,mBAAmB9G,OAAOC,IAAI,CAAC2G;oBACrC,MAAMG,aAAaJ,0BAA0BtO,UAAUC,QAAQ;oBAE/D,IAAIyO,cAAc1O,UAAUC,QAAQ,EAAE;wBACpCtF,8LAAAA,EAAemF,KAAK,cAAcE,UAAUC,QAAQ;oBACtD;oBACA,MAAM0O,iBAAiB,IAAIC;oBAE3B,KAAK,MAAMC,OAAOlH,OAAOC,IAAI,CAAC5H,UAAUwC,KAAK,EAAG;wBAC9C,MAAMsM,QAAQ9O,UAAUwC,KAAK,CAACqM,IAAI;kMAElCnS,0BAAAA,EAAwBmS,KAAK,CAACE;4BAC5B,IAAI,CAAC/O,WAAW,QAAO,YAAY;4BAEnCA,UAAUwC,KAAK,CAACuM,cAAc,GAAGD;4BACjCH,eAAeK,GAAG,CAACD;4BACnB,OAAO/O,UAAUwC,KAAK,CAACqM,IAAI;wBAC7B;oBACF;oBAEA,yDAAyD;oBACzD,IAAInB,eAAe;wBACjB,IAAI3M,SAAiC,CAAC;wBAEtC,IAAIkO,eAAerB,MAAMsB,2BAA2B,CAClDlP,UAAUwC,KAAK;wBAGjB,yDAAyD;wBACzD,wDAAwD;wBACxD,wDAAwD;wBACxD,qDAAqD;wBACrD,IACE,CAACyM,aAAaE,cAAc,IAC5B,sMAACtV,iBAAAA,EAAewT,oBAChB;4BACA,IAAI+B,gBAAgBxB,MAAMyB,mBAAmB,IAAA,OAAA,KAAA,IAAzBzB,MAAMyB,mBAAmB,CAAA,IAAA,CAAzBzB,OAA4BP;4BAEhD,IAAI+B,eAAe;gCACjBxB,MAAMsB,2BAA2B,CAACE;gCAClCzH,OAAO2H,MAAM,CAACL,aAAalO,MAAM,EAAEqO;gCACnCH,aAAaE,cAAc,GAAG;4BAChC;wBACF;wBAEA,uDAAuD;wBACvD,4DAA4D;wBAC5D,oEAAoE;wBACpE,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,yBAAyB;wBACzB,IAEErC,AADA,gBACgB,YAChB,CAACmC,aAAaE,cAAc,IAC5B,EAH8D,oMAG7DtV,iBAAAA,EAAeiT,cAChB;4BACA,IAAIsC,gBAAgBxB,MAAMyB,mBAAmB,IAAA,OAAA,KAAA,IAAzBzB,MAAMyB,mBAAmB,CAAA,IAAA,CAAzBzB,OAA4Bd;4BAEhD,IAAIsC,eAAe;gCACjB,MAAMG,kBACJ3B,MAAMsB,2BAA2B,CAACE;gCAEpC,IAAIG,gBAAgBJ,cAAc,EAAE;oCAClCxH,OAAO2H,MAAM,CAACvO,QAAQqO;oCACtBH,eAAeM;gCACjB;4BACF;wBACF;wBAEA,IAAIN,aAAaE,cAAc,EAAE;4BAC/BpO,SAASkO,aAAalO,MAAM;wBAC9B;wBAEA,IACEjB,IAAIQ,OAAO,CAAC,sBAAsB,yMAClCzG,iBAAAA,EAAeiT,gBACf,CAACmC,aAAaE,cAAc,EAC5B;4BACA,MAAMK,OAA+B,CAAC;4BACtC,MAAMC,cAAc7B,MAAM8B,yBAAyB,CACjD5P,KACA0P,MACAxP,UAAUwC,KAAK,CAACC,YAAY,IAAI;4BAGlC,kEAAkE;4BAClE,qBAAqB;4BACrB,IAAI+M,KAAKnB,MAAM,EAAE;gCACfrO,UAAUwC,KAAK,CAACC,YAAY,GAAG+M,KAAKnB,MAAM;gCAE1C,6DAA6D;gCAC7D,mDAAmD;gCACnD,OAAOrO,UAAUwC,KAAK,CAACG,+BAA+B;4BACxD;4BACAsM,eAAerB,MAAMsB,2BAA2B,CAC9CO,aACA;4BAGF,IAAIR,aAAaE,cAAc,EAAE;gCAC/BpO,SAASkO,aAAalO,MAAM;4BAC9B;wBACF;wBAEA,uDAAuD;wBACvD,IACE6M,MAAM+B,mBAAmB,IACzBtC,sBAAsBI,eACtB,CAACwB,aAAaE,cAAc,IAC5B,CAACvB,MAAMsB,2BAA2B,CAAC;4BAAE,GAAGnO,MAAM;wBAAC,GAAG,MAC/CoO,cAAc,EACjB;4BACApO,SAAS6M,MAAM+B,mBAAmB;4BAElC,8DAA8D;4BAC9D,kBAAkB;yMAClBhV,iBAAAA,EAAemF,KAAK,6BAA6B;wBACnD;wBAEA,IAAIiB,QAAQ;4BACV+L,cAAcc,MAAMgC,sBAAsB,CAACnC,aAAa1M;4BACxDjB,IAAIW,GAAG,GAAGmN,MAAMgC,sBAAsB,CAAC9P,IAAIW,GAAG,EAAGM;wBACnD;oBACF;oBAEA,IAAI2M,iBAAiBgB,YAAY;4BAGdd;wBAFjBA,MAAMiC,kBAAkB,CAAC/P,KAAK,MAAM;+BAC/B2O;+BACA9G,OAAOC,IAAI,CAACgG,CAAAA,CAAAA,2BAAAA,MAAMkC,iBAAiB,KAAA,OAAA,KAAA,IAAvBlC,yBAAyBmC,MAAM,KAAI,CAAC;yBACpD;oBACH;oBACA,KAAK,MAAMlB,OAAOF,eAAgB;wBAChC,OAAO3O,UAAUwC,KAAK,CAACqM,IAAI;oBAC7B;oBACA7O,UAAUC,QAAQ,GAAG6M;oBACrBrM,IAAIR,QAAQ,GAAGD,UAAUC,QAAQ;oBACjCmD,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACrD,KAAKc,KAAKZ;oBAC3D,IAAIoD,UAAU;gBAChB,EAAE,OAAOqG,KAAK;oBACZ,IAAIA,qLAAe5Q,cAAAA,IAAe4Q,qLAAe7Q,iBAAAA,EAAgB;wBAC/DgI,IAAIuK,UAAU,GAAG;wBACjB,OAAO,IAAI,CAAC6E,WAAW,CAAC,MAAMlQ,KAAKc,KAAK,WAAW,CAAC;oBACtD;oBACA,MAAM6I;gBACR;YACF;yLAEA9O,iBAAAA,EAAemF,KAAK,kBAAkBmQ,QAAQhO;YAE9C,IAAI2K,aAAayB,MAAM,EAAE;gBACvBvO,IAAIW,GAAG,kLAAGxH,SAAAA,EAAUwH;6LACpB9F,iBAAAA,EAAemF,KAAK,kBAAkB;YACxC;YAEA,kEAAkE;YAClE,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAACiE,WAAW,IAAI,CAAC/D,UAAUwC,KAAK,CAACC,YAAY,EAAE;gBACtD,gEAAgE;gBAChE,IAAImK,aAAayB,MAAM,EAAE;oBACvBrO,UAAUwC,KAAK,CAACC,YAAY,GAAGmK,aAAayB,MAAM;gBACpD,OAGK,IAAIlM,eAAe;oBACtBnC,UAAUwC,KAAK,CAACC,YAAY,GAAGN;oBAC/BnC,UAAUwC,KAAK,CAACG,+BAA+B,GAAG;gBACpD;YACF;YAEA,kDAAkD;YAClD,uDAAuD;YACvD,iCAAiC;YACjC,IACE,CAAE,IAAI,CAACwB,aAAa,CAAS+L,eAAe,IAC5C,8KAACtV,iBAAAA,EAAekF,KAAK,qBACrB;gBACA,IAAIqQ,WAA+B;gBAEnC,IAAI;oBACF,MAAMC,gBAAgB,IAAIrD,iLACxBnS,iBAAAA,EAAekF,KAAK,cAAc,KAClC;oBAEFqQ,WAAWC,cAAcD,QAAQ;gBACnC,EAAE,OAAM,CAAC;gBAET,MAAME,mBAAmB,MAAM,IAAI,CAACC,mBAAmB,CAAC;oBACtDC,gBAAgB5I,OAAO2H,MAAM,CAAC,CAAC,GAAGxP,IAAIQ,OAAO;oBAC7CkQ,iBAAiBL,SAASvO,SAAS,CAAC,GAAGuO,SAAS3O,MAAM,GAAG;gBAG3D;gBAEA,MAAMiP,cAKFhR;gBAEJ,IAAIgR,YAAYC,mBAAmB,EAAE;wBAEhC5Q;oBADH,MAAM6Q,cACJ,CAAA,CAAC7Q,kDAAAA,IAAIQ,OAAO,iKAACvD,qCAAAA,CAAmC,KAAA,OAAA,KAAA,IAA/C+C,gDAA4DkC,KAAK,CAChE,IAAA,KACG,EAAE;oBAET,KAAK,MAAM4O,WAAWjJ,OAAOkJ,MAAM,CACjCJ,YAAYC,mBAAmB,EAC9B;wBACD,IAAI,OAAOE,QAAQE,kBAAkB,KAAK,YAAY;4BACpD,MAAMF,QAAQE,kBAAkB,IAAIH;wBACtC;oBACF;gBACF;gBAEAN,iBAAiBU,iBAAiB;4LAClCpW,kBAAAA,EAAemF,KAAK,oBAAoBuQ;gBACtC5Q,WAAmBuR,kBAAkB,GAAGX;YAC5C;YAEA,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,8KAACzV,iBAAAA,EAAekF,KAAK,6BAA6B;gBACpDnF,8LAAAA,EACEmF,KACA,4BACA,IAAI,CAACT,2BAA2B;YAEpC;YAEA,oEAAoE;YACpE,mEAAmE;YACnE,mDAAmD;YACnD,MAAM4R,0LAAarW,iBAAAA,EAAekF,KAAK;YACvC,MAAMoR,gBACJ,CAACrE,wBACD3L,QAAQC,GAAG,CAACC,YAAY,qBAAK,UAC7B6P;YAEF,IAAIC,mCAAe;;oBAkCf;YAwBJ;YAEA,IACEhQ,QAAQC,GAAG,CAACC,YAAY,qBAAK,uLAC7BxG,iBAAAA,EAAekF,KAAK,qBACpB;;YAqBF;YAEA,oEAAoE;YACpE,sDAAsD;YAEtD,+DAA+D;YAC/D,IAAI,CAAC+M,wBAAwBD,aAAalG,QAAQ,EAAE;gBAClD1G,UAAUC,QAAQ,mNAAGlF,mBAAAA,EACnBiF,UAAUC,QAAQ,EAClB2M,aAAalG,QAAQ;YAEzB;YAEA9F,IAAIuK,UAAU,GAAG;YACjB,OAAO,MAAM,IAAI,CAAC2G,GAAG,CAAChS,KAAKc,KAAKZ;QAClC,EAAE,OAAOyJ,KAAU;YACjB,IAAIA,eAAe1K,iBAAiB;gBAClC,MAAM0K;YACR;YAEA,IACGA,OAAO,OAAOA,QAAQ,YAAYA,IAAIsI,IAAI,KAAK,qBAChDtI,oLAAe5Q,eAAAA,IACf4Q,qLAAe7Q,iBAAAA,EACf;gBACAgI,IAAIuK,UAAU,GAAG;gBACjB,OAAO,IAAI,CAAC6E,WAAW,CAAC,MAAMlQ,KAAKc,KAAK,WAAW,CAAC;YACtD;YAEA,IACE,IAAI,CAACmD,WAAW,IAChB,IAAI,CAACgC,UAAU,CAACjC,GAAG,wLAClB1H,iBAAAA,EAAeqN,QAAQA,IAAIoI,MAAM,EAClC;gBACA,MAAMpI;YACR;YACA,IAAI,CAACU,QAAQ,EAACzP,sLAAAA,EAAe+O;YAC7B7I,IAAIuK,UAAU,GAAG;YACjBvK,IAAIoL,IAAI,CAAC,yBAAyBC,IAAI;QACxC;IACF;IAkDA;;GAEC,GACM+F,8BACLC,IAAiB,EACkC;QACnD,MAAMrB,UAAU,IAAI,CAACsB,iBAAiB;QACtC,OAAO,CAACpS,KAAKc,KAAKZ;yLAChBlF,iBAAAA,EAAegF,KAAKmS;YACpB,OAAOrB,QAAQ9Q,KAAKc,KAAKZ;QAC3B;IACF;IAEOkS,oBAGL;QACA,OAAO,IAAI,CAAC9H,aAAa,CAAC/B,IAAI,CAAC,IAAI;IACrC;IAQOa,eAAeiJ,MAAe,EAAQ;QAC3C,IAAI,CAACpM,UAAU,CAACb,WAAW,GAAGiN,SAASA,OAAOxF,OAAO,CAAC,OAAO,MAAM;IACrE;IAIA;;;GAGC,GACD,MAAatC,UAAyB;QACpC,IAAI,IAAI,CAAC9G,QAAQ,EAAE;QAEnB,IAAI,IAAI,CAACC,eAAe,KAAK,MAAM;YACjC,6BAA6B;YAC7B,IAAI,CAACmG,eAAe,GAAG,MAAM,IAAI,CAACyI,yBAAyB;YAC3D,IAAI,CAAC5O,eAAe,GAAG,IAAI,CAAC6O,WAAW,GAAGC,IAAI,CAAC;gBAC7C,IAAI,CAAC/O,QAAQ,GAAG;gBAChB,IAAI,CAACC,eAAe,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAACA,eAAe;IAC7B;IACA,MAAgB6O,cAA6B,CAAC;IAC9C,MAAgBD,4BAA0C,CAAC;IAE3D,MAAaG,QAAuB,CAAC;IAE3B3J,mBAA6C;QACrD,MAAMD,gBAA0C,CAAC;QAEjDhB,OAAOC,IAAI,CAAC,IAAI,CAACa,gBAAgB,IAAI,CAAC,GAAG+J,OAAO,CAAC,CAACC;YAChD,MAAMC,qNAAiB1X,mBAAAA,EAAiByX;YACxC,IAAI,CAAC9J,aAAa,CAAC+J,eAAe,EAAE;gBAClC/J,aAAa,CAAC+J,eAAe,GAAG,EAAE;YACpC;YACA/J,aAAa,CAAC+J,eAAe,CAACzP,IAAI,CAACwP;QACrC;QACA,OAAO9J;IACT;IAEA,MAAgBmJ,IACdhS,GAAkB,EAClBc,GAAmB,EACnBZ,SAA6B,EACd;QACf,2LAAO7D,YAAAA,IAAYsO,KAAK,oLAACnO,iBAAAA,CAAewV,GAAG,EAAE,UAC3C,IAAI,CAACa,OAAO,CAAC7S,KAAKc,KAAKZ;IAE3B;IAEA,MAAc2S,QACZ7S,GAAkB,EAClBc,GAAmB,EACnBZ,SAA6B,EACd;QACf,MAAM,IAAI,CAAC8C,2BAA2B,CAAChD,KAAKc,KAAKZ;IACnD;IAEA,MAAc4S,KACZC,EAEoC,EACpCC,cAGC,EACc;QACf,OAAO3W,gMAAAA,IAAYsO,KAAK,oLAACnO,iBAAAA,CAAesW,IAAI,EAAE,UAC5C,IAAI,CAACG,QAAQ,CAACF,IAAIC;IAEtB;IAEA,MAAcC,SACZF,EAEoC,EACpCC,cAGC,EACc;QACf,MAAME,gNAAe7Y,QAAAA,EAAM2Y,eAAehT,GAAG,CAACQ,OAAO,CAAC,aAAa,IAAI;QACvE,MAAMoJ,MAAqD;YACzD,GAAGoJ,cAAc;YACjB/M,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB,CAACgN;gBAC1B7Y,OAAO,CAAC,CAAC6Y;YACX;QACF;QACA,MAAMC,UAAU,MAAMJ,GAAGnJ;QACzB,IAAIuJ,YAAY,MAAM;YACpB;QACF;QACA,MAAM,EAAEnT,GAAG,EAAEc,GAAG,EAAE,GAAG8I;QACrB,MAAMwJ,iBAAiBtS,IAAIuK,UAAU;QACrC,MAAM,EAAEa,IAAI,EAAEmH,IAAI,EAAE,GAAGF;QACvB,IAAI,EAAEG,UAAU,EAAE,GAAGH;QACrB,IAAI,CAACrS,IAAIyS,IAAI,EAAE;YACb,MAAM,EAAElO,aAAa,EAAEe,eAAe,EAAEpC,GAAG,EAAE,GAAG,IAAI,CAACiC,UAAU;YAE/D,oDAAoD;YACpD,IAAIjC,KAAK;gBACPlD,IAAI0S,SAAS,CAAC,iBAAiB;gBAC/BF,aAAazT;YACf;YAEA,MAAM,IAAI,CAAC4T,gBAAgB,CAACzT,KAAKc,KAAK;gBACpC8Q,QAAQ1F;gBACRmH;gBACAhO;gBACAe;gBACAkN;gBACAtL,YAAY,IAAI,CAACxI,UAAU,CAACwI,UAAU;YACxC;YACAlH,IAAIuK,UAAU,GAAG+H;QACnB;IACF;IAEA,MAAcM,cACZX,EAEoC,EACpCC,cAGC,EACuB;QACxB,MAAMpJ,MAAqD;YACzD,GAAGoJ,cAAc;YACjB/M,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB;YAC3B;QACF;QACA,MAAMiN,UAAU,MAAMJ,GAAGnJ;QACzB,IAAIuJ,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAOA,QAAQjH,IAAI,CAACyH,iBAAiB;IACvC;IAEA,MAAaC,OACX5T,GAAkB,EAClBc,GAAmB,EACnBX,QAAgB,EAChBuC,QAA4B,CAAC,CAAC,EAC9BxC,SAAkC,EAClC2T,iBAAiB,KAAK,EACP;QACf,2LAAOxX,YAAAA,IAAYsO,KAAK,oLAACnO,iBAAAA,CAAeoX,MAAM,EAAE,UAC9C,IAAI,CAACE,UAAU,CAAC9T,KAAKc,KAAKX,UAAUuC,OAAOxC,WAAW2T;IAE1D;IAEUE,eAAsC;QAC9C,MAAMC,4NAAwBxV,2BAAAA;QAC9B,IAAIwV,uBAAuB;YACzB,2CAA2C;YAC3C,qEAAqE;YACrE,sCAAsC;YAEtC,uGAAuG;YACvG,OAAOA,sBAAsBC,SAAS;QACxC;QAEA,IAAI,IAAI,CAAChQ,WAAW,EAAE;YACpB,8EAA8E;YAC9E,4DAA4D;YAC5D,0DAA0D;YAC1D,kDAAkD;YAClD,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,wGAAwG;YACxG,wBAAwB;YACxB,OAAOpE;QACT;QAEA,OAAO,IAAI,CAACqU,oBAAoB;IAClC;IAEUA,uBAA8C;QACtD,OAAOrU;IACT;IAEA,MAAciU,WACZ9T,GAAkB,EAClBc,GAAmB,EACnBX,QAAgB,EAChBuC,QAA4B,CAAC,CAAC,EAC9BxC,SAAkC,EAClC2T,iBAAiB,KAAK,EACP;YAyBZ7T;QAxBH,IAAI,CAACG,SAASwR,UAAU,CAAC,MAAM;YAC7BxH,QAAQvG,IAAI,CACV,CAAC,8BAA8B,EAAEzD,SAAS,kBAAkB,EAAEA,SAAS,iFAAiF,CAAC;QAE7J;QAEA,IACE,IAAI,CAAC8F,UAAU,CAAC/B,YAAY,IAC5B/D,aAAa,YACb,CAAE,MAAM,IAAI,CAACgU,OAAO,CAAC,WACrB;YACA,qDAAqD;YACrD,wCAAwC;YACxChU,WAAW;QACb;QAEA,sDAAsD;QACtD,2DAA2D;QAC3D,2DAA2D;QAC3D,kEAAkE;QAClE,IACE,CAAC0T,kBACD,CAAC,IAAI,CAAC5P,WAAW,IACjB,CAACvB,MAAMI,aAAa,IACnB9C,CAAAA,CAAAA,CAAAA,WAAAA,IAAIW,GAAG,KAAA,OAAA,KAAA,IAAPX,SAASM,KAAK,CAAC,aAAA,KACb,IAAI,CAACuE,YAAY,IAAI7E,IAAIW,GAAG,CAAEL,KAAK,CAAC,cAAc,GACrD;YACA,OAAO,IAAI,CAACgK,aAAa,CAACtK,KAAKc,KAAKZ;QACtC;QAEA,uKAAI9F,gBAAAA,EAAc+F,WAAW;YAC3B,OAAO,IAAI,CAACoB,SAAS,CAACvB,KAAKc,KAAKZ;QAClC;QAEA,OAAO,IAAI,CAAC4S,IAAI,CAAC,CAAClJ,MAAQ,IAAI,CAACwK,gBAAgB,CAACxK,MAAM;YACpD5J;YACAc;YACAX;YACAuC;QACF;IACF;IAEA,MAAgB2R,eAAe,EAC7BlU,QAAQ,EAMT,EAGE;YAGC;QAFF,+DAA+D;QAC/D,MAAMmU,gBAAAA,CACJ,oDAAA,IAAI,CAAC9N,oBAAoB,GAAG+N,aAAa,CAACpU,SAAS,KAAA,OAAA,KAAA,IAAnD,kDAAqDiO,QAAQ;QAE/D,OAAO;YACL,oEAAoE;YACpE,uCAAuC;YACvCoG,aAAa3U;YACb4U,iLAAc3V,qBAAAA,EAAmBwV;QACnC;IACF;IAEA,MAAcI,+BACZC,cAA6D,EAC7DC,oBAA0C,EACT;QACjC,2LAAOvY,YAAAA,IAAYsO,KAAK,oLACtBnO,iBAAAA,CAAekY,8BAA8B,EAC7C,UACE,IAAI,CAACG,kCAAkC,CACrCF,gBACAC;IAGR;IAEUE,uBAAuBC,gBAAwB,EAAW;QAClE,kMACE7W,6BAAAA,EAA2B6W,qBAC3B,IAAI,CAAChM,yBAAyB,CAACiM,IAAI,CAAC,CAACC;YACnC,OAAOA,OAAOC,IAAI,CAACH;QACrB;IAEJ;IAEUI,cACRnV,GAAkB,EAClBc,GAAmB,EACnBsU,SAAkB,EAClBL,gBAAwB,EAClB;QACN,MAAMM,iBAAiB,mMAAG/Z,aAAAA,CAAW,EAAE,kMAAEM,gCAAAA,CAA8B,EAAE,kMAAEJ,8BAAAA,CAA4B,EAAE,kMAAEC,sCAAAA,EAAqC;QAChJ,MAAM0P,mBAAerQ,0LAAAA,EAAekF,KAAK,mBAAmB;QAE5D,IAAIsV,qBAAqB;QAEzB,IAAIF,aAAa,IAAI,CAACN,sBAAsB,CAACC,mBAAmB;YAC9D,wEAAwE;YACxE,+FAA+F;YAC/FjU,IAAI0S,SAAS,CAAC,QAAQ,GAAG6B,eAAe,EAAE,kMAAE1Z,WAAAA,EAAU;YACtD2Z,qBAAqB;QACvB,OAAO,IAAIF,aAAajK,cAAc;YACpC,yHAAyH;YACzH,mGAAmG;YACnGrK,IAAI0S,SAAS,CAAC,QAAQ6B;QACxB;QAEA,IAAI,CAACC,oBAAoB;YACvB,8GAA8G;YAC9G,sGAAsG;YACtG,OAAOtV,IAAIQ,OAAO,iMAAC7E,WAAAA,CAAS;QAC9B;IACF;IAEA,MAAckZ,mCACZ,EACE7U,GAAG,EACHc,GAAG,EACHX,QAAQ,EACR8F,YAAYyJ,IAAI,EAC8B,EAChD,EAAE6F,UAAU,EAAE7S,KAAK,EAAwB,EACV;YAcJ6S,uBA6JzBC,OA0GA,uBAIY,wBAq5BdC,mBAkCAA;QA/sCF,IAAItV,uMAAatG,6BAAAA,EAA4B;YAC3CsG,WAAW;QACb;QACA,MAAMuV,kBAAkBvV,aAAa;QACrC,MAAMwV,YACJxV,aAAa,UAAWuV,mBAAmB5U,IAAIuK,UAAU,KAAK;QAChE,MAAMuK,YACJzV,aAAa,UAAWuV,mBAAmB5U,IAAIuK,UAAU,KAAK;QAChE,MAAM+J,YAAYG,WAAWH,SAAS,KAAK;QAE3C,MAAMS,iBAAiB,CAAC,CAACN,WAAWO,kBAAkB;QACtD,IAAIC,oBAAoB,CAAC,CAACR,WAAWlB,cAAc;QACnD,MAAM2B,yNAAiB/X,oBAAAA,EAAkB+B;QACzC,MAAMiW,qBAAqB,CAAC,CAAA,CAAA,CAACV,wBAAAA,WAAWW,SAAS,KAAA,OAAA,KAAA,IAApBX,sBAAsBY,eAAe;QAClE,IAAIC,QAAQ,CAAC,CAACb,WAAWc,cAAc;QAEvC,0DAA0D;QAC1D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAInJ,6LAAc7T,QAAAA,EAAS2G,IAAIW,GAAG,IAAI,IAAIR,QAAQ,IAAI;QAEtD,IAAImW,0BAAsBxb,0LAAAA,EAAekF,KAAK,iBAAiBkN;QAE/D,IAAI,CAACiI,aAAa,CAACnV,KAAKc,KAAKsU,WAAWkB;QAExC,IAAI9B;QACJ,IAAIC;QACJ,IAAI8B,cAAc;QAElB,MAAMC,iNAAYzc,iBAAAA,EAAewb,WAAWxH,IAAI;QAEhD,MAAMyH,oBAAoB,IAAI,CAAChP,oBAAoB;QAEnD,IAAI4O,aAAaoB,WAAW;YAC1B,MAAMC,cAAc,MAAM,IAAI,CAACpC,cAAc,CAAC;gBAC5ClU;gBACA4N,MAAMwH,WAAWxH,IAAI;gBACrBqH;gBACA3E,gBAAgBzQ,IAAIQ,OAAO;YAC7B;YAEAgU,cAAciC,YAAYjC,WAAW;YACrCC,eAAegC,YAAYhC,YAAY;YACvC8B,cAAc,OAAO9B,iBAAiB;YAEtC,IAAI,IAAI,CAACjV,UAAU,CAACwH,MAAM,KAAK,UAAU;gBACvC,MAAM+G,OAAOwH,WAAWxH,IAAI;gBAC5B,IAAI,CAACyG,aAAa;oBAChB,MAAM,IAAItV,MACR,CAAC,MAAM,EAAE6O,KAAK,wGAAwG,CAAC;gBAE3H;gBAEA,MAAM2I,0OAAuBnc,sBAAAA,EAAoB+b;gBACjD,IAAI,CAAC9B,YAAYmC,QAAQ,CAACD,uBAAuB;oBAC/C,MAAM,IAAIxX,MACR,CAAC,MAAM,EAAE6O,KAAK,oBAAoB,EAAE2I,qBAAqB,8EAA8E,CAAC;gBAE5I;YACF;YAEA,IAAIH,aAAa;gBACfR,oBAAoB;YACtB;QACF;QAEA,IACEQ,eAAAA,CACA/B,eAAAA,OAAAA,KAAAA,IAAAA,YAAamC,QAAQ,CAACL,oBAAAA,KACtB,mDAAmD;QACnD,+BAA+B;QAC/BtW,IAAIQ,OAAO,CAAC,sBAAsB,EAClC;YACA4V,QAAQ;QACV,OAAO,IAAI,CAAC,IAAI,CAACnQ,UAAU,CAACjC,GAAG,EAAE;YAC/BoS,UAAU,CAAC,CAACZ,kBAAkBoB,MAAM,iLAACzY,UAAAA,EAAQgC,UAAU;QACzD;QAEA,+CAA+C;QAC/C,MAAM0W,oBACJ,CAAC,CACCnU,CAAAA,MAAMI,aAAa,IAClB9C,IAAIQ,OAAO,CAAC,gBAAgB,IAC1B,IAAI,CAAC6D,aAAa,CAAS+L,eAAe,KAE9CgG,CAAAA,SAASP,cAAa;QAEzB;;;KAGC,GACD,MAAMiB,wBACJhc,6LAAAA,EAAekF,KAAK,2BAA2B;QAEjD,uFAAuF;QAEvF,MAAMmL,gBAAerQ,6LAAAA,EAAekF,KAAK,mBAAmB;QAE5D,4DAA4D;QAC5D,wDAAwD;QACxD,6BAA6B;QAC7B,IACE,CAACoW,SACDpW,IAAIQ,OAAO,CAAC,wBAAwB,IACpC,CAAEmV,CAAAA,aAAaxV,aAAa,SAAQ,GACpC;YACAW,IAAI0S,SAAS,CAACxW,sLAAAA,EAAqBmD;YACnCW,IAAI0S,SAAS,CAAC,qBAAqB;YACnC1S,IAAI0S,SAAS,CACX,iBACA;YAEF1S,IAAIoL,IAAI,CAAC,MAAMC,IAAI;YACnB,OAAO;QACT;QAEA,OAAOzJ,MAAMI,aAAa;QAE1B,uDAAuD;QACvD,iEAAiE;QACjE,IACEsT,SACA,IAAI,CAACnS,WAAW,IAChBjE,IAAIQ,OAAO,iKAACxD,sBAAAA,CAAoB,IAChCgD,IAAIW,GAAG,CAACgR,UAAU,CAAC,gBACnB;YACA3R,IAAIW,GAAG,GAAG,IAAI,CAAC6M,iBAAiB,CAACxN,IAAIW,GAAG;QAC1C;QAEA,IACE,CAAC,CAACX,IAAIQ,OAAO,CAAC,gBAAgB,IAC7B,CAAA,CAACM,IAAIuK,UAAU,IAAIvK,IAAIuK,UAAU,KAAK,GAAE,GACzC;YACAvK,IAAI0S,SAAS,CACX,yBACA,GAAG9Q,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAED,MAAMC,YAAY,EAAE,GAAG,KAAKxC,UAAU;QAEtE;QAEA,IAAI4W;QACJ,IAAIxB,WAAWwB,WAAW,EAAE;YAC1BA,cAAcxB,WAAWwB,WAAW;QACtC;QAEA;;;KAGC,GACD,MAAMC,kBACJ,IAAI,CAACtR,eAAe,IACpB,OAAOqR,gBAAgB,uMACvBnZ,uBAAAA,EAAqBmZ;QAEvB,yEAAyE;QACzE,wCAAwC;QACxC,MAAME,2BACJ7V,QAAQC,GAAG,CAAC6V,0CAA0C,KAAK,OAC3D,OAAOxU,MAAMyU,aAAa,KAAK,eAC/BH;QAEF,sEAAsE;QACtE,6CAA6C;QAC7C,MAAMI,6BACJH,4BAA4BvU,MAAMyU,aAAa,KAAK;QAEtD,4EAA4E;QAC5E,8CAA8C;QAC9C,MAAME,oBACJL,mBACC,CAAA,CAAA,CACCxB,QAAAA,kBAAkBoB,MAAM,CAACzW,SAAS,IAClCqV,kBAAkBjB,aAAa,CAACpU,SAAS,KAAA,OAAA,KAAA,IADzCqV,MAEC8B,aAAa,MAAK,sBACnB,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,+BAA+B;QAC9BL,4BACE,CAAA,IAAI,CAAChR,UAAU,CAACjC,GAAG,KAAK,QACvB,IAAI,CAACI,qBAAqB,KAAK,IAAG,CAAE;QAE5C,MAAMmT,qBACJN,4BAA4BI;QAE9B,oEAAoE;QACpE,iEAAiE;QACjE,MAAMG,yBACJD,sBAAsB,IAAI,CAACtR,UAAU,CAACjC,GAAG,KAAK;QAEhD,MAAMyT,uBAAuBL,8BAA8BC;QAE3D,2EAA2E;QAC3E,wEAAwE;QACxE,UAAU;QACV,MAAMK,mBAAmBL,oBACrBvc,8LAAAA,EAAekF,KAAK,eACpBH;QAEJ,0EAA0E;QAC1E,wEAAwE;QACxE,0DAA0D;QAC1D,MAAM8X,sBACJN,qBAAqBlM,gBAAgB,CAAC2L;QAExC,yEAAyE;QACzE,iEAAiE;QACjE,yEAAyE;QACzE,yEAAyE;QACzE,MAAMc,wBACJ5X,IAAIQ,OAAO,iMAAC/E,sCAAAA,CAAoCgF,WAAW,GAAG;QAEhE,gEAAgE;QAChE,IAAIkV,aAAa,CAACkB,qBAAqB,CAAC1L,cAAc;YACpDrK,IAAIuK,UAAU,GAAG;QACnB;QAEA,2DAA2D;QAC3D,qBAAqB;QACrB,IAAIzR,gNAAAA,CAAoB+c,QAAQ,CAACxW,WAAW;YAC1CW,IAAIuK,UAAU,GAAGwM,SAAS1X,SAAS2X,KAAK,CAAC,IAAI;QAC/C;QAEA,IACE,AACA,CAAC9B,kBACD,4BAF+C,WAER;QACvC,CAAC0B,oBACD,CAAC/B,aACD,CAACC,aACDzV,aAAa,aACbH,IAAI+J,MAAM,KAAK,UACf/J,IAAI+J,MAAM,KAAK,SACd,CAAA,OAAOwL,WAAWW,SAAS,KAAK,YAAYE,KAAI,GACjD;YACAtV,IAAIuK,UAAU,GAAG;YACjBvK,IAAI0S,SAAS,CAAC,SAAS;gBAAC;gBAAO;aAAO;YACtC,MAAM,IAAI,CAACtD,WAAW,CAAC,MAAMlQ,KAAKc,KAAKX;YACvC,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,OAAOoV,WAAWW,SAAS,KAAK,UAAU;YAC5C,OAAO;gBACL7C,MAAM;gBACN,0DAA0D;gBAC1DnH,gLAAM5R,UAAAA,CAAayd,UAAU,CAACxC,WAAWW,SAAS;YACpD;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,SAASxT,SAAS,CAACA,MAAM4D,GAAG,EAAE,OAAO5D,MAAM4D,GAAG;QAElD,IAAIoJ,KAAKxJ,uBAAuB,KAAK,MAAM;gBAGhCqP;YAFT,MAAMrC,eAAe7Y,yMAAAA,EAAM2F,IAAIQ,OAAO,CAAC,aAAa,IAAI;YACxD,MAAMwX,sBACJ,OAAA,CAAA,CAAOzC,uBAAAA,WAAW0C,QAAQ,KAAA,OAAA,KAAA,IAAnB1C,qBAAqBY,eAAe,MAAK,cAChD,oFAAoF;sMACpFzc,wBAAAA,IAAyB6b,WAAW0C,QAAQ;YAE9C,oEAAoE;YACpE,gEAAgE;YAChE,2DAA2D;YAC3D,0DAA0D;YAC1D,kDAAkD;YAClDvI,KAAKxJ,uBAAuB,GAC1B,CAACkQ,SAAS,CAAClD,gBAAgB,CAACxQ,MAAM4D,GAAG,IAAI0R;YAC3CtI,KAAKrV,KAAK,GAAG6Y;QACf;QAEA,2DAA2D;QAC3D,IAAI,CAAC2D,qBAAqBzB,aAAa1F,KAAK1L,GAAG,EAAE;YAC/C0L,KAAKxJ,uBAAuB,GAAG;QACjC;QAEA,MAAM7D,gBAAgB+T,QAAAA,CAClB,wBAAA,IAAI,CAAC5W,UAAU,CAACuF,IAAI,KAAA,OAAA,KAAA,IAApB,sBAAsB1C,aAAa,GACnCK,MAAME,mBAAmB;QAE7B,MAAM2L,SAAS7L,MAAMC,YAAY;QACjC,MAAMqC,UAAAA,CAAU,yBAAA,IAAI,CAACxF,UAAU,CAACuF,IAAI,KAAA,OAAA,KAAA,IAApB,uBAAsBC,OAAO;QAE7C,IAAIkT;QACJ,IAAIC,gBAAgB;QAEpB,IAAItC,kBAAkBO,SAAShB,WAAW;YACxC,8DAA8D;YAC9D,IAAIhU,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;YAUzC;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,gCAAgC;QAChC,IACE8T,aACA,CAAC1F,KAAK1L,GAAG,IACT,CAACmU,iBACD/B,SACAjL,gBACA,CAACwM,uBACA,CAAA,iLAACne,gBAAAA,EAAckW,KAAK4I,OAAO,KACzB,IAAI,CAACjU,aAAa,CAAS+L,eAAc,GAC5C;aACAzS,6NAAAA,EAAmBqC,IAAIQ,OAAO;QAChC;QAEA,IAAI+X,uBAAuB;QAC3B,IAAIC,0BAA0B;QAE9B,IAAIpC,OAAO;;YACP,CAAA,EAAEmC,oBAAoB,EAAEC,uBAAuB,EAAE,IACjDxe,8MAAAA,EAA0BgG,KAAK,IAAI,CAACiG,UAAU,CAACM,YAAY,CAAA;QAC/D;QAEA,IAAI6P,SAAS,IAAI,CAACnS,WAAW,IAAIjE,IAAIQ,OAAO,iKAACxD,sBAAAA,CAAoB,EAAE;YACjE,uEAAuE;YACvEsZ,sBAAsBpJ;QACxB;QAEAA,iOAAc3S,sBAAAA,EAAoB2S;QAClCoJ,yOAAsB/b,sBAAAA,EAAoB+b;QAC1C,IAAI,IAAI,CAACrR,gBAAgB,EAAE;YACzBqR,sBAAsB,IAAI,CAACrR,gBAAgB,CAAC1E,SAAS,CAAC+V;QACxD;QAEA,MAAMmC,iBAAiB,CAACC;YACtB,MAAMzM,WAAW;gBACf0M,aAAaD,SAASE,SAAS,CAACC,YAAY;gBAC5CxN,YAAYqN,SAASE,SAAS,CAACE,mBAAmB;gBAClDlS,UAAU8R,SAASE,SAAS,CAACG,sBAAsB;YACrD;YACA,MAAM1N,aAAa9R,iMAAAA,EAAkB0S;YACrC,MAAM,EAAErF,QAAQ,EAAE,GAAG,IAAI,CAACpH,UAAU;YAEpC,IACEoH,YACAqF,SAASrF,QAAQ,KAAK,SACtBqF,SAAS0M,WAAW,CAAChH,UAAU,CAAC,MAChC;gBACA1F,SAAS0M,WAAW,GAAG,GAAG/R,WAAWqF,SAAS0M,WAAW,EAAE;YAC7D;YAEA,IAAI1M,SAAS0M,WAAW,CAAChH,UAAU,CAAC,MAAM;gBACxC1F,SAAS0M,WAAW,6KAAG3f,2BAAAA,EAAyBiT,SAAS0M,WAAW;YACtE;YAEA7X,IACGmL,QAAQ,CAACA,SAAS0M,WAAW,EAAEtN,YAC/Ba,IAAI,CAACD,SAAS0M,WAAW,EACzBxM,IAAI;QACT;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAI0K,mBAAmB;YACrBP,sBAAsB,IAAI,CAAC9I,iBAAiB,CAAC8I;YAC7CpJ,cAAc,IAAI,CAACM,iBAAiB,CAACN;QACvC;QAEA,IAAI8L,cAA6B;QACjC,IACE,CAACb,iBACD/B,SACA,CAAC1G,KAAKxJ,uBAAuB,IAC7B,CAAC8P,kBACD,CAAC0B,oBACD,CAACC,qBACD;YACAqB,cAAc,GAAGzK,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACtCpO,CAAAA,aAAa,OAAOmW,wBAAwB,GAAE,KAAM/H,SACjD,KACA+H,sBACH5T,MAAM4D,GAAG,GAAG,SAAS,IAAI;QAC9B;QAEA,IAAKqP,CAAAA,aAAaC,SAAQ,KAAMQ,OAAO;YACrC4C,cAAc,GAAGzK,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKpO,WAC5CuC,MAAM4D,GAAG,GAAG,SAAS,IACrB;QACJ;QAEA,IAAI0S,aAAa;YACfA,kBAAcvb,+NAAAA,EAAiBub;YAE/B,+CAA+C;YAC/CA,cACEA,gBAAgB,YAAY7Y,aAAa,MAAM,MAAM6Y;QACzD;QACA,IAAI3I,WAA+B;QAEnC,IAAI;YACF,MAAMC,gBAAgB,IAAIrD,iLACxBnS,iBAAAA,EAAekF,KAAK,cAAc,KAClC;YAEFqQ,WAAWC,cAAcD,QAAQ;QACnC,EAAE,OAAM,CAAC;QAET,sDAAsD;QACtD,MAAME,mBACH5Q,WAAmBuR,kBAAkB,IACrC,MAAM,IAAI,CAACV,mBAAmB,CAAC;YAC9BC,gBAAgB5I,OAAO2H,MAAM,CAAC,CAAC,GAAGxP,IAAIQ,OAAO;YAC7CkQ,iBAAiBL,SAASvO,SAAS,CAAC,GAAGuO,SAAS3O,MAAM,GAAG;QAG3D;QAEF,0EAA0E;QAC1E6O,iBAAiBU,iBAAiB;QAkBlC,MAAMgI,WAAqB,OAAO,EAAE7L,SAAS,EAAE8L,mBAAmB,EAAE;YAClE,2DAA2D;YAC3D,IAAIhT,0BAGF,AAFA,AACA,6DAA6D,UADU;YAEtE,CAAC2Q,qBAAqBnH,KAAK1L,GAAG,KAAK,QACpC,qEAAqE;YACrE,gBAAgB;YACf,CAACoS,SAAS,CAACL,qBACZ,mEAAmE;YACnE,QAAQ;YACR,OAAO3I,cAAc,YACrB,sEAAsE;YACtE,uBAAuB;YACvBuK;YAEF,MAAMwB,gBAAY9f,mLAAAA,EAAS2G,IAAIW,GAAG,IAAI,IAAI,MAAM+B,KAAK;YAErD,mDAAmD;YACnD,kBAAkB;YAClB,IAAIgN,KAAKzO,MAAM,EAAE;gBACf4G,OAAOC,IAAI,CAAC4H,KAAKzO,MAAM,EAAEyR,OAAO,CAAC,CAAC3D;oBAChC,OAAOoK,SAAS,CAACpK,IAAI;gBACvB;YACF;YACA,MAAMqK,mBACJlM,gBAAgB,OAAO,IAAI,CAAC1N,UAAU,CAACqC,aAAa;YAEtD,MAAMwX,6LAAclgB,SAAAA,EAAU;gBAC5BgH,UAAU,GAAGmW,sBAAsB8C,mBAAmB,MAAM,IAAI;gBAChE,uDAAuD;gBACvD1W,OAAOyW;YACT;YACA,MAAMlT,aAA+B;gBACnC,GAAGsP,UAAU;gBACb,GAAG7F,IAAI;gBACP,GAAI0F,YACA;oBACE7E;oBACA,gEAAgE;oBAChE,+DAA+D;oBAC/D,4DAA4D;oBAC5D,WAAW;oBACX+I,cAAclD,SAAS,CAAChJ,aAAa,CAACuK;oBACtC4B,eAAe,IAAI,CAAC/Z,UAAU,CAACC,YAAY,CAAC8Z,aAAa;gBAC3D,IACA,CAAC,CAAC;gBACN1C;gBACAwC;gBACA9K;gBACAvJ;gBACA3C;gBACAgW,oBAAoB,IAAI,CAAC7Y,UAAU,CAACC,YAAY,CAAC4Y,kBAAkB;gBACnE,uFAAuF;gBACvF,8DAA8D;gBAC9D,SAAS;gBACTmB,gBACE3D,kBAAkBI,yBACd9c,oLAAAA,EAAU;oBACR,iEAAiE;oBACjE,UAAU;oBACVgH,UAAU,GAAG+M,cAAckM,mBAAmB,MAAM,IAAI;oBACxD1W,OAAOyW;gBACT,KACAE;gBACN5Z,cAAc;oBACZ,GAAGiQ,KAAKjQ,YAAY;oBACpB4X;gBACF;gBACAnR;gBACAqS;gBACAkB,aAAatB;gBACbnC;gBACA5I;gBACA6G,WAAW,IAAI,CAACF,YAAY;gBAC5B2F,SAAS5Y,IAAI4Y,OAAO,CAACnR,IAAI,CAACzH;gBAC1B6Y,kBAAkB9Z;gBAClB,wBAAwB;gBACxB+Z,iBAAkB,IAAI,CAASA,eAAe;YAChD;YAEA,IAAIrC,sBAAsBC,wBAAwB;gBAChDtR,0BAA0B;gBAC1BD,WAAW4T,UAAU,GAAG;gBACxB5T,WAAWC,uBAAuB,GAAG;gBACrCD,WAAW6T,kBAAkB,GAAG;gBAChC7T,WAAWqT,YAAY,GAAG;gBAC1BrT,WAAWsR,kBAAkB,GAAGA;gBAChCtR,WAAWuR,sBAAsB,GAAGA;YACtC;YAEA,qEAAqE;YACrE,wBAAwB;YACxB,IAAI5F;YAEJ,IAAImF,aAAa;gBACf,4LAAIlZ,wBAAAA,EAAsBkZ,cAAc;wBAuBf;oBAtBvB,IACE,AACA,6DAA6D,QADQ;oBAErE3V,QAAQC,GAAG,CAACC,YAAY,qBAAK,UAC7B,EAAClD,wMAAAA,EAAkB4B,QACnB,sLAAC3B,qBAAAA,EAAmByC,MACpB;wBACA,MAAM,IAAI5B,MACR;oBAEJ;oBAEA,MAAM6a,UAAuC;wBAC3C9Y,QAAQyO,KAAKzO,MAAM;wBACnBuU;wBACAvP,YAAY;4BACVxG,cAAc;gCACZyI,WAAWjC,WAAWxG,YAAY,CAACyI,SAAS;gCAC5CE,gBAAgBnC,WAAWxG,YAAY,CAAC2I,cAAc;4BACxD;4BACAlC;4BACAqK;4BACAjJ,iBAAiB,EAAA,CAAE,gCAAA,IAAI,CAAC9H,UAAU,CAACC,YAAY,KAAA,OAAA,KAAA,IAA5B,8BAA8B8H,SAAS;4BAC1D+R,cAAclD;4BACdnC,WAAW,IAAI,CAACF,YAAY;4BAC5B2F,SAAS5Y,IAAI4Y,OAAO,CAACnR,IAAI,CAACzH;4BAC1B6Y,kBAAkB9Z;4BAClBwI,+BACE,IAAI,CAACpC,UAAU,CAACoC,6BAA6B;4BAC/ClH,SAAS,IAAI,CAAC8E,UAAU,CAAC9E,OAAO;wBAClC;oBACF;oBAEA,IAAI;wBACF,MAAM6Y,0NAAU3c,sBAAAA,CAAmB4c,mBAAmB,CACpDja,0NACA1C,yBAAAA,EAAuBwD,IAAI+K,gBAAgB;wBAG7C,MAAMgG,WAAW,MAAMkF,YAAYmD,MAAM,CAACF,SAASD;wBAEjD/Z,IAAYma,YAAY,GACxBJ,QAAQ9T,UAAU,CAClBkU,YAAY;wBAEd,MAAMC,YAAYL,QAAQ9T,UAAU,CAACoU,aAAa;wBAElD,mEAAmE;wBACnE,oBAAoB;wBACpB,IAAIjE,OAAO;4BACT,MAAMkE,OAAO,MAAMzI,SAASyI,IAAI;4BAEhC,sCAAsC;4BACtC,MAAM9Z,oLAAU3D,4BAAAA,EAA0BgV,SAASrR,OAAO;4BAE1D,IAAI4Z,WAAW;gCACb5Z,OAAO,iKAACtD,yBAAAA,CAAuB,GAAGkd;4BACpC;4BAEA,IAAI,CAAC5Z,OAAO,CAAC,eAAe,IAAI8Z,KAAKjH,IAAI,EAAE;gCACzC7S,OAAO,CAAC,eAAe,GAAG8Z,KAAKjH,IAAI;4BACrC;4BAEA,MAAMC,aACJ,OAAOyG,QAAQ9T,UAAU,CAACsU,mBAAmB,KAAK,eAClDR,QAAQ9T,UAAU,CAACsU,mBAAmB,oKAAIxd,iBAAAA,GACtC,QACAgd,QAAQ9T,UAAU,CAACsU,mBAAmB;4BAE5C,2CAA2C;4BAC3C,MAAM9E,aAAiC;gCACrCzG,OAAO;oCACLnE,0LAAMhS,kBAAAA,CAAgB2hB,SAAS;oCAC/BC,QAAQ5I,SAAS4I,MAAM;oCACvBvO,4HAAMmB,SAAAA,CAAOqN,IAAI,CAAC,MAAMJ,KAAKK,WAAW;oCACxCna;gCACF;gCACA8S;gCACAsH,YAAY;4BACd;4BAEA,OAAOnF;wBACT;wBAEA,+DAA+D;wBAC/D,oLAAM/Y,eAAAA,EACJsD,KACAc,KACA+Q,UACAkI,QAAQ9T,UAAU,CAAC4U,gBAAgB;wBAErC,OAAO;oBACT,EAAE,OAAOlR,KAAK;wBACZ,MAAM,IAAI,CAACrB,6BAA6B,CAACqB,KAAK3J,KAAK;4BACjD8a,YAAY;4BACZC,WAAW5a;4BACX6a,WAAW;4BACXC,wMAAkBtc,sBAAAA,EAAoBsH;wBACxC;wBAEA,8DAA8D;wBAC9D,IAAImQ,OAAO,MAAMzM;wBAEjBlP,sKAAI2P,KAAK,CAACT;wBAEV,kCAAkC;wBAClC,oLAAMjN,eAAAA,EAAasD,KAAKc,KAAK,IAAIgR,SAAS,MAAM;4BAAE2I,QAAQ;wBAAI;wBAE9D,OAAO;oBACT;gBACF,OAAO,4LACL3c,qBAAAA,EAAmBiZ,wMACnBnZ,uBAAAA,EAAqBmZ,cACrB;oBACA,mDAAmD;oBACnD,IAAI/W,IAAI+J,MAAM,KAAK,aAAa,CAAC4L,WAAW;wBAC1C,oLAAMjZ,eAAAA,EAAasD,KAAKc,KAAK,IAAIgR,SAAS,MAAM;4BAAE2I,QAAQ;wBAAI;wBAC9D,OAAO;oBACT;oBAEA,4LAAI3c,qBAAAA,EAAmBiZ,cAAc;wBACnC,wEAAwE;wBACxE,sEAAsE;wBACtE,iCAAiC;wBACjC,4HAA4H;wBAC5H9Q,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;wBACnDI,WAAWiV,uBAAuB,GAChC3F,WAAW2F,uBAAuB;wBAEpC,MAAMlB,+LAAU5b,oBAAAA,EAAkB4B,OAAOA,IAAI2L,eAAe,GAAG3L;wBAC/D,MAAM6R,gMAAWxT,qBAAAA,EAAmByC,OAChCA,IAAI+K,gBAAgB,GACpB/K;wBAEJ,iDAAiD;wBACjD,IAAI;4BACF8Q,SAAS,MAAMmF,YAAYnD,MAAM,CAC/B,AACA,sBADsB,yBACyB;4BAC/CoG,SACAnI,UACA;gCACE9D,MAAM5N;gCACNc,QAAQyO,KAAKzO,MAAM;gCACnByB;gCACAuD;4BACF;wBAEJ,EAAE,OAAO0D,KAAK;4BACZ,MAAM,IAAI,CAACrB,6BAA6B,CAACqB,KAAK3J,KAAK;gCACjD8a,YAAY;gCACZC,WAAW5a;gCACX6a,WAAW;gCACXC,wMAAkBtc,sBAAAA,EAAoB;oCACpC2a,cAAclD;oCACdmC,sBAAsBtS,WAAWsS,oBAAoB;gCACvD;4BACF;4BACA,MAAM5O;wBACR;oBACF,OAAO;wBACL,MAAMwR,SAAS5F,WAAWwB,WAAW;wBAErC,4EAA4E;wBAC5E,8DAA8D;wBAC9D,4HAA4H;wBAC5H9Q,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;wBAEnD,MAAMkU,UAAsC;4BAC1ChM,MAAM4H,YAAY,SAASxV;4BAC3Bc,QAAQyO,KAAKzO,MAAM;4BACnByB;4BACAwW;4BACAjT;4BACAvG,0BAA0B,IAAI,CAACH,2BAA2B;wBAC5D;wBAEA,4DAA4D;wBAC5D,iEAAiE;wBACjE,wCAAwC;wBACxC,IACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACyI,SAAS,IACtC,IAAI,CAACjC,UAAU,CAACjC,GAAG,IACnB,CAAC8S,wBACD,CAACd,gBACD;4BACA,MAAMoF,SAAS,MAAMD,OAAOC,MAAM,CAACpb,KAAKc,KAAKiZ;4BAE7C,6DAA6D;4BAC7D,yBAAyB;4BACzB,IAAIqB,OAAOC,QAAQ,CAACC,wBAAwB,EAAE;gCAC5CrV,WAAWqV,wBAAwB,GACjCF,OAAOC,QAAQ,CAACC,wBAAwB;4BAC5C;wBACF;wBAEA,iDAAiD;wBACjD1J,SAAS,MAAMuJ,OAAOvH,MAAM,CAAC5T,KAAKc,KAAKiZ;oBACzC;gBACF,OAAO;oBACL,MAAM,IAAI7a,MAAM;gBAClB;YACF,OAAO;gBACL,oEAAoE;gBACpE,iBAAiB;gBACjB0S,SAAS,MAAM,IAAI,CAAC2J,UAAU,CAACvb,KAAKc,KAAKX,UAAUuC,OAAOuD;YAC5D;YAEA,MAAM,EAAEoV,QAAQ,EAAE,GAAGzJ;YAErB,MAAM,EACJpR,UAAU,CAAC,CAAC,EACZ,AACAgb,WAAWpB,SAAS,EACrB,GAAGiB,2CAFkE;YAItE,IAAIjB,WAAW;gBACb5Z,OAAO,iKAACtD,yBAAAA,CAAuB,GAAGkd;YACpC;YAEA,2DAA2D;;YACzDpa,IAAYma,YAAY,GAAGkB,SAASlB,YAAY;YAElD,0DAA0D;YAC1D,gEAAgE;YAChE,qDAAqD;YACrD,IACE/E,aACAgB,SACAiF,SAAS/H,UAAU,KAAK,KACxB,CAAC,IAAI,CAACrN,UAAU,CAACjC,GAAG,IACpB,CAACqT,mBACD;gBACA,MAAMoE,oBAAoBJ,SAASI,iBAAiB;gBAEpD,MAAM9R,MAAM,IAAIzK,MACd,CAAC,+CAA+C,EAAEgO,cAChDuO,CAAAA,qBAAAA,OAAAA,KAAAA,IAAAA,kBAAmBC,WAAW,IAC1B,CAAC,UAAU,EAAED,kBAAkBC,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC;gBAGlF,IAAID,qBAAAA,OAAAA,KAAAA,IAAAA,kBAAmBE,KAAK,EAAE;oBAC5B,MAAMA,QAAQF,kBAAkBE,KAAK;oBACrChS,IAAIgS,KAAK,GAAGhS,IAAIiS,OAAO,GAAGD,MAAM7Z,SAAS,CAAC6Z,MAAME,OAAO,CAAC;gBAC1D;gBAEA,MAAMlS;YACR;YAEA,uEAAuE;YACvE,iBAAiB;YAEjB,uBAAuB;YACvB,IAAI,gBAAgB0R,YAAYA,SAASS,UAAU,EAAE;gBACnD,OAAO;oBACL9M,OAAO;oBACPsE,YAAY+H,SAAS/H,UAAU;oBAC/BsH,YAAY;gBACd;YACF;YAEA,uBAAuB;YACvB,IAAIS,SAASU,UAAU,EAAE;gBACvB,OAAO;oBACL/M,OAAO;wBACLnE,yLAAMhS,mBAAAA,CAAgBmjB,QAAQ;wBAC9BC,OAAOZ,SAAS3C,QAAQ,IAAI2C,SAASa,UAAU;oBACjD;oBACA5I,YAAY+H,SAAS/H,UAAU;oBAC/BsH,YAAY;gBACd;YACF;YAEA,mBAAmB;YACnB,IAAIhJ,OAAOuK,MAAM,EAAE;gBACjB,OAAO;YACT;YAEA,kEAAkE;YAClE,IAAI/G,WAAW;gBACb,OAAO;oBACLpG,OAAO;wBACLnE,0LAAMhS,kBAAAA,CAAgBujB,QAAQ;wBAC9BC,MAAMzK;wBACNpR;wBACA8b,SAASjB,SAASa,UAAU;wBAC5B9O,WAAWiO,SAASjO,SAAS;wBAC7BqN,QAAQ3Z,IAAIuK,UAAU;wBACtBkR,aAAalB,SAASkB,WAAW;oBACnC;oBACAjJ,YAAY+H,SAAS/H,UAAU;oBAC/BsH,YAAY,CAAC,CAAC1B;gBAChB;YACF;YAEA,OAAO;gBACLlK,OAAO;oBACLnE,0LAAMhS,kBAAAA,CAAgB2jB,KAAK;oBAC3BH,MAAMzK;oBACN8G,UAAU2C,SAAS3C,QAAQ,IAAI2C,SAASa,UAAU;oBAClD1b;oBACAia,QAAQrF,YAAYtU,IAAIuK,UAAU,GAAGxL;gBACvC;gBACAyT,YAAY+H,SAAS/H,UAAU;gBAC/BsH,YAAYlY,MAAM+Z,cAAc,KAAK;YACvC;QACF;QAEA,IAAIC,oBAAuC,OAAO,EAChDC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,EACf;YACC,MAAMC,eAAe,CAAC,IAAI,CAAC7W,UAAU,CAACjC,GAAG;YACzC,MAAM+Y,aAAaJ,eAAe7b,IAAIyS,IAAI;YAE1C,sEAAsE;YACtE,IAAI,CAACiB,eAAegC,WAAW;gBAC7B,IAAIT,mBAAmB;oBACrB,MAAMU,cAAc,MAAM,IAAI,CAACpC,cAAc,CAAC;wBAC5ClU;wBACAsQ,gBAAgBzQ,IAAIQ,OAAO;wBAC3B4U;wBACArH,MAAMwH,WAAWxH,IAAI;oBACvB;oBAEAyG,cAAciC,YAAYjC,WAAW;oBACrCC,eAAegC,YAAYhC,YAAY;gBACzC,OAAO;oBACLD,cAAc3U;oBACd4U,8KAAe5V,eAAAA,CAAame,SAAS;gBACvC;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,IACEvI,gLAAiB5V,eAAAA,CAAaoe,SAAS,IACvC5iB,yMAAAA,EAAM2F,IAAIQ,OAAO,CAAC,aAAa,IAAI,KACnC;gBACAiU,8KAAe5V,eAAAA,CAAaqe,sBAAsB;YACpD;YAEA,wDAAwD;YACxD,iCAAiC;YACjC,IACE3E,wBACAC,2BACA,CAACoE,sBACD,CAAC,IAAI,CAAC3Y,WAAW,EACjB;;YAGF;YAEA,IAAI2Y,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,mBAAoBO,OAAO,MAAK,CAAC,GAAG;gBACtC5E,uBAAuB;YACzB;YAEA,sBAAsB;YACtB,8DAA8D;YAC9D,2CAA2C;YAC3C,IACEA,wBACC9D,CAAAA,gLAAiB5V,eAAAA,CAAame,SAAS,IAAIJ,kBAAiB,GAC7D;gBACAnI,8KAAe5V,eAAAA,CAAaqe,sBAAsB;YACpD;YAEA,sEAAsE;YACtE,uDAAuD;YACvD,EAAE;YACF,sEAAsE;YACtE,8DAA8D;YAC9D,EAAE;YACF,sEAAsE;YACtE,0BAA0B;YAC1B,IAAIE,gBAAgBpE;YACpB,IAAI,CAACoE,iBAAiB1N,KAAK1L,GAAG,IAAIoR,WAAW;gBAC3CgI,gOAAgB3f,mBAAAA,EAAiB6Y;YACnC;YACA,IAAI8G,iBAAiB1a,MAAM4D,GAAG,EAAE;gBAC9B8W,gBAAgBA,cAAcvQ,OAAO,CAAC,UAAU;YAClD;YAEA,MAAMwQ,8BACJD,iBAAAA,CAAiB5I,eAAAA,OAAAA,KAAAA,IAAAA,YAAamC,QAAQ,CAACyG,cAAAA;YAEzC,qEAAqE;YACrE,kCAAkC;YAElC,kCAAkC;YAClC,IAAI,IAAI,CAAC5d,UAAU,CAACC,YAAY,CAACsI,qBAAqB,EAAE;gBACtD0M,eAAe5V,8KAAAA,CAAaqe,sBAAsB;YACpD;YAEA,oEAAoE;YACpE,kCAAkC;YAClC,EAAE;YACF,gCAAgC;YAChC,0CAA0C;YAC1C,wEAAwE;YACxE,iEAAiE;YACjE,yBAAyB;YACzB,iEAAiE;YACjE,qEAAqE;YACrE,EAAE;YACF,IACE9b,QAAQC,GAAG,CAACC,YAAY,qBAAK,UAC7B,CAAC,IAAI,CAAC2C,WAAW,IACjBwQ,gLAAiB5V,eAAAA,CAAaqe,sBAAsB,IACpDE,iBACA,CAACL,cACD,CAAC5E,iBACD3B,aACCsG,CAAAA,gBAAgB,CAACtI,eAAe,CAAC6I,2BAA0B,GAC5D;gBACA,IAGE,AAFA,AACA,kBAAkB,yCADyC;gBAE1DP,CAAAA,gBAAiBtI,eAAeA,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAa9S,MAAM,IAAG,CAAC,KACxD,2DAA2D;gBAC3D+S,+KAAiB5V,gBAAAA,CAAame,SAAS,EACvC;oBACA,MAAM,IAAI/d;gBACZ;gBAEA,IAAIqe;gBAEJ,kCAAkC;gBAClC,QAAIxf,yMAAAA,EAAmByX,WAAWwB,WAAW,KAAK,CAACF,mBAAmB;oBACpE,gEAAgE;oBAChE,oCAAoC;oBACpCyG,mBAAmB,MAAM,IAAI,CAACjU,aAAa,CAACmC,GAAG,CAC7CsR,eAAgBvO,SAAS,CAAC,CAAC,EAAEA,SAASpO,UAAU,GAAGA,WAAY,MAC/D,AACA,OAAO,EACLyc,oBAAoBW,4BAFmC,CAEN,IAAI,EACtD;wBACC,2DAA2D;wBAC3D,8DAA8D;wBAC9D,gEAAgE;wBAChE,iEAAiE;wBACjE,YAAY;wBACZ,IAAIT,cAAc;4BAChB,+LAAO/d,uBAAAA,EAAqBwe;wBAC9B;wBAEA,2DAA2D;wBAC3D,+DAA+D;wBAC/D,4DAA4D;wBAC5D,cAAc;wBACd7a,MAAM+Z,cAAc,GAAG;wBAEvB,kEAAkE;wBAClE,UAAU;wBACV,OAAOxD,SAAS;4BACd7L,WAAWvN;4BACXqZ,qBAAqB;wBACvB;oBACF,GACA;wBACEsE,kLAAW5e,YAAAA,CAAU4d,KAAK;wBAC1BjM;wBACA8G;wBACAuD,YAAY;oBACd;gBAEJ,OAGK,IACHvD,6MACAzZ,uBAAAA,EAAqB2X,WAAWwB,WAAW,KAC3C,CAAC5L,cACD;oBACA,gEAAgE;oBAChE,oCAAoC;oBACpCmS,mBAAmB,MAAM,IAAI,CAACjU,aAAa,CAACmC,GAAG,CAC7CsR,eAAe3c,WAAW,MAC1B,AACA,UACE8Y,SAAS,sCAF8C;4BAGrD,4DAA4D;4BAC5D,QAAQ;4BACR7L,WAAWvN;4BACXqZ,qBACE,AACA,wDAAwD,CADC;4BAEzD,YAAY;4BACZ4D,gBAAgBrF,wBACZ7e,mNAAAA,EAAuBuH,YACvB;wBACR,IACF;wBACEqd,kLAAW5e,YAAAA,CAAUwd,QAAQ;wBAC7B7L;wBACA8G;wBACAuD,YAAY;oBACd;gBAEJ;gBAEA,wEAAwE;gBACxE,IAAI0C,qBAAqB,MAAM,OAAO;gBAEtC,qEAAqE;gBACrE,IAAIA,kBAAkB;oBACpB,mEAAmE;oBACnE,iCAAiC;oBACjC,OAAOA,iBAAiBhK,UAAU;oBAElC,OAAOgK;gBACT;YACF;YAEA,wEAAwE;YACxE,oEAAoE;YACpE,MAAMlQ,YACJ,CAACmL,wBAAwB,CAACsE,kBAAkBnF,mBACxCA,mBACA7X;YAEN,yEAAyE;YACzE,wEAAwE;YACxE,IACG0X,CAAAA,sBAAsBC,sBAAqB,KAC5C,OAAOpK,cAAc,aACrB;gBACA,OAAO;oBACLkG,YAAY;oBACZsH,YAAY;oBACZ5L,OAAO;wBACLnE,0LAAMhS,kBAAAA,CAAgB2jB,KAAK;wBAC3BH,gLAAM/hB,UAAAA,CAAayd,UAAU,CAAC;wBAC9BW,UAAU,CAAC;wBACXlY,SAASX;wBACT4a,QAAQ5a;oBACV;gBACF;YACF;YAEA,oEAAoE;YACpE,qEAAqE;YACrE,2DAA2D;YAC3D,MAAMqZ,sBACJ1C,aACAa,qBACCvc,8KAAAA,iBAAAA,EAAekF,KAAK,gCACnByX,oBAAmB,KACjB7e,mNAAAA,EAAuBuH,YACvB;YAEN,sBAAsB;YACtB,MAAMyR,SAAS,MAAMqH,SAAS;gBAC5B7L;gBACA8L;YACF;YACA,IAAI,CAACtH,QAAQ,OAAO;YAEpB,OAAO;gBACL,GAAGA,MAAM;gBACT0B,YAAY1B,OAAO0B,UAAU;YAC/B;QACF;QAEA,MAAMmC,aAAa,MAAM,IAAI,CAACpM,aAAa,CAACmC,GAAG,CAC7CwN,aACA0D,mBACA;YACEc,WACE,AACA,qCAAqC,iCADiC;YAEtEzG,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAalJ,UAAU,CAAChD,IAAI,KAC3BuK,CAAAA,mLAAYxW,YAAAA,CAAUwd,QAAQ,0KAAGxd,YAAAA,CAAU4d,KAAI;YAClDjM;YACAgI;YACAkF,YAAYzd,IAAIQ,OAAO,CAACkd,OAAO,KAAK;YACpCrG;QACF;QAGF,IAAIP,wBAAwB,OAAOc,0BAA0B,UAAU;gBAMnE,AACA,+DAA+D,GADG;YAElE,mDAAmD;YACnDnC;YARF,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YACtE,IACEA,eAAe,QAIfA,CAAAA,CAAAA,qBAAAA,WAAWzG,KAAK,KAAA,OAAA,KAAA,IAAhByG,mBAAkB5K,IAAI,0LAAKhS,kBAAAA,CAAgBujB,QAAQ,IACnD3G,WAAWzG,KAAK,CAACuN,WAAW,EAC5B;gBACA,MAAMoB,iBAAiBlI,WAAWzG,KAAK,CAACuN,WAAW,CAAC/Q,GAAG,CACrDoM;gBAEF,IAAI+F,mBAAmB9d,WAAW;oBAChC,YAAY;oBACZ,OAAO;wBACLwT,MAAM;wBACNnH,MAAM5R,oLAAAA,CAAayd,UAAU,CAAC4F;wBAC9B,0DAA0D;wBAC1D,0CAA0C;wBAC1CrK,YAAYmC,WAAWnC,UAAU;oBACnC;gBACF;YACF;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,uEAAuE;YACvE,wEAAwE;YACxE,oBAAoB;YACpB,oEAAoE;YACpE,gEAAgE;YAChE,eAAe;YACfxS,IAAIuK,UAAU,GAAG;YACjB,IAAIgM,mBAAmB;gBACrB,oEAAoE;gBACpE,uEAAuE;gBACvE,kCAAkC;gBAClC,oEAAoE;gBACpE,oEAAoE;gBACpE,uBAAuB;gBACvBvW,IAAI0S,SAAS,iMAAC9X,2BAAAA,EAA0B;YAC1C;YACA,OAAO;gBACL2X,MAAM;gBACNnH,MAAM5R,oLAAAA,CAAayd,UAAU,CAAC;gBAC9BzE,UAAU,EAAEmC,cAAAA,OAAAA,KAAAA,IAAAA,WAAYnC,UAAU;YACpC;QACF;QAEA,IAAI6E,eAAe;YACjBrX,IAAI0S,SAAS,CACX,iBACA;QAEJ;QAEA,IAAI,CAACiC,YAAY;YACf,IAAIuD,eAAe,CAAET,CAAAA,wBAAwBC,uBAAsB,GAAI;gBACrE,gEAAgE;gBAChE,oEAAoE;gBACpE,kEAAkE;gBAClE,mEAAmE;gBACnE,yBAAyB;gBACzB,MAAM,IAAItZ,MAAM;YAClB;YACA,OAAO;QACT;QAEA,2EAA2E;QAC3E,4EAA4E;QAC5E,IACE8Z,eACA,CAAC,IAAI,CAAC/U,WAAW,IACjBoT,qBACA5B,CAAAA,CAAAA,oBAAAA,WAAWzG,KAAK,KAAA,OAAA,KAAA,IAAhByG,kBAAkB5K,IAAI,0LAAKhS,kBAAAA,CAAgBujB,QAAQ,IACnD3G,WAAWmF,UAAU,IACrB,CAACrC,wBACD,uEAAuE;QACvE,mBAAmB;QACnB,CAACd,wBACDrW,QAAQC,GAAG,CAACuc,8BAA8B,KAAK,QAC/C;gLACA5e,qBAAAA,EAAmB;gBACjB,IAAI;oBACF,MAAM,IAAI,CAACqK,aAAa,CAACmC,GAAG,CAC1BwN,aACA,IACEC,SAAS;4BACP,8DAA8D;4BAC9D,uBAAuB;4BACvBC,qBAAqB;4BACrB9L,WAAWvN;wBACb,IACF;wBACE2d,kLAAW5e,YAAAA,CAAUwd,QAAQ;wBAC7B7L;wBACAgI,sBAAsB;wBACtBkF,YAAY;wBACZpG,mBAAmB;oBACrB;gBAEJ,EAAE,OAAO1N,KAAK;oBACZQ,QAAQC,KAAK,CAAC,gDAAgDT;gBAChE;YACF;QACF;QAEA,MAAMkU,cACJpI,CAAAA,CAAAA,qBAAAA,WAAWzG,KAAK,KAAA,OAAA,KAAA,IAAhByG,mBAAkB5K,IAAI,0LAAKhS,kBAAAA,CAAgBujB,QAAQ,IACnD,OAAO3G,WAAWzG,KAAK,CAAC5B,SAAS,KAAK;QAExC,IACEgJ,SACA,yEAAyE;QACzE,kEAAkE;QAClE,gDAAgD;QAChD,CAACuB,uBACA,CAAA,CAACkG,eAAe/G,oBAAmB,GACpC;YACA,IAAI,CAAC,IAAI,CAAC7S,WAAW,EAAE;gBACrB,gDAAgD;gBAChD,iCAAiC;gBACjCnD,IAAI0S,SAAS,CACX,kBACA+E,uBACI,gBACA9C,WAAWqI,MAAM,GACf,SACArI,WAAW0H,OAAO,GAChB,UACA;YAEZ;YACA,0EAA0E;YAC1E,yDAAyD;YACzDrc,IAAI0S,SAAS,iMAAC3X,2BAAAA,EAA0B;QAC1C;QAEA,MAAM,EAAEmT,OAAO+O,UAAU,EAAE,GAAGtI;QAE9B,yDAAyD;QACzD,IAAIsI,CAAAA,cAAAA,OAAAA,KAAAA,IAAAA,WAAYlT,IAAI,MAAKhS,sMAAAA,CAAgBmlB,KAAK,EAAE;YAC9C,MAAM,IAAI9e,MAAM;QAClB;QAEA,mDAAmD;QACnD,IAAIoU;QAEJ,0EAA0E;QAC1E,oCAAoC;QACpC,IAAIoE,kBAAkB;YACpBpE,aAAa;QACf,OAKK,IACH,IAAI,CAACrP,WAAW,IAChBkH,gBACA,CAAC2L,wBACDO,mBACA;YACA/D,aAAa;QACf,OAAO,IAAI,CAAC,IAAI,CAACrN,UAAU,CAACjC,GAAG,IAAK6R,kBAAkB,CAACgB,mBAAoB;YACzE,2DAA2D;YAC3D,IAAIsB,eAAe;gBACjB7E,aAAa;YACf,OAIK,IAAI,CAAC8C,OAAO;gBACf,IAAI,CAACtV,IAAImd,SAAS,CAAC,kBAAkB;oBACnC3K,aAAa;gBACf;YACF,OAQK,IAAIqC,WAAW;gBAClB,MAAMuI,kMAAqBpjB,iBAAAA,EAAekF,KAAK;gBAC/CsT,aACE,OAAO4K,uBAAuB,cAAc,IAAIA;YACpD,OAAO,IAAItI,WAAW;gBACpBtC,aAAa;YACf,OAGK,IAAI,OAAOmC,WAAWnC,UAAU,KAAK,UAAU;gBAClD,IAAImC,WAAWnC,UAAU,GAAG,GAAG;oBAC7B,MAAM,IAAIpU,MACR,CAAC,2CAA2C,EAAEuW,WAAWnC,UAAU,CAAC,IAAI,CAAC;gBAE7E;gBAEAA,aAAamC,WAAWnC,UAAU;YACpC,OAGK,IAAImC,WAAWnC,UAAU,KAAK,OAAO;gBACxCA,6KAAaxW,iBAAAA;YACf;QACF;QAEA2Y,WAAWnC,UAAU,GAAGA;QAExB,yEAAyE;QACzE,8BAA8B;QAC9B,MAAM6K,4LAAerjB,iBAAAA,EAAekF,KAAK;QACzC,IAAIme,cAAc;gBASR1I,oBAEIA;YAVZ,MAAMnS,WAAW,MAAM6a,aACrB;gBACE,GAAG1I,UAAU;gBACb,0CAA0C;gBAC1C,wCAAwC;gBACxCzG,OAAO;oBACL,GAAGyG,WAAWzG,KAAK;oBACnBnE,MACE4K,CAAAA,CAAAA,qBAAAA,WAAWzG,KAAK,KAAA,OAAA,KAAA,IAAhByG,mBAAkB5K,IAAI,0LAAKhS,kBAAAA,CAAgBujB,QAAQ,GAC/C,SAAA,CACA3G,qBAAAA,WAAWzG,KAAK,KAAA,OAAA,KAAA,IAAhByG,mBAAkB5K,IAAI;gBAC9B;YACF,GACA;gBACElK,KAAK7F,8LAAAA,EAAekF,KAAK;YAC3B;YAEF,IAAIsD,UAAU;gBACZ,0CAA0C;gBAC1C,OAAO;YACT;QACF;QAEA,IAAI,CAACya,YAAY;YACf,oDAAoD;YACpD,qDAAqD;YACrD,4DAA4D;YAC5D,2BAA2B;yLAC3BljB,iBAAAA,EAAemF,KAAK,sBAAsByV,WAAWnC,UAAU;YAE/D,2DAA2D;YAC3D,6DAA6D;YAC7D,IACE,OAAOmC,WAAWnC,UAAU,KAAK,eACjC,CAACxS,IAAImd,SAAS,CAAC,kBACf;gBACAnd,IAAI0S,SAAS,CACX,gMACAtZ,mBAAAA,EAAiB;oBACfoZ,YAAYmC,WAAWnC,UAAU;oBACjCtL,YAAY,IAAI,CAACxI,UAAU,CAACwI,UAAU;gBACxC;YAEJ;YACA,IAAI6O,mBAAmB;gBACrB/V,IAAIuK,UAAU,GAAG;gBACjBvK,IAAIoL,IAAI,CAAC,qBAAqBC,IAAI;gBAClC,OAAO;YACT;YAEA,IAAI,IAAI,CAAClG,UAAU,CAACjC,GAAG,EAAE;gBACvBtB,MAAM0b,qBAAqB,GAAGje;YAChC;YACA,MAAM,IAAI,CAACoB,SAAS,CAACvB,KAAKc,KAAK;gBAAEX;gBAAUuC;YAAM,GAAG;YACpD,OAAO;QACT,OAAO,IAAIqb,WAAWlT,IAAI,yLAAKhS,kBAAAA,CAAgBmjB,QAAQ,EAAE;YACvD,2DAA2D;YAC3D,6DAA6D;YAC7D,IACE,OAAOvG,WAAWnC,UAAU,KAAK,eACjC,CAACxS,IAAImd,SAAS,CAAC,kBACf;gBACAnd,IAAI0S,SAAS,CACX,gMACAtZ,mBAAAA,EAAiB;oBACfoZ,YAAYmC,WAAWnC,UAAU;oBACjCtL,YAAY,IAAI,CAACxI,UAAU,CAACwI,UAAU;gBACxC;YAEJ;YAEA,IAAI6O,mBAAmB;gBACrB,OAAO;oBACLxD,MAAM;oBACNnH,gLAAM5R,UAAAA,CAAayd,UAAU,CAC3B,AACAsG,KAAKC,SAAS,CAACP,WAAW9B,GADG,EACE;oBAEjC3I,YAAYmC,WAAWnC,UAAU;gBACnC;YACF,OAAO;gBACL,MAAMmF,eAAesF,WAAW9B,KAAK;gBACrC,OAAO;YACT;QACF,OAAO,IAAI8B,WAAWlT,IAAI,yLAAKhS,kBAAAA,CAAgB2hB,SAAS,EAAE;YACxD,MAAMha,oLAAU7D,8BAAAA,EAA4BohB,WAAWvd,OAAO;YAE9D,IAAI,CAAE,CAAA,IAAI,CAACyD,WAAW,IAAImS,KAAI,GAAI;gBAChC5V,QAAQ+d,MAAM,iKAACrhB,yBAAAA;YACjB;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IACE,OAAOuY,WAAWnC,UAAU,KAAK,eACjC,CAACxS,IAAImd,SAAS,CAAC,oBACf,CAACzd,QAAQgL,GAAG,CAAC,kBACb;gBACAhL,QAAQge,GAAG,CACT,gMACAtkB,mBAAAA,EAAiB;oBACfoZ,YAAYmC,WAAWnC,UAAU;oBACjCtL,YAAY,IAAI,CAACxI,UAAU,CAACwI,UAAU;gBACxC;YAEJ;YAEA,oLAAMtL,eAAAA,EACJsD,KACAc,KACA,IAAIgR,SAASiM,WAAW7R,IAAI,EAAE;gBAC5B1L;gBACAia,QAAQsD,WAAWtD,MAAM,IAAI;YAC/B;YAEF,OAAO;QACT,OAAO,IAAIsD,WAAWlT,IAAI,yLAAKhS,kBAAAA,CAAgBujB,QAAQ,EAAE;gBAmCrD2B;YAlCF,oEAAoE;YACpE,gBAAgB;YAChB,IAAIF,eAAenG,kBAAkB;gBACnC,MAAM,IAAIxY,MACR;YAEJ;YAEA,IAAI6e,WAAWvd,OAAO,EAAE;gBACtB,MAAMA,UAAU;oBAAE,GAAGud,WAAWvd,OAAO;gBAAC;gBAExC,IAAI,CAAC,IAAI,CAACyD,WAAW,IAAI,CAACmS,OAAO;oBAC/B,OAAO5V,OAAO,CAACtD,yLAAAA,CAAuB;gBACxC;gBAEA,KAAK,IAAI,CAAC6R,KAAKC,MAAM,IAAInH,OAAOoC,OAAO,CAACzJ,SAAU;oBAChD,IAAI,OAAOwO,UAAU,aAAa;oBAElC,IAAIyP,MAAMC,OAAO,CAAC1P,QAAQ;wBACxB,KAAK,MAAM2P,KAAK3P,MAAO;4BACrBlO,IAAI8d,YAAY,CAAC7P,KAAK4P;wBACxB;oBACF,OAAO,IAAI,OAAO3P,UAAU,UAAU;wBACpCA,QAAQA,MAAMvC,QAAQ;wBACtB3L,IAAI8d,YAAY,CAAC7P,KAAKC;oBACxB,OAAO;wBACLlO,IAAI8d,YAAY,CAAC7P,KAAKC;oBACxB;gBACF;YACF;YAEA,IACE,IAAI,CAAC/K,WAAW,IAChBmS,SAAAA,CAAAA,CACA2H,sBAAAA,WAAWvd,OAAO,KAAA,OAAA,KAAA,IAAlBud,mBAAoB,iKAAC7gB,yBAAAA,CAAuB,GAC5C;gBACA4D,IAAI0S,SAAS,iKACXtW,yBAAAA,EACA6gB,WAAWvd,OAAO,iKAACtD,yBAAAA,CAAuB;YAE9C;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,oCAAoC;YACpC,IAAI6gB,WAAWtD,MAAM,IAAK,CAAA,CAACtP,gBAAgB,CAACkM,iBAAgB,GAAI;gBAC9DvW,IAAIuK,UAAU,GAAG0S,WAAWtD,MAAM;YACpC;YAEA,sCAAsC;YACtC,IAAIoD,aAAa;gBACf/c,IAAI0S,SAAS,iMAAC9X,2BAAAA,EAA0B;YAC1C;YAEA,2DAA2D;YAC3D,oEAAoE;YACpE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAIyP,gBAAgB,CAACgN,eAAe;gBAClC,8DAA8D;gBAC9D,IAAI,OAAO4F,WAAWzB,OAAO,KAAK,aAAa;oBAC7C,IAAIyB,WAAW3Q,SAAS,EAAE;wBACxB,MAAM,IAAIlO,MAAM;oBAClB;oBAEA,OAAO;wBACLmU,MAAM;wBACNnH,MAAM6R,WAAW1B,IAAI;wBACrB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB;wBACnB,+EAA+E;wBAC/E/I,YAAYqE,sBAAsB,IAAIlC,WAAWnC,UAAU;oBAC7D;gBACF;gBAEA,sEAAsE;gBACtE,QAAQ;gBACR,OAAO;oBACLD,MAAM;oBACNnH,gLAAM5R,UAAAA,CAAayd,UAAU,CAACgG,WAAWzB,OAAO;oBAChDhJ,YAAYmC,WAAWnC,UAAU;gBACnC;YACF;YAEA,mCAAmC;YACnC,IAAIpH,OAAO6R,WAAW1B,IAAI;YAE1B,qEAAqE;YACrE,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAACwB,eAAe,IAAI,CAAC5Z,WAAW,EAAE;gBACpC,OAAO;oBACLoP,MAAM;oBACNnH;oBACAoH,YAAYmC,WAAWnC,UAAU;gBACnC;YACF;YAEA,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAIiE,sBAAsBC,wBAAwB;gBAChD,mEAAmE;gBACnE,mDAAmD;gBACnDtL,KAAK2S,KAAK,CACR,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,WAAWC,OAAO,yLAACxgB,eAAAA,CAAaygB,MAAM,CAACC,aAAa;wBACpDH,WAAWvM,KAAK;oBAClB;gBACF;gBAGF,OAAO;oBAAEY,MAAM;oBAAQnH;oBAAMoH,YAAY;gBAAE;YAC7C;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,mBAAmB;YACnB,MAAM8L,cAAc,IAAIC;YACxBnT,KAAK2S,KAAK,CAACO,YAAYE,QAAQ;YAE/B,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzErG,SAAS;gBACP7L,WAAW2Q,WAAW3Q,SAAS;gBAC/B,sEAAsE;gBACtE,YAAY;gBACZ8L,qBAAqB;YACvB,GACG1G,IAAI,CAAC,OAAOZ;oBAKPA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,IAAI1S,MAAM;gBAClB;gBAEA,IAAI0S,CAAAA,CAAAA,gBAAAA,OAAO5C,KAAK,KAAA,OAAA,KAAA,IAAZ4C,cAAc/G,IAAI,0LAAKhS,kBAAAA,CAAgBujB,QAAQ,EAAE;wBAELxK;oBAD9C,MAAM,IAAI1S,MACR,CAAC,yCAAyC,EAAA,CAAE0S,iBAAAA,OAAO5C,KAAK,KAAA,OAAA,KAAA,IAAZ4C,eAAc/G,IAAI,EAAE;gBAEpE;gBAEA,6CAA6C;gBAC7C,MAAM+G,OAAO5C,KAAK,CAACqN,IAAI,CAACkD,MAAM,CAACH,YAAYI,QAAQ;YACrD,GACCC,KAAK,CAAC,CAAC9V;gBACN,iEAAiE;gBACjE,0DAA0D;gBAC1DyV,YAAYI,QAAQ,CAACE,KAAK,CAAC/V,KAAK8V,KAAK,CAAC,CAACE;oBACrCxV,QAAQC,KAAK,CAAC,8BAA8BuV;gBAC9C;YACF;YAEF,OAAO;gBACLtM,MAAM;gBACNnH;gBACA,uEAAuE;gBACvE,wEAAwE;gBACxE,qCAAqC;gBACrCoH,YAAY;YACd;QACF,OAAO,IAAIuD,mBAAmB;YAC5B,OAAO;gBACLxD,MAAM;gBACNnH,gLAAM5R,UAAAA,CAAayd,UAAU,CAACsG,KAAKC,SAAS,CAACP,WAAWrF,QAAQ;gBAChEpF,YAAYmC,WAAWnC,UAAU;YACnC;QACF,OAAO;YACL,OAAO;gBACLD,MAAM;gBACNnH,MAAM6R,WAAW1B,IAAI;gBACrB/I,YAAYmC,WAAWnC,UAAU;YACnC;QACF;IACF;IAEQ9F,kBAAkBtM,IAAY,EAAE0e,cAAc,IAAI,EAAE;QAC1D,IAAI1e,KAAKyV,QAAQ,CAAC,IAAI,CAACxV,OAAO,GAAG;YAC/B,MAAM0e,YAAY3e,KAAKY,SAAS,CAC9BZ,KAAK2a,OAAO,CAAC,IAAI,CAAC1a,OAAO,IAAI,IAAI,CAACA,OAAO,CAACO,MAAM;YAGlDR,uNAAO1G,sBAAAA,EAAoBqlB,UAAUhT,OAAO,CAAC,WAAW;QAC1D;QAEA,IAAI,IAAI,CAAC5H,gBAAgB,IAAI2a,aAAa;YACxC,OAAO,IAAI,CAAC3a,gBAAgB,CAAC1E,SAAS,CAACW;QACzC;QACA,OAAOA;IACT;IAEA,0CAA0C;IAChC4e,oBAAoBrU,KAAa,EAAE;QAC3C,IAAI,IAAI,CAAClI,kBAAkB,CAACoC,GAAG,EAAE;gBACP;YAAxB,MAAMoa,kBAAAA,CAAkB,sBAAA,IAAI,CAAClX,aAAa,KAAA,OAAA,KAAA,IAAlB,mBAAoB,CAAC4C,MAAM;YAEnD,IAAI,CAACsU,iBAAiB;gBACpB,OAAO;YACT;YAEA,OAAOA;QACT;QACA,OAAO;IACT;IAEA,MAAgBC,oBACdpW,GAAkD,EAClDqW,gBAAyB,EACzB;YAiBgB;QAhBhB,MAAM,EAAEvd,KAAK,EAAEvC,QAAQ,EAAE,GAAGyJ;QAE5B,MAAMsW,WAAW,IAAI,CAACJ,mBAAmB,CAAC3f;QAC1C,MAAMiV,YAAYqJ,MAAMC,OAAO,CAACwB;QAEhC,IAAInS,OAAO5N;QACX,IAAIiV,WAAW;YACb,4EAA4E;YAC5ErH,OAAOmS,QAAQ,CAACA,SAASxe,MAAM,GAAG,EAAE;QACtC;QAEA,MAAMkQ,SAAS,MAAM,IAAI,CAACuO,kBAAkB,CAAC;YAC3CpS;YACArL;YACAzB,QAAQ2I,IAAI3D,UAAU,CAAChF,MAAM,IAAI,CAAC;YAClCmU;YACAgL,YAAY,CAAC,CAAA,CAAA,CAAC,oCAAA,IAAI,CAAC5gB,UAAU,CAACC,YAAY,CAAC4gB,GAAG,KAAA,OAAA,KAAA,IAAhC,kCAAkCC,SAAS;YACzDJ;YACA,sEAAsE;YACtEK,cAAc;QAChB;QACA,IAAI3O,QAAQ;aACVvV,+LAAAA,IAAYmkB,oBAAoB,CAAC,cAAcrgB;YAC/C,IAAI;gBACF,OAAO,MAAM,IAAI,CAACuU,8BAA8B,CAAC9K,KAAKgI;YACxD,EAAE,OAAOjI,KAAK;gBACZ,MAAM8W,oBAAoB9W,eAAe1K;gBAEzC,IAAI,CAACwhB,qBAAsBA,qBAAqBR,kBAAmB;oBACjE,MAAMtW;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAcyK,iBACZxK,GAAkD,EACjB;QACjC,2LAAOvN,YAAAA,IAAYsO,KAAK,mLACtBnO,kBAAAA,CAAe4X,gBAAgB,EAC/B;YACExJ,UAAU,CAAC,cAAc,CAAC;YAC1BG,YAAY;gBACV,cAAcnB,IAAIzJ,QAAQ;YAC5B;QACF,GACA;YACE,OAAO,IAAI,CAACugB,oBAAoB,CAAC9W;QACnC;IAEJ;IAQA,MAAc8W,qBACZ9W,GAAkD,EACjB;YAQzB;QAPR,MAAM,EAAE9I,GAAG,EAAE4B,KAAK,EAAEvC,QAAQ,EAAE,GAAGyJ;QACjC,IAAImE,OAAO5N;QACX,MAAM8f,mBAAmB,CAAC,CAACvd,MAAMie,qBAAqB;QACtD,OAAOje,KAAK,CAACnH,uNAAAA,CAAqB;QAClC,OAAOmH,MAAMie,qBAAqB;QAElC,MAAM7gB,UAAwB;YAC5BiF,IAAI,EAAA,CAAE,qBAAA,IAAI,CAAChD,YAAY,KAAA,OAAA,KAAA,IAAjB,mBAAmB6e,SAAS,CAACzgB,UAAUuC;QAC/C;QAEA,IAAI;YACF,WAAW,MAAMpC,SAAS,IAAI,CAAC2I,QAAQ,CAAC4X,QAAQ,CAAC1gB,UAAUL,SAAU;gBACnE,uDAAuD;gBACvD,0DAA0D;gBAC1D,MAAMghB,4LAAehmB,iBAAAA,EAAe8O,IAAI5J,GAAG,EAAE;gBAC7C,IACE,CAAC,IAAI,CAACiE,WAAW,IACjB,OAAO6c,iBAAiB,gBACxB/mB,kNAAAA,EAAe+mB,gBAAgB,OAC/BA,iBAAiBxgB,MAAMuN,UAAU,CAAC1N,QAAQ,EAC1C;oBACA;gBACF;gBAEA,MAAMyR,SAAS,MAAM,IAAI,CAACoO,mBAAmB,CAC3C;oBACE,GAAGpW,GAAG;oBACNzJ,UAAUG,MAAMuN,UAAU,CAAC1N,QAAQ;oBACnC8F,YAAY;wBACV,GAAG2D,IAAI3D,UAAU;wBACjBhF,QAAQX,MAAMW,MAAM;oBACtB;gBACF,GACAgf;gBAEF,IAAIrO,WAAW,OAAO,OAAOA;YAC/B;YAEA,+DAA+D;YAC/D,6DAA6D;YAC7D,4DAA4D;YAC5D,mBAAmB;YACnB,sDAAsD;YACtD,IAAI,IAAI,CAACvN,aAAa,CAAC+L,eAAe,EAAE;gBACtC,sDAAsD;gBACtDxG,IAAIzJ,QAAQ,GAAG,IAAI,CAACkE,aAAa,CAAC+L,eAAe,CAACrC,IAAI;gBACtD,MAAM6D,SAAS,MAAM,IAAI,CAACoO,mBAAmB,CAACpW,KAAKqW;gBACnD,IAAIrO,WAAW,OAAO,OAAOA;YAC/B;QACF,EAAE,OAAOxH,OAAO;YACd,MAAMT,UAAM/O,mLAAAA,EAAewP;YAE3B,IAAIA,uLAAiBnR,oBAAAA,EAAmB;gBACtCkR,QAAQC,KAAK,CACX,yCACAiU,KAAKC,SAAS,CACZ;oBACEvQ;oBACApN,KAAKiJ,IAAI5J,GAAG,CAACW,GAAG;oBAChBqM,aAAapD,IAAI5J,GAAG,CAACQ,OAAO,iKAACxD,sBAAAA,CAAoB;oBACjD+jB,sLAASjmB,iBAAAA,EAAe8O,IAAI5J,GAAG,EAAE;oBACjC4O,YAAY,CAAC,8KAAC9T,iBAAAA,EAAe8O,IAAI5J,GAAG,EAAE;oBACtCghB,YAAYlmB,8LAAAA,EAAe8O,IAAI5J,GAAG,EAAE;gBACtC,GACA,MACA;gBAGJ,MAAM2J;YACR;YAEA,IAAIA,eAAe1K,mBAAmBghB,kBAAkB;gBACtD,MAAMtW;YACR;YACA,IAAIA,qLAAe5Q,cAAAA,IAAe4Q,qLAAe7Q,iBAAAA,EAAgB;gBAC/DgI,IAAIuK,UAAU,GAAG;gBACjB,OAAO,MAAM,IAAI,CAAC4V,qBAAqB,CAACrX,KAAKD;YAC/C;YAEA7I,IAAIuK,UAAU,GAAG;YAEjB,mDAAmD;YACnD,qDAAqD;YACrD,IAAI,MAAM,IAAI,CAAC8I,OAAO,CAAC,SAAS;gBAC9BvK,IAAIlH,KAAK,CAACwe,uBAAuB,GAAG;gBACpC,MAAM,IAAI,CAACD,qBAAqB,CAACrX,KAAKD;gBACtC,OAAOC,IAAIlH,KAAK,CAACwe,uBAAuB;YAC1C;YAEA,MAAMC,iBAAiBxX,eAAexK;YAEtC,IAAI,CAACgiB,gBAAgB;gBACnB,IACG,IAAI,CAACld,WAAW,IAAI7C,QAAQC,GAAG,CAACC,YAAY,qBAAK,UAClD,IAAI,CAAC2E,UAAU,CAACjC,GAAG,EACnB;oBACA,QAAIrJ,4KAAAA,EAAQgP,MAAMA,IAAIoE,IAAI,GAAGA;oBAC7B,MAAMpE;gBACR;gBACA,IAAI,CAACU,QAAQ,uKAACzP,iBAAAA,EAAe+O;YAC/B;YACA,MAAMkI,WAAW,MAAM,IAAI,CAACoP,qBAAqB,CAC/CrX,KACAuX,iBAAkBxX,IAA0BtK,UAAU,GAAGsK;YAE3D,OAAOkI;QACT;QAEA,IACE,IAAI,CAAC7Q,aAAa,MAClB,CAAC,CAAC4I,IAAI5J,GAAG,CAACQ,OAAO,CAAC,gBAAgB,IACjC,CAAA,CAACM,IAAIuK,UAAU,IAAIvK,IAAIuK,UAAU,KAAK,OAAOvK,IAAIuK,UAAU,KAAK,GAAE,GACnE;YACAvK,IAAI0S,SAAS,CACX,yBACA,GAAG9Q,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAED,MAAMC,YAAY,EAAE,GAAG,KAAKxC,UAAU;YAEpEW,IAAIuK,UAAU,GAAG;YACjBvK,IAAI0S,SAAS,CAAC,gBAAgB;YAC9B1S,IAAIoL,IAAI,CAAC;YACTpL,IAAIqL,IAAI;YACR,OAAO;QACT;QAEArL,IAAIuK,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC4V,qBAAqB,CAACrX,KAAK;IACzC;IAEA,MAAawX,aACXphB,GAAkB,EAClBc,GAAmB,EACnBX,QAAgB,EAChBuC,QAAwB,CAAC,CAAC,EACF;QACxB,2LAAOrG,YAAAA,IAAYsO,KAAK,oLAACnO,iBAAAA,CAAe4kB,YAAY,EAAE;YACpD,OAAO,IAAI,CAACC,gBAAgB,CAACrhB,KAAKc,KAAKX,UAAUuC;QACnD;IACF;IAEA,MAAc2e,iBACZrhB,GAAkB,EAClBc,GAAmB,EACnBX,QAAgB,EAChBuC,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAACgR,aAAa,CAAC,CAAC9J,MAAQ,IAAI,CAACwK,gBAAgB,CAACxK,MAAM;YAC7D5J;YACAc;YACAX;YACAuC;QACF;IACF;IAEA,MAAawN,YACXvG,GAAiB,EACjB3J,GAAkB,EAClBc,GAAmB,EACnBX,QAAgB,EAChBuC,QAA4B,CAAC,CAAC,EAC9B4e,aAAa,IAAI,EACF;QACf,2LAAOjlB,YAAAA,IAAYsO,KAAK,oLAACnO,iBAAAA,CAAe0T,WAAW,EAAE;YACnD,OAAO,IAAI,CAACqR,eAAe,CAAC5X,KAAK3J,KAAKc,KAAKX,UAAUuC,OAAO4e;QAC9D;IACF;IAEA,MAAcC,gBACZ5X,GAAiB,EACjB3J,GAAkB,EAClBc,GAAmB,EACnBX,QAAgB,EAChBuC,QAA4B,CAAC,CAAC,EAC9B4e,aAAa,IAAI,EACF;QACf,IAAIA,YAAY;YACdxgB,IAAI0S,SAAS,CACX,iBACA;QAEJ;QAEA,OAAO,IAAI,CAACV,IAAI,CACd,OAAOlJ;YACL,MAAMiI,WAAW,MAAM,IAAI,CAACoP,qBAAqB,CAACrX,KAAKD;YACvD,IAAI,IAAI,CAAC1F,WAAW,IAAInD,IAAIuK,UAAU,KAAK,KAAK;gBAC9C,MAAM1B;YACR;YACA,OAAOkI;QACT,GACA;YAAE7R;YAAKc;YAAKX;YAAUuC;QAAM;IAEhC;IAQA,MAAcue,sBACZrX,GAAkD,EAClDD,GAAiB,EACgB;QACjC,OAAOtN,gMAAAA,IAAYsO,KAAK,oLAACnO,iBAAAA,CAAeykB,qBAAqB,EAAE;YAC7D,OAAO,IAAI,CAACO,yBAAyB,CAAC5X,KAAKD;QAC7C;IACF;IAEA,MAAgB6X,0BACd5X,GAAkD,EAClDD,GAAiB,EACgB;QACjC,wGAAwG;QACxG,+DAA+D;QAC/D,IAAI,IAAI,CAAC1D,UAAU,CAACjC,GAAG,IAAI4F,IAAIzJ,QAAQ,KAAK,gBAAgB;YAC1D,OAAO;gBACLkT,MAAM;gBACNnH,+KAAM5R,WAAAA,CAAayd,UAAU,CAAC;YAChC;QACF;QACA,MAAM,EAAEjX,GAAG,EAAE4B,KAAK,EAAE,GAAGkH;QAEvB,IAAI;YACF,IAAIgI,SAAsC;YAE1C,MAAM6P,QAAQ3gB,IAAIuK,UAAU,KAAK;YACjC,IAAIqW,eAAe;YAEnB,IAAID,OAAO;gBACT,IAAI,IAAI,CAACle,kBAAkB,CAACoC,GAAG,EAAE;oBAC/B,2CAA2C;oBAC3CiM,SAAS,MAAM,IAAI,CAACuO,kBAAkB,CAAC;wBACrCpS,gMAAMjU,mCAAAA;wBACN4I;wBACAzB,QAAQ,CAAC;wBACTmU,WAAW;wBACXmL,cAAc;wBACd5f,KAAKiJ,IAAI5J,GAAG,CAACW,GAAG;oBAClB;oBACA+gB,eAAe9P,WAAW;gBAC5B;gBAEA,IAAI,CAACA,UAAW,MAAM,IAAI,CAACuC,OAAO,CAAC,SAAU;oBAC3CvC,SAAS,MAAM,IAAI,CAACuO,kBAAkB,CAAC;wBACrCpS,MAAM;wBACNrL;wBACAzB,QAAQ,CAAC;wBACTmU,WAAW;wBACX,qEAAqE;wBACrEmL,cAAc;wBACd5f,KAAKiJ,IAAI5J,GAAG,CAACW,GAAG;oBAClB;oBACA+gB,eAAe9P,WAAW;gBAC5B;YACF;YACA,IAAI+P,aAAa,CAAC,CAAC,EAAE7gB,IAAIuK,UAAU,EAAE;YAErC,IACE,CAACzB,IAAIlH,KAAK,CAACwe,uBAAuB,IAClC,CAACtP,oMACDhY,sBAAAA,CAAoB+c,QAAQ,CAACgL,aAC7B;gBACA,0DAA0D;gBAC1D,8BAA8B;gBAC9B,IAAIA,eAAe,UAAU,CAAC,IAAI,CAAC1b,UAAU,CAACjC,GAAG,EAAE;oBACjD4N,SAAS,MAAM,IAAI,CAACuO,kBAAkB,CAAC;wBACrCpS,MAAM4T;wBACNjf;wBACAzB,QAAQ,CAAC;wBACTmU,WAAW;wBACX,8DAA8D;wBAC9D,SAAS;wBACTmL,cAAc;wBACd5f,KAAKiJ,IAAI5J,GAAG,CAACW,GAAG;oBAClB;gBACF;YACF;YAEA,IAAI,CAACiR,QAAQ;gBACXA,SAAS,MAAM,IAAI,CAACuO,kBAAkB,CAAC;oBACrCpS,MAAM;oBACNrL;oBACAzB,QAAQ,CAAC;oBACTmU,WAAW;oBACX,iEAAiE;oBACjE,SAAS;oBACTmL,cAAc;oBACd5f,KAAKiJ,IAAI5J,GAAG,CAACW,GAAG;gBAClB;gBACAghB,aAAa;YACf;YAEA,IACEvgB,QAAQC,GAAG,CAACugB,QAAQ,gCAAK,gBACzB,CAACF,gBACA,MAAM,IAAI,CAACvN,OAAO,CAAC,cACpB,CAAE,MAAM,IAAI,CAACA,OAAO,CAAC,SACrB;gBACA,IAAI,CAACxQ,oBAAoB;YAC3B;YAEA,IAAI,CAACiO,QAAQ;gBACX,iEAAiE;gBACjE,wDAAwD;gBACxD,IAAI,IAAI,CAAC3L,UAAU,CAACjC,GAAG,EAAE;oBACvB,OAAO;wBACLqP,MAAM;wBACN,mDAAmD;wBACnDnH,gLAAM5R,UAAAA,CAAayd,UAAU,CAC3B,CAAC;;;;;;;;;;;;;uBAaQ,CAAC;oBAEd;gBACF;gBAEA,MAAM,IAAI5Y,kBACR,IAAID,MAAM;YAEd;YAEA,0EAA0E;YAC1E,yCAAyC;YACzC,IAAI0S,OAAO2D,UAAU,CAACwB,WAAW,EAAE;6LACjClc,iBAAAA,EAAe+O,IAAI5J,GAAG,EAAE,SAAS;oBAC/B6N,YAAY+D,OAAO2D,UAAU,CAACwB,WAAW,CAAClJ,UAAU;oBACpD5M,QAAQpB;gBACV;YACF,OAAO;6LACL9E,oBAAAA,EAAkB6O,IAAI5J,GAAG,EAAE;YAC7B;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAAC0U,8BAA8B,CAC9C;oBACE,GAAG9K,GAAG;oBACNzJ,UAAUwhB;oBACV1b,YAAY;wBACV,GAAG2D,IAAI3D,UAAU;wBACjB0D;oBACF;gBACF,GACAiI;YAEJ,EAAE,OAAOiQ,oBAAoB;gBAC3B,IAAIA,8BAA8B5iB,iBAAiB;oBACjD,MAAM,IAAIC,MAAM;gBAClB;gBACA,MAAM2iB;YACR;QACF,EAAE,OAAOzX,OAAO;YACd,MAAM0X,0LAAoBlnB,iBAAAA,EAAewP;YACzC,MAAM+W,iBAAiBW,6BAA6B3iB;YACpD,IAAI,CAACgiB,gBAAgB;gBACnB,IAAI,CAAC9W,QAAQ,CAACyX;YAChB;YACAhhB,IAAIuK,UAAU,GAAG;YACjB,MAAM0W,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAC9DpY,IAAI5J,GAAG,CAACW,GAAG;YAGb,IAAIohB,oBAAoB;gBACtB,mEAAmE;gBACnE,mCAAmC;6LACnClnB,iBAAAA,EAAe+O,IAAI5J,GAAG,EAAE,SAAS;oBAC/B6N,YAAYkU,mBAAmBhL,WAAW,CAAElJ,UAAU;oBACtD5M,QAAQpB;gBACV;gBAEA,OAAO,IAAI,CAAC6U,8BAA8B,CACxC;oBACE,GAAG9K,GAAG;oBACNzJ,UAAU;oBACV8F,YAAY;wBACV,GAAG2D,IAAI3D,UAAU;wBACjB,sDAAsD;wBACtD,sCAAsC;wBACtC0D,KAAKwX,iBACDW,kBAAkBziB,UAAU,GAC5ByiB;oBACN;gBACF,GACA;oBACEpf;oBACA6S,YAAYwM;gBACd;YAEJ;YACA,OAAO;gBACL1O,MAAM;gBACNnH,+KAAM5R,WAAAA,CAAayd,UAAU,CAAC;YAChC;QACF;IACF;IAEA,MAAakK,kBACXtY,GAAiB,EACjB3J,GAAkB,EAClBc,GAAmB,EACnBX,QAAgB,EAChBuC,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAACgR,aAAa,CAAC,CAAC9J,MAAQ,IAAI,CAACqX,qBAAqB,CAACrX,KAAKD,MAAM;YACvE3J;YACAc;YACAX;YACAuC;QACF;IACF;IAEA,MAAanB,UACXvB,GAAkB,EAClBc,GAAmB,EACnBZ,SAA8D,EAC9DohB,aAAa,IAAI,EACF;QACf,MAAM,EAAEnhB,QAAQ,EAAEuC,KAAK,EAAE,GAAGxC,YAAYA,YAAY7G,uLAAAA,EAAS2G,IAAIW,GAAG,EAAG;QAEvE,IAAI,IAAI,CAACnB,UAAU,CAACuF,IAAI,EAAE;YACxBrC,MAAMC,YAAY,KAAK,IAAI,CAACnD,UAAU,CAACuF,IAAI,CAAC1C,aAAa;YACzDK,MAAME,mBAAmB,KAAK,IAAI,CAACpD,UAAU,CAACuF,IAAI,CAAC1C,aAAa;QAClE;QAEAvB,IAAIuK,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC6E,WAAW,CAAC,MAAMlQ,KAAKc,KAAKX,UAAWuC,OAAO4e;IAC5D;AACF", "ignoreList": [0]}}, {"offset": {"line": 2515, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
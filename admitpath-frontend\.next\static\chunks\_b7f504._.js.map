{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nCard.displayName = \"Card\";\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardHeader.displayName = \"CardHeader\";\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardTitle.displayName = \"CardTitle\";\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardDescription.displayName = \"CardDescription\";\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n));\r\nCardContent.displayName = \"CardContent\";\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardFooter.displayName = \"CardFooter\";\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardDescription,\r\n  CardContent,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,8JAAM,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8JAAM,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/usePackages.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nexport interface StudentPackageItem {\r\n  service_name: string;\r\n  hours: {\r\n    total: number;\r\n    used: number;\r\n    remaining: number;\r\n  };\r\n}\r\n\r\nexport interface StudentPackage {\r\n  id: number;\r\n  package: {\r\n    id: number;\r\n    title: string;\r\n    description: string;\r\n    counselor_name: string;\r\n    counselor_profile_picture: string;\r\n  };\r\n  start_date: string;\r\n  end_date: string | null;\r\n  status: 'pending' | 'active' | 'completed' | 'cancelled' | 'expired';\r\n  service_hours: {\r\n    [service_name: string]: {\r\n      total: number;\r\n      used: number;\r\n    };\r\n  };\r\n  sessions: {\r\n    id: number;\r\n    date: string;\r\n    start_time: string;\r\n    end_time: string;\r\n    status: string;\r\n    service_name: string;\r\n  }[];\r\n  progress?: {\r\n    [service_name: string]: {\r\n      total: number;\r\n      used: number;\r\n      remaining: number;\r\n    };\r\n  };\r\n}\r\n\r\nexport interface FirstSessionData {\r\n  event_name: string;\r\n  date: string;\r\n  start_time: string;\r\n  end_time: string;\r\n  student_email: string;\r\n}\r\n\r\nexport interface PackageSubscriptionPayload {\r\n  package_id: number;\r\n  selected_service: string;\r\n  first_session: FirstSessionData;\r\n  promo_code?: string;\r\n}\r\n\r\nexport interface SessionFromPackagePayload {\r\n  selected_service: string;\r\n  date: string;\r\n  start_time: string;\r\n  end_time: string;\r\n}\r\n\r\nexport interface PackagesState {\r\n  packages: StudentPackage[];\r\n  selectedPackage: StudentPackage | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  activePackages: StudentPackage[];\r\n  completedPackages: StudentPackage[];\r\n  fetchPackages: (status?: string) => Promise<void>;\r\n  fetchPackageById: (subscriptionId: number) => Promise<void>;\r\n  subscribeToPackage: (data: PackageSubscriptionPayload) => Promise<any>;\r\n  clearError: () => void;\r\n}\r\n\r\nconst usePackages = create<PackagesState>((set, get) => ({\r\n  packages: [],\r\n  selectedPackage: null,\r\n  loading: false,\r\n  error: null,\r\n  activePackages: [],\r\n  completedPackages: [],\r\n\r\n  fetchPackages: async (status = \"active\") => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.get<StudentPackage[]>(\r\n        `/student/packages/my-packages?status=${status}`\r\n      );\r\n\r\n      // Store packages in the appropriate state based on status\r\n      if (status === \"active\") {\r\n        set({\r\n          packages: response.data,\r\n          activePackages: response.data,\r\n          loading: false\r\n        });\r\n      } else if (status === \"completed\") {\r\n        set({\r\n          completedPackages: response.data,\r\n          loading: false\r\n        });\r\n      } else {\r\n        set({ packages: response.data, loading: false });\r\n      }\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.message || \"Failed to fetch packages\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchPackageById: async (subscriptionId) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.get<StudentPackage>(\r\n        `/student/packages/${subscriptionId}`\r\n      );\r\n      set({ selectedPackage: response.data, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.message || \"Failed to fetch package details\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  subscribeToPackage: async (data) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.post(\r\n        \"/student/packages/subscribe\",\r\n        data\r\n      );\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage = error.response?.data?.message || \"Failed to subscribe to package\";\r\n      set({ error: errorMessage, loading: false });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n\r\nexport default usePackages;\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAmFA,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QACvD,UAAU,EAAE;QACZ,iBAAiB;QACjB,SAAS;QACT,OAAO;QACP,gBAAgB,EAAE;QAClB,mBAAmB,EAAE;QAErB,eAAe,OAAO,SAAS,QAAQ;YACrC,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,qCAAqC,EAAE,QAAQ;gBAGlD,0DAA0D;gBAC1D,IAAI,WAAW,UAAU;oBACvB,IAAI;wBACF,UAAU,SAAS,IAAI;wBACvB,gBAAgB,SAAS,IAAI;wBAC7B,SAAS;oBACX;gBACF,OAAO,IAAI,WAAW,aAAa;oBACjC,IAAI;wBACF,mBAAmB,SAAS,IAAI;wBAChC,SAAS;oBACX;gBACF,OAAO;oBACL,IAAI;wBAAE,UAAU,SAAS,IAAI;wBAAE,SAAS;oBAAM;gBAChD;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;oBACxC,SAAS;gBACX;YACF;QACF;QAEA,kBAAkB,OAAO;YACvB,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,gBAAgB;gBAEvC,IAAI;oBAAE,iBAAiB,SAAS,IAAI;oBAAE,SAAS;gBAAM;YACvD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;oBACxC,SAAS;gBACX;YACF;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,+BACA;gBAEF,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACtD,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC;uCAEc"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/packages/success/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON>ooter } from \"@components/ui/card\";\r\nimport { useEffect, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { CheckCircle } from \"lucide-react\";\r\n\r\nimport { Button } from \"@components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@components/ui/card\";\r\nimport { Skeleton } from \"@components/ui/skeleton\";\r\nimport usePackages from \"@/app/hooks/student/usePackages\";\r\nimport { useSearchParams } from \"next/navigation\";\r\n\r\nexport default function PackageSuccessPage() {\r\n  const searchParams = useSearchParams();\r\n  const subscriptionId = searchParams.get(\"subscription_id\"); // subscription_id from query params\r\n  const { packages: myPackages, fetchPackages, loading } = usePackages();\r\n  const [packageSubscription, setPackageSubscription] = useState<any>(null);\r\n  const [loadingPackage, setLoadingPackage] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (!loading && myPackages) {\r\n      // Only set loading to false when we've found the package or confirmed it doesn't exist\r\n      const foundPackage = myPackages.find(\r\n        (p) => p.id === Number(subscriptionId)\r\n      );\r\n      setPackageSubscription(foundPackage);\r\n      setLoadingPackage(false);\r\n    }\r\n  }, [loading, myPackages, subscriptionId]);\r\n\r\n  useEffect(() => {\r\n    fetchPackages();\r\n  }, [fetchPackages]);\r\n\r\n  if (loadingPackage) {\r\n    return <LoadingState />;\r\n  }\r\n\r\n  if (!packageSubscription) {\r\n    return <PackageNotFound />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-[80vh] px-4\">\r\n      <Card className=\"w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900\">\r\n        <CardHeader className=\"text-center pb-2\">\r\n          <div className=\"mx-auto mb-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-full\">\r\n            <Image\r\n              src=\"/images/success.png\"\r\n              alt=\"Success\"\r\n              width={80}\r\n              height={80}\r\n              className=\"mx-auto\"\r\n            />\r\n          </div>\r\n          <CardTitle className=\"text-2xl md:text-3xl text-green-600 dark:text-green-400 font-bold\">\r\n            Purchase Successful!\r\n          </CardTitle>\r\n          <CardDescription className=\"text-base mt-2\">\r\n            Your package has been successfully purchased\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-6 px-6\">\r\n          <div className=\"bg-slate-50 dark:bg-slate-800 rounded-lg p-6 space-y-4 shadow-sm\">\r\n            <h3 className=\"font-semibold text-xl text-slate-800 dark:text-slate-200\">\r\n              {packageSubscription.package.title}\r\n            </h3>\r\n\r\n            <div className=\"grid grid-cols-1 gap-4 pt-2\">\r\n              <div className=\"bg-white dark:bg-slate-700 p-4 rounded-md shadow-sm space-y-2\">\r\n                <h4 className=\"font-medium text-slate-700 dark:text-slate-300 mb-2\">\r\n                  Services Included:\r\n                </h4>\r\n                {Object.entries(packageSubscription.service_hours).map(\r\n                  ([service, hours]: [string, any], index: number) => (\r\n                    <div key={index} className=\"flex items-center gap-3\">\r\n                      <CheckCircle className=\"h-5 w-5 flex-shrink-0 text-green-600 dark:text-green-400\" />\r\n                      <span className=\"font-medium\">\r\n                        {service}:{\" \"}\r\n                        <span className=\"text-green-600 dark:text-green-400\">\r\n                          {hours.total} hours\r\n                        </span>\r\n                      </span>\r\n                    </div>\r\n                  )\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-lg p-4 text-center\">\r\n            <p className=\"text-green-700 dark:text-green-400\">\r\n              You will receive a confirmation email with all the details. Your\r\n              counselor will schedule sessions for you.\r\n            </p>\r\n          </div>\r\n        </CardContent>\r\n        <CardFooter className=\"flex justify-center pb-6 pt-2\">\r\n          <Button\r\n            size=\"lg\"\r\n            className=\"bg-slate-800 hover:bg-slate-700 dark:bg-green-600 dark:hover:bg-green-700 text-white font-medium px-8 py-2 h-12 rounded-md transition-colors\"\r\n            asChild\r\n          >\r\n            <Link href=\"/student/dashboard/packages\">Go to your packages</Link>\r\n          </Button>\r\n        </CardFooter>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction LoadingState() {\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-[80vh] px-4\">\r\n      <Card className=\"w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900\">\r\n        <CardHeader className=\"text-center pb-2\">\r\n          <div className=\"mx-auto mb-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-full animate-pulse\">\r\n            <div className=\"h-20 w-20 rounded-full mx-auto bg-green-100 dark:bg-green-800/30\"></div>\r\n          </div>\r\n          <Skeleton className=\"h-8 w-64 mx-auto\" />\r\n          <Skeleton className=\"h-4 w-48 mx-auto mt-2\" />\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-6 px-6\">\r\n          <div className=\"bg-slate-50 dark:bg-slate-800 rounded-lg p-6 space-y-4 shadow-sm\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Skeleton className=\"h-5 w-5 rounded-full\" />\r\n              <Skeleton className=\"h-5 w-3/4\" />\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Skeleton className=\"h-5 w-5 rounded-full\" />\r\n              <Skeleton className=\"h-5 w-2/3\" />\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Skeleton className=\"h-5 w-5 rounded-full\" />\r\n              <Skeleton className=\"h-5 w-3/4\" />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"pt-2 space-y-2\">\r\n            <Skeleton className=\"h-5 w-1/2\" />\r\n            <Skeleton className=\"h-4 w-full\" />\r\n            <Skeleton className=\"h-4 w-3/4\" />\r\n          </div>\r\n        </CardContent>\r\n        <CardFooter className=\"flex justify-center pb-6 pt-2\">\r\n          <Skeleton className=\"h-10 w-48 rounded-md\" />\r\n        </CardFooter>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction PackageNotFound() {\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-[80vh] px-4\">\r\n      <Card className=\"w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900\">\r\n        <CardHeader className=\"text-center\">\r\n          <div className=\"mx-auto mb-4 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-full\">\r\n            <div className=\"text-blue-500 dark:text-blue-400 text-4xl font-bold\">\r\n              i\r\n            </div>\r\n          </div>\r\n          <CardTitle className=\"text-2xl text-blue-600 dark:text-blue-400 font-bold\">\r\n            Verifying Your Purchase\r\n          </CardTitle>\r\n          <CardDescription className=\"mt-2\">\r\n            We're confirming your package purchase\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent className=\"text-center px-6\">\r\n          <p className=\"text-slate-600 dark:text-slate-400\">\r\n            Your payment has been received and we're finalizing your package\r\n            details. This usually takes just a moment. If you've completed\r\n            payment, your package will appear shortly.\r\n          </p>\r\n        </CardContent>\r\n        <CardFooter className=\"flex justify-center pb-6\">\r\n          <Button\r\n            className=\"bg-slate-800 hover:bg-slate-700 dark:bg-slate-700 dark:hover:bg-slate-600 text-white font-medium px-6\"\r\n            asChild\r\n          >\r\n            <Link href=\"/student/dashboard/packages\">View all packages</Link>\r\n          </Button>\r\n        </CardFooter>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AAQA;AACA;AACA;AAZA;;;AANA;;;;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,iBAAiB,aAAa,GAAG,CAAC,oBAAoB,oCAAoC;IAChG,MAAM,EAAE,UAAU,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,UAAW,AAAD;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,WAAW,YAAY;gBAC1B,uFAAuF;gBACvF,MAAM,eAAe,WAAW,IAAI;iEAClC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO;;gBAEzB,uBAAuB;gBACvB,kBAAkB;YACpB;QACF;uCAAG;QAAC;QAAS;QAAY;KAAe;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG;QAAC;KAAc;IAElB,IAAI,gBAAgB;QAClB,qBAAO,6LAAC;;;;;IACV;IAEA,IAAI,CAAC,qBAAqB;QACxB,qBAAO,6LAAC;;;;;IACV;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAoE;;;;;;sCAGzF,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;sCAAiB;;;;;;;;;;;;8BAI9C,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,oBAAoB,OAAO,CAAC,KAAK;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;4CAGnE,OAAO,OAAO,CAAC,oBAAoB,aAAa,EAAE,GAAG,CACpD,CAAC,CAAC,SAAS,MAAqB,EAAE,sBAChC,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAK,WAAU;;gEACb;gEAAQ;gEAAE;8EACX,6LAAC;oEAAK,WAAU;;wEACb,MAAM,KAAK;wEAAC;;;;;;;;;;;;;;mDALT;;;;;;;;;;;;;;;;;;;;;;sCAepB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;8BAMtD,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,OAAO;kCAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrD;GAjGwB;;QACD,qIAAA,CAAA,kBAAe;QAEqB,yIAAA,CAAA,UAAW;;;KAH9C;AAmGxB,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAEtB,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;sCAIxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAGxB,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9B;MAvCS;AAyCT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAsD;;;;;;;;;;;sCAIvE,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAsD;;;;;;sCAG3E,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;sCAAO;;;;;;;;;;;;8BAIpC,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;8BAMpD,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,OAAO;kCAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrD;MAnCS"}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,GAAG,OAAO,GAAG,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,wIAAA,CAAA,OAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0]}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('CircleCheckBig', [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n]);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAiB,UAAA,EAAiB,gBAAkB,CAAA,CAAA,CAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
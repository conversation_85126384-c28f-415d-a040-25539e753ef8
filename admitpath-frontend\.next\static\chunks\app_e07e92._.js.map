{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/counselor/useService.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport { ServicesState } from \"@/app/types/counselor/service\";\r\n\r\nexport const useServices = create<ServicesState>((set) => ({\r\n  services: [],\r\n  selectedService: null,\r\n  loading: false,\r\n  error: null,\r\n\r\n  fetchServices: async (id) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(`/counselor/services-offered/${id}`);\r\n      const fetchedServices = response.data.map((service: any) => ({\r\n        ...service,\r\n        service_type: service.custom_type || service.service_type,\r\n      }));\r\n      set({ services: fetchedServices });\r\n    } catch (error) {\r\n      set({ error: \"Error fetching services\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  fetchSingleService: async (serviceId) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/counselor/services-offered/service/${serviceId}`\r\n      );\r\n      set({ selectedService: response.data });\r\n    } catch (error) {\r\n      set({ error: \"Error fetching single service\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  addService: async (serviceData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.post(\r\n        \"/counselor/services-offered/\",\r\n        serviceData\r\n      );\r\n      toast.success(\"Service added successfully!\");\r\n      set((state) => ({ services: [...state.services, response.data] }));\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error?.response?.data?.message ||\r\n        error?.message ||\r\n        \"Failed to add the service. Please try again.\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  updateService: async (serviceId, serviceData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.put(\r\n        `/counselor/services-offered/${serviceId}`,\r\n        serviceData\r\n      );\r\n      toast.success(\"Service updated successfully!\");\r\n      set((state) => ({\r\n        services: state.services.map((service) =>\r\n          service.id === serviceId ? { ...service, ...serviceData } : service\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      set({ error: \"Error updating service\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  deleteService: async (serviceId) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.delete(`/counselor/services-offered/${serviceId}`);\r\n      toast.success(\"Service deleted successfully!\");\r\n      set((state) => ({\r\n        services: state.services.filter((service) => service.id !== serviceId),\r\n      }));\r\n    } catch (error) {\r\n      set({ error: \"Error deleting service\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAKO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,MAAQ,CAAC;QACzD,UAAU,EAAE;QACZ,iBAAiB;QACjB,SAAS;QACT,OAAO;QAEP,eAAe,OAAO;YACpB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI;gBACxE,MAAM,kBAAkB,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;wBAC3D,GAAG,OAAO;wBACV,cAAc,QAAQ,WAAW,IAAI,QAAQ,YAAY;oBAC3D,CAAC;gBACD,IAAI;oBAAE,UAAU;gBAAgB;YAClC,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;gBAA0B;gBACvC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,oCAAoC,EAAE,WAAW;gBAEpD,IAAI;oBAAE,iBAAiB,SAAS,IAAI;gBAAC;YACvC,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;gBAAgC;gBAC7C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,gCACA;gBAEF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,IAAI,CAAC,QAAU,CAAC;wBAAE,UAAU;+BAAI,MAAM,QAAQ;4BAAE,SAAS,IAAI;yBAAC;oBAAC,CAAC;YAClE,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,OAAO,UAAU,MAAM,WACvB,OAAO,WACP;gBACF,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,WAAW;YAC/B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CACjB,CAAC,4BAA4B,EAAE,WAAW,EAC1C;gBAEF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,UAC5B,QAAQ,EAAE,KAAK,YAAY;gCAAE,GAAG,OAAO;gCAAE,GAAG,WAAW;4BAAC,IAAI;oBAEhE,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;gBAAyB;gBACtC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,mHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,4BAA4B,EAAE,WAAW;gBACjE,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK;oBAC9D,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;gBAAyB;gBACtC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/mainBtn/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { IconProp } from \"@fortawesome/fontawesome-svg-core\";\r\n\r\ninterface ButtonProps {\r\n  type?: \"submit\" | \"reset\" | \"button\";\r\n  children: React.ReactNode;\r\n  onClick?: (\r\n    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<Element>\r\n  ) => void;\r\n  style?: React.CSSProperties;\r\n  className?: string;\r\n  variant?: \"primary\" | \"secondary\" | \"neutral\";\r\n  icon?: IconProp;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const MainButton: React.FC<ButtonProps> = ({\r\n  type,\r\n  children,\r\n  onClick,\r\n  style,\r\n  className = \"\",\r\n  variant = \"neutral\",\r\n  icon,\r\n  disabled = false,\r\n}) => {\r\n  // Default styles for button variants\r\n  const variantStyles = {\r\n    primary: \"bg-mainClr text-white hover:bg-[#152070] focus:ring-blue-300\",\r\n    secondary: \"bg-[#EB5757] text-white hover:bg-[#EB6767]\",\r\n    neutral: \"bg-neutral1 text-neutral10 hover:bg-neutral2 focus:ring-neutral3\",\r\n  };\r\n\r\n  const disabledStyles = \"opacity-50 cursor-not-allowed\";\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={`py-3 px-6 font-medium rounded-xl text-sm flex items-center justify-center gap-2 \r\n        ${variantStyles[variant]} ${\r\n        disabled ? disabledStyles : \"\"\r\n      } ${className}`}\r\n      style={style}\r\n      onClick={!disabled ? onClick : undefined}\r\n      disabled={disabled}\r\n    >\r\n      {children}\r\n      {icon && <FontAwesomeIcon icon={icon} />}\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAmBO,MAAM,aAAoC,CAAC,EAChD,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EACL,YAAY,EAAE,EACd,UAAU,SAAS,EACnB,IAAI,EACJ,WAAW,KAAK,EACjB;IACC,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,MAAM,iBAAiB;IAEvB,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAC;QACV,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAC1B,WAAW,iBAAiB,GAC7B,CAAC,EAAE,WAAW;QACf,OAAO;QACP,SAAS,CAAC,WAAW,UAAU;QAC/B,UAAU;;YAET;YACA,sBAAQ,6LAAC,uKAAA,CAAA,kBAAe;gBAAC,MAAM;;;;;;;;;;;;AAGtC;KAlCa"}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/styledIcon/index.tsx"], "sourcesContent": ["import { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { IconDefinition } from \"@fortawesome/fontawesome-svg-core\";\r\n\r\ninterface StyledIconProps {\r\n  icon: IconDefinition;\r\n  bgColor?: string;\r\n  color?: string;\r\n}\r\n\r\nconst StyledIcon: React.FC<StyledIconProps> = ({ icon, bgColor, color }) => (\r\n  <span\r\n    className=\"p-2 rounded-xl h-9 w-9 flex justify-center items-center\"\r\n    style={{ backgroundColor: bgColor, color: color }}\r\n  >\r\n    <FontAwesomeIcon icon={icon} size=\"sm\" />\r\n  </span>\r\n);\r\n\r\nexport default StyledIcon;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AASA,MAAM,aAAwC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,iBACrE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,iBAAiB;YAAS,OAAO;QAAM;kBAEhD,cAAA,6LAAC,uKAAA,CAAA,kBAAe;YAAC,MAAM;YAAM,MAAK;;;;;;;;;;;KALhC;uCASS"}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/threeDotIconMenu/index.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faEllipsisV,\r\n  faPencil,\r\n  faTrash,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\n\r\ninterface DropdownProps {\r\n  handleEdit: () => void;\r\n  handleRemove: () => void;\r\n  name?: string;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst ThreeDotIconDropdown: React.FC<DropdownProps> = ({\r\n  handleEdit,\r\n  handleRemove,\r\n  name = \"Edit\",\r\n  isLoading = false,\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  const toggleMenu = () => setIsOpen((prev) => !prev);\r\n\r\n  const handleClickOutside = (event: MouseEvent) => {\r\n    if (\r\n      dropdownRef.current &&\r\n      !dropdownRef.current.contains(event.target as Node)\r\n    ) {\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"relative\" ref={dropdownRef}>\r\n      <button\r\n        onClick={toggleMenu}\r\n        className={isLoading ? \"opacity-50 cursor-wait\" : \"\"}\r\n        disabled={isLoading}\r\n      >\r\n        <FontAwesomeIcon\r\n          icon={faEllipsisV}\r\n          className={`w-4 ${isLoading ? \"animate-pulse\" : \"\"}`}\r\n        />\r\n      </button>\r\n      {isOpen && (\r\n        <ul className=\"absolute top-full right-0 text-sm p-3 bg-white rounded-lg shadow-md w-36\">\r\n          <li\r\n            className=\"mb-2 last:mb-0 cursor-pointer flex items-center text-neutral8\"\r\n            onClick={() => {\r\n              handleEdit();\r\n              setIsOpen(false);\r\n            }}\r\n          >\r\n            <FontAwesomeIcon icon={faPencil} className=\"w-4 mr-2\" />\r\n            {name}\r\n          </li>\r\n          <li\r\n            className=\"cursor-pointer flex items-center text-[#EB5757]\"\r\n            onClick={() => {\r\n              handleRemove();\r\n              setIsOpen(false);\r\n            }}\r\n          >\r\n            <FontAwesomeIcon icon={faTrash} className=\"w-4 mr-2\" />\r\n            Remove\r\n          </li>\r\n        </ul>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ThreeDotIconDropdown;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAaA,MAAM,uBAAgD,CAAC,EACrD,UAAU,EACV,YAAY,EACZ,OAAO,MAAM,EACb,YAAY,KAAK,EAClB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,aAAa,IAAM,UAAU,CAAC,OAAS,CAAC;IAE9C,MAAM,qBAAqB,CAAC;QAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;YACA,UAAU;QACZ;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,SAAS,gBAAgB,CAAC,aAAa;YACvC;kDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;yCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,6LAAC;gBACC,SAAS;gBACT,WAAW,YAAY,2BAA2B;gBAClD,UAAU;0BAEV,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oBACd,MAAM,2KAAA,CAAA,cAAW;oBACjB,WAAW,CAAC,IAAI,EAAE,YAAY,kBAAkB,IAAI;;;;;;;;;;;YAGvD,wBACC,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC;wBACC,WAAU;wBACV,SAAS;4BACP;4BACA,UAAU;wBACZ;;0CAEA,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,WAAQ;gCAAE,WAAU;;;;;;4BAC1C;;;;;;;kCAEH,6LAAC;wBACC,WAAU;wBACV,SAAS;4BACP;4BACA,UAAU;wBACZ;;0CAEA,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,UAAO;gCAAE,WAAU;;;;;;4BAAa;;;;;;;;;;;;;;;;;;;AAOnE;GA/DM;KAAA;uCAiES"}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/dropdown/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\ninterface DropdownProps {\r\n  label: string;\r\n  name: string;\r\n  options: { value: string | number; label: string }[];\r\n  value: string | number;\r\n  onChange: React.ChangeEventHandler<HTMLSelectElement>;\r\n  required?: boolean;\r\n  className?: string;\r\n  selectStyle?: string;\r\n}\r\n\r\nconst Dropdown: React.FC<DropdownProps> = ({\r\n  label,\r\n  name,\r\n  options,\r\n  value,\r\n  onChange,\r\n  required = false,\r\n  className = \"\",\r\n  selectStyle = \"\",\r\n}) => {\r\n  return (\r\n    <div className={`dropdown relative ${className}`}>\r\n      <label htmlFor={name} className=\"block font-medium mb-2 text-sm\">\r\n        {label} {required && <span>*</span>}\r\n      </label>\r\n      <div className=\"relative\">\r\n        <select\r\n          name={name}\r\n          id={name}\r\n          value={value}\r\n          onChange={onChange}\r\n          required={required}\r\n          className={`appearance-none py-3.5 px-3 pr-10 rounded border border-solid border-slate-200 w-full bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${selectStyle}`}\r\n        >\r\n          {options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n        {/* Custom Down Arrow Icon */}\r\n        <span className=\"absolute inset-y-0 right-4 flex items-center pointer-events-none\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"h-5 w-5 text-gray-500\"\r\n            viewBox=\"0 0 20 20\"\r\n            fill=\"currentColor\"\r\n          >\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\r\n              clipRule=\"evenodd\"\r\n            />\r\n          </svg>\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dropdown;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAeA,MAAM,WAAoC,CAAC,EACzC,KAAK,EACL,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,EAAE,EACjB;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;;0BAC9C,6LAAC;gBAAM,SAAS;gBAAM,WAAU;;oBAC7B;oBAAM;oBAAE,0BAAY,6LAAC;kCAAK;;;;;;;;;;;;0BAE7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAM;wBACN,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,qKAAqK,EAAE,aAAa;kCAE/L,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gCAA0B,OAAO,OAAO,KAAK;0CAC3C,OAAO,KAAK;+BADF,OAAO,KAAK;;;;;;;;;;kCAM7B,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BACC,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,MAAK;sCAEL,cAAA,6LAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;KAhDM;uCAkDS"}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/inputField/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"container\": \"index-module__8yU72G__container\",\n  \"error\": \"index-module__8yU72G__error\",\n  \"errorMessage\": \"index-module__8yU72G__errorMessage\",\n  \"input\": \"index-module__8yU72G__input\",\n  \"label\": \"index-module__8yU72G__label\",\n  \"required\": \"index-module__8yU72G__required\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/inputField/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, InputHTMLAttributes } from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {\r\n  label: string;\r\n  error?: string;\r\n  required?: boolean;\r\n}\r\n\r\nexport const InputField: FC<InputFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  ...inputProps\r\n}) => {\r\n  return (\r\n    <div className={styles.container}>\r\n      <label\r\n        htmlFor={inputProps.id || inputProps.name}\r\n        className={styles.label}\r\n      >\r\n        {label} {required && <span className={styles.required}>*</span>}\r\n      </label>\r\n\r\n      <input\r\n        {...inputProps}\r\n        className={`${styles.input} ${error ? styles.error : \"\"}`}\r\n        aria-required={required}\r\n        aria-invalid={!!error}\r\n        aria-describedby={error ? `${inputProps.id}-error` : undefined}\r\n      />\r\n\r\n      {error && (\r\n        <span\r\n          id={`${inputProps.id}-error`}\r\n          className={styles.errorMessage}\r\n          role=\"alert\"\r\n        >\r\n          {error}\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWO,MAAM,aAAkC,CAAC,EAC9C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,GAAG,YACJ;IACC,qBACE,6LAAC;QAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,6LAAC;gBACC,SAAS,WAAW,EAAE,IAAI,WAAW,IAAI;gBACzC,WAAW,iKAAA,CAAA,UAAM,CAAC,KAAK;;oBAEtB;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAW,iKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAGzD,6LAAC;gBACE,GAAG,UAAU;gBACd,WAAW,GAAG,iKAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,iKAAA,CAAA,UAAM,CAAC,KAAK,GAAG,IAAI;gBACzD,iBAAe;gBACf,gBAAc,CAAC,CAAC;gBAChB,oBAAkB,QAAQ,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG;;;;;;YAGtD,uBACC,6LAAC;gBACC,IAAI,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC;gBAC5B,WAAW,iKAAA,CAAA,UAAM,CAAC,YAAY;gBAC9B,MAAK;0BAEJ;;;;;;;;;;;;AAKX;KAlCa"}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/textarea/index.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ninterface TextareaProps {\r\n  label?: string;\r\n  name: string;\r\n  value: string;\r\n  onChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  rows?: number;\r\n  className?: string;\r\n  style?: React.CSSProperties;\r\n}\r\n\r\nexport const Textarea: React.FC<TextareaProps> = ({\r\n  label,\r\n  name,\r\n  value,\r\n  onChange,\r\n  placeholder = \"Enter text here...\",\r\n  required = false,\r\n  rows = 4,\r\n  className = \"\",\r\n  style = {},\r\n}) => {\r\n  return (\r\n    <div className={`w-full ${className}`}>\r\n      {label && (\r\n        <label\r\n          htmlFor={name}\r\n          className=\"block text-sm font-medium text-gray-700 mb-2\"\r\n        >\r\n          {label} {required && <span>*</span>}\r\n        </label>\r\n      )}\r\n      <textarea\r\n        id={name}\r\n        name={name}\r\n        value={value}\r\n        onChange={onChange}\r\n        placeholder={placeholder}\r\n        required={required}\r\n        rows={rows}\r\n        className=\"w-full border rounded-lg p-3 text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n        style={style}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAcO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,cAAc,oBAAoB,EAClC,WAAW,KAAK,EAChB,OAAO,CAAC,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,CAAC,EACX;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,OAAO,EAAE,WAAW;;YAClC,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBAAM;oBAAE,0BAAY,6LAAC;kCAAK;;;;;;;;;;;;0BAG/B,6LAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,WAAU;gBACV,OAAO;;;;;;;;;;;;AAIf;KAlCa"}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/popup/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactNode } from \"react\";\r\n\r\ninterface PopupProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title?: string;\r\n  width?: string;\r\n  height?: string;\r\n  children: ReactNode;\r\n}\r\n\r\nconst Popup: React.FC<PopupProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  width,\r\n  height = \"80vh\",\r\n  children,\r\n}) => {\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div\r\n        className=\"bg-white rounded-xl shadow-lg relative overflow-hidden w-[95vw] md:w-[80vw] lg:w-[60vw] max-w-4xl\"\r\n        style={{ width }}\r\n      >\r\n        <div className=\"bg-grayLight p-4 mb-4\">\r\n          {title && <h2 className=\"text-xl font-semibold \">{title}</h2>}\r\n          <button\r\n            onClick={onClose}\r\n            className=\"absolute top-5 right-4 text-xs text-gray-500 hover:text-black\"\r\n            aria-label=\"Close\"\r\n          >\r\n            ✖\r\n          </button>\r\n        </div>\r\n        <div\r\n          className=\"popupz p-4 pb-6 overflow-y-auto overflow-x-hidden\"\r\n          style={{ maxHeight: height }}\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Popup;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,SAAS,MAAM,EACf,QAAQ,EACT;IACC,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAM;;8BAEf,6LAAC;oBAAI,WAAU;;wBACZ,uBAAS,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAClD,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCACZ;;;;;;;;;;;;8BAIH,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,WAAW;oBAAO;8BAE1B;;;;;;;;;;;;;;;;;AAKX;KAnCM;uCAqCS"}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useFormState.ts"], "sourcesContent": ["import { useState } from \"react\";\r\n\r\nfunction useFormState<T extends Record<string, any>>(initialState: T) {\r\n  const [formData, setFormData] = useState(initialState);\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<\r\n      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement\r\n    >\r\n  ) => {\r\n    const { name, type } = e.target;\r\n\r\n    // Narrow down the type to HTMLInputElement for checkboxes and radio buttons\r\n    if (\r\n      e.target instanceof HTMLInputElement &&\r\n      (type === \"checkbox\" || type === \"radio\")\r\n    ) {\r\n      const { checked } = e.target;\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        [name]: checked, // Use `checked` for checkboxes and radio buttons\r\n      }));\r\n    } else {\r\n      const { value } = e.target;\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        [name]: value, // Use `value` for other input types\r\n      }));\r\n    }\r\n  };\r\n\r\n  return { formData, setFormData, handleChange };\r\n}\r\n\r\nexport default useFormState;\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,SAAS,aAA4C,YAAe;;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe,CACnB;QAIA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QAE/B,4EAA4E;QAC5E,IACE,EAAE,MAAM,YAAY,oBACpB,CAAC,SAAS,cAAc,SAAS,OAAO,GACxC;YACA,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;YAC5B,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH,OAAO;YACL,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;YAC1B,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,OAAO;QAAE;QAAU;QAAa;IAAa;AAC/C;GA9BS;uCAgCM"}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/constants/counselor.ts"], "sourcesContent": ["export const serviceItems = [\r\n  \"Please select\",\r\n  \"Free 15 Minutes Intro Call\",\r\n  \"Personal Statement Guidance\",\r\n  \"Essay Review\",\r\n  \"University Shortlisting\",\r\n  \"Extra Curricular Profile Building\",\r\n  \"Supplementary Essay Guidance\",\r\n  \"Financial Aid Advice\",\r\n  \"Interview Preparation\",\r\n  \"Other\",\r\n].map((item) => ({ value: item === \"Please select\" ? \"\" : item, label: item }));\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;QAAE,OAAO,SAAS,kBAAkB,KAAK;QAAM,OAAO;IAAK,CAAC"}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/constants/serviceDescriptions.ts"], "sourcesContent": ["export const serviceDescriptions: { [key: string]: string } = {\r\n  \"Free 15 Minutes Intro Call\":\r\n    \"Get to know each other! In this quick consultation, I'll learn about your background, goals, and application needs while you get a sense of how I can help you navigate the admissions process.\",\r\n  \"Personal Statement Guidance\":\r\n    \"Struggling with your personal statement? I'll help you brainstorm ideas, craft a compelling narrative, and refine your story to make a lasting impression on admissions officers.\",\r\n  \"Essay Review\":\r\n    \"Get detailed feedback on your college essays to enhance structure, clarity, and impact. I'll ensure your writing is authentic, engaging, and aligns with what top universities look for.\",\r\n  \"University Shortlisting\":\r\n    \"Not sure which universities fit you best? I'll help you build a balanced college list based on your profile, preferences, and career aspirations—maximizing your chances of acceptance.\",\r\n  \"Extra Curricular Profile Building\":\r\n    \"Stand out beyond academics! I'll guide you on how to strengthen your extracurriculars, highlight leadership experiences, and craft a compelling activities list that showcases your unique strengths.\",\r\n  \"Supplementary Essay Guidance\":\r\n    \"Supplementary essays matter just as much as your personal statement. I'll help you tailor responses for different schools, ensuring each essay aligns with their values and expectations.\",\r\n  \"Interview Preparation\":\r\n    \"Ace your admissions interview with personalized mock sessions, expert feedback, and tips on how to confidently articulate your story and why you're a great fit for your dream school.\",\r\n  \"Financial Aid Advice\":\r\n    \"Navigate scholarships, grants, and financial aid applications with ease. I'll provide insights on funding opportunities and strategies to make college more affordable.\",\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,sBAAiD;IAC5D,8BACE;IACF,+BACE;IACF,gBACE;IACF,2BACE;IACF,qCACE;IACF,gCACE;IACF,yBACE;IACF,wBACE;AACJ"}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/view/popup/AddServicePopup.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Dropdown from \"@/app/components/common/dropdown\";\r\nimport { InputField } from \"@/app/components/common/inputField\";\r\nimport { Textarea } from \"@/app/components/common/textarea\";\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport useFormState from \"@/app/hooks/useFormState\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport { faCheck, faClose } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { useServices } from \"@/app/hooks/counselor/useService\";\r\nimport { useProfile } from \"@/app/hooks/counselor/useProfile\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useState } from \"react\";\r\nimport { serviceItems } from \"@/app/constants/counselor\";\r\nimport { serviceDescriptions } from \"@/app/constants/serviceDescriptions\";\r\n\r\nconst initialState = {\r\n  service_type: \"\",\r\n  custom_type: \"\",\r\n  description: \"\",\r\n  price: 0,\r\n  offers_intro_call: false,\r\n};\r\n\r\nconst initialErrorState = {\r\n  service_type: \"\",\r\n  custom_type: \"\",\r\n  description: \"\",\r\n  price: \"\",\r\n};\r\n\r\nexport default function AddServicePopup({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n}: PopupProps) {\r\n  const { addService, fetchServices } = useServices();\r\n  const { userInfo } = useProfile();\r\n\r\n  const {\r\n    formData,\r\n    setFormData,\r\n    handleChange: baseHandleChange,\r\n  } = useFormState<{\r\n    service_type: string;\r\n    custom_type?: string;\r\n    description: string;\r\n    price: number | string;\r\n    offers_intro_call: boolean;\r\n  }>(initialState);\r\n\r\n  const [errors, setErrors] = useState(initialErrorState);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const validateForm = () => {\r\n    const newErrors = { ...initialErrorState };\r\n    let isValid = true;\r\n\r\n    if (!formData.service_type) {\r\n      newErrors.service_type = \"Please select a service type.\";\r\n      isValid = false;\r\n    }\r\n\r\n    if (formData.service_type === \"Other\" && !formData.custom_type) {\r\n      newErrors.custom_type = \"Please enter a custom service type.\";\r\n      isValid = false;\r\n    }\r\n\r\n    if (!formData.description) {\r\n      newErrors.description = \"Description is required.\";\r\n      isValid = false;\r\n    }\r\n\r\n    if (\r\n      formData.service_type !== \"Free 15 Minutes Intro Call\" &&\r\n      typeof formData.price === \"number\" &&\r\n      formData.price <= 0\r\n    ) {\r\n      newErrors.price = \"Price must be greater than 0.\";\r\n      isValid = false;\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return isValid;\r\n  };\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<\r\n      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement\r\n    >\r\n  ) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Clear the specific error when the input becomes valid\r\n    if (errors[name as keyof typeof errors]) {\r\n      const newErrors = { ...errors, [name]: \"\" };\r\n      setErrors(newErrors);\r\n    }\r\n\r\n    // If service_type changes, update the description and handle price\r\n    if (name === \"service_type\") {\r\n      const description =\r\n        serviceDescriptions[value as keyof typeof serviceDescriptions] || \"\";\r\n\r\n      if (value === \"Free 15 Minutes Intro Call\") {\r\n        setFormData((prevState) => ({\r\n          ...prevState,\r\n          service_type: value,\r\n          description: description,\r\n          price: \"Free\",\r\n        }));\r\n      } else {\r\n        setFormData((prevState) => ({\r\n          ...prevState,\r\n          service_type: value,\r\n          description: description,\r\n          price: prevState.price === \"Free\" ? 0 : prevState.price,\r\n        }));\r\n      }\r\n    } else {\r\n      baseHandleChange(e); // Call the original handleChange logic to update formData\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (loading) return;\r\n    setLoading(true);\r\n    if (!validateForm()) return;\r\n\r\n    const requestData = {\r\n      service_type: formData.service_type,\r\n      description: formData.description,\r\n      price:\r\n        formData.service_type === \"Free 15 Minutes Intro Call\"\r\n          ? 0\r\n          : formData.price, // Set price to 0 if service_type is \"Free 15 Minutes Intro Call\"\r\n      offers_intro_call: formData.offers_intro_call,\r\n      ...(formData.service_type === \"Other\" && formData.custom_type\r\n        ? { custom_type: formData.custom_type }\r\n        : {}),\r\n    };\r\n\r\n    try {\r\n      await addService(requestData);\r\n      setFormData(initialState);\r\n      setIsPopupOpen(false);\r\n      fetchServices(userInfo?.counselor_id);\r\n    } catch (error) {\r\n      toast.error(\"Failed to add service. Please try again.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  function handleCancel() {\r\n    setIsPopupOpen(false);\r\n    setFormData(initialState);\r\n  }\r\n\r\n  return (\r\n    <Popup\r\n      isOpen={isPopupOpen}\r\n      onClose={() => setIsPopupOpen(false)}\r\n      title=\"Add Service\"\r\n    >\r\n      <form onSubmit={handleSubmit}>\r\n        <div className=\"mb-4\">\r\n          <Dropdown\r\n            label=\"Select Service *\"\r\n            name=\"service_type\"\r\n            options={serviceItems}\r\n            value={formData.service_type}\r\n            onChange={handleChange}\r\n            selectStyle=\"w-full border p-3 bg-neutral1\"\r\n          />\r\n          {errors.service_type && (\r\n            <p className=\"text-red-500 text-sm mt-1\">{errors.service_type}</p>\r\n          )}\r\n        </div>\r\n\r\n        {formData.service_type === \"Other\" && (\r\n          <InputField\r\n            label=\"Specify Service Type *\"\r\n            type=\"text\"\r\n            placeholder=\"Enter custom service type\"\r\n            name=\"custom_type\"\r\n            value={formData.custom_type || \"\"}\r\n            onChange={handleChange}\r\n            className=\"mb-4\"\r\n          />\r\n        )}\r\n        {errors.custom_type && (\r\n          <p className=\"text-red-500 text-sm mt-1\">{errors.custom_type}</p>\r\n        )}\r\n\r\n        <div className=\"my-4\">\r\n          <Textarea\r\n            label=\"Description *\"\r\n            name=\"description\"\r\n            value={formData.description}\r\n            onChange={handleChange}\r\n            placeholder=\"Enter service description\"\r\n            rows={5}\r\n          />\r\n          {errors.description && (\r\n            <p className=\"text-red-500 text-sm mt-1\">{errors.description}</p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"mb-6\">\r\n          <InputField\r\n            label=\"Price per hour ($) *\"\r\n            type={\r\n              formData.service_type === \"Free 15 Minutes Intro Call\"\r\n                ? \"text\"\r\n                : \"number\"\r\n            } // Change input type to \"text\" if service_type is \"Free 15 Minutes Intro Call\"\r\n            placeholder={\r\n              formData.service_type === \"Free 15 Minutes Intro Call\"\r\n                ? \"Free\"\r\n                : \"Enter price\"\r\n            }\r\n            name=\"price\"\r\n            value={\r\n              formData.service_type === \"Free 15 Minutes Intro Call\"\r\n                ? \"Free\"\r\n                : formData.price || \"\"\r\n            }\r\n            onChange={handleChange}\r\n            min=\"0\"\r\n            disabled={formData.service_type === \"Free 15 Minutes Intro Call\"} // Disable the input if service_type is \"Free 15 Minutes Intro Call\"\r\n            className={\r\n              formData.service_type === \"Free 15 Minutes Intro Call\"\r\n                ? \"bg-red-500\"\r\n                : \"\"\r\n            }\r\n          />\r\n          {errors.price && (\r\n            <p className=\"text-red-500 text-sm mt-1\">{errors.price}</p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex justify-end space-x-4 mt-6\">\r\n          <MainButton\r\n            type=\"button\"\r\n            variant=\"neutral\"\r\n            children=\"Cancel\"\r\n            icon={faClose}\r\n            onClick={handleCancel}\r\n          />\r\n\r\n          <MainButton\r\n            type=\"submit\"\r\n            variant=\"primary\"\r\n            children={loading ? \"Adding...\" : \"Add\"}\r\n            icon={faCheck}\r\n            disabled={loading}\r\n          />\r\n        </div>\r\n      </form>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AANA;;;AATA;;;;;;;;;;;;;;AAiBA,MAAM,eAAe;IACnB,cAAc;IACd,aAAa;IACb,aAAa;IACb,OAAO;IACP,mBAAmB;AACrB;AAEA,MAAM,oBAAoB;IACxB,cAAc;IACd,aAAa;IACb,aAAa;IACb,OAAO;AACT;AAEe,SAAS,gBAAgB,EACtC,WAAW,EACX,cAAc,EACH;;IACX,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD;IAChD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD;IAE9B,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,cAAc,gBAAgB,EAC/B,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD,EAMZ;IAEH,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe;QACnB,MAAM,YAAY;YAAE,GAAG,iBAAiB;QAAC;QACzC,IAAI,UAAU;QAEd,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,UAAU,YAAY,GAAG;YACzB,UAAU;QACZ;QAEA,IAAI,SAAS,YAAY,KAAK,WAAW,CAAC,SAAS,WAAW,EAAE;YAC9D,UAAU,WAAW,GAAG;YACxB,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,UAAU,WAAW,GAAG;YACxB,UAAU;QACZ;QAEA,IACE,SAAS,YAAY,KAAK,gCAC1B,OAAO,SAAS,KAAK,KAAK,YAC1B,SAAS,KAAK,IAAI,GAClB;YACA,UAAU,KAAK,GAAG;YAClB,UAAU;QACZ;QAEA,UAAU;QACV,OAAO;IACT;IAEA,MAAM,eAAe,CACnB;QAIA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,wDAAwD;QACxD,IAAI,MAAM,CAAC,KAA4B,EAAE;YACvC,MAAM,YAAY;gBAAE,GAAG,MAAM;gBAAE,CAAC,KAAK,EAAE;YAAG;YAC1C,UAAU;QACZ;QAEA,mEAAmE;QACnE,IAAI,SAAS,gBAAgB;YAC3B,MAAM,cACJ,0IAAA,CAAA,sBAAmB,CAAC,MAA0C,IAAI;YAEpE,IAAI,UAAU,8BAA8B;gBAC1C,YAAY,CAAC,YAAc,CAAC;wBAC1B,GAAG,SAAS;wBACZ,cAAc;wBACd,aAAa;wBACb,OAAO;oBACT,CAAC;YACH,OAAO;gBACL,YAAY,CAAC,YAAc,CAAC;wBAC1B,GAAG,SAAS;wBACZ,cAAc;wBACd,aAAa;wBACb,OAAO,UAAU,KAAK,KAAK,SAAS,IAAI,UAAU,KAAK;oBACzD,CAAC;YACH;QACF,OAAO;YACL,iBAAiB,IAAI,0DAA0D;QACjF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,SAAS;QACb,WAAW;QACX,IAAI,CAAC,gBAAgB;QAErB,MAAM,cAAc;YAClB,cAAc,SAAS,YAAY;YACnC,aAAa,SAAS,WAAW;YACjC,OACE,SAAS,YAAY,KAAK,+BACtB,IACA,SAAS,KAAK;YACpB,mBAAmB,SAAS,iBAAiB;YAC7C,GAAI,SAAS,YAAY,KAAK,WAAW,SAAS,WAAW,GACzD;gBAAE,aAAa,SAAS,WAAW;YAAC,IACpC,CAAC,CAAC;QACR;QAEA,IAAI;YACF,MAAM,WAAW;YACjB,YAAY;YACZ,eAAe;YACf,cAAc,UAAU;QAC1B,EAAE,OAAO,OAAO;YACd,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;QACP,eAAe;QACf,YAAY;IACd;IAEA,qBACE,6LAAC,iJAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,eAAe;QAC9B,OAAM;kBAEN,cAAA,6LAAC;YAAK,UAAU;;8BACd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oJAAA,CAAA,UAAQ;4BACP,OAAM;4BACN,MAAK;4BACL,SAAS,gIAAA,CAAA,eAAY;4BACrB,OAAO,SAAS,YAAY;4BAC5B,UAAU;4BACV,aAAY;;;;;;wBAEb,OAAO,YAAY,kBAClB,6LAAC;4BAAE,WAAU;sCAA6B,OAAO,YAAY;;;;;;;;;;;;gBAIhE,SAAS,YAAY,KAAK,yBACzB,6LAAC,sJAAA,CAAA,aAAU;oBACT,OAAM;oBACN,MAAK;oBACL,aAAY;oBACZ,MAAK;oBACL,OAAO,SAAS,WAAW,IAAI;oBAC/B,UAAU;oBACV,WAAU;;;;;;gBAGb,OAAO,WAAW,kBACjB,6LAAC;oBAAE,WAAU;8BAA6B,OAAO,WAAW;;;;;;8BAG9D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oJAAA,CAAA,WAAQ;4BACP,OAAM;4BACN,MAAK;4BACL,OAAO,SAAS,WAAW;4BAC3B,UAAU;4BACV,aAAY;4BACZ,MAAM;;;;;;wBAEP,OAAO,WAAW,kBACjB,6LAAC;4BAAE,WAAU;sCAA6B,OAAO,WAAW;;;;;;;;;;;;8BAIhE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,sJAAA,CAAA,aAAU;4BACT,OAAM;4BACN,MACE,SAAS,YAAY,KAAK,+BACtB,SACA;4BAEN,aACE,SAAS,YAAY,KAAK,+BACtB,SACA;4BAEN,MAAK;4BACL,OACE,SAAS,YAAY,KAAK,+BACtB,SACA,SAAS,KAAK,IAAI;4BAExB,UAAU;4BACV,KAAI;4BACJ,UAAU,SAAS,YAAY,KAAK;4BACpC,WACE,SAAS,YAAY,KAAK,+BACtB,eACA;;;;;;wBAGP,OAAO,KAAK,kBACX,6LAAC;4BAAE,WAAU;sCAA6B,OAAO,KAAK;;;;;;;;;;;;8BAI1D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,SAAQ;4BACR,UAAS;4BACT,MAAM,2KAAA,CAAA,UAAO;4BACb,SAAS;;;;;;sCAGX,6LAAC,mJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,SAAQ;4BACR,UAAU,UAAU,cAAc;4BAClC,MAAM,2KAAA,CAAA,UAAO;4BACb,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAMtB;GAvOwB;;QAIgB,0IAAA,CAAA,cAAW;QAC5B,0IAAA,CAAA,aAAU;QAM3B,+HAAA,CAAA,UAAY;;;KAXM"}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/view/popup/EditServicePopup.tsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\n\r\nimport Dropdown from \"@/app/components/common/dropdown\";\r\nimport { InputField } from \"@/app/components/common/inputField\";\r\nimport { Textarea } from \"@/app/components/common/textarea\";\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport useFormState from \"@/app/hooks/useFormState\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport { faCheck, faClose } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { useServices } from \"@/app/hooks/counselor/useService\";\r\nimport { serviceItems } from \"@/app/constants/counselor\";\r\n\r\nconst initialErrorState = {\r\n  service_type: \"\",\r\n  custom_type: \"\",\r\n  description: \"\",\r\n  price: \"\",\r\n};\r\n\r\n/////////////////////////// MAIN COMPONENT //////////////////////\r\nexport default function EditServicePopup({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n  serviceData,\r\n}: PopupProps) {\r\n  const { updateService } = useServices();\r\n\r\n  const {\r\n    formData,\r\n    setFormData,\r\n    handleChange: baseHandleChange,\r\n  } = useFormState<{\r\n    service_type: string;\r\n    custom_type?: string | null;\r\n    description: string;\r\n    price: number | string;\r\n    offers_intro_call?: boolean;\r\n  }>({\r\n    service_type: \"\",\r\n    custom_type: \"\",\r\n    description: \"\",\r\n    price: 0,\r\n    offers_intro_call: false,\r\n  });\r\n\r\n  const [errors, setErrors] = useState(initialErrorState);\r\n\r\n  useEffect(() => {\r\n    if (serviceData) {\r\n      setFormData({\r\n        service_type: serviceData.service_type,\r\n        description: serviceData.description,\r\n        price: serviceData.price,\r\n        custom_type: serviceData.custom_type || null,\r\n        offers_intro_call: serviceData.offers_intro_call,\r\n      });\r\n    }\r\n  }, [serviceData]);\r\n\r\n  const validateForm = () => {\r\n    const newErrors = { ...initialErrorState };\r\n    let isValid = true;\r\n\r\n    if (!formData.service_type) {\r\n      newErrors.service_type = \"Please select a service type.\";\r\n      isValid = false;\r\n    }\r\n\r\n    if (formData.service_type === \"Other\" && !formData.custom_type) {\r\n      newErrors.custom_type = \"Please enter a custom service type.\";\r\n      isValid = false;\r\n    }\r\n\r\n    if (!formData.description) {\r\n      newErrors.description = \"Description is required.\";\r\n      isValid = false;\r\n    }\r\n\r\n    if (\r\n      formData.service_type !== \"Free 15 Minutes Intro Call\" &&\r\n      typeof formData.price === \"number\" &&\r\n      formData.price <= 0\r\n    ) {\r\n      newErrors.price = \"Price must be greater than 0.\";\r\n      isValid = false;\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return isValid;\r\n  };\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<\r\n      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement\r\n    >\r\n  ) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Clear the specific error when the input becomes valid\r\n    if (errors[name as keyof typeof errors]) {\r\n      const newErrors = { ...errors, [name]: \"\" };\r\n      setErrors(newErrors);\r\n    }\r\n\r\n    // Handle switching between service_type and custom_type\r\n    if (name === \"service_type\") {\r\n      // If the user selects \"Free 15 Minutes Intro Call\", set price to \"Free\"\r\n      if (value === \"Free 15 Minutes Intro Call\") {\r\n        setFormData((prev) => ({\r\n          ...prev,\r\n          service_type: value,\r\n          price: \"Free\", // Set price to \"Free\"\r\n        }));\r\n      } else {\r\n        // If the user selects a predefined service type, clear the custom_type\r\n        setFormData((prev) => ({\r\n          ...prev,\r\n          service_type: value,\r\n          custom_type: value === \"Other\" ? prev.custom_type : null, // Clear custom_type if not \"Other\"\r\n          price: prev.price === \"Free\" ? 0 : prev.price, // Reset price if service_type changes\r\n        }));\r\n      }\r\n    } else if (name === \"custom_type\") {\r\n      // If the user enters a custom type\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        custom_type: value,\r\n        service_type: \"Other\", // Set service_type to \"Other\"\r\n      }));\r\n    } else {\r\n      // For all other fields, use the base handleChange logic\r\n      baseHandleChange(e);\r\n    }\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleUpdate = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!serviceData) return; // If serviceData is undefined, exit early\r\n    if (!validateForm()) return;\r\n\r\n    // Convert price to number before sending the request\r\n    const updatedData = {\r\n      ...formData,\r\n      price:\r\n        formData.service_type === \"Free 15 Minutes Intro Call\"\r\n          ? 0\r\n          : formData.price,\r\n    };\r\n\r\n    await updateService(serviceData.id, updatedData);\r\n    setIsPopupOpen(false);\r\n  };\r\n\r\n  //////////////////////////// JSX /////////////////////////////\r\n  return (\r\n    <Popup\r\n      isOpen={isPopupOpen}\r\n      onClose={() => setIsPopupOpen(false)}\r\n      title=\"Edit Service\"\r\n      height=\"80vh\"\r\n    >\r\n      <form>\r\n        <div className=\"mb-4\">\r\n          <Dropdown\r\n            label=\"Select Service\"\r\n            name=\"service_type\"\r\n            options={serviceItems} // Corrected to match the expected format\r\n            value={formData.service_type}\r\n            onChange={handleChange}\r\n            required={true}\r\n            selectStyle=\"w-full border p-3 bg-neutral1\"\r\n          />\r\n          {errors.service_type && (\r\n            <p className=\"text-red-500 text-sm mt-1\">{errors.service_type}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Conditional input for custom type */}\r\n        {formData.service_type === \"Other\" && (\r\n          <InputField\r\n            label=\"Update Service Type\"\r\n            required={true}\r\n            type=\"text\"\r\n            placeholder=\"Enter custom service type\"\r\n            name=\"custom_type\"\r\n            value={formData.custom_type || \"\"}\r\n            onChange={handleChange}\r\n          />\r\n        )}\r\n        {errors.custom_type && (\r\n          <p className=\"text-red-500 text-sm mt-1\">{errors.custom_type}</p>\r\n        )}\r\n\r\n        <div className=\"my-4\">\r\n          <Textarea\r\n            label=\"Description\"\r\n            name=\"description\"\r\n            value={formData.description}\r\n            onChange={handleChange}\r\n            placeholder=\"Enter service description\"\r\n            required={true}\r\n            rows={5}\r\n          />\r\n          {errors.description && (\r\n            <p className=\"text-red-500 text-sm mt-1\">{errors.description}</p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"mb-4\">\r\n          <InputField\r\n            label=\"Price per hour ($)\"\r\n            required={true}\r\n            type={\r\n              formData.service_type === \"Free 15 Minutes Intro Call\"\r\n                ? \"text\"\r\n                : \"number\"\r\n            } // Change input type to \"text\" if service_type is \"Free 15 Minutes Intro Call\"\r\n            placeholder={\r\n              formData.service_type === \"Free 15 Minutes Intro Call\"\r\n                ? \"Free\"\r\n                : \"Enter price\"\r\n            }\r\n            name=\"price\"\r\n            value={\r\n              formData.service_type === \"Free 15 Minutes Intro Call\"\r\n                ? \"Free\"\r\n                : formData.price\r\n            }\r\n            onChange={handleChange}\r\n            min=\"0\"\r\n            disabled={formData.service_type === \"Free 15 Minutes Intro Call\"} // Disable the input if service_type is \"Free 15 Minutes Intro Call\"\r\n          />\r\n          {errors.price && (\r\n            <p className=\"text-red-500 text-sm mt-1\">{errors.price}</p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex justify-end space-x-4 mt-6\">\r\n          <MainButton\r\n            type=\"button\"\r\n            variant=\"neutral\"\r\n            children=\"Discard\"\r\n            icon={faClose}\r\n            onClick={() => setIsPopupOpen(false)}\r\n          />\r\n\r\n          <MainButton\r\n            type=\"submit\"\r\n            variant=\"primary\"\r\n            children=\"Save\"\r\n            icon={faCheck}\r\n            onClick={handleUpdate}\r\n          />\r\n        </div>\r\n      </form>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAFA;;;;;;;;;;;;;AAIA,MAAM,oBAAoB;IACxB,cAAc;IACd,aAAa;IACb,aAAa;IACb,OAAO;AACT;AAGe,SAAS,iBAAiB,EACvC,WAAW,EACX,cAAc,EACd,WAAW,EACA;;IACX,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD;IAEpC,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,cAAc,gBAAgB,EAC/B,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD,EAMZ;QACD,cAAc;QACd,aAAa;QACb,aAAa;QACb,OAAO;QACP,mBAAmB;IACrB;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,aAAa;gBACf,YAAY;oBACV,cAAc,YAAY,YAAY;oBACtC,aAAa,YAAY,WAAW;oBACpC,OAAO,YAAY,KAAK;oBACxB,aAAa,YAAY,WAAW,IAAI;oBACxC,mBAAmB,YAAY,iBAAiB;gBAClD;YACF;QACF;qCAAG;QAAC;KAAY;IAEhB,MAAM,eAAe;QACnB,MAAM,YAAY;YAAE,GAAG,iBAAiB;QAAC;QACzC,IAAI,UAAU;QAEd,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,UAAU,YAAY,GAAG;YACzB,UAAU;QACZ;QAEA,IAAI,SAAS,YAAY,KAAK,WAAW,CAAC,SAAS,WAAW,EAAE;YAC9D,UAAU,WAAW,GAAG;YACxB,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,UAAU,WAAW,GAAG;YACxB,UAAU;QACZ;QAEA,IACE,SAAS,YAAY,KAAK,gCAC1B,OAAO,SAAS,KAAK,KAAK,YAC1B,SAAS,KAAK,IAAI,GAClB;YACA,UAAU,KAAK,GAAG;YAClB,UAAU;QACZ;QAEA,UAAU;QACV,OAAO;IACT;IAEA,MAAM,eAAe,CACnB;QAIA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,wDAAwD;QACxD,IAAI,MAAM,CAAC,KAA4B,EAAE;YACvC,MAAM,YAAY;gBAAE,GAAG,MAAM;gBAAE,CAAC,KAAK,EAAE;YAAG;YAC1C,UAAU;QACZ;QAEA,wDAAwD;QACxD,IAAI,SAAS,gBAAgB;YAC3B,wEAAwE;YACxE,IAAI,UAAU,8BAA8B;gBAC1C,YAAY,CAAC,OAAS,CAAC;wBACrB,GAAG,IAAI;wBACP,cAAc;wBACd,OAAO;oBACT,CAAC;YACH,OAAO;gBACL,uEAAuE;gBACvE,YAAY,CAAC,OAAS,CAAC;wBACrB,GAAG,IAAI;wBACP,cAAc;wBACd,aAAa,UAAU,UAAU,KAAK,WAAW,GAAG;wBACpD,OAAO,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK;oBAC/C,CAAC;YACH;QACF,OAAO,IAAI,SAAS,eAAe;YACjC,mCAAmC;YACnC,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,aAAa;oBACb,cAAc;gBAChB,CAAC;QACH,OAAO;YACL,wDAAwD;YACxD,iBAAiB;QACnB;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,aAAa,QAAQ,0CAA0C;QACpE,IAAI,CAAC,gBAAgB;QAErB,qDAAqD;QACrD,MAAM,cAAc;YAClB,GAAG,QAAQ;YACX,OACE,SAAS,YAAY,KAAK,+BACtB,IACA,SAAS,KAAK;QACtB;QAEA,MAAM,cAAc,YAAY,EAAE,EAAE;QACpC,eAAe;IACjB;IAEA,8DAA8D;IAC9D,qBACE,6LAAC,iJAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,eAAe;QAC9B,OAAM;QACN,QAAO;kBAEP,cAAA,6LAAC;;8BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oJAAA,CAAA,UAAQ;4BACP,OAAM;4BACN,MAAK;4BACL,SAAS,gIAAA,CAAA,eAAY;4BACrB,OAAO,SAAS,YAAY;4BAC5B,UAAU;4BACV,UAAU;4BACV,aAAY;;;;;;wBAEb,OAAO,YAAY,kBAClB,6LAAC;4BAAE,WAAU;sCAA6B,OAAO,YAAY;;;;;;;;;;;;gBAKhE,SAAS,YAAY,KAAK,yBACzB,6LAAC,sJAAA,CAAA,aAAU;oBACT,OAAM;oBACN,UAAU;oBACV,MAAK;oBACL,aAAY;oBACZ,MAAK;oBACL,OAAO,SAAS,WAAW,IAAI;oBAC/B,UAAU;;;;;;gBAGb,OAAO,WAAW,kBACjB,6LAAC;oBAAE,WAAU;8BAA6B,OAAO,WAAW;;;;;;8BAG9D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oJAAA,CAAA,WAAQ;4BACP,OAAM;4BACN,MAAK;4BACL,OAAO,SAAS,WAAW;4BAC3B,UAAU;4BACV,aAAY;4BACZ,UAAU;4BACV,MAAM;;;;;;wBAEP,OAAO,WAAW,kBACjB,6LAAC;4BAAE,WAAU;sCAA6B,OAAO,WAAW;;;;;;;;;;;;8BAIhE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,sJAAA,CAAA,aAAU;4BACT,OAAM;4BACN,UAAU;4BACV,MACE,SAAS,YAAY,KAAK,+BACtB,SACA;4BAEN,aACE,SAAS,YAAY,KAAK,+BACtB,SACA;4BAEN,MAAK;4BACL,OACE,SAAS,YAAY,KAAK,+BACtB,SACA,SAAS,KAAK;4BAEpB,UAAU;4BACV,KAAI;4BACJ,UAAU,SAAS,YAAY,KAAK;;;;;;wBAErC,OAAO,KAAK,kBACX,6LAAC;4BAAE,WAAU;sCAA6B,OAAO,KAAK;;;;;;;;;;;;8BAI1D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,SAAQ;4BACR,UAAS;4BACT,MAAM,2KAAA,CAAA,UAAO;4BACb,SAAS,IAAM,eAAe;;;;;;sCAGhC,6LAAC,mJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,SAAQ;4BACR,UAAS;4BACT,MAAM,2KAAA,CAAA,UAAO;4BACb,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GA/OwB;;QAKI,0IAAA,CAAA,cAAW;QAMjC,+HAAA,CAAA,UAAY;;;KAXM"}}, {"offset": {"line": 1420, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1426, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/view/popup/DeleteServicePopup.tsx"], "sourcesContent": ["import { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\nimport { faCheck, faClose } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\n\r\nexport default function DeleteServicePopup({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n  setSuccessPopup,\r\n  serviceData,\r\n  onSave,\r\n}: PopupProps) {\r\n  return (\r\n    <Popup\r\n      isOpen={isPopupOpen}\r\n      onClose={() => setIsPopupOpen(false)}\r\n      title=\"Remove Service\"\r\n      height=\"auto\"\r\n    >\r\n      <div className=\"mx-4 bg-neutral1 py-2 px-4 rounded-md mb-4\">\r\n        <h3 className=\"text-neutral8\">Do you want to remove this service</h3>\r\n        <p className=\"text-neutral5 text-sm\">\r\n          This service will be permanently removed from your profile.\r\n        </p>\r\n      </div>\r\n\r\n      <hr />\r\n\r\n      <div className=\"p-4\">\r\n        <h2 className=\"mb-3\">\r\n          {\" \"}\r\n          {serviceData?.custom_type || serviceData?.service_type}\r\n        </h2>\r\n        <p className=\"text-sm mb-3\"> {serviceData?.description}</p>\r\n        <p className=\"mb-1 text-neutral5\">Price</p>\r\n        <p className=\"text-3xl\">${serviceData?.price}</p>\r\n      </div>\r\n\r\n      <hr />\r\n\r\n      <div className=\"flex justify-end gap-3 p-4\">\r\n        <MainButton variant=\"neutral\" onClick={() => setIsPopupOpen(false)}>\r\n          Cancel <FontAwesomeIcon icon={faClose} />\r\n        </MainButton>\r\n\r\n        <MainButton variant=\"secondary\" onClick={onSave}>\r\n          Remove <FontAwesomeIcon icon={faCheck} />\r\n        </MainButton>\r\n      </div>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAGA;AADA;;;;;;AAGe,SAAS,mBAAmB,EACzC,WAAW,EACX,cAAc,EACd,eAAe,EACf,WAAW,EACX,MAAM,EACK;IACX,qBACE,6LAAC,iJAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,eAAe;QAC9B,OAAM;QACN,QAAO;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAC9B,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,6LAAC;;;;;0BAED,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BACX;4BACA,aAAa,eAAe,aAAa;;;;;;;kCAE5C,6LAAC;wBAAE,WAAU;;4BAAe;4BAAE,aAAa;;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBAAE,WAAU;;4BAAW;4BAAE,aAAa;;;;;;;;;;;;;0BAGzC,6LAAC;;;;;0BAED,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mJAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAU,SAAS,IAAM,eAAe;;4BAAQ;0CAC3D,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,UAAO;;;;;;;;;;;;kCAGvC,6LAAC,mJAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAY,SAAS;;4BAAQ;0CACxC,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;AAK/C;KA9CwB"}}, {"offset": {"line": 1589, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1595, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/counselor/dashboard/services/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { useServices } from \"@/app/hooks/counselor/useService\";\r\nimport { useProfile } from \"@/app/hooks/counselor/useProfile\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport StyledIcon from \"@/app/components/common/styledIcon\";\r\nimport ThreeDotIconDropdown from \"@/app/components/common/threeDotIconMenu\";\r\nimport { faBoxOpen } from \"@fortawesome/free-solid-svg-icons\";\r\nimport AddServicePopup from \"@/app/components/counselor/profile/view/popup/AddServicePopup\";\r\nimport EditServicePopup from \"@/app/components/counselor/profile/view/popup/EditServicePopup\";\r\nimport DeleteServicePopup from \"@/app/components/counselor/profile/view/popup/DeleteServicePopup\";\r\nimport { useState } from \"react\";\r\n\r\nexport default function ServicesPage() {\r\n  const {\r\n    services,\r\n    loading,\r\n    error,\r\n    fetchServices,\r\n    fetchSingleService,\r\n    deleteService,\r\n    selectedService,\r\n  } = useServices();\r\n  const { userInfo } = useProfile();\r\n\r\n  const [addServicePopup, setAddServicePopup] = useState(false);\r\n  const [editServicePopup, setEditServicePopup] = useState(false);\r\n  const [deleteServicePopup, setDeleteServicePopup] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (userInfo?.counselor_id) {\r\n      fetchServices(userInfo.counselor_id);\r\n    }\r\n  }, [userInfo?.counselor_id]);\r\n\r\n  ////////////// function handles update service items\r\n  const handleEditService = async (serviceId: number) => {\r\n    await fetchSingleService(serviceId);\r\n    setEditServicePopup(true);\r\n  };\r\n\r\n  ////////////// function handles delete service items\r\n  const handleDeleteService = async (serviceId: number) => {\r\n    await fetchSingleService(serviceId);\r\n    setDeleteServicePopup(true);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <AddServicePopup\r\n        isPopupOpen={addServicePopup}\r\n        setIsPopupOpen={setAddServicePopup}\r\n      />\r\n      <EditServicePopup\r\n        isPopupOpen={editServicePopup}\r\n        setIsPopupOpen={setEditServicePopup}\r\n        serviceData={selectedService}\r\n      />\r\n      <DeleteServicePopup\r\n        isPopupOpen={deleteServicePopup}\r\n        setIsPopupOpen={setDeleteServicePopup}\r\n        serviceData={selectedService}\r\n        onSave={async () => {\r\n          if (selectedService) {\r\n            await deleteService(selectedService?.id);\r\n            setDeleteServicePopup(false);\r\n            fetchServices(userInfo?.counselor_id);\r\n          }\r\n        }}\r\n      />\r\n\r\n      <div className=\"bg-white rounded-xl p-6\">\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n          <h1 className=\"text-2xl font-semibold\">Services</h1>\r\n          <MainButton\r\n            variant=\"neutral\"\r\n            onClick={() => setAddServicePopup(true)}\r\n          >\r\n            Add service\r\n          </MainButton>\r\n        </div>\r\n\r\n        {/* Loading State */}\r\n        {loading && (\r\n          <div className=\"flex justify-center items-center h-32\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Error State */}\r\n        {error && (\r\n          <div className=\"text-red-500 text-center py-4\">\r\n            {error}\r\n          </div>\r\n        )}\r\n\r\n        {/* Empty State */}\r\n        {!loading && !error && services.length === 0 && (\r\n          <div className=\"text-center py-8 text-neutral5\">\r\n            <p>No services found. Click \"Add service\" to create your first service.</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Services Cards */}\r\n        {!loading && !error && services.length > 0 && (\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n            {services.map((service) => (\r\n              <div\r\n                key={service.id}\r\n                className=\"border p-4 rounded-xl flex flex-col justify-between bg-white shadow-sm hover:shadow-md transition-shadow\"\r\n              >\r\n                <div>\r\n                  <div className=\"flex justify-between items-center mb-2\">\r\n                    <StyledIcon\r\n                      icon={faBoxOpen}\r\n                      bgColor=\"#F2C94C33\"\r\n                      color=\"#F2C94C\"\r\n                    />\r\n\r\n                    <ThreeDotIconDropdown\r\n                      handleEdit={() => handleEditService(service.id)}\r\n                      handleRemove={() => handleDeleteService(service.id)}\r\n                    />\r\n                  </div>\r\n                  <h3 className=\"text-neutral8 font-semibold mb-2\">\r\n                    {service.service_type}\r\n                  </h3>\r\n                  <p className=\"text-neutral5 text-sm mb-2\">\r\n                    {service.description}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm text-neutral5 mb-1\">Price</p>\r\n                  <p className=\"text-lg font-bold mb-4 text-neutral8\">\r\n                    ${service.price}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;;;AARA;;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EACL,aAAa,EACb,kBAAkB,EAClB,aAAa,EACb,eAAe,EAChB,GAAG,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD;IAE9B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU,cAAc;gBAC1B,cAAc,SAAS,YAAY;YACrC;QACF;iCAAG;QAAC,UAAU;KAAa;IAE3B,oDAAoD;IACpD,MAAM,oBAAoB,OAAO;QAC/B,MAAM,mBAAmB;QACzB,oBAAoB;IACtB;IAEA,oDAAoD;IACpD,MAAM,sBAAsB,OAAO;QACjC,MAAM,mBAAmB;QACzB,sBAAsB;IACxB;IAEA,qBACE,6LAAC;;0BACC,6LAAC,iLAAA,CAAA,UAAe;gBACd,aAAa;gBACb,gBAAgB;;;;;;0BAElB,6LAAC,kLAAA,CAAA,UAAgB;gBACf,aAAa;gBACb,gBAAgB;gBAChB,aAAa;;;;;;0BAEf,6LAAC,oLAAA,CAAA,UAAkB;gBACjB,aAAa;gBACb,gBAAgB;gBAChB,aAAa;gBACb,QAAQ;oBACN,IAAI,iBAAiB;wBACnB,MAAM,cAAc,iBAAiB;wBACrC,sBAAsB;wBACtB,cAAc,UAAU;oBAC1B;gBACF;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,6LAAC,mJAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,SAAS,IAAM,mBAAmB;0CACnC;;;;;;;;;;;;oBAMF,yBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;oBAKlB,uBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;oBAKJ,CAAC,WAAW,CAAC,SAAS,SAAS,MAAM,KAAK,mBACzC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAE;;;;;;;;;;;oBAKN,CAAC,WAAW,CAAC,SAAS,SAAS,MAAM,GAAG,mBACvC,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,sJAAA,CAAA,UAAU;wDACT,MAAM,2KAAA,CAAA,YAAS;wDACf,SAAQ;wDACR,OAAM;;;;;;kEAGR,6LAAC,4JAAA,CAAA,UAAoB;wDACnB,YAAY,IAAM,kBAAkB,QAAQ,EAAE;wDAC9C,cAAc,IAAM,oBAAoB,QAAQ,EAAE;;;;;;;;;;;;0DAGtD,6LAAC;gDAAG,WAAU;0DACX,QAAQ,YAAY;;;;;;0DAEvB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;;;;;;kDAGxB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,6LAAC;gDAAE,WAAU;;oDAAuC;oDAChD,QAAQ,KAAK;;;;;;;;;;;;;;+BA1Bd,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAoC/B;GApIwB;;QASlB,0IAAA,CAAA,cAAW;QACM,0IAAA,CAAA,aAAU;;;KAVT"}}, {"offset": {"line": 1867, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
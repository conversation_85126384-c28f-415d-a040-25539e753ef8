"use client";

import { <PERSON>Left, MessageCircle } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/app/components/ui/card";
import HeadWrapper from "@/app/components/student/head-wrapper";
import { ChatInterface } from "@/app/components/student/ai-chat/chat-interface";

export default function AIChatPage() {
  return (
    <>
      <HeadWrapper title="AI Counsellor - Chat" />
      <div className="p-2 sm:p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Chat Interface */}
          <ChatInterface />
        </div>
      </div>
    </>
  );
}

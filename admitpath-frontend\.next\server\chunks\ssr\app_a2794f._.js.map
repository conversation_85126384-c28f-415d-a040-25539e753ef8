{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/counselor/useAvailability.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport {\r\n    AvailabilityCreate,\r\n    AvailabilityResponse,\r\n} from \"@/app/types/counselor/availability\";\r\nimport { toast } from \"react-toastify\";\r\n\r\ninterface AvailabilityState {\r\n    loading: boolean;\r\n    status: \"Loading\" | \"Confirm Availability\" | \"Saving\" | \"Deleting\";\r\n    error: string | null;\r\n    availabilities: AvailabilityResponse[];\r\n    timeSlots: string[]; // Changed to string array\r\n    availableDates: string[];\r\n    fetchAvailabilities: (timezone: string) => Promise<void>;\r\n    createAvailability: (\r\n        data: AvailabilityCreate,\r\n        timezone: string\r\n    ) => Promise<void>;\r\n    updateAvailability: (id: number, data: AvailabilityCreate) => Promise<void>;\r\n    deleteAvailability: (id: number) => Promise<void>;\r\n    clearError: () => void;\r\n}\r\n\r\nexport const useAvailability = create<AvailabilityState>((set) => ({\r\n    loading: false,\r\n    status: \"Loading\",\r\n    error: null,\r\n    availabilities: [],\r\n    timeSlots: [],\r\n    availableDates: [],\r\n\r\n    fetchAvailabilities: async (timezone) => {\r\n        set({ loading: true, status: \"Loading\" });\r\n        try {\r\n            const response = await apiClient.get<AvailabilityResponse[]>(\r\n                \"/counselor/availability\",\r\n                { params: { timezone } }\r\n            );\r\n            console.log(response.data);\r\n            set({\r\n                availabilities: response.data,\r\n                loading: false,\r\n                status: \"Confirm Availability\",\r\n            });\r\n        } catch (error) {\r\n            set({\r\n                error: \"Failed to fetch availability\",\r\n                loading: false,\r\n                status: \"Confirm Availability\",\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    createAvailability: async (data, timezone) => {\r\n        set({ loading: true, status: \"Saving\" });\r\n        try {\r\n            console.log(\"Saving availability data:\", JSON.stringify(data));\r\n            console.log(\"Using timezone:\", timezone);\r\n            \r\n            const response = await apiClient.post(\"/counselor/availability\", data, {\r\n                params: { timezone },\r\n            });\r\n            \r\n            console.log(\"Save response:\", response.data);\r\n            set({ loading: false, status: \"Confirm Availability\" });\r\n        } catch (error: any) {\r\n            console.error(\"Save error:\", error.response?.data);\r\n            const message =\r\n                error.response?.data?.detail?.[0]?.msg || \"Creation failed\";\r\n            toast.error(message);\r\n            set({\r\n                error: message,\r\n                loading: false,\r\n                status: \"Confirm Availability\",\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    updateAvailability: async (id, data) => {\r\n        set({ loading: true, status: \"Saving\" });\r\n        try {\r\n            console.log(`Updating availability ID ${id} with data:`, JSON.stringify(data));\r\n            console.log(\"Using timezone:\", data.timezone);\r\n            \r\n            const response = await apiClient.put(`/counselor/availability/${id}`, data, {\r\n                params: { timezone: data.timezone },\r\n            });\r\n            \r\n            console.log(\"Update response:\", response.data);\r\n            set({ loading: false, status: \"Confirm Availability\" });\r\n        } catch (error: any) {\r\n            console.error(\"Update error:\", error.response?.data);\r\n            const message = error.response?.data?.detail || \"Update failed\";\r\n            toast.error(message);\r\n            set({\r\n                error: \"Update failed\",\r\n                loading: false,\r\n                status: \"Confirm Availability\",\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    deleteAvailability: async (id) => {\r\n        set({ loading: true, status: \"Deleting\" });\r\n        try {\r\n            await apiClient.delete(`/counselor/availability/${id}`);\r\n            set({ loading: false, status: \"Confirm Availability\" });\r\n        } catch (error) {\r\n            set({\r\n                error: \"Deletion failed\",\r\n                loading: false,\r\n                status: \"Confirm Availability\",\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AACA;AAKA;AANA;;;;AAyBO,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAqB,CAAC,MAAQ,CAAC;QAC/D,SAAS;QACT,QAAQ;QACR,OAAO;QACP,gBAAgB,EAAE;QAClB,WAAW,EAAE;QACb,gBAAgB,EAAE;QAElB,qBAAqB,OAAO;YACxB,IAAI;gBAAE,SAAS;gBAAM,QAAQ;YAAU;YACvC,IAAI;gBACA,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,2BACA;oBAAE,QAAQ;wBAAE;oBAAS;gBAAE;gBAE3B,QAAQ,GAAG,CAAC,SAAS,IAAI;gBACzB,IAAI;oBACA,gBAAgB,SAAS,IAAI;oBAC7B,SAAS;oBACT,QAAQ;gBACZ;YACJ,EAAE,OAAO,OAAO;gBACZ,IAAI;oBACA,OAAO;oBACP,SAAS;oBACT,QAAQ;gBACZ;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO,MAAM;YAC7B,IAAI;gBAAE,SAAS;gBAAM,QAAQ;YAAS;YACtC,IAAI;gBACA,QAAQ,GAAG,CAAC,6BAA6B,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC,mBAAmB;gBAE/B,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,2BAA2B,MAAM;oBACnE,QAAQ;wBAAE;oBAAS;gBACvB;gBAEA,QAAQ,GAAG,CAAC,kBAAkB,SAAS,IAAI;gBAC3C,IAAI;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;YACzD,EAAE,OAAO,OAAY;gBACjB,QAAQ,KAAK,CAAC,eAAe,MAAM,QAAQ,EAAE;gBAC7C,MAAM,UACF,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAC9C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI;oBACA,OAAO;oBACP,SAAS;oBACT,QAAQ;gBACZ;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO,IAAI;YAC3B,IAAI;gBAAE,SAAS;gBAAM,QAAQ;YAAS;YACtC,IAAI;gBACA,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,GAAG,WAAW,CAAC,EAAE,KAAK,SAAS,CAAC;gBACxE,QAAQ,GAAG,CAAC,mBAAmB,KAAK,QAAQ;gBAE5C,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,IAAI,EAAE,MAAM;oBACxE,QAAQ;wBAAE,UAAU,KAAK,QAAQ;oBAAC;gBACtC;gBAEA,QAAQ,GAAG,CAAC,oBAAoB,SAAS,IAAI;gBAC7C,IAAI;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;YACzD,EAAE,OAAO,OAAY;gBACjB,QAAQ,KAAK,CAAC,iBAAiB,MAAM,QAAQ,EAAE;gBAC/C,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAChD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI;oBACA,OAAO;oBACP,SAAS;oBACT,QAAQ;gBACZ;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO;YACvB,IAAI;gBAAE,SAAS;gBAAM,QAAQ;YAAW;YACxC,IAAI;gBACA,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,wBAAwB,EAAE,IAAI;gBACtD,IAAI;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;YACzD,EAAE,OAAO,OAAO;gBACZ,IAAI;oBACA,OAAO;oBACP,SAAS;oBACT,QAAQ;gBACZ;gBACA,MAAM;YACV;QACJ;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACxC,CAAC"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,eAAe,6KAAsB,IAAI;AAE/C,MAAM,sBAAsB,6KAAsB,OAAO;AAEzD,MAAM,oBAAoB,6KAAsB,KAAK;AAErD,MAAM,qBAAqB,6KAAsB,MAAM;AAEvD,MAAM,kBAAkB,6KAAsB,GAAG;AAEjD,MAAM,yBAAyB,6KAAsB,UAAU;AAE/D,MAAM,uCAAyB,sMAAM,UAAU,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,6KAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,6KAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,6KAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,6KAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,sMAAM,UAAU,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,6KAAsB,MAAM;kBAC3B,cAAA,8OAAC,6KAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wGACA,oVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,6KAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,sMAAM,UAAU,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,6KAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6KAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,sMAAM,UAAU,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,6KAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,6KAAsB,aAAa;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,6KAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,sMAAM,UAAU,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,6KAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,6KAAsB,aAAa;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,6KAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,sMAAM,UAAU,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,6KAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,6KAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,sMAAM,UAAU,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,6KAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,6KAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/availability/week-navigation.tsx"], "sourcesContent": ["import { ChevronLeft, ChevronRight, MoreVertical } from \"lucide-react\";\r\nimport {\r\n    DropdownMenu,\r\n    DropdownMenuContent,\r\n    DropdownMenuItem,\r\n    DropdownMenuTrigger,\r\n} from \"@components/ui/dropdown-menu\";\r\n\r\ninterface WeekNavigationProps {\r\n    weekRangeText: string;\r\n    onPreviousWeek: () => void;\r\n    onNextWeek: () => void;\r\n    onTodayClick: () => void;\r\n    onDeleteWeek: () => void;\r\n    hasAvailability: boolean;\r\n}\r\n\r\nexport function WeekNavigation({\r\n    weekRangeText,\r\n    onPreviousWeek,\r\n    onNextWeek,\r\n    onTodayClick,\r\n    onDeleteWeek,\r\n    hasAvailability,\r\n}: WeekNavigationProps) {\r\n    return (\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n                <button\r\n                    onClick={onPreviousWeek}\r\n                    className=\"p-1 rounded hover:bg-gray-200\"\r\n                    aria-label=\"Previous week\"\r\n                >\r\n                    <ChevronLeft className=\"h-5 w-5\" />\r\n                </button>\r\n                <span className=\"font-medium text-sm md:text-base\">\r\n                    {weekRangeText}\r\n                </span>\r\n                <button\r\n                    onClick={onNextWeek}\r\n                    className=\"p-1 rounded hover:bg-gray-200\"\r\n                    aria-label=\"Next week\"\r\n                >\r\n                    <ChevronRight className=\"h-5 w-5\" />\r\n                </button>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n                <button\r\n                    onClick={onTodayClick}\r\n                    className=\"text-sm text-blue-600 hover:underline\"\r\n                >\r\n                    Today\r\n                </button>\r\n\r\n                <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                        <button\r\n                            className=\"p-1 rounded hover:bg-gray-200\"\r\n                            aria-label=\"More options\"\r\n                        >\r\n                            <MoreVertical className=\"h-4 w-4\" />\r\n                        </button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent align=\"end\" className=\"bg-white\">\r\n                        <DropdownMenuItem\r\n                            onClick={onDeleteWeek}\r\n                            className={\r\n                                !hasAvailability\r\n                                    ? \"text-gray-400 cursor-not-allowed\"\r\n                                    : \"text-red-600 cursor-pointer\"\r\n                            }\r\n                            disabled={!hasAvailability}\r\n                        >\r\n                            Delete this week\r\n                        </DropdownMenuItem>\r\n                    </DropdownMenuContent>\r\n                </DropdownMenu>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AADA;AAAA;AAAA;;;;AAiBO,SAAS,eAAe,EAC3B,aAAa,EACb,cAAc,EACd,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,eAAe,EACG;IAClB,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBACG,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAE3B,8OAAC;wBAAK,WAAU;kCACX;;;;;;kCAEL,8OAAC;wBACG,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGhC,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBACG,SAAS;wBACT,WAAU;kCACb;;;;;;kCAID,8OAAC,4IAAA,CAAA,eAAY;;0CACT,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CACxB,cAAA,8OAAC;oCACG,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGhC,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAM,WAAU;0CACvC,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;oCACb,SAAS;oCACT,WACI,CAAC,kBACK,qCACA;oCAEV,UAAU,CAAC;8CACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB"}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/availability/constants.tsx"], "sourcesContent": ["// Time slots to display in the grid\r\nexport const TIME_SLOTS = [\r\n    \"00:00\",\r\n    \"01:00\",\r\n    \"02:00\",\r\n    \"03:00\",\r\n    \"04:00\",\r\n    \"05:00\",\r\n    \"06:00\",\r\n    \"07:00\",\r\n    \"08:00\",\r\n    \"09:00\",\r\n    \"10:00\",\r\n    \"11:00\",\r\n    \"12:00\",\r\n    \"13:00\",\r\n    \"14:00\",\r\n    \"15:00\",\r\n    \"16:00\",\r\n    \"17:00\",\r\n    \"18:00\",\r\n    \"19:00\",\r\n    \"20:00\",\r\n    \"21:00\",\r\n    \"22:00\",\r\n    \"23:00\",\r\n  ]\r\n  \r\n  // Timezone options - expanded list of common timezones\r\n  export const TIMEZONE_OPTIONS = [\r\n    // North America\r\n    { value: \"America/New_York\", label: \"(GMT-05:00) Eastern Time - New York\" },\r\n    { value: \"America/Chicago\", label: \"(GMT-06:00) Central Time - Chicago\" },\r\n    { value: \"America/Denver\", label: \"(GMT-07:00) Mountain Time - Denver\" },\r\n    { value: \"America/Los_Angeles\", label: \"(GMT-08:00) Pacific Time - Los Angeles\" },\r\n    { value: \"America/Anchorage\", label: \"(GMT-09:00) Alaska Time\" },\r\n    { value: \"America/Toronto\", label: \"(GMT-05:00) Eastern Time - Toronto\" },\r\n    { value: \"America/Vancouver\", label: \"(GMT-08:00) Pacific Time - Vancouver\" },\r\n    { value: \"America/Mexico_City\", label: \"(GMT-06:00) Mexico City\" },\r\n    \r\n    // Europe\r\n    { value: \"Europe/London\", label: \"(GMT+00:00) London\" },\r\n    { value: \"Europe/Paris\", label: \"(GMT+01:00) Paris\" },\r\n    { value: \"Europe/Berlin\", label: \"(GMT+01:00) Berlin\" },\r\n    { value: \"Europe/Madrid\", label: \"(GMT+01:00) Madrid\" },\r\n    { value: \"Europe/Rome\", label: \"(GMT+01:00) Rome\" },\r\n    { value: \"Europe/Zurich\", label: \"(GMT+01:00) Zurich\" },\r\n    { value: \"Europe/Moscow\", label: \"(GMT+03:00) Moscow\" },\r\n    \r\n    // Asia\r\n    { value: \"Asia/Singapore\", label: \"(GMT+08:00) Singapore\" },\r\n    { value: \"Asia/Tokyo\", label: \"(GMT+09:00) Tokyo\" },\r\n    { value: \"Asia/Shanghai\", label: \"(GMT+08:00) Shanghai\" },\r\n    { value: \"Asia/Hong_Kong\", label: \"(GMT+08:00) Hong Kong\" },\r\n    { value: \"Asia/Dubai\", label: \"(GMT+04:00) Dubai\" },\r\n    { value: \"Asia/Seoul\", label: \"(GMT+09:00) Seoul\" },\r\n    { value: \"Asia/Kolkata\", label: \"(GMT+05:30) Mumbai/New Delhi\" },\r\n    \r\n    // Australia/Pacific\r\n    { value: \"Australia/Sydney\", label: \"(GMT+10:00) Sydney\" },\r\n    { value: \"Australia/Melbourne\", label: \"(GMT+10:00) Melbourne\" },\r\n    { value: \"Australia/Perth\", label: \"(GMT+08:00) Perth\" },\r\n    { value: \"Pacific/Auckland\", label: \"(GMT+12:00) Auckland\" },\r\n    \r\n    // Other\r\n    { value: \"UTC\", label: \"(GMT+00:00) UTC\" },\r\n  ]\r\n  \r\n  "], "names": [], "mappings": "AAAA,oCAAoC;;;;;AAC7B,MAAM,aAAa;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,mBAAmB;IAC9B,gBAAgB;IAChB;QAAE,OAAO;QAAoB,OAAO;IAAsC;IAC1E;QAAE,OAAO;QAAmB,OAAO;IAAqC;IACxE;QAAE,OAAO;QAAkB,OAAO;IAAqC;IACvE;QAAE,OAAO;QAAuB,OAAO;IAAyC;IAChF;QAAE,OAAO;QAAqB,OAAO;IAA0B;IAC/D;QAAE,OAAO;QAAmB,OAAO;IAAqC;IACxE;QAAE,OAAO;QAAqB,OAAO;IAAuC;IAC5E;QAAE,OAAO;QAAuB,OAAO;IAA0B;IAEjE,SAAS;IACT;QAAE,OAAO;QAAiB,OAAO;IAAqB;IACtD;QAAE,OAAO;QAAgB,OAAO;IAAoB;IACpD;QAAE,OAAO;QAAiB,OAAO;IAAqB;IACtD;QAAE,OAAO;QAAiB,OAAO;IAAqB;IACtD;QAAE,OAAO;QAAe,OAAO;IAAmB;IAClD;QAAE,OAAO;QAAiB,OAAO;IAAqB;IACtD;QAAE,OAAO;QAAiB,OAAO;IAAqB;IAEtD,OAAO;IACP;QAAE,OAAO;QAAkB,OAAO;IAAwB;IAC1D;QAAE,OAAO;QAAc,OAAO;IAAoB;IAClD;QAAE,OAAO;QAAiB,OAAO;IAAuB;IACxD;QAAE,OAAO;QAAkB,OAAO;IAAwB;IAC1D;QAAE,OAAO;QAAc,OAAO;IAAoB;IAClD;QAAE,OAAO;QAAc,OAAO;IAAoB;IAClD;QAAE,OAAO;QAAgB,OAAO;IAA+B;IAE/D,oBAAoB;IACpB;QAAE,OAAO;QAAoB,OAAO;IAAqB;IACzD;QAAE,OAAO;QAAuB,OAAO;IAAwB;IAC/D;QAAE,OAAO;QAAmB,OAAO;IAAoB;IACvD;QAAE,OAAO;QAAoB,OAAO;IAAuB;IAE3D,QAAQ;IACR;QAAE,OAAO;QAAO,OAAO;IAAkB;CAC1C"}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/availability/time-grid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type React from \"react\";\r\n\r\nimport { format, addDays } from \"date-fns\";\r\nimport { useRef, useState } from \"react\";\r\nimport { TIME_SLOTS } from \"./constants\";\r\n\r\n// CSS to prevent text selection\r\nconst noSelectStyle: React.CSSProperties = {\r\n  WebkitUserSelect: \"none\",\r\n  MozUserSelect: \"none\",\r\n  msUserSelect: \"none\",\r\n  userSelect: \"none\",\r\n} as React.CSSProperties;\r\n\r\ninterface TimeGridProps {\r\n  currentWeekStart: Date;\r\n  selectedSlots: Record<string, string[]>;\r\n  toggleTimeSlot: (dayIndex: number, timeSlot: string) => void;\r\n}\r\n\r\nexport function TimeGrid({\r\n  currentWeekStart,\r\n  selectedSlots,\r\n  toggleTimeSlot,\r\n}: TimeGridProps) {\r\n  // Generate days of the week based on current week start\r\n  const weekDays = Array.from({ length: 7 }, (_, i) =>\r\n    addDays(currentWeekStart, i)\r\n  );\r\n\r\n  // Drag selection state\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [isSelecting, setIsSelecting] = useState(true);\r\n  const dragStartRef = useRef<{ dayIndex: number; timeSlot: string } | null>(\r\n    null\r\n  );\r\n\r\n  const now = new Date();\r\n\r\n  // Check if a day is today\r\n  const isToday = (date: Date) => {\r\n    const today = new Date();\r\n    return (\r\n      date.getDate() === today.getDate() &&\r\n      date.getMonth() === today.getMonth() &&\r\n      date.getFullYear() === today.getFullYear()\r\n    );\r\n  };\r\n\r\n  // Check if date is in the past (before today)\r\n  const isPastDay = (date: Date) => {\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    const compareDate = new Date(date);\r\n    compareDate.setHours(0, 0, 0, 0);\r\n\r\n    return compareDate < today;\r\n  };\r\n\r\n  // Check if a specific time slot is in the past\r\n  const isPastTimeSlot = (date: Date, timeSlot: string) => {\r\n    if (isPastDay(date)) {\r\n      return true;\r\n    }\r\n\r\n    if (isToday(date)) {\r\n      const now = new Date();\r\n      const [hours, minutes] = timeSlot.split(\":\").map(Number);\r\n\r\n      // Check if this time is in the past\r\n      return (\r\n        now.getHours() > hours ||\r\n        (now.getHours() === hours && now.getMinutes() > minutes)\r\n      );\r\n    }\r\n\r\n    return false;\r\n  };\r\n\r\n  const isSlotSelected = (dayIndex: number, timeSlot: string) => {\r\n    const dayKey = dayIndex.toString();\r\n    return selectedSlots[dayKey]?.includes(timeSlot) || false;\r\n  };\r\n\r\n  // Handle drag start\r\n  const handleDragStart = (\r\n    dayIndex: number,\r\n    timeSlot: string,\r\n    isDisabled: boolean,\r\n    e: React.MouseEvent | React.TouchEvent\r\n  ) => {\r\n    // Prevent default to avoid text selection\r\n    e.preventDefault();\r\n\r\n    if (isDisabled) return;\r\n\r\n    setIsDragging(true);\r\n    dragStartRef.current = { dayIndex, timeSlot };\r\n\r\n    // Determine if we're selecting or deselecting based on the initial slot\r\n    const initiallySelected = isSlotSelected(dayIndex, timeSlot);\r\n    setIsSelecting(!initiallySelected);\r\n\r\n    // Toggle the initial slot\r\n    toggleTimeSlot(dayIndex, timeSlot);\r\n  };\r\n\r\n  // Handle drag over\r\n  const handleDragOver = (\r\n    dayIndex: number,\r\n    timeSlot: string,\r\n    isDisabled: boolean,\r\n    e: React.MouseEvent\r\n  ) => {\r\n    // Prevent default to avoid text selection\r\n    e.preventDefault();\r\n\r\n    if (!isDragging || isDisabled) return;\r\n\r\n    // Only toggle if the current selection state doesn't match our drag operation\r\n    const isSelected = isSlotSelected(dayIndex, timeSlot);\r\n    if (isSelecting !== isSelected) {\r\n      toggleTimeSlot(dayIndex, timeSlot);\r\n    }\r\n  };\r\n\r\n  // Handle drag end\r\n  const handleDragEnd = () => {\r\n    setIsDragging(false);\r\n    dragStartRef.current = null;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"min-w-max select-none\"\r\n      onMouseUp={(e) => {\r\n        e.preventDefault();\r\n        handleDragEnd();\r\n      }}\r\n      onMouseLeave={(e) => {\r\n        e.preventDefault();\r\n        handleDragEnd();\r\n      }}\r\n      onTouchEnd={(e) => {\r\n        e.preventDefault();\r\n        handleDragEnd();\r\n      }}\r\n      onTouchCancel={(e) => {\r\n        e.preventDefault();\r\n        handleDragEnd();\r\n      }}\r\n    >\r\n      {/* Day headers */}\r\n      <div className=\"grid grid-cols-[80px_repeat(7,1fr)] border-b\">\r\n        <div className=\"p-2\"></div>\r\n        {weekDays.map((day, index) => (\r\n          <div\r\n            key={index}\r\n            className={`p-2 text-center font-medium ${\r\n              isToday(day) ? \"text-blue-600\" : \"\"\r\n            }`}\r\n          >\r\n            <div className=\"uppercase text-xs\">{format(day, \"EEE\")}</div>\r\n            <div\r\n              className={`text-sm ${\r\n                isToday(day)\r\n                  ? \"bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mx-auto\"\r\n                  : \"\"\r\n              }`}\r\n            >\r\n              {format(day, \"dd\")}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Time slots grid */}\r\n      <div className=\"max-h-[300px] md:max-h-[400px] overflow-y-auto\">\r\n        {TIME_SLOTS.map((time) => (\r\n          <div\r\n            key={time}\r\n            className=\"grid grid-cols-[80px_repeat(7,1fr)] border-b\"\r\n          >\r\n            <div className=\"p-2 text-right pr-3 text-gray-500 text-sm border-r\">\r\n              {time}\r\n            </div>\r\n            {weekDays.map((day, dayIndex) => {\r\n              const isDisabled = isPastTimeSlot(day, time);\r\n              const isSelected = isSlotSelected(dayIndex, time);\r\n\r\n              return (\r\n                <div\r\n                  key={dayIndex}\r\n                  onMouseDown={(e) =>\r\n                    handleDragStart(dayIndex, time, isDisabled, e)\r\n                  }\r\n                  onMouseOver={(e) =>\r\n                    handleDragOver(dayIndex, time, isDisabled, e)\r\n                  }\r\n                  onTouchStart={(e) => {\r\n                    e.preventDefault();\r\n                    handleDragStart(dayIndex, time, isDisabled, e);\r\n                  }}\r\n                  onTouchMove={(e) => {\r\n                    e.preventDefault();\r\n\r\n                    // Get the element under the touch point\r\n                    const touch = e.touches[0];\r\n                    const element = document.elementFromPoint(\r\n                      touch.clientX,\r\n                      touch.clientY\r\n                    );\r\n\r\n                    // If we have a data attribute for day and time, use it\r\n                    if (\r\n                      element?.getAttribute(\"data-day-index\") &&\r\n                      element?.getAttribute(\"data-time\")\r\n                    ) {\r\n                      const touchDayIndex = Number.parseInt(\r\n                        element.getAttribute(\"data-day-index\") || \"0\"\r\n                      );\r\n                      const touchTime = element.getAttribute(\"data-time\") || \"\";\r\n                      const touchDisabled =\r\n                        element.getAttribute(\"data-disabled\") === \"true\";\r\n\r\n                      handleDragOver(\r\n                        touchDayIndex,\r\n                        touchTime,\r\n                        touchDisabled,\r\n                        e as any\r\n                      );\r\n                    }\r\n                  }}\r\n                  className={`border-r p-2 transition-colors ${\r\n                    isDisabled\r\n                      ? \"bg-gray-100 text-gray-300 cursor-not-allowed\"\r\n                      : isSelected\r\n                      ? \"bg-emerald-500 hover:bg-emerald-600 cursor-pointer\"\r\n                      : \"bg-white hover:bg-emerald-300 cursor-pointer\"\r\n                  }`}\r\n                  aria-label={`Time slot ${time} on ${format(day, \"EEEE\")}`}\r\n                  role=\"checkbox\"\r\n                  aria-checked={isSelected}\r\n                  aria-disabled={isDisabled}\r\n                  data-day-index={dayIndex}\r\n                  data-time={time}\r\n                  data-disabled={isDisabled}\r\n                >\r\n                  &nbsp;\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      <div className=\"mt-4 flex items-center justify-end\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"w-4 h-4 bg-emerald-500 border border-gray-200\"></div>\r\n          <span className=\"text-sm text-gray-600\">Available</span>\r\n        </div>\r\n        <div className=\"flex items-center space-x-2 ml-4\">\r\n          <div className=\"w-4 h-4 bg-white border border-gray-200\"></div>\r\n          <span className=\"text-sm text-gray-600\">Unavailable</span>\r\n        </div>\r\n        <div className=\"flex items-center space-x-2 ml-4\">\r\n          <div className=\"w-4 h-4 bg-gray-100 border border-gray-200\"></div>\r\n          <span className=\"text-sm text-gray-600\">Past</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"mt-2 text-xs text-gray-500\">\r\n        Tip: Click and drag to select multiple time slots at once\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAKA;AACA;AAFA;AAAA;AAJA;;;;;AAQA,gCAAgC;AAChC,MAAM,gBAAqC;IACzC,kBAAkB;IAClB,eAAe;IACf,cAAc;IACd,YAAY;AACd;AAQO,SAAS,SAAS,EACvB,gBAAgB,EAChB,aAAa,EACb,cAAc,EACA;IACd,wDAAwD;IACxD,MAAM,WAAW,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAC7C,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IAG5B,uBAAuB;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EACxB;IAGF,MAAM,MAAM,IAAI;IAEhB,0BAA0B;IAC1B,MAAM,UAAU,CAAC;QACf,MAAM,QAAQ,IAAI;QAClB,OACE,KAAK,OAAO,OAAO,MAAM,OAAO,MAChC,KAAK,QAAQ,OAAO,MAAM,QAAQ,MAClC,KAAK,WAAW,OAAO,MAAM,WAAW;IAE5C;IAEA,8CAA8C;IAC9C,MAAM,YAAY,CAAC;QACjB,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;QAExB,MAAM,cAAc,IAAI,KAAK;QAC7B,YAAY,QAAQ,CAAC,GAAG,GAAG,GAAG;QAE9B,OAAO,cAAc;IACvB;IAEA,+CAA+C;IAC/C,MAAM,iBAAiB,CAAC,MAAY;QAClC,IAAI,UAAU,OAAO;YACnB,OAAO;QACT;QAEA,IAAI,QAAQ,OAAO;YACjB,MAAM,MAAM,IAAI;YAChB,MAAM,CAAC,OAAO,QAAQ,GAAG,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC;YAEjD,oCAAoC;YACpC,OACE,IAAI,QAAQ,KAAK,SAChB,IAAI,QAAQ,OAAO,SAAS,IAAI,UAAU,KAAK;QAEpD;QAEA,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC,UAAkB;QACxC,MAAM,SAAS,SAAS,QAAQ;QAChC,OAAO,aAAa,CAAC,OAAO,EAAE,SAAS,aAAa;IACtD;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CACtB,UACA,UACA,YACA;QAEA,0CAA0C;QAC1C,EAAE,cAAc;QAEhB,IAAI,YAAY;QAEhB,cAAc;QACd,aAAa,OAAO,GAAG;YAAE;YAAU;QAAS;QAE5C,wEAAwE;QACxE,MAAM,oBAAoB,eAAe,UAAU;QACnD,eAAe,CAAC;QAEhB,0BAA0B;QAC1B,eAAe,UAAU;IAC3B;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CACrB,UACA,UACA,YACA;QAEA,0CAA0C;QAC1C,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc,YAAY;QAE/B,8EAA8E;QAC9E,MAAM,aAAa,eAAe,UAAU;QAC5C,IAAI,gBAAgB,YAAY;YAC9B,eAAe,UAAU;QAC3B;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB;QACpB,cAAc;QACd,aAAa,OAAO,GAAG;IACzB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,WAAW,CAAC;YACV,EAAE,cAAc;YAChB;QACF;QACA,cAAc,CAAC;YACb,EAAE,cAAc;YAChB;QACF;QACA,YAAY,CAAC;YACX,EAAE,cAAc;YAChB;QACF;QACA,eAAe,CAAC;YACd,EAAE,cAAc;YAChB;QACF;;0BAGA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;oBACd,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,8OAAC;4BAEC,WAAW,CAAC,4BAA4B,EACtC,QAAQ,OAAO,kBAAkB,IACjC;;8CAEF,8OAAC;oCAAI,WAAU;8CAAqB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;;;;;;8CAChD,8OAAC;oCACC,WAAW,CAAC,QAAQ,EAClB,QAAQ,OACJ,yFACA,IACJ;8CAED,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;;;;;;;2BAbV;;;;;;;;;;;0BAoBX,8OAAC;gBAAI,WAAU;0BACZ,4JAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,qBACf,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CACZ;;;;;;4BAEF,SAAS,GAAG,CAAC,CAAC,KAAK;gCAClB,MAAM,aAAa,eAAe,KAAK;gCACvC,MAAM,aAAa,eAAe,UAAU;gCAE5C,qBACE,8OAAC;oCAEC,aAAa,CAAC,IACZ,gBAAgB,UAAU,MAAM,YAAY;oCAE9C,aAAa,CAAC,IACZ,eAAe,UAAU,MAAM,YAAY;oCAE7C,cAAc,CAAC;wCACb,EAAE,cAAc;wCAChB,gBAAgB,UAAU,MAAM,YAAY;oCAC9C;oCACA,aAAa,CAAC;wCACZ,EAAE,cAAc;wCAEhB,wCAAwC;wCACxC,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;wCAC1B,MAAM,UAAU,SAAS,gBAAgB,CACvC,MAAM,OAAO,EACb,MAAM,OAAO;wCAGf,uDAAuD;wCACvD,IACE,SAAS,aAAa,qBACtB,SAAS,aAAa,cACtB;4CACA,MAAM,gBAAgB,OAAO,QAAQ,CACnC,QAAQ,YAAY,CAAC,qBAAqB;4CAE5C,MAAM,YAAY,QAAQ,YAAY,CAAC,gBAAgB;4CACvD,MAAM,gBACJ,QAAQ,YAAY,CAAC,qBAAqB;4CAE5C,eACE,eACA,WACA,eACA;wCAEJ;oCACF;oCACA,WAAW,CAAC,+BAA+B,EACzC,aACI,iDACA,aACA,uDACA,gDACJ;oCACF,cAAY,CAAC,UAAU,EAAE,KAAK,IAAI,EAAE,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,SAAS;oCACzD,MAAK;oCACL,gBAAc;oCACd,iBAAe;oCACf,kBAAgB;oCAChB,aAAW;oCACX,iBAAe;8CAChB;mCAvDM;;;;;4BA2DX;;uBAvEK;;;;;;;;;;0BA4EX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAE1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAE1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAI5C,8OAAC;gBAAI,WAAU;0BAA6B;;;;;;;;;;;;AAKlD"}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\";\r\nimport { Check } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n));\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName;\r\n\r\nexport { Checkbox };\r\n"], "names": [], "mappings": ";;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,yBAAW,sMAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,qKAAkB,IAAI;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,sQACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,qKAAkB,SAAS;YAC1B,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,qKAAkB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1009, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,mKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,sMAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,mKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,mKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,mKAAgB,MAAM;kBACrB,cAAA,8OAAC,mKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,mKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mKAAgB,aAAa;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,8OAAC,mKAAgB,QAAQ;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,mKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 1255, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@components/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Portal>\r\n    <TooltipPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </TooltipPrimitive.Portal>\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,kBAAkB,oKAAiB,QAAQ;AAEjD,MAAM,UAAU,oKAAiB,IAAI;AAErC,MAAM,iBAAiB,oKAAiB,OAAO;AAE/C,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,oKAAiB,MAAM;kBACtB,cAAA,8OAAC,oKAAiB,OAAO;YACvB,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,qXACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,oKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/availability/availability-controls.tsx"], "sourcesContent": ["import { Info } from \"lucide-react\";\r\nimport { Checkbox } from \"@components/ui/checkbox\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport {\r\n    Select,\r\n    SelectContent,\r\n    SelectItem,\r\n    SelectTrigger,\r\n    SelectValue,\r\n} from \"@components/ui/select\";\r\nimport {\r\n    Tooltip,\r\n    TooltipContent,\r\n    TooltipProvider,\r\n    TooltipTrigger,\r\n} from \"@components/ui/tooltip\";\r\n\r\ninterface AvailabilityControlsProps {\r\n    timezone: string;\r\n    setTimezone: (timezone: string) => void;\r\n    applyToFollowingWeeks: boolean;\r\n    setApplyToFollowingWeeks: (apply: boolean) => void;\r\n    onSave: () => Promise<void>;\r\n    loading: boolean;\r\n    status: string;\r\n    hasSelectedSlots: boolean;\r\n    timezoneOptions: { id?: string; value: string; label: string }[];\r\n}\r\n\r\nexport function AvailabilityControls({\r\n    timezone,\r\n    setTimezone,\r\n    applyToFollowingWeeks,\r\n    setApplyToFollowingWeeks,\r\n    onSave,\r\n    loading,\r\n    status,\r\n    hasSelectedSlots,\r\n    timezoneOptions,\r\n}: AvailabilityControlsProps) {\r\n    const currentTimezoneLabel =\r\n        timezoneOptions.find((tz) => tz.value === timezone)?.label || timezone;\r\n\r\n    return (\r\n        <>\r\n            <div className=\"flex flex-col md:flex-row items-start md:items-center justify-between gap-4\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                    <Checkbox\r\n                        id=\"apply-following-weeks\"\r\n                        checked={applyToFollowingWeeks}\r\n                        onCheckedChange={(checked) =>\r\n                            setApplyToFollowingWeeks(checked === true)\r\n                        }\r\n                    />\r\n                    <label\r\n                        htmlFor=\"apply-following-weeks\"\r\n                        className=\"text-sm text-gray-600 cursor-pointer flex items-center\"\r\n                    >\r\n                        Apply changes to following 12 weeks\r\n                        <TooltipProvider>\r\n                            <Tooltip>\r\n                                <TooltipTrigger asChild>\r\n                                    <Info className=\"h-4 w-4 ml-1 text-gray-400\" />\r\n                                </TooltipTrigger>\r\n                                <TooltipContent>\r\n                                    <p>\r\n                                        Sets the same availability pattern for the\r\n                                        next 12 weeks\r\n                                    </p>\r\n                                </TooltipContent>\r\n                            </Tooltip>\r\n                        </TooltipProvider>\r\n                    </label>\r\n                </div>\r\n\r\n                <div className=\"w-full md:w-auto\">\r\n                    <div className=\"flex items-center space-x-2 text-sm text-gray-600 mb-2 md:hidden\">\r\n                        <span className=\"flex items-center\">\r\n                            Current timezone: {currentTimezoneLabel}\r\n                            <Info className=\"h-4 w-4 ml-1 text-gray-400\" />\r\n                        </span>\r\n                    </div>\r\n                    <Select value={timezone} onValueChange={setTimezone}>\r\n                        <SelectTrigger className=\"w-full md:w-[240px]\">\r\n                            <SelectValue placeholder=\"Select timezone\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent className=\"bg-white\">\r\n                            {timezoneOptions.map((tz) => (\r\n                                <SelectItem\r\n                                    key={tz.id || `tz-${tz.value}`}\r\n                                    value={tz.value}\r\n                                    className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                                >\r\n                                    {tz.label}\r\n                                </SelectItem>\r\n                            ))}\r\n                        </SelectContent>\r\n                    </Select>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"flex justify-center mt-4\">\r\n                <Button\r\n                    onClick={onSave}\r\n                    className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-2 rounded-full w-full md:w-auto\"\r\n                    disabled={loading || !hasSelectedSlots}\r\n                >\r\n                    {status}\r\n                </Button>\r\n            </div>\r\n        </>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAOA;AAVA;;;;;;;AA6BO,SAAS,qBAAqB,EACjC,QAAQ,EACR,WAAW,EACX,qBAAqB,EACrB,wBAAwB,EACxB,MAAM,EACN,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,eAAe,EACS;IACxB,MAAM,uBACF,gBAAgB,IAAI,CAAC,CAAC,KAAO,GAAG,KAAK,KAAK,WAAW,SAAS;IAElE,qBACI;;0BACI,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,oIAAA,CAAA,WAAQ;gCACL,IAAG;gCACH,SAAS;gCACT,iBAAiB,CAAC,UACd,yBAAyB,YAAY;;;;;;0CAG7C,8OAAC;gCACG,SAAQ;gCACR,WAAU;;oCACb;kDAEG,8OAAC,mIAAA,CAAA,kBAAe;kDACZ,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACJ,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACnB,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC,mIAAA,CAAA,iBAAc;8DACX,cAAA,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAK,WAAU;;wCAAoB;wCACb;sDACnB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAU,eAAe;;kDACpC,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACrB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE7B,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACpB,gBAAgB,GAAG,CAAC,CAAC,mBAClB,8OAAC,kIAAA,CAAA,aAAU;gDAEP,OAAO,GAAG,KAAK;gDACf,WAAU;0DAET,GAAG,KAAK;+CAJJ,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYtD,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACH,SAAS;oBACT,WAAU;oBACV,UAAU,WAAW,CAAC;8BAErB;;;;;;;;;;;;;AAKrB"}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/availability/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { format, startOfWeek, addWeeks, subWeeks } from \"date-fns\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useAvailability } from \"@/app/hooks/counselor/useAvailability\";\r\nimport type { AvailabilityCreate } from \"@/app/types/counselor/availability\";\r\nimport { WeekNavigation } from \"./week-navigation\";\r\nimport { TimeGrid } from \"./time-grid\";\r\nimport { AvailabilityControls } from \"./availability-controls\";\r\nimport { TIMEZONE_OPTIONS } from \"./constants\";\r\n\r\nexport default function AvailabilityManager() {\r\n    const {\r\n        fetchAvailabilities,\r\n        createAvailability,\r\n        updateAvailability,\r\n        deleteAvailability,\r\n        availabilities,\r\n        loading,\r\n        status,\r\n    } = useAvailability();\r\n\r\n    // Get user's local timezone\r\n    const [timezone, setTimezone] = useState(() => {\r\n        return Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n    });\r\n\r\n    const [currentWeekStart, setCurrentWeekStart] = useState(() => {\r\n        const today = new Date();\r\n        return startOfWeek(today, { weekStartsOn: 1 }); // Start week on Monday\r\n    });\r\n\r\n    // Store unsaved selections for each week to persist them when navigating\r\n    const [unsavedSelections, setUnsavedSelections] = useState<\r\n        Record<string, Record<string, string[]>>\r\n    >({});\r\n\r\n    const [selectedSlots, setSelectedSlots] = useState<\r\n        Record<string, string[]>\r\n    >({});\r\n    const [applyToFollowingWeeks, setApplyToFollowingWeeks] = useState(false);\r\n    const [currentAvailability, setCurrentAvailability] = useState<\r\n        number | null\r\n    >(null);\r\n    const [isEditing, setIsEditing] = useState(false);\r\n    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\r\n\r\n    // Format the week range for display\r\n    const weekRangeText = `${format(currentWeekStart, \"MMM dd\")} — ${format(\r\n        addWeeks(currentWeekStart, 0).setDate(currentWeekStart.getDate() + 6),\r\n        \"MMM dd, yyyy\"\r\n    )}`;\r\n\r\n    useEffect(() => {\r\n        const loadAvailabilities = async () => {\r\n            await fetchAvailabilities(timezone);\r\n        };\r\n\r\n        loadAvailabilities();\r\n    }, [timezone, fetchAvailabilities]);\r\n\r\n    useEffect(() => {\r\n        const currentWeekKey = format(currentWeekStart, \"yyyy-MM-dd\");\r\n\r\n        // Check for unsaved changes for this week\r\n        const hasUnsavedForThisWeek = unsavedSelections[currentWeekKey];\r\n\r\n        // Find if there's an existing availability for the current week\r\n        const existingAvailability = availabilities.find(\r\n            (avail) => avail.week_start === currentWeekKey\r\n        );\r\n\r\n        if (hasUnsavedForThisWeek) {\r\n            // Prioritize unsaved changes if they exist\r\n            setSelectedSlots(hasUnsavedForThisWeek);\r\n            setCurrentAvailability(existingAvailability?.id || null);\r\n            setIsEditing(!!existingAvailability);\r\n            setHasUnsavedChanges(true);\r\n        } else if (existingAvailability) {\r\n            // Otherwise, use saved availability\r\n            setSelectedSlots(existingAvailability.available_slots);\r\n            setCurrentAvailability(existingAvailability.id);\r\n            setIsEditing(true);\r\n            setHasUnsavedChanges(false);\r\n        } else {\r\n            // No existing data for this week\r\n            setSelectedSlots({});\r\n            setCurrentAvailability(null);\r\n            setIsEditing(false);\r\n            setHasUnsavedChanges(false);\r\n        }\r\n    }, [availabilities, currentWeekStart, unsavedSelections]);\r\n\r\n    const saveCurrentSelections = () => {\r\n        // Store current selections before navigating\r\n        const currentWeekKey = format(currentWeekStart, \"yyyy-MM-dd\");\r\n        if (hasUnsavedChanges) {\r\n            setUnsavedSelections((prev) => ({\r\n                ...prev,\r\n                [currentWeekKey]: { ...selectedSlots },\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handlePreviousWeek = () => {\r\n        saveCurrentSelections();\r\n        const newWeekStart = subWeeks(currentWeekStart, 1);\r\n        setCurrentWeekStart(newWeekStart);\r\n    };\r\n\r\n    const handleNextWeek = () => {\r\n        saveCurrentSelections();\r\n        const newWeekStart = addWeeks(currentWeekStart, 1);\r\n        setCurrentWeekStart(newWeekStart);\r\n    };\r\n\r\n    const handleTodayClick = () => {\r\n        saveCurrentSelections();\r\n        const today = new Date();\r\n        setCurrentWeekStart(startOfWeek(today, { weekStartsOn: 1 }));\r\n    };\r\n\r\n    const toggleTimeSlot = (dayIndex: number, timeSlot: string) => {\r\n        const dayKey = dayIndex.toString();\r\n\r\n        setSelectedSlots((prev) => {\r\n            const newSlots = { ...prev };\r\n\r\n            if (!newSlots[dayKey]) {\r\n                newSlots[dayKey] = [timeSlot];\r\n            } else if (newSlots[dayKey].includes(timeSlot)) {\r\n                newSlots[dayKey] = newSlots[dayKey].filter(\r\n                    (slot) => slot !== timeSlot\r\n                );\r\n                if (newSlots[dayKey].length === 0) {\r\n                    delete newSlots[dayKey];\r\n                }\r\n            } else {\r\n                newSlots[dayKey] = [...newSlots[dayKey], timeSlot].sort();\r\n            }\r\n\r\n            return newSlots;\r\n        });\r\n    };\r\n\r\n    const handleSaveAvailability = async () => {\r\n        // Log selected slots for debugging\r\n        console.log(\"Selected slots to save:\", JSON.stringify(selectedSlots));\r\n\r\n        // Check if there are any time slots beyond 8:00 (looking for any double-digit hour slots)\r\n        const hasLateSlots = Object.values(selectedSlots).some((daySlots) =>\r\n            daySlots.some((slot) => {\r\n                const hour = parseInt(slot.split(\":\")[0], 10);\r\n                return hour >= 10; // Hours 10 and above\r\n            })\r\n        );\r\n\r\n        console.log(\"Has slots with hours >= 10:\", hasLateSlots);\r\n\r\n        const newAvailability: AvailabilityCreate = {\r\n            week_start: format(currentWeekStart, \"yyyy-MM-dd\"),\r\n            available_slots: selectedSlots,\r\n            timezone,\r\n        };\r\n\r\n        try {\r\n            // If editing, update the existing availability\r\n            if (isEditing && currentAvailability) {\r\n                console.log(\r\n                    `Updating existing availability ID ${currentAvailability}`\r\n                );\r\n                await updateAvailability(currentAvailability, newAvailability);\r\n            } else {\r\n                console.log(\"Creating new availability\");\r\n                await createAvailability(newAvailability, timezone);\r\n            }\r\n\r\n            // If applying to following weeks, create additional availabilities\r\n            if (applyToFollowingWeeks) {\r\n                // Create for the next 12 weeks\r\n                const numOfFollowingWeeks = 12;\r\n                for (let i = 1; i <= numOfFollowingWeeks; i++) {\r\n                    const nextWeekStart = addWeeks(currentWeekStart, i);\r\n                    const nextWeekAvailability: AvailabilityCreate = {\r\n                        week_start: format(nextWeekStart, \"yyyy-MM-dd\"),\r\n                        available_slots: selectedSlots,\r\n                        timezone,\r\n                    };\r\n\r\n                    try {\r\n                        // Attempt to create or update availability for each week\r\n                        const existingAvailability = availabilities.find(\r\n                            (avail) =>\r\n                                avail.week_start ===\r\n                                format(nextWeekStart, \"yyyy-MM-dd\")\r\n                        );\r\n\r\n                        if (existingAvailability) {\r\n                            await updateAvailability(\r\n                                existingAvailability.id,\r\n                                nextWeekAvailability\r\n                            );\r\n                        } else {\r\n                            await createAvailability(\r\n                                nextWeekAvailability,\r\n                                timezone\r\n                            );\r\n                        }\r\n                    } catch (error) {\r\n                        console.error(\r\n                            `Failed to set availability for week ${i}:`,\r\n                            error\r\n                        );\r\n                        // Continue with other weeks even if one fails\r\n                    }\r\n                }\r\n\r\n                setApplyToFollowingWeeks(false);\r\n                toast.success(\r\n                    `Availability pattern applied to the next ${numOfFollowingWeeks} weeks`\r\n                );\r\n            }\r\n\r\n            toast.success(\"Availability saved successfully\");\r\n\r\n            // Clear unsaved changes for this week\r\n            const currentWeekKey = format(currentWeekStart, \"yyyy-MM-dd\");\r\n            setUnsavedSelections((prev) => {\r\n                const newSelections = { ...prev };\r\n                delete newSelections[currentWeekKey];\r\n                return newSelections;\r\n            });\r\n            setHasUnsavedChanges(false);\r\n\r\n            // Explicitly fetch availabilities again to ensure UI is updated\r\n            console.log(\"Refreshing availabilities from server...\");\r\n            await fetchAvailabilities(timezone);\r\n\r\n            // Short delay to make sure state updates\r\n            setTimeout(() => {\r\n                console.log(\"Refreshed availabilities:\", availabilities);\r\n            }, 500);\r\n        } catch (error) {\r\n            console.error(\"Save error:\", error);\r\n            toast.error(\"Failed to save availability\");\r\n        }\r\n    };\r\n\r\n    const handleDeleteWeek = async () => {\r\n        if (!isEditing || currentAvailability === null) {\r\n            toast.error(\"No availability to delete for this week\");\r\n            return;\r\n        }\r\n\r\n        try {\r\n            await deleteAvailability(currentAvailability);\r\n            toast.success(\"Availability for this week has been deleted\");\r\n            setSelectedSlots({});\r\n            setCurrentAvailability(null);\r\n            setIsEditing(false);\r\n            await fetchAvailabilities(timezone);\r\n        } catch (error) {\r\n            toast.error(\"Failed to delete availability\");\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-lg max-w-4xl mx-auto\">\r\n            <div className=\"p-4 md:p-6 space-y-4\">\r\n                <div className=\"flex justify-between items-center\">\r\n                    <h2 className=\"text-xl md:text-2xl font-semibold text-gray-800\">\r\n                        Set Your Session Availability\r\n                    </h2>\r\n                    <button className=\"text-gray-400 hover:text-gray-600\">\r\n                        <span className=\"sr-only\">Close</span>✕\r\n                    </button>\r\n                </div>\r\n\r\n                {/* <p className=\"text-gray-600 text-center text-sm md:text-base\">\r\n                    Please ensure that your meeting availability is as accurate\r\n                    as possible\r\n                    <br className=\"hidden md:block\" />\r\n                    for faster bookings.\r\n                </p> */}\r\n\r\n                <div className=\"bg-gray-50 rounded-lg p-2 md:p-4\">\r\n                    <WeekNavigation\r\n                        weekRangeText={weekRangeText}\r\n                        onPreviousWeek={handlePreviousWeek}\r\n                        onNextWeek={handleNextWeek}\r\n                        onTodayClick={handleTodayClick}\r\n                        onDeleteWeek={handleDeleteWeek}\r\n                        hasAvailability={isEditing}\r\n                    />\r\n\r\n                    <div className=\"overflow-x-auto\">\r\n                        <TimeGrid\r\n                            currentWeekStart={currentWeekStart}\r\n                            selectedSlots={selectedSlots}\r\n                            toggleTimeSlot={toggleTimeSlot}\r\n                        />\r\n                    </div>\r\n                </div>\r\n\r\n                <AvailabilityControls\r\n                    timezone={timezone}\r\n                    setTimezone={setTimezone}\r\n                    applyToFollowingWeeks={applyToFollowingWeeks}\r\n                    setApplyToFollowingWeeks={setApplyToFollowingWeeks}\r\n                    onSave={handleSaveAvailability}\r\n                    loading={loading}\r\n                    status={status}\r\n                    hasSelectedSlots={Object.keys(selectedSlots).length > 0}\r\n                    timezoneOptions={[\r\n                        // Add current timezone with a unique ID prefix\r\n                        {\r\n                            id: \"user-timezone\", // Unique ID that won't conflict with other entries\r\n                            value: Intl.DateTimeFormat().resolvedOptions()\r\n                                .timeZone,\r\n                            label: `Your Timezone (${\r\n                                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n                            })`,\r\n                        },\r\n                        // Add each timezone option with a unique ID\r\n                        ...TIMEZONE_OPTIONS.map((tz, index) => ({\r\n                            id: `timezone-${index}`,\r\n                            ...tz,\r\n                        })),\r\n                    ]}\r\n                />\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAHA;;;;;;;;;;AAYe,SAAS;IACpB,MAAM,EACF,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,OAAO,EACP,MAAM,EACT,GAAG,CAAA,GAAA,4IAAA,CAAA,kBAAe,AAAD;IAElB,4BAA4B;IAC5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;IAC3D;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,MAAM,QAAQ,IAAI;QAClB,OAAO,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YAAE,cAAc;QAAE,IAAI,uBAAuB;IAC3E;IAEA,yEAAyE;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEvD,CAAC;IAEH,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE/C,CAAC;IACH,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE3D;IACF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,oCAAoC;IACpC,MAAM,gBAAgB,GAAG,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,kBAAkB,UAAU,GAAG,EAAE,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAClE,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,OAAO,KAAK,IACnE,iBACD;IAEH,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,qBAAqB;YACvB,MAAM,oBAAoB;QAC9B;QAEA;IACJ,GAAG;QAAC;QAAU;KAAoB;IAElC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,iBAAiB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,kBAAkB;QAEhD,0CAA0C;QAC1C,MAAM,wBAAwB,iBAAiB,CAAC,eAAe;QAE/D,gEAAgE;QAChE,MAAM,uBAAuB,eAAe,IAAI,CAC5C,CAAC,QAAU,MAAM,UAAU,KAAK;QAGpC,IAAI,uBAAuB;YACvB,2CAA2C;YAC3C,iBAAiB;YACjB,uBAAuB,sBAAsB,MAAM;YACnD,aAAa,CAAC,CAAC;YACf,qBAAqB;QACzB,OAAO,IAAI,sBAAsB;YAC7B,oCAAoC;YACpC,iBAAiB,qBAAqB,eAAe;YACrD,uBAAuB,qBAAqB,EAAE;YAC9C,aAAa;YACb,qBAAqB;QACzB,OAAO;YACH,iCAAiC;YACjC,iBAAiB,CAAC;YAClB,uBAAuB;YACvB,aAAa;YACb,qBAAqB;QACzB;IACJ,GAAG;QAAC;QAAgB;QAAkB;KAAkB;IAExD,MAAM,wBAAwB;QAC1B,6CAA6C;QAC7C,MAAM,iBAAiB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,kBAAkB;QAChD,IAAI,mBAAmB;YACnB,qBAAqB,CAAC,OAAS,CAAC;oBAC5B,GAAG,IAAI;oBACP,CAAC,eAAe,EAAE;wBAAE,GAAG,aAAa;oBAAC;gBACzC,CAAC;QACL;IACJ;IAEA,MAAM,qBAAqB;QACvB;QACA,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB;QAChD,oBAAoB;IACxB;IAEA,MAAM,iBAAiB;QACnB;QACA,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB;QAChD,oBAAoB;IACxB;IAEA,MAAM,mBAAmB;QACrB;QACA,MAAM,QAAQ,IAAI;QAClB,oBAAoB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YAAE,cAAc;QAAE;IAC7D;IAEA,MAAM,iBAAiB,CAAC,UAAkB;QACtC,MAAM,SAAS,SAAS,QAAQ;QAEhC,iBAAiB,CAAC;YACd,MAAM,WAAW;gBAAE,GAAG,IAAI;YAAC;YAE3B,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACnB,QAAQ,CAAC,OAAO,GAAG;oBAAC;iBAAS;YACjC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW;gBAC5C,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CACtC,CAAC,OAAS,SAAS;gBAEvB,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;oBAC/B,OAAO,QAAQ,CAAC,OAAO;gBAC3B;YACJ,OAAO;gBACH,QAAQ,CAAC,OAAO,GAAG;uBAAI,QAAQ,CAAC,OAAO;oBAAE;iBAAS,CAAC,IAAI;YAC3D;YAEA,OAAO;QACX;IACJ;IAEA,MAAM,yBAAyB;QAC3B,mCAAmC;QACnC,QAAQ,GAAG,CAAC,2BAA2B,KAAK,SAAS,CAAC;QAEtD,0FAA0F;QAC1F,MAAM,eAAe,OAAO,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC,WACpD,SAAS,IAAI,CAAC,CAAC;gBACX,MAAM,OAAO,SAAS,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;gBAC1C,OAAO,QAAQ,IAAI,qBAAqB;YAC5C;QAGJ,QAAQ,GAAG,CAAC,+BAA+B;QAE3C,MAAM,kBAAsC;YACxC,YAAY,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,kBAAkB;YACrC,iBAAiB;YACjB;QACJ;QAEA,IAAI;YACA,+CAA+C;YAC/C,IAAI,aAAa,qBAAqB;gBAClC,QAAQ,GAAG,CACP,CAAC,kCAAkC,EAAE,qBAAqB;gBAE9D,MAAM,mBAAmB,qBAAqB;YAClD,OAAO;gBACH,QAAQ,GAAG,CAAC;gBACZ,MAAM,mBAAmB,iBAAiB;YAC9C;YAEA,mEAAmE;YACnE,IAAI,uBAAuB;gBACvB,+BAA+B;gBAC/B,MAAM,sBAAsB;gBAC5B,IAAK,IAAI,IAAI,GAAG,KAAK,qBAAqB,IAAK;oBAC3C,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB;oBACjD,MAAM,uBAA2C;wBAC7C,YAAY,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;wBAClC,iBAAiB;wBACjB;oBACJ;oBAEA,IAAI;wBACA,yDAAyD;wBACzD,MAAM,uBAAuB,eAAe,IAAI,CAC5C,CAAC,QACG,MAAM,UAAU,KAChB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;wBAG9B,IAAI,sBAAsB;4BACtB,MAAM,mBACF,qBAAqB,EAAE,EACvB;wBAER,OAAO;4BACH,MAAM,mBACF,sBACA;wBAER;oBACJ,EAAE,OAAO,OAAO;wBACZ,QAAQ,KAAK,CACT,CAAC,oCAAoC,EAAE,EAAE,CAAC,CAAC,EAC3C;oBAEJ,8CAA8C;oBAClD;gBACJ;gBAEA,yBAAyB;gBACzB,mJAAA,CAAA,QAAK,CAAC,OAAO,CACT,CAAC,yCAAyC,EAAE,oBAAoB,MAAM,CAAC;YAE/E;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,sCAAsC;YACtC,MAAM,iBAAiB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,kBAAkB;YAChD,qBAAqB,CAAC;gBAClB,MAAM,gBAAgB;oBAAE,GAAG,IAAI;gBAAC;gBAChC,OAAO,aAAa,CAAC,eAAe;gBACpC,OAAO;YACX;YACA,qBAAqB;YAErB,gEAAgE;YAChE,QAAQ,GAAG,CAAC;YACZ,MAAM,oBAAoB;YAE1B,yCAAyC;YACzC,WAAW;gBACP,QAAQ,GAAG,CAAC,6BAA6B;YAC7C,GAAG;QACP,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,eAAe;YAC7B,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,MAAM,mBAAmB;QACrB,IAAI,CAAC,aAAa,wBAAwB,MAAM;YAC5C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACJ;QAEA,IAAI;YACA,MAAM,mBAAmB;YACzB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,iBAAiB,CAAC;YAClB,uBAAuB;YACvB,aAAa;YACb,MAAM,oBAAoB;QAC9B,EAAE,OAAO,OAAO;YACZ,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QAChB;IACJ;IAEA,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAO,WAAU;;8CACd,8OAAC;oCAAK,WAAU;8CAAU;;;;;;gCAAY;;;;;;;;;;;;;8BAW9C,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,qKAAA,CAAA,iBAAc;4BACX,eAAe;4BACf,gBAAgB;4BAChB,YAAY;4BACZ,cAAc;4BACd,cAAc;4BACd,iBAAiB;;;;;;sCAGrB,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,+JAAA,CAAA,WAAQ;gCACL,kBAAkB;gCAClB,eAAe;gCACf,gBAAgB;;;;;;;;;;;;;;;;;8BAK5B,8OAAC,2KAAA,CAAA,uBAAoB;oBACjB,UAAU;oBACV,aAAa;oBACb,uBAAuB;oBACvB,0BAA0B;oBAC1B,QAAQ;oBACR,SAAS;oBACT,QAAQ;oBACR,kBAAkB,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG;oBACtD,iBAAiB;wBACb,+CAA+C;wBAC/C;4BACI,IAAI;4BACJ,OAAO,KAAK,cAAc,GAAG,eAAe,GACvC,QAAQ;4BACb,OAAO,CAAC,eAAe,EACnB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,CACnD,CAAC,CAAC;wBACP;wBACA,4CAA4C;2BACzC,4JAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,QAAU,CAAC;gCACpC,IAAI,CAAC,SAAS,EAAE,OAAO;gCACvB,GAAG,EAAE;4BACT,CAAC;qBACJ;;;;;;;;;;;;;;;;;AAKrB"}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/compiled/react-dom/react-dom.react-server.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-dom.react-server.production.js');\n} else {\n  module.exports = require('./cjs/react-dom.react-server.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/compiled/react-dom/server.edge.js"], "sourcesContent": ["'use strict';\n\nvar b;\nvar l;\nif (process.env.NODE_ENV === 'production') {\n  b = require('./cjs/react-dom-server.edge.production.js');\n  l = require('./cjs/react-dom-server-legacy.browser.production.js');\n} else {\n  b = require('./cjs/react-dom-server.edge.development.js');\n  l = require('./cjs/react-dom-server-legacy.browser.development.js');\n}\n\nexports.version = b.version;\nexports.renderToReadableStream = b.renderToReadableStream;\nexports.renderToString = l.renderToString;\nexports.renderToStaticMarkup = l.renderToStaticMarkup;\nif (b.resume) {\n  exports.resume = b.resume;\n}\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,uCAA2C;;AAG3C,OAAO;IACL;IACA;AACF;AAEA,QAAQ,OAAO,GAAG,EAAE,OAAO;AAC3B,QAAQ,sBAAsB,GAAG,EAAE,sBAAsB;AACzD,QAAQ,cAAc,GAAG,EAAE,cAAc;AACzC,QAAQ,oBAAoB,GAAG,EAAE,oBAAoB;AACrD,IAAI,EAAE,MAAM,EAAE;IACZ,QAAQ,MAAM,GAAG,EAAE,MAAM;AAC3B", "ignoreList": [0]}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/compiled/react-dom/static.edge.js"], "sourcesContent": ["'use strict';\n\nvar s;\nif (process.env.NODE_ENV === 'production') {\n  s = require('./cjs/react-dom-server.edge.production.js');\n} else {\n  s = require('./cjs/react-dom-server.edge.development.js');\n}\n\nexports.version = s.version;\nexports.prerender = s.prerender;\nexports.resumeAndPrerender = s.resumeAndPrerender;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,uCAA2C;;AAE3C,OAAO;IACL;AACF;AAEA,QAAQ,OAAO,GAAG,EAAE,OAAO;AAC3B,QAAQ,SAAS,GAAG,EAAE,SAAS;AAC/B,QAAQ,kBAAkB,GAAG,EAAE,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/compiled/react-dom/index.js"], "sourcesContent": ["'use strict';\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' ||\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function'\n  ) {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (process.env.NODE_ENV === 'production') {\n  // DCE check should happen before ReactDOM bundle executes so that\n  // DevTools can report bad minification during injection.\n  checkDCE();\n  module.exports = require('./cjs/react-dom.production.js');\n} else {\n  module.exports = require('./cjs/react-dom.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,SAAS;IACP,yCAAyC,GACzC,IACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,QAAQ,KAAK,YACnD;QACA;IACF;IACA,wCAA2C;QACzC,kEAAkE;QAClE,gEAAgE;QAChE,sEAAsE;QACtE,oBAAoB;QACpB,wEAAwE;QACxE,0EAA0E;QAC1E,oBAAoB;QACpB,MAAM,IAAI,MAAM;IAClB;IACA,IAAI;QACF,oEAAoE;QACpE,+BAA+B,QAAQ,CAAC;IAC1C,EAAE,OAAO,KAAK;QACZ,kDAAkD;QAClD,qDAAqD;QACrD,QAAQ,KAAK,CAAC;IAChB;AACF;AAEA,uCAA2C;;AAK3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0]}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/compiled/react-dom/server.edge.js"], "sourcesContent": ["'use strict';\n\nvar b;\nvar l;\nif (process.env.NODE_ENV === 'production') {\n  b = require('./cjs/react-dom-server.edge.production.js');\n  l = require('./cjs/react-dom-server-legacy.browser.production.js');\n} else {\n  b = require('./cjs/react-dom-server.edge.development.js');\n  l = require('./cjs/react-dom-server-legacy.browser.development.js');\n}\n\nexports.version = b.version;\nexports.renderToReadableStream = b.renderToReadableStream;\nexports.renderToString = l.renderToString;\nexports.renderToStaticMarkup = l.renderToStaticMarkup;\nif (b.resume) {\n  exports.resume = b.resume;\n}\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,uCAA2C;;AAG3C,OAAO;IACL;IACA;AACF;AAEA,QAAQ,OAAO,GAAG,EAAE,OAAO;AAC3B,QAAQ,sBAAsB,GAAG,EAAE,sBAAsB;AACzD,QAAQ,cAAc,GAAG,EAAE,cAAc;AACzC,QAAQ,oBAAoB,GAAG,EAAE,oBAAoB;AACrD,IAAI,EAAE,MAAM,EAAE;IACZ,QAAQ,MAAM,GAAG,EAAE,MAAM;AAC3B", "ignoreList": [0]}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/compiled/react-dom/static.edge.js"], "sourcesContent": ["'use strict';\n\nvar s;\nif (process.env.NODE_ENV === 'production') {\n  s = require('./cjs/react-dom-server.edge.production.js');\n} else {\n  s = require('./cjs/react-dom-server.edge.development.js');\n}\n\nexports.version = s.version;\nexports.prerender = s.prerender;\nexports.resumeAndPrerender = s.resumeAndPrerender;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,uCAA2C;;AAE3C,OAAO;IACL;AACF;AAEA,QAAQ,OAAO,GAAG,EAAE,OAAO;AAC3B,QAAQ,SAAS,GAAG,EAAE,SAAS;AAC/B,QAAQ,kBAAkB,GAAG,EAAE,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
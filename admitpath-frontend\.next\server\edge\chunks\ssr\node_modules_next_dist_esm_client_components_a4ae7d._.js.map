{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n"], "names": ["RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_IS_PRERENDER_HEADER"], "mappings": ";;;;;;;;;;;;;;;AAAO,MAAMA,aAAa,MAAc;AACjC,MAAMC,gBAAgB,cAAsB;AAI5C,MAAMC,gCAAgC,yBAAiC;AACvE,MAAMC,8BAA8B,uBAA+B;AAKnE,MAAMC,sCACX,+BAAuC;AAClC,MAAMC,0BAA0B,mBAA2B;AAC3D,MAAMC,WAAW,WAAmB;AACpC,MAAMC,0BAA0B,mBAA2B;AAE3D,MAAMC,iBAAiB;IAC5BR;IACAE;IACAC;IACAE;IACAD;CACD,CAAS;AAEH,MAAMK,uBAAuB,OAAe;AAE5C,MAAMC,gCAAgC,sBAA8B;AACpE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,2BAA2B,qBAA6B", "ignoreList": [0]}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;AAAO,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA;MAIX", "ignoreList": [0]}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DYNAMIC_ERROR_CODE", "DynamicServerError", "Error", "constructor", "description", "digest", "isDynamicServerError", "err"], "mappings": ";;;;AAAA,MAAMA,qBAAqB;AAEpB,MAAMC,2BAA2BC;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCL;IAIpC;AACF;AAEO,SAASM,qBAAqBC,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAIF,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOE,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0]}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["NEXT_STATIC_GEN_BAILOUT", "StaticGenBailoutError", "Error", "code", "isStaticGenBailoutError", "error"], "mappings": ";;;;AAAA,MAAMA,0BAA0B;AAEzB,MAAMC,8BAA8BC;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AAEO,SAASI,wBACdC,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMF,IAAI,KAAKH;AACxB", "ignoreList": [0]}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "HTTP_ERROR_FALLBACK_ERROR_CODE", "isHTTPAccessFallbackError", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "status"], "mappings": ";;;;;;;AAAO,MAAMA,wBAAwB;IACnCC,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB,EAAC;AAED,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACP;AAErC,MAAMQ,iCAAiC,2BAA0B;AAajE,SAASC,0BACdC,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWJ,kCACXJ,cAAcW,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASI,4BACdP,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASK,mCACdC,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0]}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "HTTP_ERROR_FALLBACK_ERROR_CODE", "isHTTPAccessFallbackError", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "status"], "mappings": ";;;;;;;AAAO,MAAMA,wBAAwB;IACnCC,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB,EAAC;AAED,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACP;AAErC,MAAMQ,iCAAiC,2BAA0B;AAajE,SAASC,0BACdC,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWJ,kCACXJ,cAAcW,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASI,4BACdP,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASK,mCACdC,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0]}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/http-access-fallback/error-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      if (this.props.missingSlots.size > 0) {\n        const formattedSlots = Array.from(this.props.missingSlots)\n          .sort((a, b) => a.localeCompare(b))\n          .map((slot) => `@${slot}`)\n          .join(', ')\n\n        warningMessage += 'Missing slots: ' + formattedSlots\n      }\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "useUntrackedPathname", "HTTPAccessErrorStatus", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isHTTPAccessFallbackError", "warnOnce", "MissingSlotContext", "HTTPAccessFallbackErrorBoundary", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "has", "warningMessage", "size", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "getDerivedStateFromError", "error", "httpStatus", "triggeredStatus", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "undefined", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "constructor", "HTTPAccessFallbackBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;AAEA;;;;;;;;;CASC,GAED,OAAOA,SAASC,UAAU,QAAQ,QAAO;AACzC,SAASC,oBAAoB,QAAQ,0BAAyB;AAC9D,SACEC,qBAAqB,EACrBC,2BAA2B,EAC3BC,kCAAkC,EAClCC,yBAAyB,QACpB,yBAAwB;AAC/B,SAASC,QAAQ,QAAQ,sCAAqC;AAC9D,SAASC,kBAAkB,QAAQ,wDAAuD;AAtB1F;;;;;;;AA2CA,MAAMC,2MAAwCT,UAAAA,CAAMU,SAAS;IAY3DC,oBAA0B;QACxB,IACEC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzB,IAAI,CAACC,KAAK,CAACC,YAAY,IACvB,4EAA4E;QAC5E,CAAC,IAAI,CAACD,KAAK,CAACC,YAAY,CAACC,GAAG,CAAC,aAC7B;YACA,IAAIC,iBACF,4HACA;YAEF,IAAI,IAAI,CAACH,KAAK,CAACC,YAAY,CAACG,IAAI,GAAG,GAAG;gBACpC,MAAMC,iBAAiBC,MAAMC,IAAI,CAAC,IAAI,CAACP,KAAK,CAACC,YAAY,EACtDO,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,OAAU,MAAGA,MAClBC,IAAI,CAAC;gBAERX,kBAAkB,oBAAoBE;YACxC;sMAEAb,WAAAA,EAASW;QACX;IACF;IAEA,OAAOY,yBAAyBC,KAAU,EAAE;QAC1C,IAAIzB,gQAAAA,EAA0ByB,QAAQ;YACpC,MAAMC,iPAAa5B,8BAAAA,EAA4B2B;YAC/C,OAAO;gBACLE,iBAAiBD;YACnB;QACF;QACA,mCAAmC;QACnC,MAAMD;IACR;IAEA,OAAOG,yBACLnB,KAA2C,EAC3CoB,KAA8B,EACE;QAChC;;;;;KAKC,GACD,IAAIpB,MAAMqB,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMF,eAAe,EAAE;YACtE,OAAO;gBACLA,iBAAiBK;gBACjBD,kBAAkBtB,MAAMqB,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,iBAAiBE,MAAMF,eAAe;YACtCI,kBAAkBtB,MAAMqB,QAAQ;QAClC;IACF;IAEAG,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAAC5B,KAAK;QAClE,MAAM,EAAEkB,eAAe,EAAE,GAAG,IAAI,CAACE,KAAK;QACtC,MAAMS,kBAAkB;YACtB,iOAACzC,wBAAAA,CAAsB0C,SAAS,CAAC,EAAEL;YACnC,iOAACrC,wBAAAA,CAAsB2C,SAAS,CAAC,EAAEL;YACnC,iOAACtC,wBAAAA,CAAsB4C,YAAY,CAAC,EAAEL;QACxC;QAEA,IAAIT,iBAAiB;YACnB,MAAMe,aACJf,oPAAoB9B,wBAAAA,CAAsB0C,SAAS,IAAIL;YACzD,MAAMS,cACJhB,oPAAoB9B,wBAAAA,CAAsB2C,SAAS,IAAIL;YACzD,MAAMS,iBACJjB,oPAAoB9B,wBAAAA,CAAsB4C,YAAY,IAAIL;YAE5D,kGAAkG;YAClG,IAAI,CAAEM,CAAAA,cAAcC,eAAeC,cAAa,GAAI;gBAClD,OAAOP;YACT;YAEA,OAAA,WAAA,mLACE,OAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;kNACE,MAAA,EAACQ,QAAAA;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3BzC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAA,WAAA,mLACxB,MAAA,EAACqC,QAAAA;wBACCC,MAAK;wBACLC,6OAAShD,qCAAAA,EAAmC4B;;oBAG/CW,eAAe,CAACX,gBAAgB;;;QAGvC;QAEA,OAAOU;IACT;IAtGAW,YAAYvC,KAA2C,CAAE;QACvD,KAAK,CAACA;QACN,IAAI,CAACoB,KAAK,GAAG;YACXF,iBAAiBK;YACjBD,kBAAkBtB,MAAMqB,QAAQ;QAClC;IACF;AAiGF;AAEO,SAASmB,2BAA2B,KAKT;IALS,IAAA,EACzCf,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACwB,GALS;IAMzC,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,mEAAmE;IACnE,MAAMP,8MAAWlC,uBAAAA;IACjB,MAAMc,gBAAef,mLAAAA,yMAAWO,qBAAAA;IAChC,MAAMgD,mBAAmB,CAAC,CAAEhB,CAAAA,YAAYC,aAAaC,YAAW;IAEhE,IAAIc,kBAAkB;QACpB,OAAA,WAAA,IACE,qLAAA,EAAC/C,iCAAAA;YACC2B,UAAUA;YACVI,UAAUA;YACVC,WAAWA;YACXC,cAAcA;YACd1B,cAAcA;sBAEb2B;;IAGP;IAEA,OAAA,WAAA,mLAAO,MAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0]}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/http-access-fallback/error-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      if (this.props.missingSlots.size > 0) {\n        const formattedSlots = Array.from(this.props.missingSlots)\n          .sort((a, b) => a.localeCompare(b))\n          .map((slot) => `@${slot}`)\n          .join(', ')\n\n        warningMessage += 'Missing slots: ' + formattedSlots\n      }\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "useUntrackedPathname", "HTTPAccessErrorStatus", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isHTTPAccessFallbackError", "warnOnce", "MissingSlotContext", "HTTPAccessFallbackErrorBoundary", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "has", "warningMessage", "size", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "getDerivedStateFromError", "error", "httpStatus", "triggeredStatus", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "undefined", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "constructor", "HTTPAccessFallbackBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN"], "mappings": ";;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;;AAEpD,MAAMC,sBAAsB,gBAAe;AAE3C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;MAGX;AAaM,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,gNAAcd,qBAAAA;AAElB", "ignoreList": [0]}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/redirect.ts"], "sourcesContent": ["import { actionAsyncStorage } from '../../server/app-render/action-async-storage.external'\nimport { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  const actionStore = actionAsyncStorage.getStore()\n  const redirectType =\n    type || (actionStore?.isAction ? RedirectType.push : RedirectType.replace)\n  throw getRedirectError(\n    url,\n    redirectType,\n    RedirectStatusCode.TemporaryRedirect\n  )\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["actionAsyncStorage", "RedirectStatusCode", "RedirectType", "isRedirectError", "REDIRECT_ERROR_CODE", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "redirect", "actionStore", "getStore", "redirectType", "isAction", "push", "replace", "permanentRedirect", "PermanentRedirect", "getURLFromRedirectError", "split", "slice", "join", "getRedirectTypeFromError", "getRedirectStatusCodeFromError", "Number", "at"], "mappings": ";;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,wDAAuD;AAC1F,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,YAAY,EAEZC,eAAe,EACfC,mBAAmB,QACd,mBAAkB;;;;;AAElB,SAASC,iBACdC,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,+MAAiCP,qBAAAA,CAAmBQ,iBAAiB;IAErE,MAAMC,QAAQ,IAAIC,+LAAMP,sBAAAA;IACxBM,MAAME,MAAM,4LAAMR,sBAAAA,GAAoB,MAAGG,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOE;AACT;AAcO,SAASG,SACd,2BAA2B,GAC3BP,GAAW,EACXC,IAAmB;IAEnB,MAAMO,sSAAcd,qBAAAA,CAAmBe,QAAQ;IAC/C,MAAMC,eACJT,QAASO,CAAAA,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAaG,QAAQ,6LAAGf,eAAAA,CAAagB,IAAI,4LAAGhB,eAAAA,CAAaiB,OAAM;IAC1E,MAAMd,iBACJC,KACAU,gNACAf,qBAAAA,CAAmBQ,iBAAiB;AAExC;AAaO,SAASW,kBACd,2BAA2B,GAC3Bd,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,gMAAqBL,eAAAA,CAAaiB,OAAO;IAEzC,MAAMd,iBAAiBC,KAAKC,wMAAMN,qBAAAA,CAAmBoB,iBAAiB;AACxE;AAUO,SAASC,wBAAwBZ,KAAc;IACpD,IAAI,8LAACP,kBAAAA,EAAgBO,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACW,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAASC,yBAAyBhB,KAAoB;IAC3D,IAAI,8LAACP,kBAAAA,EAAgBO,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD,MAAME,MAAM,CAACW,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASI,+BAA+BjB,KAAoB;IACjE,IAAI,8LAACP,kBAAAA,EAAgBO,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOiB,OAAOlB,MAAME,MAAM,CAACW,KAAK,CAAC,KAAKM,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0]}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isHTTPAccessFallbackError", "isRedirectError", "isNextRouterError", "error"], "mappings": ";;;AAAA,SACEA,yBAAyB,QAEpB,8CAA6C;AACpD,SAASC,eAAe,QAA4B,mBAAkB;;;AAO/D,SAASC,kBACdC,KAAc;IAEd,oMAAOF,kBAAAA,EAAgBE,8OAAUH,4BAAAA,EAA0BG;AAC7D", "ignoreList": [0]}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/match-segments.ts"], "sourcesContent": ["import { getSegmentParam } from '../../server/app-render/get-segment-param'\nimport type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n\n/*\n * This function is used to determine if an existing segment can be overridden by the incoming segment.\n */\nexport const canSegmentBeOverridden = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  if (Array.isArray(existingSegment) || !Array.isArray(segment)) {\n    return false\n  }\n\n  return getSegmentParam(existingSegment)?.param === segment[0]\n}\n"], "names": ["getSegmentParam", "matchSegment", "existingSegment", "segment", "canSegmentBeOverridden", "Array", "isArray", "param"], "mappings": ";;;;AAAA,SAASA,eAAe,QAAQ,4CAA2C;;AAGpE,MAAMC,eAAe,CAC1BC,iBACAC;IAEA,oCAAoC;IACpC,IAAI,OAAOD,oBAAoB,UAAU;QACvC,IAAI,OAAOC,YAAY,UAAU;YAC/B,wCAAwC;YACxC,OAAOD,oBAAoBC;QAC7B;QACA,OAAO;IACT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,OAAOD,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE,IAAID,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE;AAC/E,EAAC;AAKM,MAAMC,yBAAyB,CACpCF,iBACAC;QAMOH;IAJP,IAAIK,MAAMC,OAAO,CAACJ,oBAAoB,CAACG,MAAMC,OAAO,CAACH,UAAU;QAC7D,OAAO;IACT;IAEA,OAAOH,CAAAA,CAAAA,yNAAAA,kBAAAA,EAAgBE,gBAAAA,KAAAA,OAAAA,KAAAA,IAAhBF,iBAAkCO,KAAK,MAAKJ,OAAO,CAAC,EAAE;AAC/D,EAAC", "ignoreList": [0]}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "notFound", "error", "Error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,qOAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,4CAA4C;IAC5C,MAAMC,QAAQ,IAAIC,MAAMH;IACtBE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0]}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/parallel-route-default.tsx"], "sourcesContent": ["import { notFound } from './not-found'\n\nexport const PARALLEL_ROUTE_DEFAULT_PATH =\n  'next/dist/client/components/parallel-route-default.js'\n\nexport default function ParallelRouteDefault() {\n  notFound()\n}\n"], "names": ["notFound", "PARALLEL_ROUTE_DEFAULT_PATH", "<PERSON>llel<PERSON><PERSON><PERSON>ault"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,cAAa;;AAE/B,MAAMC,8BACX,wDAAuD;AAE1C,SAASC;4LACtBF,WAAAA;AACF", "ignoreList": [0]}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/app-router.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/app-router.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/app-router.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/app-router.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/app-router.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  useCallback,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  CacheNode,\n  AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  ACTION_HMR_REFRESH,\n  ACTION_NAVIGATE,\n  ACTION_PREFETCH,\n  ACTION_REFRESH,\n  ACTION_RESTORE,\n  ACTION_SERVER_PATCH,\n  PrefetchKind,\n} from './router-reducer/router-reducer-types'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  RouterChangeByServerResponse,\n  RouterNavigate,\n} from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { useReducer, useUnwrapState } from './use-reducer'\nimport { ErrorBoundary, type ErrorComponent } from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport { useServerActionDispatcher } from '../app-call-server'\nimport type { AppRouterActionQueue } from '../../shared/lib/router/action-queue'\nimport { prefetch as prefetchWithSegmentCache } from '../components/segment-cache/prefetch'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nfunction isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n  }\n}\n\n/**\n * Server response that only patches the cache and tree.\n */\nfunction useChangeByServerResponse(\n  dispatch: React.Dispatch<ReducerActions>\n): RouterChangeByServerResponse {\n  return useCallback(\n    ({ previousTree, serverResponse }) => {\n      startTransition(() => {\n        dispatch({\n          type: ACTION_SERVER_PATCH,\n          previousTree,\n          serverResponse,\n        })\n      })\n    },\n    [dispatch]\n  )\n}\n\nfunction useNavigate(dispatch: React.Dispatch<ReducerActions>): RouterNavigate {\n  return useCallback(\n    (href, navigateType, shouldScroll) => {\n      const url = new URL(addBasePath(href), location.href)\n\n      if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n        window.next.__pendingUrl = url\n      }\n\n      return dispatch({\n        type: ACTION_NAVIGATE,\n        url,\n        isExternalUrl: isExternalURL(url),\n        locationSearch: location.search,\n        shouldScroll: shouldScroll ?? true,\n        navigateType,\n        allowAliasing: true,\n      })\n    },\n    [dispatch]\n  )\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  //\n  // @ts-expect-error The second argument to `useDeferredValue` is only\n  // available in the experimental builds. When its disabled, it will always\n  // return `head`.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n}) {\n  const [state, dispatch] = useReducer(actionQueue)\n  const { canonicalUrl } = useUnwrapState(state)\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  const changeByServerResponse = useChangeByServerResponse(dispatch)\n  const navigate = useNavigate(dispatch)\n  useServerActionDispatcher(dispatch)\n\n  /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */\n  const appRouter = useMemo<AppRouterInstance>(() => {\n    const routerInstance: AppRouterInstance = {\n      back: () => window.history.back(),\n      forward: () => window.history.forward(),\n      prefetch:\n        process.env.__NEXT_PPR && process.env.__NEXT_CLIENT_SEGMENT_CACHE\n          ? // Unlike the old implementation, the Segment Cache doesn't store its\n            // data in the router reducer state; it writes into a global mutable\n            // cache. So we don't need to dispatch an action.\n            (href) => prefetchWithSegmentCache(href, actionQueue.state.nextUrl)\n          : (href, options) => {\n              // Use the old prefetch implementation.\n              const url = createPrefetchURL(href)\n              if (url !== null) {\n                startTransition(() => {\n                  dispatch({\n                    type: ACTION_PREFETCH,\n                    url,\n                    kind: options?.kind ?? PrefetchKind.FULL,\n                  })\n                })\n              }\n            },\n      replace: (href, options = {}) => {\n        startTransition(() => {\n          navigate(href, 'replace', options.scroll ?? true)\n        })\n      },\n      push: (href, options = {}) => {\n        startTransition(() => {\n          navigate(href, 'push', options.scroll ?? true)\n        })\n      },\n      refresh: () => {\n        startTransition(() => {\n          dispatch({\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          })\n        })\n      },\n      hmrRefresh: () => {\n        if (process.env.NODE_ENV !== 'development') {\n          throw new Error(\n            'hmrRefresh can only be used in development mode. Please use refresh instead.'\n          )\n        } else {\n          startTransition(() => {\n            dispatch({\n              type: ACTION_HMR_REFRESH,\n              origin: window.location.origin,\n            })\n          })\n        }\n      },\n    }\n\n    return routerInstance\n  }, [actionQueue, dispatch, navigate])\n\n  useEffect(() => {\n    // Exists for debugging purposes. Don't use in application code.\n    if (window.next) {\n      window.next.router = appRouter\n    }\n  }, [appRouter])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = useUnwrapState(state)\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: appRouter,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [appRouter, cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatch({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [dispatch])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        if (redirectType === RedirectType.push) {\n          appRouter.push(url, {})\n        } else {\n          appRouter.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [appRouter])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = useUnwrapState(state)\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatch({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatch({\n          type: ACTION_RESTORE,\n          url: new URL(window.location.href),\n          tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n        })\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [dispatch])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = useUnwrapState(state)\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      childNodes: cache.parallelRoutes,\n      tree,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n      loading: cache.loading,\n    }\n  }, [cache.parallelRoutes, tree, canonicalUrl, cache.loading])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      changeByServerResponse,\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [changeByServerResponse, tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = <HotReloader assetPrefix={assetPrefix}>{content}</HotReloader>\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={useUnwrapState(state)} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              <AppRouterContext.Provider value={appRouter}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [ErrorComponent, React.ReactNode | undefined]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      errorComponent={globalErrorComponent}\n      errorStyles={globalErrorStyles}\n    >\n      <Router actionQueue={actionQueue} assetPrefix={assetPrefix} />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n"], "names": ["React", "use", "useEffect", "useMemo", "useCallback", "startTransition", "useInsertionEffect", "useDeferredValue", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "ACTION_HMR_REFRESH", "ACTION_NAVIGATE", "ACTION_PREFETCH", "ACTION_REFRESH", "ACTION_RESTORE", "ACTION_SERVER_PATCH", "PrefetchKind", "createHrefFromUrl", "SearchParamsContext", "PathnameContext", "PathParamsContext", "useReducer", "useUnwrapState", "Error<PERSON>ou<PERSON><PERSON>", "isBot", "addBasePath", "AppRouterAnnouncer", "RedirectBoundary", "findHeadInCache", "unresolvedThenable", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "getSelectedParams", "useNavFailureHandler", "useServerActionDispatcher", "prefetch", "prefetchWithSegmentCache", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "RedirectType", "globalMutable", "isExternalURL", "url", "origin", "window", "location", "createPrefetchURL", "href", "navigator", "userAgent", "URL", "_", "Error", "process", "env", "NODE_ENV", "HistoryUpdater", "appRouterState", "__NEXT_APP_NAV_FAIL_HANDLING", "next", "__pendingUrl", "undefined", "tree", "pushRef", "canonicalUrl", "historyState", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "pushState", "replaceState", "createEmptyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "useChangeByServerResponse", "dispatch", "previousTree", "serverResponse", "type", "useNavigate", "navigateType", "shouldScroll", "isExternalUrl", "locationSearch", "search", "allowAliasing", "copyNextJsInternalHistoryState", "data", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "Router", "actionQueue", "assetPrefix", "searchParams", "pathname", "changeByServerResponse", "navigate", "appRouter", "routerInstance", "back", "forward", "__NEXT_PPR", "__NEXT_CLIENT_SEGMENT_CACHE", "nextUrl", "options", "kind", "FULL", "replace", "scroll", "push", "refresh", "hmrRefresh", "router", "cache", "prefetchCache", "nd", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "error", "reason", "preventDefault", "redirectType", "mpaNavigation", "assign", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "focusAndScrollRef", "matchingHead", "pathParams", "layoutRouterContext", "childNodes", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "DevRootHTTPAccessFallbackBoundary", "require", "HotReloader", "default", "RuntimeStyles", "Provider", "value", "AppRouter", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "errorComponent", "errorStyles", "runtimeStyles", "Set", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "size", "add", "for<PERSON>ach", "cb", "Promise", "resolve", "forceUpdate", "useState", "renderedStylesSize", "changed", "c", "delete", "dplId", "NEXT_DEPLOYMENT_ID", "map", "i", "link", "rel", "precedence"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/promise-queue.ts"], "sourcesContent": ["/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/\nexport class PromiseQueue {\n  #maxConcurrency: number\n  #runningCount: number\n  #queue: Array<{\n    promiseFn: Promise<any>\n    task: () => void\n  }>\n\n  constructor(maxConcurrency = 5) {\n    this.#maxConcurrency = maxConcurrency\n    this.#runningCount = 0\n    this.#queue = []\n  }\n\n  enqueue<T>(promiseFn: () => Promise<T>): Promise<T> {\n    let taskResolve: (value: T | PromiseLike<T>) => void\n    let taskReject: (reason?: any) => void\n\n    const taskPromise = new Promise((resolve, reject) => {\n      taskResolve = resolve\n      taskReject = reject\n    }) as Promise<T>\n\n    const task = async () => {\n      try {\n        this.#runningCount++\n        const result = await promiseFn()\n        taskResolve(result)\n      } catch (error) {\n        taskReject(error)\n      } finally {\n        this.#runningCount--\n        this.#processNext()\n      }\n    }\n\n    const enqueueResult = { promiseFn: taskPromise, task }\n    // wonder if we should take a LIFO approach here\n    this.#queue.push(enqueueResult)\n    this.#processNext()\n\n    return taskPromise\n  }\n\n  bump(promiseFn: Promise<any>) {\n    const index = this.#queue.findIndex((item) => item.promiseFn === promiseFn)\n\n    if (index > -1) {\n      const bumpedItem = this.#queue.splice(index, 1)[0]\n      this.#queue.unshift(bumpedItem)\n      this.#processNext(true)\n    }\n  }\n\n  #processNext(forced = false) {\n    if (\n      (this.#runningCount < this.#maxConcurrency || forced) &&\n      this.#queue.length > 0\n    ) {\n      this.#queue.shift()?.task()\n    }\n  }\n}\n"], "names": ["PromiseQueue", "enqueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "Promise", "resolve", "reject", "task", "result", "error", "enqueueResult", "push", "bump", "index", "findIndex", "item", "bumpedItem", "splice", "unshift", "constructor", "maxConcurrency", "forced", "length", "shift"], "mappings": "AAAA;;;;;AAKA;;;;AAAA;;;IAEE,kBAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,oBACA,gBAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,kBACA,SAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,WAmDA,eAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA;AAtDK,MAAMA;IAcXC,QAAWC,SAA2B,EAAc;QAClD,IAAIC;QACJ,IAAIC;QAEJ,MAAMC,cAAc,IAAIC,QAAQ,CAACC,SAASC;YACxCL,cAAcI;YACdH,aAAaI;QACf;QAEA,MAAMC,OAAO;YACX,IAAI;gBACF,2LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA;gBACL,MAAMC,SAAS,MAAMR;gBACrBC,YAAYO;YACd,EAAE,OAAOC,OAAO;gBACdP,WAAWO;YACb,SAAU;gBACR,2LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA;uMACL,IAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA;YACP;QACF;QAEA,MAAMC,gBAAgB;YAAEV,WAAWG;YAAaI;QAAK;QACrD,gDAAgD;+LAChD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOI,IAAI,CAACD;+LACjB,IAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA;QAEL,OAAOP;IACT;IAEAS,KAAKZ,SAAuB,EAAE;QAC5B,MAAMa,+LAAQ,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,SAAS,CAAC,CAACC,OAASA,KAAKf,SAAS,KAAKA;QAEjE,IAAIa,QAAQ,CAAC,GAAG;YACd,MAAMG,oMAAa,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,MAAM,CAACJ,OAAO,EAAE,CAAC,EAAE;mMAClD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOK,OAAO,CAACF;aACpB,0LAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA,CAAa;QACpB;IACF;IA5CAG,YAAYC,iBAAiB,CAAC,CAAE;QA8ChC,OAAA,cAAA,CAAA,IAAA,EAAA,cAAA;mBAAA;;QArDA,OAAA,cAAA,CAAA,IAAA,EAAA,iBAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,eAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,QAAA;;mBAAA,KAAA;;+LAME,IAAA,EAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,GAAkBA;SACvB,0LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA,GAAgB;+LACrB,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,GAAS,EAAE;IAClB;AAkDF;AARE,SAAA,YAAaC,MAAc;IAAdA,IAAAA,WAAAA,KAAAA,GAAAA,SAAS;IACpB,IACG,wLAAA,IAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA,GAAgB,2LAAA,EAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,IAAmBA,MAAK,4LACnD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,MAAM,GAAG,GACrB;YACA;SAAA,sOAAA,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,KAAK,EAAA,KAAA,OAAA,KAAA,IAAjB,6CAAqBhB,IAAI;IAC3B;AACF", "ignoreList": [0]}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/dev-root-http-access-fallback-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\n\n// TODO: error on using forbidden and unauthorized in root layout\nexport function bailOnRootNotFound() {\n  throw new Error('notFound() is not allowed to use in root layout')\n}\n\nfunction NotAllowedRootHTTPFallbackError() {\n  bailOnRootNotFound()\n  return null\n}\n\nexport function DevRootHTTPAccessFallbackBoundary({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <HTTPAccessFallbackBoundary notFound={<NotAllowedRootHTTPFallbackError />}>\n      {children}\n    </HTTPAccessFallbackBoundary>\n  )\n}\n"], "names": ["React", "HTTPAccessFallbackBoundary", "bailOnRootNotFound", "Error", "NotAllowedRootHTTPFallbackError", "DevRootHTTPAccessFallbackBoundary", "children", "notFound"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n"], "names": ["RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_IS_PRERENDER_HEADER"], "mappings": ";;;;;;;;;;;;;;;AAAO,MAAMA,aAAa,MAAc;AACjC,MAAMC,gBAAgB,cAAsB;AAI5C,MAAMC,gCAAgC,yBAAiC;AACvE,MAAMC,8BAA8B,uBAA+B;AAKnE,MAAMC,sCACX,+BAAuC;AAClC,MAAMC,0BAA0B,mBAA2B;AAC3D,MAAMC,WAAW,WAAmB;AACpC,MAAMC,0BAA0B,mBAA2B;AAE3D,MAAMC,iBAAiB;IAC5BR;IACAE;IACAC;IACAE;IACAD;CACD,CAAS;AAEH,MAAMK,uBAAuB,OAAe;AAE5C,MAAMC,gCAAgC,sBAA8B;AACpE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,2BAA2B,qBAA6B", "ignoreList": [0]}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DYNAMIC_ERROR_CODE", "DynamicServerError", "Error", "constructor", "description", "digest", "isDynamicServerError", "err"], "mappings": ";;;;AAAA,MAAMA,qBAAqB;AAEpB,MAAMC,2BAA2BC;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCL;IAIpC;AACF;AAEO,SAASM,qBAAqBC,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAIF,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOE,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0]}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["NEXT_STATIC_GEN_BAILOUT", "StaticGenBailoutError", "Error", "code", "isStaticGenBailoutError", "error"], "mappings": ";;;;AAAA,MAAMA,0BAA0B;AAEzB,MAAMC,8BAA8BC;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AAEO,SAASI,wBACdC,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMF,IAAI,KAAKH;AACxB", "ignoreList": [0]}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;AAAO,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA;MAIX", "ignoreList": [0]}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 760, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN"], "mappings": ";;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;;AAEpD,MAAMC,sBAAsB,gBAAe;AAE3C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;MAGX;AAaM,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,gNAAcd,qBAAAA;AAElB", "ignoreList": [0]}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/redirect.ts"], "sourcesContent": ["import { actionAsyncStorage } from '../../server/app-render/action-async-storage.external'\nimport { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  const actionStore = actionAsyncStorage.getStore()\n  const redirectType =\n    type || (actionStore?.isAction ? RedirectType.push : RedirectType.replace)\n  throw getRedirectError(\n    url,\n    redirectType,\n    RedirectStatusCode.TemporaryRedirect\n  )\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["actionAsyncStorage", "RedirectStatusCode", "RedirectType", "isRedirectError", "REDIRECT_ERROR_CODE", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "redirect", "actionStore", "getStore", "redirectType", "isAction", "push", "replace", "permanentRedirect", "PermanentRedirect", "getURLFromRedirectError", "split", "slice", "join", "getRedirectTypeFromError", "getRedirectStatusCodeFromError", "Number", "at"], "mappings": ";;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,wDAAuD;AAC1F,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,YAAY,EAEZC,eAAe,EACfC,mBAAmB,QACd,mBAAkB;;;;;AAElB,SAASC,iBACdC,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,+MAAiCP,qBAAAA,CAAmBQ,iBAAiB;IAErE,MAAMC,QAAQ,IAAIC,+LAAMP,sBAAAA;IACxBM,MAAME,MAAM,4LAAMR,sBAAAA,GAAoB,MAAGG,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOE;AACT;AAcO,SAASG,SACd,2BAA2B,GAC3BP,GAAW,EACXC,IAAmB;IAEnB,MAAMO,sSAAcd,qBAAAA,CAAmBe,QAAQ;IAC/C,MAAMC,eACJT,QAASO,CAAAA,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAaG,QAAQ,6LAAGf,eAAAA,CAAagB,IAAI,4LAAGhB,eAAAA,CAAaiB,OAAM;IAC1E,MAAMd,iBACJC,KACAU,gNACAf,qBAAAA,CAAmBQ,iBAAiB;AAExC;AAaO,SAASW,kBACd,2BAA2B,GAC3Bd,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,gMAAqBL,eAAAA,CAAaiB,OAAO;IAEzC,MAAMd,iBAAiBC,KAAKC,wMAAMN,qBAAAA,CAAmBoB,iBAAiB;AACxE;AAUO,SAASC,wBAAwBZ,KAAc;IACpD,IAAI,8LAACP,kBAAAA,EAAgBO,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACW,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAASC,yBAAyBhB,KAAoB;IAC3D,IAAI,8LAACP,kBAAAA,EAAgBO,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD,MAAME,MAAM,CAACW,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASI,+BAA+BjB,KAAoB;IACjE,IAAI,8LAACP,kBAAAA,EAAgBO,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOiB,OAAOlB,MAAME,MAAM,CAACW,KAAK,CAAC,KAAKM,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0]}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isHTTPAccessFallbackError", "isRedirectError", "isNextRouterError", "error"], "mappings": ";;;AAAA,SACEA,yBAAyB,QAEpB,8CAA6C;AACpD,SAASC,eAAe,QAA4B,mBAAkB;;;AAO/D,SAASC,kBACdC,KAAc;IAEd,oMAAOF,kBAAAA,EAAgBE,8OAAUH,4BAAAA,EAA0BG;AAC7D", "ignoreList": [0]}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/match-segments.ts"], "sourcesContent": ["import { getSegmentParam } from '../../server/app-render/get-segment-param'\nimport type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n\n/*\n * This function is used to determine if an existing segment can be overridden by the incoming segment.\n */\nexport const canSegmentBeOverridden = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  if (Array.isArray(existingSegment) || !Array.isArray(segment)) {\n    return false\n  }\n\n  return getSegmentParam(existingSegment)?.param === segment[0]\n}\n"], "names": ["getSegmentParam", "matchSegment", "existingSegment", "segment", "canSegmentBeOverridden", "Array", "isArray", "param"], "mappings": ";;;;AAAA,SAASA,eAAe,QAAQ,4CAA2C;;AAGpE,MAAMC,eAAe,CAC1BC,iBACAC;IAEA,oCAAoC;IACpC,IAAI,OAAOD,oBAAoB,UAAU;QACvC,IAAI,OAAOC,YAAY,UAAU;YAC/B,wCAAwC;YACxC,OAAOD,oBAAoBC;QAC7B;QACA,OAAO;IACT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,OAAOD,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE,IAAID,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE;AAC/E,EAAC;AAKM,MAAMC,yBAAyB,CACpCF,iBACAC;QAMOH;IAJP,IAAIK,MAAMC,OAAO,CAACJ,oBAAoB,CAACG,MAAMC,OAAO,CAACH,UAAU;QAC7D,OAAO;IACT;IAEA,OAAOH,CAAAA,CAAAA,yNAAAA,kBAAAA,EAAgBE,gBAAAA,KAAAA,OAAAA,KAAAA,IAAhBF,iBAAkCO,KAAK,MAAKJ,OAAO,CAAC,EAAE;AAC/D,EAAC", "ignoreList": [0]}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "notFound", "error", "Error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,qOAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,4CAA4C;IAC5C,MAAMC,QAAQ,IAAIC,MAAMH;IACtBE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0]}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/parallel-route-default.tsx"], "sourcesContent": ["import { notFound } from './not-found'\n\nexport const PARALLEL_ROUTE_DEFAULT_PATH =\n  'next/dist/client/components/parallel-route-default.js'\n\nexport default function ParallelRouteDefault() {\n  notFound()\n}\n"], "names": ["notFound", "PARALLEL_ROUTE_DEFAULT_PATH", "<PERSON>llel<PERSON><PERSON><PERSON>ault"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,cAAa;;AAE/B,MAAMC,8BACX,wDAAuD;AAE1C,SAASC;4LACtBF,WAAAA;AACF", "ignoreList": [0]}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/use-reducer.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use } from 'react'\nimport { useCallback } from 'react'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\nimport type { AppRouterActionQueue } from '../../shared/lib/router/action-queue'\nimport { isThenable } from '../../shared/lib/is-thenable'\n\nexport function useUnwrapState(state: ReducerState): AppRouterState {\n  // reducer actions can be async, so sometimes we need to suspend until the state is resolved\n  if (isThenable(state)) {\n    const result = use(state)\n    return result\n  }\n\n  return state\n}\n\nexport function useReducer(\n  actionQueue: AppRouterActionQueue\n): [ReducerState, Dispatch<ReducerActions>] {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  const dispatch = useCallback(\n    (action: ReducerActions) => {\n      actionQueue.dispatch(action, setState)\n    },\n    [actionQueue]\n  )\n\n  return [state, dispatch]\n}\n"], "names": ["React", "use", "useCallback", "isThenable", "useUnwrapState", "state", "result", "useReducer", "actionQueue", "setState", "useState", "dispatch", "action"], "mappings": ";;;;AACA,OAAOA,SAASC,GAAG,QAAQ,QAAO;AAQlC,SAASE,UAAU,QAAQ,+BAA8B;;;;AAElD,SAASC,eAAeC,KAAmB;IAChD,4FAA4F;IAC5F,uLAAIF,aAAAA,EAAWE,QAAQ;QACrB,MAAMC,SAASL,6KAAAA,EAAII;QACnB,OAAOC;IACT;IAEA,OAAOD;AACT;AAEO,SAASE,WACdC,WAAiC;IAEjC,MAAM,CAACH,OAAOI,SAAS,GAAGT,6KAAAA,CAAMU,QAAQ,CAAeF,YAAYH,KAAK;IAExE,MAAMM,kLAAWT,cAAAA,EACf,CAACU;QACCJ,YAAYG,QAAQ,CAACC,QAAQH;IAC/B,GACA;QAACD;KAAY;IAGf,OAAO;QAACH;QAAOM;KAAS;AAC1B", "ignoreList": [0]}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/navigation-untracked.ts"], "sourcesContent": ["import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams() {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) return false\n\n    const { fallbackRouteParams } = workStore\n    if (!fallbackRouteParams || fallbackRouteParams.size === 0) return false\n\n    return true\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n"], "names": ["useContext", "PathnameContext", "hasFallbackRouteParams", "window", "workAsyncStorage", "require", "workStore", "getStore", "fallbackRouteParams", "size", "useUntrackedPathname"], "mappings": ";;;AAAA,SAASA,UAAU,QAAQ,QAAO;AAClC,SAASC,eAAe,QAAQ,uDAAsD;;;AAEtF;;;;;CAKC,GACD,SAASC;IACP,IAAI,OAAOC,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,MAAMC,YAAYF,iBAAiBG,QAAQ;QAC3C,IAAI,CAACD,WAAW,OAAO;QAEvB,MAAM,EAAEE,mBAAmB,EAAE,GAAGF;QAChC,IAAI,CAACE,uBAAuBA,oBAAoBC,IAAI,KAAK,GAAG,OAAO;QAEnE,OAAO;IACT;IAEA,OAAO;AACT;AAaO,SAASC;IACd,qEAAqE;IACrE,8EAA8E;IAC9E,8EAA8E;IAC9E,uBAAuB;IACvB,IAAIR,0BAA0B;QAC5B,OAAO;IACT;IAEA,2EAA2E;IAC3E,qDAAqD;IACrD,sDAAsD;IACtD,8KAAOF,aAAAA,2MAAWC,kBAAAA;AACpB", "ignoreList": [0]}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/nav-failure-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n"], "names": ["useEffect", "createHrefFromUrl", "handleHardNavError", "error", "window", "next", "__pendingUrl", "URL", "location", "href", "console", "toString", "useNavFailureHandler", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "uncaughtExceptionHandler", "evt", "reason", "addEventListener", "removeEventListener"], "mappings": ";;;;AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,iBAAiB,QAAQ,wCAAuC;;;AAElE,SAASC,mBAAmBC,KAAc;IAC/C,IACEA,SACA,OAAOC,WAAW,eAClBA,OAAOC,IAAI,CAACC,YAAY,kOACxBL,oBAAAA,EAAkB,IAAIM,IAAIH,OAAOI,QAAQ,CAACC,IAAI,qOAC5CR,oBAAAA,EAAkBG,OAAOC,IAAI,CAACC,YAAY,GAC5C;QACAI,QAAQP,KAAK,CACV,qEACDA;QAEFC,OAAOI,QAAQ,CAACC,IAAI,GAAGL,OAAOC,IAAI,CAACC,YAAY,CAACK,QAAQ;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAASC;IACd,IAAIC,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;IAuB5C;AACF", "ignoreList": [0]}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  reset: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError({ error }: { error: any }) {\n  const store = workAsyncStorage.getStore()\n  if (store?.isRevalidate || store?.isStaticGeneration) {\n    console.error(error)\n    throw error\n  }\n\n  return null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    if (this.state.error) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function GlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              {`Application error: a ${\n                digest ? 'server' : 'client'\n              }-side exception has occurred (see the ${\n                digest ? 'server logs' : 'browser console'\n              } for more information).`}\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default GlobalError\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useUntrackedPathname", "isNextRouterError", "handleHardNavError", "workAsyncStorage", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "getDerivedStateFromProps", "props", "state", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "previousPathname", "pathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "GlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "p", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": ";;;;;;;AAEA,OAAOA,WAAyB,QAAO;AACvC,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,kBAAkB,QAAQ,wBAAuB;;AAC1D,SAASC,gBAAgB,QAAQ,sDAAqD;AANtF;;;;;;;AAQA,MAAMC,SAAS;IACbC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,UAAU;QACVC,YAAY;QACZC,YAAY;QACZC,QAAQ;IACV;AACF;AAwBA,8DAA8D;AAC9D,yDAAyD;AACzD,oCAAoC;AACpC,SAASC,eAAe,KAAyB;IAAzB,IAAA,EAAEb,KAAK,EAAkB,GAAzB;IACtB,MAAMc,0RAAQhB,mBAAAA,CAAiBiB,QAAQ;IACvC,IAAID,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAOE,YAAY,KAAA,CAAIF,SAAAA,OAAAA,KAAAA,IAAAA,MAAOG,kBAAkB,GAAE;QACpDC,QAAQlB,KAAK,CAACA;QACd,MAAMA;IACR;IAEA,OAAO;AACT;AAEO,MAAMmB,6BAA6BzB,6KAAAA,CAAM0B,SAAS;IASvD,OAAOC,yBAAyBrB,KAAY,EAAE;QAC5C,6MAAIJ,oBAAAA,EAAkBI,QAAQ;YAC5B,+DAA+D;YAC/D,4GAA4G;YAC5G,MAAMA;QACR;QAEA,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOsB,yBACLC,KAAgC,EAChCC,KAAgC,EACE;QAClC,MAAM,EAAExB,KAAK,EAAE,GAAGwB;QAElB,iCAAiC;QACjC,8CAA8C;QAC9C,iDAAiD;QACjD,6CAA6C;QAC7C,IAAIC,QAAQC,GAAG,CAACC,uBAA8B,KAAF;;QAQ5C;QAEA;;;;;KAKC,GACD,IAAIJ,MAAMM,QAAQ,KAAKL,MAAMI,gBAAgB,IAAIJ,MAAMxB,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACP4B,kBAAkBL,MAAMM,QAAQ;YAClC;QACF;QACA,OAAO;YACL7B,OAAOwB,MAAMxB,KAAK;YAClB4B,kBAAkBL,MAAMM,QAAQ;QAClC;IACF;IAMA,yIAAyI;IACzIC,SAA0B;QACxB,IAAI,IAAI,CAACN,KAAK,CAACxB,KAAK,EAAE;YACpB,OAAA,WAAA,kLACE,QAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;kNACE,MAAA,EAACa,gBAAAA;wBAAeb,OAAO,IAAI,CAACwB,KAAK,CAACxB,KAAK;;oBACtC,IAAI,CAACuB,KAAK,CAACQ,WAAW;oBACtB,IAAI,CAACR,KAAK,CAACS,YAAY;kNACxB,MAAA,EAACC,IAAI,CAACV,KAAK,CAACW,cAAc,EAAA;wBACxBlC,OAAO,IAAI,CAACwB,KAAK,CAACxB,KAAK;wBACvBmC,OAAO,IAAI,CAACA,KAAK;;;;QAIzB;QAEA,OAAO,IAAI,CAACZ,KAAK,CAACa,QAAQ;IAC5B;IA1EAC,YAAYd,KAAgC,CAAE;QAC5C,KAAK,CAACA,QAAAA,IAAAA,CAoDRY,KAAAA,GAAQ;YACN,IAAI,CAACG,QAAQ,CAAC;gBAAEtC,OAAO;YAAK;QAC9B;QArDE,IAAI,CAACwB,KAAK,GAAG;YAAExB,OAAO;YAAM4B,kBAAkB,IAAI,CAACL,KAAK,CAACM,QAAQ;QAAC;IACpE;AAwEF;AAEO,SAASU,YAAY,KAAyB;IAAzB,IAAA,EAAEvC,KAAK,EAAkB,GAAzB;IAC1B,MAAMwC,SAA6BxC,SAAAA,OAAAA,KAAAA,IAAAA,MAAOwC,MAAM;IAChD,OAAA,WAAA,mLACE,OAAA,EAACC,QAAAA;QAAKC,IAAG;;8BACP,kLAAA,EAACC,QAAAA,CAAAA;0MACD,OAAA,EAACC,QAAAA;;kNACC,MAAA,EAAC/B,gBAAAA;wBAAeb,OAAOA;;kNACvB,MAAA,EAAC6C,OAAAA;wBAAIC,OAAO/C,OAAOC,KAAK;kCACtB,WAAA,mLAAA,OAAA,EAAC6C,OAAAA;;8NACC,MAAA,EAACE,MAAAA;oCAAGD,OAAO/C,OAAOS,IAAI;8CAClB,0BACAgC,CAAAA,SAAS,WAAW,QAAO,IAC5B,2CACCA,CAAAA,SAAS,gBAAgB,iBAAgB,IAC1C;;gCAEFA,SAAAA,WAAAA,mLAAS,MAAA,EAACQ,KAAAA;oCAAEF,OAAO/C,OAAOS,IAAI;8CAAI,aAAUgC;qCAAgB;;;;;;;;AAMzE;uCAIeD,YAAW;AAWnB,SAASU,cAAc,KAO7B;IAP6B,IAAA,EAC5Bf,cAAc,EACdH,WAAW,EACXC,YAAY,EACZI,QAAQ,EAGT,GAP6B;IAQ5B,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,oEAAoE;IACpE,MAAMP,YAAWlC,yNAAAA;IACjB,IAAIuC,gBAAgB;QAClB,OAAA,WAAA,mLACE,MAAA,EAACf,sBAAAA;YACCU,UAAUA;YACVK,gBAAgBA;YAChBH,aAAaA;YACbC,cAAcA;sBAEbI;;IAGP;IAEA,OAAA,WAAA,mLAAO,MAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0]}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/app-router-announcer.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport type { FlightRouterState } from '../../server/app-render/types'\n\nconst ANNOUNCER_TYPE = 'next-route-announcer'\nconst ANNOUNCER_ID = '__next-route-announcer__'\n\nfunction getAnnouncerNode() {\n  const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0]\n  if (existingAnnouncer?.shadowRoot?.childNodes[0]) {\n    return existingAnnouncer.shadowRoot.childNodes[0] as HTMLElement\n  } else {\n    const container = document.createElement(ANNOUNCER_TYPE)\n    container.style.cssText = 'position:absolute'\n    const announcer = document.createElement('div')\n    announcer.ariaLive = 'assertive'\n    announcer.id = ANNOUNCER_ID\n    announcer.role = 'alert'\n    announcer.style.cssText =\n      'position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal'\n\n    // Use shadow DOM here to avoid any potential CSS bleed\n    const shadow = container.attachShadow({ mode: 'open' })\n    shadow.appendChild(announcer)\n    document.body.appendChild(container)\n    return announcer\n  }\n}\n\nexport function AppRouterAnnouncer({ tree }: { tree: FlightRouterState }) {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const announcer = getAnnouncerNode()\n    setPortalNode(announcer)\n    return () => {\n      const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0]\n      if (container?.isConnected) {\n        document.body.removeChild(container)\n      }\n    }\n  }, [])\n\n  const [routeAnnouncement, setRouteAnnouncement] = useState('')\n  const previousTitle = useRef<string | undefined>(undefined)\n\n  useEffect(() => {\n    let currentTitle = ''\n    if (document.title) {\n      currentTitle = document.title\n    } else {\n      const pageHeader = document.querySelector('h1')\n      if (pageHeader) {\n        currentTitle = pageHeader.innerText || pageHeader.textContent || ''\n      }\n    }\n\n    // Only announce the title change, but not for the first load because screen\n    // readers do that automatically.\n    if (\n      previousTitle.current !== undefined &&\n      previousTitle.current !== currentTitle\n    ) {\n      setRouteAnnouncement(currentTitle)\n    }\n    previousTitle.current = currentTitle\n  }, [tree])\n\n  return portalNode ? createPortal(routeAnnouncement, portalNode) : null\n}\n"], "names": ["useEffect", "useRef", "useState", "createPortal", "ANNOUNCER_TYPE", "ANNOUNCER_ID", "getAnnouncerNode", "existingAnnouncer", "document", "getElementsByName", "shadowRoot", "childNodes", "container", "createElement", "style", "cssText", "announcer", "ariaLive", "id", "role", "shadow", "attachShadow", "mode", "append<PERSON><PERSON><PERSON>", "body", "AppRouterAnnouncer", "tree", "portalNode", "setPortalNode", "getElementsByTagName", "isConnected", "<PERSON><PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "previousTitle", "undefined", "currentTitle", "title", "pageHeader", "querySelector", "innerText", "textContent", "current"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,QAAO;AACnD,SAASC,YAAY,QAAQ,YAAW;;;AAGxC,MAAMC,iBAAiB;AACvB,MAAMC,eAAe;AAErB,SAASC;QAEHC;IADJ,MAAMA,oBAAoBC,SAASC,iBAAiB,CAACL,eAAe,CAAC,EAAE;IACvE,IAAIG,qBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,gCAAAA,kBAAmBG,UAAU,KAAA,OAAA,KAAA,IAA7BH,8BAA+BI,UAAU,CAAC,EAAE,EAAE;QAChD,OAAOJ,kBAAkBG,UAAU,CAACC,UAAU,CAAC,EAAE;IACnD,OAAO;QACL,MAAMC,YAAYJ,SAASK,aAAa,CAACT;QACzCQ,UAAUE,KAAK,CAACC,OAAO,GAAG;QAC1B,MAAMC,YAAYR,SAASK,aAAa,CAAC;QACzCG,UAAUC,QAAQ,GAAG;QACrBD,UAAUE,EAAE,GAAGb;QACfW,UAAUG,IAAI,GAAG;QACjBH,UAAUF,KAAK,CAACC,OAAO,GACrB;QAEF,uDAAuD;QACvD,MAAMK,SAASR,UAAUS,YAAY,CAAC;YAAEC,MAAM;QAAO;QACrDF,OAAOG,WAAW,CAACP;QACnBR,SAASgB,IAAI,CAACD,WAAW,CAACX;QAC1B,OAAOI;IACT;AACF;AAEO,SAASS,mBAAmB,KAAqC;IAArC,IAAA,EAAEC,IAAI,EAA+B,GAArC;IACjC,MAAM,CAACC,YAAYC,cAAc,0KAAG1B,WAAAA,EAA6B;2KAEjEF,YAAAA,EAAU;QACR,MAAMgB,YAAYV;QAClBsB,cAAcZ;QACd,OAAO;YACL,MAAMJ,YAAYJ,SAASqB,oBAAoB,CAACzB,eAAe,CAAC,EAAE;YAClE,IAAIQ,aAAAA,OAAAA,KAAAA,IAAAA,UAAWkB,WAAW,EAAE;gBAC1BtB,SAASgB,IAAI,CAACO,WAAW,CAACnB;YAC5B;QACF;IACF,GAAG,EAAE;IAEL,MAAM,CAACoB,mBAAmBC,qBAAqB,0KAAG/B,WAAAA,EAAS;IAC3D,MAAMgC,iBAAgBjC,+KAAAA,EAA2BkC;2KAEjDnC,YAAAA,EAAU;QACR,IAAIoC,eAAe;QACnB,IAAI5B,SAAS6B,KAAK,EAAE;YAClBD,eAAe5B,SAAS6B,KAAK;QAC/B,OAAO;YACL,MAAMC,aAAa9B,SAAS+B,aAAa,CAAC;YAC1C,IAAID,YAAY;gBACdF,eAAeE,WAAWE,SAAS,IAAIF,WAAWG,WAAW,IAAI;YACnE;QACF;QAEA,4EAA4E;QAC5E,iCAAiC;QACjC,IACEP,cAAcQ,OAAO,KAAKP,aAC1BD,cAAcQ,OAAO,KAAKN,cAC1B;YACAH,qBAAqBG;QACvB;QACAF,cAAcQ,OAAO,GAAGN;IAC1B,GAAG;QAACV;KAAK;IAET,OAAOC,aAAAA,WAAAA,iLAAaxB,eAAAA,EAAa6B,mBAAmBL,cAAc;AACpE", "ignoreList": [0]}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "forbidden", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,qOAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,IAAIC,MACP;IAEL;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,IAAID,MAAML;IACtBM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0]}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "unauthorized", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,qOAAED,iCAAAA,GAA+B;AAE1C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,IAAIC,MACP;IAEL;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,IAAID,MAAML;IACtBM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0]}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1361, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["import { isDynamicUsageError } from '../../export/helpers/is-dynamic-usage-error'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\n/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicUsageError(error) ||\n    isPostpone(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["isDynamicUsageError", "isPostpone", "isBailoutToCSRError", "isNextRouterError", "unstable_rethrow", "error", "Error", "cause"], "mappings": ";;;AAAA,SAASA,mBAAmB,QAAQ,8CAA6C;AACjF,SAASC,UAAU,QAAQ,4CAA2C;AACtE,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,yBAAwB;;;;;AASnD,SAASC,iBAAiBC,KAAc;IAC7C,6MACEF,oBAAAA,EAAkBE,sNAClBH,sBAAAA,EAAoBG,WACpBL,6NAAAA,EAAoBK,gNACpBJ,aAAAA,EAAWI,QACX;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBC,SAAS,WAAWD,OAAO;QAC9CD,iBAAiBC,MAAME,KAAK;IAC9B;AACF", "ignoreList": [0]}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParamsError", "Error", "constructor", "ReadonlyURLSearchParams", "URLSearchParams", "append", "delete", "set", "sort", "redirect", "permanentRedirect", "RedirectType", "notFound", "forbidden", "unauthorized", "unstable_rethrow"], "mappings": "AAAA,cAAc;;;AACd,MAAMA,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMC,gCAAgCC;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,SAAS;QACP,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,MAAM;QACJ,MAAM,IAAIP;IACZ;IACA,wKAAwK,GACxKQ,OAAO;QACL,MAAM,IAAIR;IACZ;AACF", "ignoreList": [0]}}, {"offset": {"line": 1416, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1436, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n"], "names": ["BailoutToCSRError", "workAsyncStorage", "bailoutToClientRendering", "reason", "workStore", "getStore", "forceStatic", "isStaticGeneration"], "mappings": ";;;AAAA,SAASA,iBAAiB,QAAQ,+CAA8C;;AAChF,SAASC,gBAAgB,QAAQ,sDAAqD;;;AAE/E,SAASC,yBAAyBC,MAAc;IACrD,MAAMC,8RAAYH,mBAAAA,CAAiBI,QAAQ;IAE3C,IAAID,aAAAA,OAAAA,KAAAA,IAAAA,UAAWE,WAAW,EAAE;IAE5B,IAAIF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWG,kBAAkB,EAAE,MAAM,4MAAIP,oBAAAA,CAAkBG;AACjE", "ignoreList": [0]}}, {"offset": {"line": 1449, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\nimport { useDynamicRouteParams } from '../../server/app-render/dynamic-rendering'\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.tree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["useContext", "useMemo", "AppRouterContext", "LayoutRouterContext", "SearchParamsContext", "PathnameContext", "PathParamsContext", "getSegmentValue", "PAGE_SEGMENT_KEY", "DEFAULT_SEGMENT_KEY", "ReadonlyURLSearchParams", "useDynamicRouteParams", "useSearchParams", "searchParams", "readonlySearchParams", "window", "bailoutToClientRendering", "require", "usePathname", "ServerInsertedHTMLContext", "useServerInsertedHTML", "useRouter", "router", "Error", "useParams", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "startsWith", "push", "useSelectedLayoutSegments", "context", "useSelectedLayoutSegment", "selectedLayoutSegments", "length", "selectedLayoutSegment", "notFound", "forbidden", "unauthorized", "redirect", "permanentRedirect", "RedirectType", "unstable_rethrow"], "mappings": ";;;;;;;;AAGA,SAASA,UAAU,EAAEC,OAAO,QAAQ,QAAO;AAM3C,SACEG,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAG7D,SAASI,uBAAuB,QAAQ,4BAA2B;AACnE,SAASC,qBAAqB,QAAQ,4CAA2C;AAbjF,SACET,gBAAgB,EAChBC,mBAAmB,QAEd,qDAAoD;AAM3D,SAASI,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,2BAA0B;;;;;;;;AAyBzE,SAASG;IACd,MAAMC,sLAAeb,aAAAA,2MAAWI,sBAAAA;IAEhC,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMU,6LAAuBb,WAAAA,EAAQ;QACnC,IAAI,CAACY,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,yNAAIH,0BAAAA,CAAwBG;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOE,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,wBAAwB,EAAE,GAChCC,QAAQ;QACV,mEAAmE;QACnED,yBAAyB;IAC3B;IAEA,OAAOF;AACT;AAoBO,SAASI;uMACdP,wBAAAA,EAAsB;IAEtB,8EAA8E;IAC9E,0EAA0E;IAC1E,8KAAOX,aAAAA,2MAAWK,kBAAAA;AACpB;;AA2BO,SAASgB;IACd,MAAMC,gLAAStB,aAAAA,EAAWE,0NAAAA;IAC1B,IAAIoB,WAAW,MAAM;QACnB,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD;AACT;AAoBO,SAASE;uMACdb,wBAAAA,EAAsB;IAEtB,OAAOX,oLAAAA,2MAAWM,oBAAAA;AACpB;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASmB,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,UAAAA,KAAAA,GAAAA,QAAQ;IACRC,IAAAA,gBAAAA,KAAAA,GAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,KAAA,OAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,mPAAe7B,kBAAAA,EAAgB4B;IAEnC,IAAI,CAACC,gBAAgBA,aAAaC,UAAU,yKAAC7B,mBAAAA,GAAmB;QAC9D,OAAOqB;IACT;IAEAA,YAAYS,IAAI,CAACF;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASU,0BACdZ,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;uMAE3BhB,wBAAAA,EAAsB;IAEtB,MAAM6B,iLAAUxC,aAAAA,wMAAWG,uBAAAA;IAC3B,wFAAwF;IACxF,IAAI,CAACqC,SAAS,OAAO;IAErB,OAAOf,6BAA6Be,QAAQd,IAAI,EAAEC;AACpD;AAqBO,SAASc,yBACdd,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;uMAE3BhB,wBAAAA,EAAsB;IAEtB,MAAM+B,yBAAyBH,0BAA0BZ;IAEzD,IAAI,CAACe,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJjB,qBAAqB,aACjBe,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,kMAA0BnC,sBAAAA,GAC7B,OACAmC;AACN", "ignoreList": [0]}}, {"offset": {"line": 1563, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/redirect-boundary.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect } from 'react'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useRouter } from './navigation'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { RedirectType, isRedirectError } from './redirect-error'\n\ninterface RedirectBoundaryProps {\n  router: AppRouterInstance\n  children: React.ReactNode\n}\n\nfunction HandleRedirect({\n  redirect,\n  reset,\n  redirectType,\n}: {\n  redirect: string\n  redirectType: RedirectType\n  reset: () => void\n}) {\n  const router = useRouter()\n\n  useEffect(() => {\n    React.startTransition(() => {\n      if (redirectType === RedirectType.push) {\n        router.push(redirect, {})\n      } else {\n        router.replace(redirect, {})\n      }\n      reset()\n    })\n  }, [redirect, redirectType, reset, router])\n\n  return null\n}\n\nexport class RedirectErrorBoundary extends React.Component<\n  RedirectBoundaryProps,\n  { redirect: string | null; redirectType: RedirectType | null }\n> {\n  constructor(props: RedirectBoundaryProps) {\n    super(props)\n    this.state = { redirect: null, redirectType: null }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isRedirectError(error)) {\n      const url = getURLFromRedirectError(error)\n      const redirectType = getRedirectTypeFromError(error)\n      return { redirect: url, redirectType }\n    }\n    // Re-throw if error is not for redirect\n    throw error\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    const { redirect, redirectType } = this.state\n    if (redirect !== null && redirectType !== null) {\n      return (\n        <HandleRedirect\n          redirect={redirect}\n          redirectType={redirectType}\n          reset={() => this.setState({ redirect: null })}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function RedirectBoundary({ children }: { children: React.ReactNode }) {\n  const router = useRouter()\n  return (\n    <RedirectErrorBoundary router={router}>{children}</RedirectErrorBoundary>\n  )\n}\n"], "names": ["React", "useEffect", "useRouter", "getRedirectTypeFromError", "getURLFromRedirectError", "RedirectType", "isRedirectError", "HandleRedirect", "redirect", "reset", "redirectType", "router", "startTransition", "push", "replace", "RedirectErrorBoundary", "Component", "getDerivedStateFromError", "error", "url", "render", "state", "setState", "props", "children", "constructor", "RedirectBoundary"], "mappings": ";;;;;AACA,OAAOA,SAASC,SAAS,QAAQ,QAAO;AAExC,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,YAAY,EAAEC,eAAe,QAAQ,mBAAkB;;AALhE;;;;;;AAYA,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,QAAQ,EACRC,KAAK,EACLC,YAAY,EAKb,GARuB;IAStB,MAAMC,8MAAST,aAAAA;2KAEfD,YAAAA,EAAU;2KACRD,UAAAA,CAAMY,eAAe,CAAC;YACpB,IAAIF,0MAAiBL,eAAAA,CAAaQ,IAAI,EAAE;gBACtCF,OAAOE,IAAI,CAACL,UAAU,CAAC;YACzB,OAAO;gBACLG,OAAOG,OAAO,CAACN,UAAU,CAAC;YAC5B;YACAC;QACF;IACF,GAAG;QAACD;QAAUE;QAAcD;QAAOE;KAAO;IAE1C,OAAO;AACT;AAEO,MAAMI,iMAA8Bf,UAAAA,CAAMgB,SAAS;IASxD,OAAOC,yBAAyBC,KAAU,EAAE;QAC1C,iMAAIZ,kBAAAA,EAAgBY,QAAQ;YAC1B,MAAMC,MAAMf,8MAAAA,EAAwBc;YACpC,MAAMR,mMAAeP,2BAAAA,EAAyBe;YAC9C,OAAO;gBAAEV,UAAUW;gBAAKT;YAAa;QACvC;QACA,wCAAwC;QACxC,MAAMQ;IACR;IAEA,yIAAyI;IACzIE,SAA0B;QACxB,MAAM,EAAEZ,QAAQ,EAAEE,YAAY,EAAE,GAAG,IAAI,CAACW,KAAK;QAC7C,IAAIb,aAAa,QAAQE,iBAAiB,MAAM;YAC9C,OAAA,WAAA,mLACE,MAAA,EAACH,gBAAAA;gBACCC,UAAUA;gBACVE,cAAcA;gBACdD,OAAO,IAAM,IAAI,CAACa,QAAQ,CAAC;wBAAEd,UAAU;oBAAK;;QAGlD;QAEA,OAAO,IAAI,CAACe,KAAK,CAACC,QAAQ;IAC5B;IA7BAC,YAAYF,KAA4B,CAAE;QACxC,KAAK,CAACA;QACN,IAAI,CAACF,KAAK,GAAG;YAAEb,UAAU;YAAME,cAAc;QAAK;IACpD;AA2BF;AAEO,SAASgB,iBAAiB,KAA2C;IAA3C,IAAA,EAAEF,QAAQ,EAAiC,GAA3C;IAC/B,MAAMb,+MAAST,YAAAA;IACf,OAAA,WAAA,mLACE,MAAA,EAACa,uBAAAA;QAAsBJ,QAAQA;kBAASa;;AAE5C", "ignoreList": [0]}}, {"offset": {"line": 1664, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1670, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/unresolved-thenable.ts"], "sourcesContent": ["/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */\nexport const unresolvedThenable = {\n  then: () => {},\n} as PromiseLike<void>\n"], "names": ["unresolvedThenable", "then"], "mappings": "AAAA;;CAEC,GACD;;;AAAO,MAAMA,qBAAqB;IAChCC,MAAM,KAAO;AACf,EAAsB", "ignoreList": [0]}}, {"offset": {"line": 1678, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/dev-root-http-access-fallback-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\n\n// TODO: error on using forbidden and unauthorized in root layout\nexport function bailOnRootNotFound() {\n  throw new Error('notFound() is not allowed to use in root layout')\n}\n\nfunction NotAllowedRootHTTPFallbackError() {\n  bailOnRootNotFound()\n  return null\n}\n\nexport function DevRootHTTPAccessFallbackBoundary({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <HTTPAccessFallbackBoundary notFound={<NotAllowedRootHTTPFallbackError />}>\n      {children}\n    </HTTPAccessFallbackBoundary>\n  )\n}\n"], "names": ["React", "HTTPAccessFallbackBoundary", "bailOnRootNotFound", "Error", "NotAllowedRootHTTPFallbackError", "DevRootHTTPAccessFallbackBoundary", "children", "notFound"], "mappings": ";;;;;AAEA,OAAOA,WAAW,QAAO;AACzB,SAASC,0BAA0B,QAAQ,wCAAuC;AAHlF;;;;AAMO,SAASC;IACd,MAAM,IAAIC,MAAM;AAClB;AAEA,SAASC;IACPF;IACA,OAAO;AACT;AAEO,SAASG,kCAAkC,KAIjD;IAJiD,IAAA,EAChDC,QAAQ,EAGT,GAJiD;IAKhD,OAAA,WAAA,mLACE,MAAA,yNAACL,6BAAAA,EAAAA;QAA2BM,UAAAA,WAAAA,mLAAU,MAAA,EAACH,iCAAAA,CAAAA;kBACpCE;;AAGP", "ignoreList": [0]}}, {"offset": {"line": 1709, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/is-hydration-error.ts"], "sourcesContent": ["import isError from '../../lib/is-error'\n\nconst hydrationErrorRegex =\n  /hydration failed|while hydrating|content does not match|did not match|HTML didn't match/i\n\nconst reactUnifiedMismatchWarning = `Hydration failed because the server rendered HTML didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used`\n\nconst reactHydrationStartMessages = [\n  reactUnifiedMismatchWarning,\n  `A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:`,\n]\n\nconst reactHydrationErrorDocLink = 'https://react.dev/link/hydration-mismatch'\n\nexport const getDefaultHydrationErrorMessage = () => {\n  return reactUnifiedMismatchWarning\n}\n\nexport function isHydrationError(error: unknown): boolean {\n  return isError(error) && hydrationErrorRegex.test(error.message)\n}\n\nexport function isReactHydrationErrorMessage(msg: string): boolean {\n  return reactHydrationStartMessages.some((prefix) => msg.startsWith(prefix))\n}\n\nexport function getHydrationErrorStackInfo(rawMessage: string): {\n  message: string | null\n  link?: string\n  stack?: string\n  diff?: string\n} {\n  rawMessage = rawMessage.replace(/^Error: /, '')\n  if (!isReactHydrationErrorMessage(rawMessage)) {\n    return { message: null }\n  }\n  const firstLineBreak = rawMessage.indexOf('\\n')\n  rawMessage = rawMessage.slice(firstLineBreak + 1).trim()\n\n  const [message, trailing] = rawMessage.split(`${reactHydrationErrorDocLink}`)\n  const trimmedMessage = message.trim()\n  // React built-in hydration diff starts with a newline, checking if length is > 1\n  if (trailing && trailing.length > 1) {\n    const stacks: string[] = []\n    const diffs: string[] = []\n    trailing.split('\\n').forEach((line) => {\n      if (line.trim() === '') return\n      if (line.trim().startsWith('at ')) {\n        stacks.push(line)\n      } else {\n        diffs.push(line)\n      }\n    })\n\n    return {\n      message: trimmedMessage,\n      link: reactHydrationErrorDocLink,\n      diff: diffs.join('\\n'),\n      stack: stacks.join('\\n'),\n    }\n  } else {\n    return {\n      message: trimmedMessage,\n      link: reactHydrationErrorDocLink,\n      stack: trailing, // without hydration diff\n    }\n  }\n}\n"], "names": ["isError", "hydrationErrorRegex", "reactUnifiedMismatchWarning", "reactHydrationStartMessages", "reactHydrationErrorDocLink", "getDefaultHydrationErrorMessage", "isHydrationError", "error", "test", "message", "isReactHydrationErrorMessage", "msg", "some", "prefix", "startsWith", "getHydrationErrorStackInfo", "rawMessage", "replace", "firstLineBreak", "indexOf", "slice", "trim", "trailing", "split", "trimmedMessage", "length", "stacks", "diffs", "for<PERSON>ach", "line", "push", "link", "diff", "join", "stack"], "mappings": ";;;;;;AAAA,OAAOA,aAAa,qBAAoB;;AAExC,MAAMC,sBACJ;AAEF,MAAMC,8BAA+B;AAErC,MAAMC,8BAA8B;IAClCD;IACC;CACF;AAED,MAAME,6BAA6B;AAE5B,MAAMC,kCAAkC;IAC7C,OAAOH;AACT,EAAC;AAEM,SAASI,iBAAiBC,KAAc;IAC7C,QAAOP,+KAAAA,EAAQO,UAAUN,oBAAoBO,IAAI,CAACD,MAAME,OAAO;AACjE;AAEO,SAASC,6BAA6BC,GAAW;IACtD,OAAOR,4BAA4BS,IAAI,CAAC,CAACC,SAAWF,IAAIG,UAAU,CAACD;AACrE;AAEO,SAASE,2BAA2BC,UAAkB;IAM3DA,aAAaA,WAAWC,OAAO,CAAC,YAAY;IAC5C,IAAI,CAACP,6BAA6BM,aAAa;QAC7C,OAAO;YAAEP,SAAS;QAAK;IACzB;IACA,MAAMS,iBAAiBF,WAAWG,OAAO,CAAC;IAC1CH,aAAaA,WAAWI,KAAK,CAACF,iBAAiB,GAAGG,IAAI;IAEtD,MAAM,CAACZ,SAASa,SAAS,GAAGN,WAAWO,KAAK,CAAE,KAAEnB;IAChD,MAAMoB,iBAAiBf,QAAQY,IAAI;IACnC,iFAAiF;IACjF,IAAIC,YAAYA,SAASG,MAAM,GAAG,GAAG;QACnC,MAAMC,SAAmB,EAAE;QAC3B,MAAMC,QAAkB,EAAE;QAC1BL,SAASC,KAAK,CAAC,MAAMK,OAAO,CAAC,CAACC;YAC5B,IAAIA,KAAKR,IAAI,OAAO,IAAI;YACxB,IAAIQ,KAAKR,IAAI,GAAGP,UAAU,CAAC,QAAQ;gBACjCY,OAAOI,IAAI,CAACD;YACd,OAAO;gBACLF,MAAMG,IAAI,CAACD;YACb;QACF;QAEA,OAAO;YACLpB,SAASe;YACTO,MAAM3B;YACN4B,MAAML,MAAMM,IAAI,CAAC;YACjBC,OAAOR,OAAOO,IAAI,CAAC;QACrB;IACF,OAAO;QACL,OAAO;YACLxB,SAASe;YACTO,MAAM3B;YACN8B,OAAOZ;QACT;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 1776, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1782, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/app-router.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  useCallback,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  CacheNode,\n  AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  ACTION_HMR_REFRESH,\n  ACTION_NAVIGATE,\n  ACTION_PREFETCH,\n  ACTION_REFRESH,\n  ACTION_RESTORE,\n  ACTION_SERVER_PATCH,\n  PrefetchKind,\n} from './router-reducer/router-reducer-types'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  RouterChangeByServerResponse,\n  RouterNavigate,\n} from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { useReducer, useUnwrapState } from './use-reducer'\nimport { ErrorBoundary, type ErrorComponent } from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport { useServerActionDispatcher } from '../app-call-server'\nimport type { AppRouterActionQueue } from '../../shared/lib/router/action-queue'\nimport { prefetch as prefetchWithSegmentCache } from '../components/segment-cache/prefetch'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nfunction isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n  }\n}\n\n/**\n * Server response that only patches the cache and tree.\n */\nfunction useChangeByServerResponse(\n  dispatch: React.Dispatch<ReducerActions>\n): RouterChangeByServerResponse {\n  return useCallback(\n    ({ previousTree, serverResponse }) => {\n      startTransition(() => {\n        dispatch({\n          type: ACTION_SERVER_PATCH,\n          previousTree,\n          serverResponse,\n        })\n      })\n    },\n    [dispatch]\n  )\n}\n\nfunction useNavigate(dispatch: React.Dispatch<ReducerActions>): RouterNavigate {\n  return useCallback(\n    (href, navigateType, shouldScroll) => {\n      const url = new URL(addBasePath(href), location.href)\n\n      if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n        window.next.__pendingUrl = url\n      }\n\n      return dispatch({\n        type: ACTION_NAVIGATE,\n        url,\n        isExternalUrl: isExternalURL(url),\n        locationSearch: location.search,\n        shouldScroll: shouldScroll ?? true,\n        navigateType,\n        allowAliasing: true,\n      })\n    },\n    [dispatch]\n  )\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  //\n  // @ts-expect-error The second argument to `useDeferredValue` is only\n  // available in the experimental builds. When its disabled, it will always\n  // return `head`.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n}) {\n  const [state, dispatch] = useReducer(actionQueue)\n  const { canonicalUrl } = useUnwrapState(state)\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  const changeByServerResponse = useChangeByServerResponse(dispatch)\n  const navigate = useNavigate(dispatch)\n  useServerActionDispatcher(dispatch)\n\n  /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */\n  const appRouter = useMemo<AppRouterInstance>(() => {\n    const routerInstance: AppRouterInstance = {\n      back: () => window.history.back(),\n      forward: () => window.history.forward(),\n      prefetch:\n        process.env.__NEXT_PPR && process.env.__NEXT_CLIENT_SEGMENT_CACHE\n          ? // Unlike the old implementation, the Segment Cache doesn't store its\n            // data in the router reducer state; it writes into a global mutable\n            // cache. So we don't need to dispatch an action.\n            (href) => prefetchWithSegmentCache(href, actionQueue.state.nextUrl)\n          : (href, options) => {\n              // Use the old prefetch implementation.\n              const url = createPrefetchURL(href)\n              if (url !== null) {\n                startTransition(() => {\n                  dispatch({\n                    type: ACTION_PREFETCH,\n                    url,\n                    kind: options?.kind ?? PrefetchKind.FULL,\n                  })\n                })\n              }\n            },\n      replace: (href, options = {}) => {\n        startTransition(() => {\n          navigate(href, 'replace', options.scroll ?? true)\n        })\n      },\n      push: (href, options = {}) => {\n        startTransition(() => {\n          navigate(href, 'push', options.scroll ?? true)\n        })\n      },\n      refresh: () => {\n        startTransition(() => {\n          dispatch({\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          })\n        })\n      },\n      hmrRefresh: () => {\n        if (process.env.NODE_ENV !== 'development') {\n          throw new Error(\n            'hmrRefresh can only be used in development mode. Please use refresh instead.'\n          )\n        } else {\n          startTransition(() => {\n            dispatch({\n              type: ACTION_HMR_REFRESH,\n              origin: window.location.origin,\n            })\n          })\n        }\n      },\n    }\n\n    return routerInstance\n  }, [actionQueue, dispatch, navigate])\n\n  useEffect(() => {\n    // Exists for debugging purposes. Don't use in application code.\n    if (window.next) {\n      window.next.router = appRouter\n    }\n  }, [appRouter])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = useUnwrapState(state)\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: appRouter,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [appRouter, cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatch({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [dispatch])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        if (redirectType === RedirectType.push) {\n          appRouter.push(url, {})\n        } else {\n          appRouter.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [appRouter])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = useUnwrapState(state)\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatch({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatch({\n          type: ACTION_RESTORE,\n          url: new URL(window.location.href),\n          tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n        })\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [dispatch])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = useUnwrapState(state)\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      childNodes: cache.parallelRoutes,\n      tree,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n      loading: cache.loading,\n    }\n  }, [cache.parallelRoutes, tree, canonicalUrl, cache.loading])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      changeByServerResponse,\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [changeByServerResponse, tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = <HotReloader assetPrefix={assetPrefix}>{content}</HotReloader>\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={useUnwrapState(state)} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              <AppRouterContext.Provider value={appRouter}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [ErrorComponent, React.ReactNode | undefined]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      errorComponent={globalErrorComponent}\n      errorStyles={globalErrorStyles}\n    >\n      <Router actionQueue={actionQueue} assetPrefix={assetPrefix} />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n"], "names": ["React", "use", "useEffect", "useMemo", "useCallback", "startTransition", "useInsertionEffect", "useDeferredValue", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "ACTION_HMR_REFRESH", "ACTION_NAVIGATE", "ACTION_PREFETCH", "ACTION_REFRESH", "ACTION_RESTORE", "ACTION_SERVER_PATCH", "PrefetchKind", "createHrefFromUrl", "SearchParamsContext", "PathnameContext", "PathParamsContext", "useReducer", "useUnwrapState", "Error<PERSON>ou<PERSON><PERSON>", "isBot", "addBasePath", "AppRouterAnnouncer", "RedirectBoundary", "findHeadInCache", "unresolvedThenable", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "getSelectedParams", "useNavFailureHandler", "useServerActionDispatcher", "prefetch", "prefetchWithSegmentCache", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "RedirectType", "globalMutable", "isExternalURL", "url", "origin", "window", "location", "createPrefetchURL", "href", "navigator", "userAgent", "URL", "_", "Error", "process", "env", "NODE_ENV", "HistoryUpdater", "appRouterState", "__NEXT_APP_NAV_FAIL_HANDLING", "next", "__pendingUrl", "undefined", "tree", "pushRef", "canonicalUrl", "historyState", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "pushState", "replaceState", "createEmptyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "useChangeByServerResponse", "dispatch", "previousTree", "serverResponse", "type", "useNavigate", "navigateType", "shouldScroll", "isExternalUrl", "locationSearch", "search", "allowAliasing", "copyNextJsInternalHistoryState", "data", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "Router", "actionQueue", "assetPrefix", "searchParams", "pathname", "changeByServerResponse", "navigate", "appRouter", "routerInstance", "back", "forward", "__NEXT_PPR", "__NEXT_CLIENT_SEGMENT_CACHE", "nextUrl", "options", "kind", "FULL", "replace", "scroll", "push", "refresh", "hmrRefresh", "router", "cache", "prefetchCache", "nd", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "error", "reason", "preventDefault", "redirectType", "mpaNavigation", "assign", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "focusAndScrollRef", "matchingHead", "pathParams", "layoutRouterContext", "childNodes", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "DevRootHTTPAccessFallbackBoundary", "require", "HotReloader", "default", "RuntimeStyles", "Provider", "value", "AppRouter", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "errorComponent", "errorStyles", "runtimeStyles", "Set", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "size", "add", "for<PERSON>ach", "cb", "Promise", "resolve", "forceUpdate", "useState", "renderedStylesSize", "changed", "c", "delete", "dplId", "NEXT_DEPLOYMENT_ID", "map", "i", "link", "rel", "precedence"], "mappings": ";;;;;;AAEA,OAAOA,SACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,kBAAkB,EAClBC,gBAAgB,QACX,QAAO;AACd,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,yBAAyB,QACpB,qDAAoD;AAK3D,SACEC,kBAAkB,EAClBC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,QACP,wCAAuC;AAO9C,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,UAAU,EAAEC,cAAc,QAAQ,gBAAe;AAC1D,SAASC,aAAa,QAA6B,mBAAkB;AACrE,SAASC,KAAK,QAAQ,uCAAsC;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,eAAe,QAAQ,+CAA8C;AAC9E,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,wCAAuC;AAEzE,SAASC,oBAAoB,QAAQ,wBAAuB;AAC5D,SAASC,yBAAyB,QAAQ,qBAAoB;AAE9D,SAASC,YAAYC,wBAAwB,QAAQ,uCAAsC;AAC3F,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,eAAe,EAAEC,YAAY,QAAQ,mBAAkB;AA1DhE;;;;;;;;;;;;;;;;;;;;;;;AA4DA,MAAMC,gBAEF,CAAC;AAEL,SAASC,cAAcC,GAAQ;IAC7B,OAAOA,IAAIC,MAAM,KAAKC,OAAOC,QAAQ,CAACF,MAAM;AAC9C;AASO,SAASG,kBAAkBC,IAAY;IAC5C,kDAAkD;IAClD,IAAIxB,yMAAAA,EAAMqB,OAAOI,SAAS,CAACC,SAAS,GAAG;QACrC,OAAO;IACT;IAEA,IAAIP;IACJ,IAAI;QACFA,MAAM,IAAIQ,oLAAI1B,eAAAA,EAAYuB,OAAOH,OAAOC,QAAQ,CAACE,IAAI;IACvD,EAAE,OAAOI,GAAG;QACV,2EAA2E;QAC3E,kDAAkD;QAClD,MAAM,IAAIC,MACP,sBAAmBL,OAAK;IAE7B;IAEA,uEAAuE;IACvE,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,OAAO;IACT;;AAQF;AAEA,SAASC,eAAe,KAIvB;IAJuB,IAAA,EACtBC,cAAc,EAGf,GAJuB;2KAKtBrD,qBAAAA,EAAmB;QACjB,IAAIiD,QAAQC,GAAG,CAACI,uBAA8B,KAAF;;QAI5C;QAEA,MAAM,EAAEI,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGP;QACxC,MAAMQ,eAAe;YACnB,GAAIF,QAAQG,0BAA0B,GAAGtB,OAAOuB,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC;YAClE,yCAAyC;YACzC,kFAAkF;YAClF,iFAAiF;YACjFC,MAAM;YACNC,iCAAiCR;QACnC;QACA,IACEC,QAAQQ,WAAW,IACnB,+FAA+F;QAC/F,2DAA2D;sOAC3DvD,oBAAAA,EAAkB,IAAIkC,IAAIN,OAAOC,QAAQ,CAACE,IAAI,OAAOiB,cACrD;YACA,qJAAqJ;YACrJD,QAAQQ,WAAW,GAAG;YACtB3B,OAAOuB,OAAO,CAACK,SAAS,CAACP,cAAc,IAAID;QAC7C,OAAO;YACLpB,OAAOuB,OAAO,CAACM,YAAY,CAACR,cAAc,IAAID;QAChD;IACF,GAAG;QAACP;KAAe;IACnB,OAAO;AACT;AAEO,SAASiB;IACd,OAAO;QACLC,UAAU;QACVC,KAAK;QACLC,aAAa;QACbC,MAAM;QACNC,cAAc;QACdC,gBAAgB,IAAIC;QACpBC,SAAS;IACX;AACF;AAEA;;CAEC,GACD,SAASC,0BACPC,QAAwC;IAExC,8KAAOlF,cAAAA,EACL,CAAA;YAAC,EAAEmF,YAAY,EAAEC,cAAc,EAAE,GAAA;+KAC/BnF,kBAAAA,EAAgB;YACdiF,SAAS;gBACPG,MAAMzE,6OAAAA;gBACNuE;gBACAC;YACF;QACF;IACF,GACA;QAACF;KAAS;AAEd;AAEA,SAASI,YAAYJ,QAAwC;IAC3D,8KAAOlF,cAAAA,EACL,CAAC6C,MAAM0C,cAAcC;QACnB,MAAMhD,MAAM,IAAIQ,qLAAI1B,cAAAA,EAAYuB,OAAOF,SAASE,IAAI;QAEpD,IAAIM,QAAQC,GAAG,CAACI,uBAA8B,KAAF;;QAE5C;QAEA,OAAO0B,SAAS;YACdG,6NAAM7E,kBAAAA;YACNgC;YACAiD,eAAelD,cAAcC;YAC7BkD,gBAAgB/C,SAASgD,MAAM;YAC/BH,cAAcA,gBAAAA,OAAAA,eAAgB;YAC9BD;YACAK,eAAe;QACjB;IACF,GACA;QAACV;KAAS;AAEd;AAEA,SAASW,+BAA+BC,IAAS;IAC/C,IAAIA,QAAQ,MAAMA,OAAO,CAAC;IAC1B,MAAMC,eAAerD,OAAOuB,OAAO,CAACC,KAAK;IACzC,MAAMC,OAAO4B,gBAAAA,OAAAA,KAAAA,IAAAA,aAAc5B,IAAI;IAC/B,IAAIA,MAAM;QACR2B,KAAK3B,IAAI,GAAGA;IACd;IACA,MAAMC,kCACJ2B,gBAAAA,OAAAA,KAAAA,IAAAA,aAAc3B,+BAA+B;IAC/C,IAAIA,iCAAiC;QACnC0B,KAAK1B,+BAA+B,GAAGA;IACzC;IAEA,OAAO0B;AACT;AAEA,SAASE,KAAK,KAIb;IAJa,IAAA,EACZC,aAAa,EAGd,GAJa;IAKZ,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,MAAMrB,OAAOqB,kBAAkB,OAAOA,cAAcrB,IAAI,GAAG;IAC3D,MAAMC,eACJoB,kBAAkB,OAAOA,cAAcpB,YAAY,GAAG;IAExD,6EAA6E;IAC7E,MAAMqB,sBAAsBrB,iBAAiB,OAAOA,eAAeD;IAEnE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,EAAE;IACF,qEAAqE;IACrE,0EAA0E;IAC1E,iBAAiB;IACjB,8KAAOzE,mBAAAA,EAAiByE,MAAMsB;AAChC;AAEA;;CAEC,GACD,SAASC,OAAO,KAMf;IANe,IAAA,EACdC,WAAW,EACXC,WAAW,EAIZ,GANe;IAOd,MAAM,CAACnC,OAAOgB,SAAS,6LAAGhE,aAAAA,EAAWkF;IACrC,MAAM,EAAEtC,YAAY,EAAE,6LAAG3C,iBAAAA,EAAe+C;IACxC,mEAAmE;IACnE,MAAM,EAAEoC,YAAY,EAAEC,QAAQ,EAAE,0KAAGxG,UAAAA,EAAQ;QACzC,MAAMyC,MAAM,IAAIQ,IACdc,cACA,OAAOpB,WAAW,cAAc,aAAaA,OAAOC,QAAQ,CAACE,IAAI;QAGnE,OAAO;YACL,4DAA4D;YAC5DyD,cAAc9D,IAAI8D,YAAY;YAC9BC,2LAAU3E,cAAAA,EAAYY,IAAI+D,QAAQ,IAC9B5E,qMAAAA,EAAea,IAAI+D,QAAQ,IAC3B/D,IAAI+D,QAAQ;QAClB;IACF,GAAG;QAACzC;KAAa;IAEjB,MAAM0C,yBAAyBvB,0BAA0BC;IACzD,MAAMuB,WAAWnB,YAAYJ;uLAC7BnD,4BAAAA,EAA0BmD;IAE1B;;GAEC,GACD,MAAMwB,kLAAY3G,WAAAA,EAA2B;QAC3C,MAAM4G,iBAAoC;YACxCC,MAAM,IAAMlE,OAAOuB,OAAO,CAAC2C,IAAI;YAC/BC,SAAS,IAAMnE,OAAOuB,OAAO,CAAC4C,OAAO;YACrC7E,UACEmB,QAAQC,GAAG,CAAC0D,UAAU,IAAI3D,QAAQC,GAAG,CAAC2D,2BAA2B,GAE7D,SAGA,CAAClE,MAAMoE,oDAH6D;gBAIlE,uCAAuC;gBACvC,MAAMzE,MAAMI,kBAAkBC;gBAC9B,IAAIL,QAAQ,MAAM;2LAChBvC,kBAAAA,EAAgB;4BAINgH;wBAHR/B,SAAS;4BACPG,6NAAM5E,kBAAAA;4BACN+B;4BACA0E,MAAMD,CAAAA,gBAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASC,IAAI,KAAA,OAAbD,uOAAiBpG,eAAAA,CAAasG,IAAI;wBAC1C;oBACF;gBACF;YACF;YACNC,SAAS,CAACvE,MAAMoE;oBAAAA,YAAAA,KAAAA,GAAAA,UAAU,CAAC;iBACzBhH,wLAAAA,EAAgB;wBACYgH;oBAA1BR,SAAS5D,MAAM,WAAWoE,CAAAA,kBAAAA,QAAQI,MAAM,KAAA,OAAdJ,kBAAkB;gBAC9C;YACF;YACAK,MAAM,CAACzE,MAAMoE;oBAAAA,YAAAA,KAAAA,GAAAA,UAAU,CAAC;iBACtBhH,wLAAAA,EAAgB;wBACSgH;oBAAvBR,SAAS5D,MAAM,QAAQoE,CAAAA,kBAAAA,QAAQI,MAAM,KAAA,OAAdJ,kBAAkB;gBAC3C;YACF;YACAM,SAAS;uLACPtH,kBAAAA,EAAgB;oBACdiF,SAAS;wBACPG,6NAAM3E,iBAAAA;wBACN+B,QAAQC,OAAOC,QAAQ,CAACF,MAAM;oBAChC;gBACF;YACF;YACA+E,YAAY;gBACV,IAAIrE,QAAQC,GAAG,CAACC,QAAQ,KAAK,UAAe;;gBAI5C,OAAO;oBACLpD,yLAAAA,EAAgB;wBACdiF,SAAS;4BACPG,6NAAM9E,qBAAAA;4BACNkC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;wBAChC;oBACF;gBACF;YACF;QACF;QAEA,OAAOkE;IACT,GAAG;QAACP;QAAalB;QAAUuB;KAAS;2KAEpC3G,YAAAA,EAAU;QACR,gEAAgE;QAChE,IAAI4C,OAAOe,IAAI,EAAE;YACff,OAAOe,IAAI,CAACgE,MAAM,GAAGf;QACvB;IACF,GAAG;QAACA;KAAU;IAEd,IAAIvD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,sDAAsD;QACtD,MAAM,EAAEqE,KAAK,EAAEC,aAAa,EAAE/D,IAAI,EAAE,6LAAGzC,iBAAAA,EAAe+C;QAEtD,4FAA4F;QAC5F,sDAAsD;+KACtDpE,YAAAA,EAAU;YACR,0CAA0C;YAC1C,uGAAuG;YACvG,mCAAmC;YACnC4C,OAAOkF,EAAE,GAAG;gBACVH,QAAQf;gBACRgB;gBACAC;gBACA/D;YACF;QACF,GAAG;YAAC8C;YAAWgB;YAAOC;YAAe/D;SAAK;IAC5C;2KAEA9D,YAAAA,EAAU;QACR,0DAA0D;QAC1D,uFAAuF;QACvF,qEAAqE;QACrE,wGAAwG;QACxG,SAAS+H,eAAeC,KAA0B;gBAG7CpF;YAFH,IACE,CAACoF,MAAMC,SAAS,IAChB,CAAA,CAAA,CAACrF,wBAAAA,OAAOuB,OAAO,CAACC,KAAK,KAAA,OAAA,KAAA,IAApBxB,sBAAsB0B,+BAA+B,GACtD;gBACA;YACF;YAEA,uGAAuG;YACvG,qHAAqH;YACrH,8BAA8B;YAC9B9B,cAAc0F,cAAc,GAAGrE;YAE/BuB,SAAS;gBACPG,6NAAM1E,iBAAAA;gBACN6B,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACE,IAAI;gBACjCe,MAAMlB,OAAOuB,OAAO,CAACC,KAAK,CAACE,+BAA+B;YAC5D;QACF;QAEA1B,OAAOuF,gBAAgB,CAAC,YAAYJ;QAEpC,OAAO;YACLnF,OAAOwF,mBAAmB,CAAC,YAAYL;QACzC;IACF,GAAG;QAAC3C;KAAS;IAEbpF,mLAAAA,EAAU;QACR,iFAAiF;QACjF,wCAAwC;QACxC,SAASqI,wBACPL,KAAyC;YAEzC,MAAMM,QAAQ,YAAYN,QAAQA,MAAMO,MAAM,GAAGP,MAAMM,KAAK;YAC5D,iMAAIhG,kBAAAA,EAAgBgG,QAAQ;gBAC1BN,MAAMQ,cAAc;gBACpB,MAAM9F,OAAML,6MAAAA,EAAwBiG;gBACpC,MAAMG,mMAAerG,2BAAAA,EAAyBkG;gBAC9C,IAAIG,0MAAiBlG,eAAAA,CAAaiF,IAAI,EAAE;oBACtCZ,UAAUY,IAAI,CAAC9E,KAAK,CAAC;gBACvB,OAAO;oBACLkE,UAAUU,OAAO,CAAC5E,KAAK,CAAC;gBAC1B;YACF;QACF;QACAE,OAAOuF,gBAAgB,CAAC,SAASE;QACjCzF,OAAOuF,gBAAgB,CAAC,sBAAsBE;QAE9C,OAAO;YACLzF,OAAOwF,mBAAmB,CAAC,SAASC;YACpCzF,OAAOwF,mBAAmB,CAAC,sBAAsBC;QACnD;IACF,GAAG;QAACzB;KAAU;IAEd,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,YAAY;IACZ,EAAE;IACF,sEAAsE;IACtE,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,MAAM,EAAE7C,OAAO,EAAE,6LAAG1C,iBAAAA,EAAe+C;IACnC,IAAIL,QAAQ2E,aAAa,EAAE;QACzB,gHAAgH;QAChH,IAAIlG,cAAc0F,cAAc,KAAKlE,cAAc;YACjD,MAAMnB,YAAWD,OAAOC,QAAQ;YAChC,IAAIkB,QAAQQ,WAAW,EAAE;gBACvB1B,UAAS8F,MAAM,CAAC3E;YAClB,OAAO;gBACLnB,UAASyE,OAAO,CAACtD;YACnB;YAEAxB,cAAc0F,cAAc,GAAGlE;QACjC;QACA,mEAAmE;QACnE,4EAA4E;QAC5E,+BAA+B;+KAC/BjE,MAAAA,gMAAI6B,qBAAAA;IACN;IAEA5B,mLAAAA,EAAU;QACR,MAAM4I,oBAAoBhG,OAAOuB,OAAO,CAACK,SAAS,CAACqE,IAAI,CAACjG,OAAOuB,OAAO;QACtE,MAAM2E,uBAAuBlG,OAAOuB,OAAO,CAACM,YAAY,CAACoE,IAAI,CAC3DjG,OAAOuB,OAAO;QAGhB,wJAAwJ;QACxJ,MAAM4E,iCAAiC,CACrCrG;gBAIEE;YAFF,MAAMG,OAAOH,OAAOC,QAAQ,CAACE,IAAI;YACjC,MAAMe,OAAAA,CACJlB,wBAAAA,OAAOuB,OAAO,CAACC,KAAK,KAAA,OAAA,KAAA,IAApBxB,sBAAsB0B,+BAA+B;mLAEvDnE,kBAAAA,EAAgB;gBACdiF,SAAS;oBACPG,6NAAM1E,iBAAAA;oBACN6B,KAAK,IAAIQ,IAAIR,OAAAA,OAAAA,MAAOK,MAAMA;oBAC1Be;gBACF;YACF;QACF;QAEA;;;;KAIC,GACDlB,OAAOuB,OAAO,CAACK,SAAS,GAAG,SAASA,UAClCwB,IAAS,EACTgD,OAAe,EACftG,GAAyB;YAEzB,qEAAqE;YACrE,IAAIsD,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAM3B,IAAI,KAAA,CAAI2B,QAAAA,OAAAA,KAAAA,IAAAA,KAAMiD,EAAE,GAAE;gBAC1B,OAAOL,kBAAkB5C,MAAMgD,SAAStG;YAC1C;YAEAsD,OAAOD,+BAA+BC;YAEtC,IAAItD,KAAK;gBACPqG,+BAA+BrG;YACjC;YAEA,OAAOkG,kBAAkB5C,MAAMgD,SAAStG;QAC1C;QAEA;;;;KAIC,GACDE,OAAOuB,OAAO,CAACM,YAAY,GAAG,SAASA,aACrCuB,IAAS,EACTgD,OAAe,EACftG,GAAyB;YAEzB,qEAAqE;YACrE,IAAIsD,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAM3B,IAAI,KAAA,CAAI2B,QAAAA,OAAAA,KAAAA,IAAAA,KAAMiD,EAAE,GAAE;gBAC1B,OAAOH,qBAAqB9C,MAAMgD,SAAStG;YAC7C;YACAsD,OAAOD,+BAA+BC;YAEtC,IAAItD,KAAK;gBACPqG,+BAA+BrG;YACjC;YACA,OAAOoG,qBAAqB9C,MAAMgD,SAAStG;QAC7C;QAEA;;;;KAIC,GACD,MAAMwG,aAAa,CAAClB;YAClB,IAAI,CAACA,MAAM5D,KAAK,EAAE;gBAChB,+IAA+I;gBAC/I;YACF;YAEA,6EAA6E;YAC7E,IAAI,CAAC4D,MAAM5D,KAAK,CAACC,IAAI,EAAE;gBACrBzB,OAAOC,QAAQ,CAACsG,MAAM;gBACtB;YACF;YAEA,gHAAgH;YAChH,oEAAoE;YACpEhJ,yLAAAA,EAAgB;gBACdiF,SAAS;oBACPG,6NAAM1E,iBAAAA;oBACN6B,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACE,IAAI;oBACjCe,MAAMkE,MAAM5D,KAAK,CAACE,+BAA+B;gBACnD;YACF;QACF;QAEA,8CAA8C;QAC9C1B,OAAOuF,gBAAgB,CAAC,YAAYe;QACpC,OAAO;YACLtG,OAAOuB,OAAO,CAACK,SAAS,GAAGoE;YAC3BhG,OAAOuB,OAAO,CAACM,YAAY,GAAGqE;YAC9BlG,OAAOwF,mBAAmB,CAAC,YAAYc;QACzC;IACF,GAAG;QAAC9D;KAAS;IAEb,MAAM,EAAEwC,KAAK,EAAE9D,IAAI,EAAEoD,OAAO,EAAEkC,iBAAiB,EAAE,6LAAG/H,iBAAAA,EAAe+C;IAEnE,MAAMiF,gBAAepJ,gLAAAA,EAAQ;QAC3B,+OAAO0B,kBAAAA,EAAgBiG,OAAO9D,IAAI,CAAC,EAAE;IACvC,GAAG;QAAC8D;QAAO9D;KAAK;IAEhB,yCAAyC;IACzC,MAAMwF,oLAAarJ,UAAAA,EAAQ;QACzB,iOAAO8B,qBAAAA,EAAkB+B;IAC3B,GAAG;QAACA;KAAK;IAET,MAAMyF,6LAAsBtJ,UAAAA,EAAQ;QAClC,OAAO;YACLuJ,YAAY5B,MAAM5C,cAAc;YAChClB;YACA,6BAA6B;YAC7B,8EAA8E;YAC9EpB,KAAKsB;YACLkB,SAAS0C,MAAM1C,OAAO;QACxB;IACF,GAAG;QAAC0C,MAAM5C,cAAc;QAAElB;QAAME;QAAc4D,MAAM1C,OAAO;KAAC;IAE5D,MAAMuE,4BAA4BxJ,iLAAAA,EAAQ;QACxC,OAAO;YACLyG;YACA5C;YACAsF;YACAlC;QACF;IACF,GAAG;QAACR;QAAwB5C;QAAMsF;QAAmBlC;KAAQ;IAE7D,IAAIpC;IACJ,IAAIuE,iBAAiB,MAAM;QACzB,0DAA0D;QAC1D,0EAA0E;QAC1E,oEAAoE;QACpE,EAAE;QACF,wEAAwE;QACxE,uBAAuB;QACvB,MAAM,CAAClD,eAAeuD,QAAQ,GAAGL;QACjCvE,OAAAA,WAAAA,mLAAO,MAAA,EAACoB,MAAAA;YAAmBC,eAAeA;WAAxBuD;IACpB,OAAO;QACL5E,OAAO;IACT;IAEA,IAAI6E,UAAAA,WAAAA,mLACF,OAAA,8LAACjI,mBAAAA,EAAAA;;YACEoD;YACA8C,MAAMhD,GAAG;0MACV,MAAA,oMAACnD,qBAAAA,EAAAA;gBAAmBqC,MAAMA;;;;IAI9B,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAI,OAAOX,WAAW,aAAa;YACjC,MAAM,EAAEgH,iCAAiC,EAAE,GACzCC,QAAQ;YACVF,UAAAA,WAAAA,mLACE,MAAA,EAACC,mCAAAA;0BACED;;QAGP;QACA,MAAMG,cACJD,QAAQ,iJAA+CE,OAAO;QAEhEJ,UAAAA,WAAAA,mLAAU,MAAA,EAACG,aAAAA;YAAYvD,aAAaA;sBAAcoD;;IACpD;IAEA,OAAA,WAAA,kLACE,QAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;0MACE,MAAA,EAACnG,gBAAAA;gBAAeC,0MAAgBpC,iBAAAA,EAAe+C;;0MAC/C,MAAA,EAAC4F,eAAAA,CAAAA;0MACD,MAAA,EAAC7I,6NAAAA,CAAkB8I,QAAQ,EAAA;gBAACC,OAAOZ;0BACjC,WAAA,mLAAA,MAAA,2MAACpI,kBAAAA,CAAgB+I,QAAQ,EAAA;oBAACC,OAAOzD;8BAC/B,WAAA,IAAA,qLAAA,2MAACxF,sBAAAA,CAAoBgJ,QAAQ,EAAA;wBAACC,OAAO1D;kCACnC,WAAA,mLAAA,MAAA,yMAAChG,4BAAAA,CAA0ByJ,QAAQ,EAAA;4BACjCC,OAAOT;sCAEP,WAAA,mLAAA,MAAA,yMAACnJ,mBAAAA,CAAiB2J,QAAQ,EAAA;gCAACC,OAAOtD;0CAChC,WAAA,IAAA,qLAAA,yMAACrG,sBAAAA,CAAoB0J,QAAQ,EAAA;oCAACC,OAAOX;8CAClCI;;;;;;;;;AASnB;AAEe,SAASQ,UAAU,KAQjC;IARiC,IAAA,EAChC7D,WAAW,EACX8D,+BAA+B,CAACC,sBAAsBC,kBAAkB,EACxE/D,WAAW,EAKZ,GARiC;yMAShCvE,uBAAAA;IAEA,OAAA,WAAA,mLACE,MAAA,2LAACV,gBAAAA,EAAAA;QACCiJ,gBAAgBF;QAChBG,aAAaF;kBAEb,WAAA,OAAA,kLAAA,EAACjE,QAAAA;YAAOC,aAAaA;YAAaC,aAAaA;;;AAGrD;AAEA,MAAMkE,gBAAgB,IAAIC;AAC1B,IAAIC,sBAAsB,IAAID;AAE9BE,WAAWC,eAAe,GAAG,SAAU9H,IAAY;IACjD,IAAI+H,MAAML,cAAcM,IAAI;IAC5BN,cAAcO,GAAG,CAACjI;IAClB,IAAI0H,cAAcM,IAAI,KAAKD,KAAK;QAC9BH,oBAAoBM,OAAO,CAAC,CAACC,KAAOA;IACtC;IACA,4CAA4C;IAC5C,gFAAgF;IAChF,OAAOC,QAAQC,OAAO;AACxB;AAEA,SAASpB;IACP,MAAM,GAAGqB,YAAY,sKAAGvL,UAAAA,CAAMwL,QAAQ,CAAC;IACvC,MAAMC,qBAAqBd,cAAcM,IAAI;2KAC7C/K,YAAAA,EAAU;QACR,MAAMwL,UAAU,IAAMH,YAAY,CAACI,IAAMA,IAAI;QAC7Cd,oBAAoBK,GAAG,CAACQ;QACxB,IAAID,uBAAuBd,cAAcM,IAAI,EAAE;YAC7CS;QACF;QACA,OAAO;YACLb,oBAAoBe,MAAM,CAACF;QAC7B;IACF,GAAG;QAACD;QAAoBF;KAAY;IAEpC,MAAMM,QAAQtI,QAAQC,GAAG,CAACsI,kBAAkB,GACxC,AAAC,UAAOvI,QAAQC,GAAG,CAACsI,kBAAkB,IACtC;IACJ,OAAO;WAAInB;KAAc,CAACoB,GAAG,CAAC,CAAC9I,MAAM+I,IAAAA,WAAAA,mLACnC,MAAA,EAACC,QAAAA;YAECC,KAAI;YACJjJ,MAAO,KAAEA,OAAO4I;YAChB,aAAa;YACbM,YAAW;WAJNH;AAUX", "ignoreList": [0]}}, {"offset": {"line": 2387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2393, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/promise-queue.ts"], "sourcesContent": ["/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/\nexport class PromiseQueue {\n  #maxConcurrency: number\n  #runningCount: number\n  #queue: Array<{\n    promiseFn: Promise<any>\n    task: () => void\n  }>\n\n  constructor(maxConcurrency = 5) {\n    this.#maxConcurrency = maxConcurrency\n    this.#runningCount = 0\n    this.#queue = []\n  }\n\n  enqueue<T>(promiseFn: () => Promise<T>): Promise<T> {\n    let taskResolve: (value: T | PromiseLike<T>) => void\n    let taskReject: (reason?: any) => void\n\n    const taskPromise = new Promise((resolve, reject) => {\n      taskResolve = resolve\n      taskReject = reject\n    }) as Promise<T>\n\n    const task = async () => {\n      try {\n        this.#runningCount++\n        const result = await promiseFn()\n        taskResolve(result)\n      } catch (error) {\n        taskReject(error)\n      } finally {\n        this.#runningCount--\n        this.#processNext()\n      }\n    }\n\n    const enqueueResult = { promiseFn: taskPromise, task }\n    // wonder if we should take a LIFO approach here\n    this.#queue.push(enqueueResult)\n    this.#processNext()\n\n    return taskPromise\n  }\n\n  bump(promiseFn: Promise<any>) {\n    const index = this.#queue.findIndex((item) => item.promiseFn === promiseFn)\n\n    if (index > -1) {\n      const bumpedItem = this.#queue.splice(index, 1)[0]\n      this.#queue.unshift(bumpedItem)\n      this.#processNext(true)\n    }\n  }\n\n  #processNext(forced = false) {\n    if (\n      (this.#runningCount < this.#maxConcurrency || forced) &&\n      this.#queue.length > 0\n    ) {\n      this.#queue.shift()?.task()\n    }\n  }\n}\n"], "names": ["PromiseQueue", "enqueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "Promise", "resolve", "reject", "task", "result", "error", "enqueueResult", "push", "bump", "index", "findIndex", "item", "bumpedItem", "splice", "unshift", "constructor", "maxConcurrency", "forced", "length", "shift"], "mappings": "AAAA;;;;;AAKA;;;;AAAA;;;IAEE,kBAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,oBACA,gBAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,kBACA,SAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA,WAmDA,eAAA,WAAA,GAAA,CAAA,GAAA,iLAAA,CAAA,IAAA,EAAA;AAtDK,MAAMA;IAcXC,QAAWC,SAA2B,EAAc;QAClD,IAAIC;QACJ,IAAIC;QAEJ,MAAMC,cAAc,IAAIC,QAAQ,CAACC,SAASC;YACxCL,cAAcI;YACdH,aAAaI;QACf;QAEA,MAAMC,OAAO;YACX,IAAI;gBACF,2LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA;gBACL,MAAMC,SAAS,MAAMR;gBACrBC,YAAYO;YACd,EAAE,OAAOC,OAAO;gBACdP,WAAWO;YACb,SAAU;gBACR,2LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA;uMACL,IAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA;YACP;QACF;QAEA,MAAMC,gBAAgB;YAAEV,WAAWG;YAAaI;QAAK;QACrD,gDAAgD;+LAChD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOI,IAAI,CAACD;+LACjB,IAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA;QAEL,OAAOP;IACT;IAEAS,KAAKZ,SAAuB,EAAE;QAC5B,MAAMa,+LAAQ,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,SAAS,CAAC,CAACC,OAASA,KAAKf,SAAS,KAAKA;QAEjE,IAAIa,QAAQ,CAAC,GAAG;YACd,MAAMG,oMAAa,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,MAAM,CAACJ,OAAO,EAAE,CAAC,EAAE;mMAClD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOK,OAAO,CAACF;aACpB,0LAAA,EAAA,IAAI,EAAC,aAAA,CAAA,aAAA,CAAa;QACpB;IACF;IA5CAG,YAAYC,iBAAiB,CAAC,CAAE;QA8ChC,OAAA,cAAA,CAAA,IAAA,EAAA,cAAA;mBAAA;;QArDA,OAAA,cAAA,CAAA,IAAA,EAAA,iBAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,eAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,QAAA;;mBAAA,KAAA;;+LAME,IAAA,EAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,GAAkBA;SACvB,0LAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA,GAAgB;+LACrB,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,GAAS,EAAE;IAClB;AAkDF;AARE,SAAA,YAAaC,MAAc;IAAdA,IAAAA,WAAAA,KAAAA,GAAAA,SAAS;IACpB,IACG,wLAAA,IAAA,EAAA,IAAI,EAAC,cAAA,CAAA,cAAA,GAAgB,2LAAA,EAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,IAAmBA,MAAK,4LACnD,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,MAAM,GAAG,GACrB;YACA;SAAA,sOAAA,IAAA,EAAA,IAAI,EAAC,OAAA,CAAA,OAAA,CAAOC,KAAK,EAAA,KAAA,OAAA,KAAA,IAAjB,6CAAqBhB,IAAI;IAC3B;AACF", "ignoreList": [0]}}, {"offset": {"line": 2471, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2476, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/error-boundary.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2478, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2483, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/error-boundary.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/error-boundary.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2491, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  reset: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError({ error }: { error: any }) {\n  const store = workAsyncStorage.getStore()\n  if (store?.isRevalidate || store?.isStaticGeneration) {\n    console.error(error)\n    throw error\n  }\n\n  return null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    if (this.state.error) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function GlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              {`Application error: a ${\n                digest ? 'server' : 'client'\n              }-side exception has occurred (see the ${\n                digest ? 'server logs' : 'browser console'\n              } for more information).`}\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default GlobalError\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useUntrackedPathname", "isNextRouterError", "handleHardNavError", "workAsyncStorage", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "HandleISRError", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "getDerivedStateFromProps", "props", "state", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "previousPathname", "pathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "GlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "p", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 2495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2500, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/layout-router.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2502, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2507, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/layout-router.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/layout-router.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2509, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2515, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  ChildSegmentMap,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport type { FocusAndScrollRef } from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { handleSmoothScroll } from '../../shared/lib/router/utils/handle-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      handleSmoothScroll(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  parallelRouterKey,\n  url,\n  childNodes,\n  segmentPath,\n  tree,\n  // TODO-APP: implement `<Offscreen>` when available.\n  // isActive,\n  cacheKey,\n}: {\n  parallelRouterKey: string\n  url: string\n  childNodes: ChildSegmentMap\n  segmentPath: FlightSegmentPath\n  tree: FlightRouterState\n  isActive: boolean\n  cacheKey: ReturnType<typeof createRouterCacheKey>\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { changeByServerResponse, tree: fullTree } = context\n\n  // Read segment path from the parallel router cache node.\n  let childNode = childNodes.get(cacheKey)\n\n  // When data is not available during rendering client-side we need to fetch\n  // it from the server.\n  if (childNode === undefined) {\n    const newLazyCacheNode: LazyCacheNode = {\n      lazyData: null,\n      rsc: null,\n      prefetchRsc: null,\n      head: null,\n      prefetchHead: null,\n      parallelRoutes: new Map(),\n      loading: null,\n    }\n\n    /**\n     * Flight data fetch kicked off during render and put into the cache.\n     */\n    childNode = newLazyCacheNode\n    childNodes.set(cacheKey, newLazyCacheNode)\n  }\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    childNode.prefetchRsc !== null ? childNode.prefetchRsc : childNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  //\n  // @ts-expect-error The second argument to `useDeferredValue` is only\n  // available in the experimental builds. When its disabled, it will always\n  // return `rsc`.\n  const rsc: any = useDeferredValue(childNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = childNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      childNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          changeByServerResponse({\n            previousTree: fullTree,\n            serverResponse,\n          })\n        })\n\n        return serverResponse\n      })\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        tree: tree[1][parallelRouterKey],\n        childNodes: childNode.parallelRoutes,\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n        loading: childNode.loading,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  segmentPath,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n}: {\n  parallelRouterKey: string\n  segmentPath: FlightSegmentPath\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { childNodes, tree, url, loading } = context\n\n  // Get the current parallelRouter cache node\n  let childNodesForParallelRouter = childNodes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!childNodesForParallelRouter) {\n    childNodesForParallelRouter = new Map()\n    childNodes.set(parallelRouterKey, childNodesForParallelRouter)\n  }\n\n  // Get the active segment in the tree\n  // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n  const treeSegment = tree[1][parallelRouterKey][0]\n\n  // If segment is an array it's a dynamic route and we want to read the dynamic route value as the segment to get from the cache.\n  const currentChildSegmentValue = getSegmentValue(treeSegment)\n\n  /**\n   * Decides which segments to keep rendering, all segments that are not active will be wrapped in `<Offscreen>`.\n   */\n  // TODO-APP: Add handling of `<Offscreen>` when it's available.\n  const preservedSegments: Segment[] = [treeSegment]\n\n  return (\n    <>\n      {preservedSegments.map((preservedSegment) => {\n        const preservedSegmentValue = getSegmentValue(preservedSegment)\n        const cacheKey = createRouterCacheKey(preservedSegment)\n\n        return (\n          /*\n            - Error boundary\n              - Only renders error boundary if error component is provided.\n              - Rendered for each segment to ensure they have their own error state.\n            - Loading boundary\n              - Only renders suspense boundary if loading components is provided.\n              - Rendered for each segment to ensure they have their own loading state.\n              - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n          */\n          <TemplateContext.Provider\n            key={createRouterCacheKey(preservedSegment, true)}\n            value={\n              <ScrollAndFocusHandler segmentPath={segmentPath}>\n                <ErrorBoundary\n                  errorComponent={error}\n                  errorStyles={errorStyles}\n                  errorScripts={errorScripts}\n                >\n                  <LoadingBoundary loading={loading}>\n                    <HTTPAccessFallbackBoundary\n                      notFound={notFound}\n                      forbidden={forbidden}\n                      unauthorized={unauthorized}\n                    >\n                      <RedirectBoundary>\n                        <InnerLayoutRouter\n                          parallelRouterKey={parallelRouterKey}\n                          url={url}\n                          tree={tree}\n                          childNodes={childNodesForParallelRouter!}\n                          segmentPath={segmentPath}\n                          cacheKey={cacheKey}\n                          isActive={\n                            currentChildSegmentValue === preservedSegmentValue\n                          }\n                        />\n                      </RedirectBoundary>\n                    </HTTPAccessFallbackBoundary>\n                  </LoadingBoundary>\n                </ErrorBoundary>\n              </ScrollAndFocusHandler>\n            }\n          >\n            {templateStyles}\n            {templateScripts}\n            {template}\n          </TemplateContext.Provider>\n        )\n      })}\n    </>\n  )\n}\n"], "names": ["React", "useContext", "use", "startTransition", "Suspense", "useDeferredValue", "ReactDOM", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "fetchServerResponse", "unresolvedThenable", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "handleSmoothScroll", "RedirectBoundary", "HTTPAccessFallbackBoundary", "getSegmentValue", "createRouterCache<PERSON>ey", "hasInterceptionRouteInCurrentTree", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "process", "env", "NODE_ENV", "console", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "Error", "InnerLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "url", "childNodes", "tree", "cache<PERSON>ey", "changeByServerResponse", "fullTree", "childNode", "get", "newLazyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "set", "resolvedPrefetchRsc", "resolvedRsc", "then", "refetchTree", "includeNextUrl", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "previousTree", "subtree", "Provider", "value", "LoadingBoundary", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "fallback", "OuterLayoutRouter", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "childNodesForParallelRouter", "treeSegment", "currentChildSegmentValue", "preservedSegments", "map", "preservedSegment", "preservedSegmentValue", "errorComponent", "isActive"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 2519, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2524, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2531, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2533, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2539, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "TemplateContext", "RenderFromTemplateContext", "children"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 2543, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/client-page.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2550, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2555, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/client-page.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/client-page.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2557, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2563, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../../server/request/search-params.browser') as typeof import('../../server/request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../../server/request/params.browser') as typeof import('../../server/request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["InvariantError", "ClientPageRoot", "Component", "searchParams", "params", "promises", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 2567, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2572, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/client-segment.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2574, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2579, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/esm/client/components/client-segment.js/proxy.js"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_export_namespace__(createClientModuleProxy(\"[project]/node_modules/next/dist/esm/client/components/client-segment.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,+BAA+B,wBAAwB", "ignoreList": [0]}}, {"offset": {"line": 2581, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2587, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when dynamicIO is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../../server/request/params.browser') as typeof import('../../server/request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["InvariantError", "ClientSegmentRoot", "Component", "slots", "params", "promise", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 2591, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
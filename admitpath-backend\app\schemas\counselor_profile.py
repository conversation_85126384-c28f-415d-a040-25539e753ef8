from pydantic import BaseModel, <PERSON>, field_validator
from datetime import datetime
from typing import Optional, List


class PersonalInfoCreate(BaseModel):
    nationality: str
    country_of_residence: str
    gender: str
    date_of_birth: datetime
    bio: Optional[str] = Field(None, max_length=1000)  # Optional bio with max length
    tagline: Optional[str] = Field(None, max_length=100)  # Optional tagline with max length

class PersonalInfoResponse(BaseModel):
    id: int
    counselor_id: int
    nationality: str
    country_of_residence: str
    gender: str
    date_of_birth: datetime
    first_name: str
    last_name: str
    bio: Optional[str] = None
    tagline: Optional[str] = None
    profile_image_url: Optional[str] = None

    class Config:
        from_attributes = True
            
class EducationCreate(BaseModel):
    university_name: str
    degree: str
    major: str
    start_date: datetime
    end_date: datetime
    grade: Optional[str] = None
    accomplishments: Optional[str] = None    
    
class EducationResponse(BaseModel):
    id: int
    counselor_id: int
    university_name: str
    degree: str
    major: str
    start_date: datetime
    end_date: datetime
    grade: Optional[str] = None
    accomplishments: Optional[str] = None

    class Config:
        from_attributes = True
            
class ProfessionalExperienceCreate(BaseModel):
    role: Optional[str] = None
    company_name: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    experience_description: Optional[str] = None

class ProfessionalExperienceUpdate(BaseModel):
    role: Optional[str] = None
    company_name: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    experience_description: Optional[str] = None

class ProfessionalExperienceResponse(BaseModel):
    id: int
    counselor_id: int
    role: Optional[str]
    company_name: Optional[str]
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    experience_description: Optional[str]

    class Config:
        from_attributes = True
        
class CounselingExperienceCreate(BaseModel):
    has_mentored_before: bool
    experience_description: str = Field(..., max_length=400)  # Matching the 0/400 limit from UI

    class Config:
        from_attributes = True   
        
class CounselingExperienceResponse(BaseModel):
    id: int
    counselor_id: int
    has_mentored_before: bool
    experience_description: str
    
    class Config:
        from_attributes = True
        
class ServicesCreate(BaseModel):
    services: List[str]        
    
    
class CommitmentCreate(BaseModel):
    hours_per_week: Optional[str] = None
    hourly_rate: Optional[float] = Field(None, gt=0)

    @field_validator('hours_per_week')
    @classmethod
    def validate_hours(cls, value: Optional[str]) -> Optional[str]:
        if value is None:
            return value
        try:
            # Check if it's a "40+" format
            if value.endswith('+'):
                base_hours = float(value[:-1])
                if base_hours <= 0:
                    raise ValueError("Hours must be greater than 0")
                return value
            # Check if it's a range (e.g., "1-5")
            elif '-' in value:
                min_hours, max_hours = map(float, value.split('-'))
                if min_hours <= 0 or max_hours <= 0:
                    raise ValueError("Hours must be greater than 0")
                if min_hours > max_hours:
                    raise ValueError("Minimum hours cannot be greater than maximum hours")
                if max_hours > 40:
                    raise ValueError("Cannot commit to more than 40 hours per week")
                return value
            # Single number case
            else:
                hours = float(value)
                if hours <= 0:
                    raise ValueError("Hours must be greater than 0")
                if hours > 40:
                    raise ValueError("Cannot commit to more than 40 hours per week")
                return value
        except ValueError as e:
            if str(e) in ["Hours must be greater than 0", "Cannot commit to more than 40 hours per week", "Minimum hours cannot be greater than maximum hours"]:
                raise e
            raise ValueError("Hours must be a valid number, range (e.g., '1-5'), or open-ended (e.g., '40+')")

class CommitmentResponse(BaseModel):
    id: int
    counselor_id: int
    hours_per_week: Optional[str]
    hourly_rate: Optional[float]

    class Config:
        from_attributes = True
        
class DocumentResponse(BaseModel):
    id: int
    document_name: str
    document_url: str
    counselor_id: int

    class Config:
        from_attributes = True


class SocialLinksCreate(BaseModel):
    linkedin_url: Optional[str] = None
    portfolio_url: Optional[str] = None
    website_url: Optional[str] = None


class SocialLinksResponse(BaseModel):
    id: int
    counselor_id: int
    linkedin_url: Optional[str] = None
    portfolio_url: Optional[str] = None
    website_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
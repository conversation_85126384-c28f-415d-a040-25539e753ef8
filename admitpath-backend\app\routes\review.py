import math
from zoneinfo import ZoneInfo
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.payment import CounselorPayoutStatus, Payment
from app.models.student_packages import StudentPackageSubscription, PackageSubscriptionStatus

from ..database import get_db
from ..models.counseling_session import *
from ..models.user import Counselor, Student, User
from ..schemas.counseling_session import *
from ..schemas.review import *
from ..utils.auth import get_current_user

from datetime import datetime, timezone as tz


router = APIRouter()

@router.post("/sessions/{session_id}/reviews", response_model=CreateReviewResponse)
async def create_review(
    session_id: int,
    review: ReviewCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify session exists
    session = db.query(CounselingSession).filter(
        CounselingSession.id == session_id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    # Verify the current user is the student who attended the session
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student or student.id != session.student_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only review sessions you attended"
        )

    # Check if review already exists
    existing_review = db.query(SessionReview).filter(
        SessionReview.session_id == session_id,
        SessionReview.student_id == student.id
    ).first()

    if existing_review:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You have already reviewed this session"
        )

    # Create review
    db_review = SessionReview(
        session_id=session_id,
        student_id=student.id,
        counselor_id=session.counselor_id,
        rating=review.rating,
        received_expected_service=review.received_expected_service,
        comment=review.comment,
        additional_feedback=review.additional_feedback
    )

    db.add(db_review)

    # Mark session as completed
    session.status = SessionStatus.COMPLETED.value
    subscription = False

    # Update package usage if this was a package session
    if session.package_subscription_id:
        subscription = db.query(StudentPackageSubscription).filter(
            StudentPackageSubscription.id == session.package_subscription_id
        ).first()

        if subscription:
            duration = (session.end_time - session.start_time).total_seconds() / 3600

            if session.event_name in subscription.service_hours:
                success = subscription.increment_used_hours(session.event_name, duration)
                if not success:
                    print(f"Warning: Failed to increment hours for service '{session.event_name}', possibly not enough remaining")
                else:
                    # Check if all hours are used
                    all_used = all(
                        service["used"] >= service["total"]
                        for service in subscription.service_hours.values()
                    )
                    if all_used:
                        subscription.status = PackageSubscriptionStatus.COMPLETED.value

    # Update payment status for counselor to APPROVED
    payment = db.query(Payment).filter(
        Payment.session_id == session_id
    ).first()

    if payment and payment.counselor_payout_status == CounselorPayoutStatus.PENDING.value:
        payment.counselor_payout_status = CounselorPayoutStatus.APPROVED.value
        payment.updated_at = datetime.utcnow()

    db.flush()

    db.commit()

    if subscription:
        db.refresh(subscription)
    db.refresh(db_review)

    return db_review

@router.get("/counselors/{user_id}/reviews", response_model=CounselorReviewsResponse)
async def get_counselor_reviews(
    user_id: int,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=50, description="Items per page"),
    db: Session = Depends(get_db)
):
    # Find counselor by user_id instead of counselor.id
    counselor = db.query(Counselor).filter(Counselor.user_id == user_id).first()
    if not counselor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Counselor not found"
        )

    # Calculate offset
    offset = (page - 1) * page_size

    # Get total count of reviews for this counselor
    total_reviews = db.query(SessionReview).filter(
        SessionReview.counselor_id == counselor.id  # Use counselor.id from the found counselor
    ).count()

    # Query to get reviews with student information
    reviews_query = (
        db.query(
            SessionReview,
            User.first_name,
            User.last_name,
            User.profile_picture_url
        )
        .join(Student, SessionReview.student_id == Student.id)
        .join(User, Student.user_id == User.id)
        .filter(SessionReview.counselor_id == counselor.id)
        .order_by(SessionReview.created_at.desc())
        .offset(offset)
        .limit(page_size)
    )

    # Execute the query
    review_results = reviews_query.all()

    # Transform the results to include student information
    enhanced_reviews = []
    for review, first_name, last_name, profile_picture in review_results:
        review_dict = {
            "id": review.id,
            "session_id": review.session_id,
            "student_id": review.student_id,
            "student_name": f"{first_name} {last_name}",
            "student_profile_picture": profile_picture,
            "rating": review.rating,
            "received_expected_service": review.received_expected_service,
            "comment": review.comment,
            "additional_feedback": review.additional_feedback,
            "created_at": review.created_at
        }
        enhanced_reviews.append(review_dict)

    # Calculate average rating
    avg_rating = db.query(func.avg(SessionReview.rating)).filter(
        SessionReview.counselor_id == counselor.id
    ).scalar() or 0.0

    # Calculate total pages
    total_pages = math.ceil(total_reviews / page_size)

    return {
        "average_rating": round(float(avg_rating), 1),
        "total_reviews": total_reviews,
        "reviews": enhanced_reviews,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "has_next": page < total_pages,
        "has_prev": page > 1
    }

@router.get("/sessions/{session_id}/reviews", response_model=ReviewResponse)
async def get_session_review(
    session_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify session exists
    session = db.query(CounselingSession).filter(
        CounselingSession.id == session_id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    # Verify the current user is either the counselor or the student
    user_is_counselor = db.query(Counselor).filter(
        Counselor.user_id == current_user.id,
        Counselor.id == session.counselor_id
    ).first()

    user_is_student = db.query(Student).filter(
        Student.user_id == current_user.id,
        Student.id == session.student_id
    ).first()

    if not (user_is_counselor or user_is_student):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to view this review"
        )

    # Get review
    review = db.query(SessionReview).filter(
        SessionReview.session_id == session_id
    ).first()

    if not review:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No review found for this session"
        )

    return review



@router.get("/pending-feedback", response_model=PendingFeedbackResponse)
async def get_pending_feedback_sessions(
    timezone: str,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    tz_info = ZoneInfo(timezone)
    current_time = datetime.utcnow()

    student = db.query(Student).filter(
        Student.user_id == current_user.id
    ).first()

    # Get completed sessions that haven't been reviewed yet
    pending_feedback = db.query(
        CounselingSession, User
    ).join(
        Counselor, Counselor.id == CounselingSession.counselor_id
    ).join(
        User, User.id == Counselor.user_id
    ).outerjoin(
        SessionReview, SessionReview.session_id == CounselingSession.id
    ).filter(
        CounselingSession.student_id == student.id,
        CounselingSession.end_time <= current_time,  # Only include past sessions
        # CounselingSession.status == SessionStatus.COMPLETED.value,
        SessionReview.id == None  # Sessions without reviews
    ).order_by(
        CounselingSession.date.desc()
    ).all()

    # Format response
    sessions = []

    for session, user in pending_feedback:
        start_local = session.start_time.replace(tzinfo=tz.utc).astimezone(tz_info)
        end_local = session.end_time.replace(tzinfo=tz.utc).astimezone(tz_info)
        sessions.append(
            PendingFeedbackSession(
                session_id=session.id,
                event_name=session.event_name,
                start_time=start_local,
                end_time=end_local,
                instructor_name=f"{user.first_name} {user.last_name}"
            )
        )


    return PendingFeedbackResponse(
        total=len(sessions),
        sessions=sessions
    )

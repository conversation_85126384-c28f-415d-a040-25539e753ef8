{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/mainBtn/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { IconProp } from \"@fortawesome/fontawesome-svg-core\";\r\n\r\ninterface ButtonProps {\r\n  type?: \"submit\" | \"reset\" | \"button\";\r\n  children: React.ReactNode;\r\n  onClick?: (\r\n    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<Element>\r\n  ) => void;\r\n  style?: React.CSSProperties;\r\n  className?: string;\r\n  variant?: \"primary\" | \"secondary\" | \"neutral\";\r\n  icon?: IconProp;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const MainButton: React.FC<ButtonProps> = ({\r\n  type,\r\n  children,\r\n  onClick,\r\n  style,\r\n  className = \"\",\r\n  variant = \"neutral\",\r\n  icon,\r\n  disabled = false,\r\n}) => {\r\n  // Default styles for button variants\r\n  const variantStyles = {\r\n    primary: \"bg-mainClr text-white hover:bg-[#152070] focus:ring-blue-300\",\r\n    secondary: \"bg-[#EB5757] text-white hover:bg-[#EB6767]\",\r\n    neutral: \"bg-neutral1 text-neutral10 hover:bg-neutral2 focus:ring-neutral3\",\r\n  };\r\n\r\n  const disabledStyles = \"opacity-50 cursor-not-allowed\";\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={`py-3 px-6 font-medium rounded-xl text-sm flex items-center justify-center gap-2 \r\n        ${variantStyles[variant]} ${\r\n        disabled ? disabledStyles : \"\"\r\n      } ${className}`}\r\n      style={style}\r\n      onClick={!disabled ? onClick : undefined}\r\n      disabled={disabled}\r\n    >\r\n      {children}\r\n      {icon && <FontAwesomeIcon icon={icon} />}\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAmBO,MAAM,aAAoC,CAAC,EAChD,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EACL,YAAY,EAAE,EACd,UAAU,SAAS,EACnB,IAAI,EACJ,WAAW,KAAK,EACjB;IACC,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,MAAM,iBAAiB;IAEvB,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAC;QACV,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAC1B,WAAW,iBAAiB,GAC7B,CAAC,EAAE,WAAW;QACf,OAAO;QACP,SAAS,CAAC,WAAW,UAAU;QAC/B,UAAU;;YAET;YACA,sBAAQ,6LAAC,uKAAA,CAAA,kBAAe;gBAAC,MAAM;;;;;;;;;;;;AAGtC;KAlCa"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/inputField/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"container\": \"index-module__8yU72G__container\",\n  \"error\": \"index-module__8yU72G__error\",\n  \"errorMessage\": \"index-module__8yU72G__errorMessage\",\n  \"input\": \"index-module__8yU72G__input\",\n  \"label\": \"index-module__8yU72G__label\",\n  \"required\": \"index-module__8yU72G__required\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/inputField/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, InputHTMLAttributes } from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {\r\n  label: string;\r\n  error?: string;\r\n  required?: boolean;\r\n}\r\n\r\nexport const InputField: FC<InputFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  ...inputProps\r\n}) => {\r\n  return (\r\n    <div className={styles.container}>\r\n      <label\r\n        htmlFor={inputProps.id || inputProps.name}\r\n        className={styles.label}\r\n      >\r\n        {label} {required && <span className={styles.required}>*</span>}\r\n      </label>\r\n\r\n      <input\r\n        {...inputProps}\r\n        className={`${styles.input} ${error ? styles.error : \"\"}`}\r\n        aria-required={required}\r\n        aria-invalid={!!error}\r\n        aria-describedby={error ? `${inputProps.id}-error` : undefined}\r\n      />\r\n\r\n      {error && (\r\n        <span\r\n          id={`${inputProps.id}-error`}\r\n          className={styles.errorMessage}\r\n          role=\"alert\"\r\n        >\r\n          {error}\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWO,MAAM,aAAkC,CAAC,EAC9C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,GAAG,YACJ;IACC,qBACE,6LAAC;QAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,6LAAC;gBACC,SAAS,WAAW,EAAE,IAAI,WAAW,IAAI;gBACzC,WAAW,iKAAA,CAAA,UAAM,CAAC,KAAK;;oBAEtB;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAW,iKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAGzD,6LAAC;gBACE,GAAG,UAAU;gBACd,WAAW,GAAG,iKAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,iKAAA,CAAA,UAAM,CAAC,KAAK,GAAG,IAAI;gBACzD,iBAAe;gBACf,gBAAc,CAAC,CAAC;gBAChB,oBAAkB,QAAQ,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG;;;;;;YAGtD,uBACC,6LAAC;gBACC,IAAI,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC;gBAC5B,WAAW,iKAAA,CAAA,UAAM,CAAC,YAAY;gBAC9B,MAAK;0BAEJ;;;;;;;;;;;;AAKX;KAlCa"}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/textarea/index.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ninterface TextareaProps {\r\n  label?: string;\r\n  name: string;\r\n  value: string;\r\n  onChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  rows?: number;\r\n  className?: string;\r\n  style?: React.CSSProperties;\r\n}\r\n\r\nexport const Textarea: React.FC<TextareaProps> = ({\r\n  label,\r\n  name,\r\n  value,\r\n  onChange,\r\n  placeholder = \"Enter text here...\",\r\n  required = false,\r\n  rows = 4,\r\n  className = \"\",\r\n  style = {},\r\n}) => {\r\n  return (\r\n    <div className={`w-full ${className}`}>\r\n      {label && (\r\n        <label\r\n          htmlFor={name}\r\n          className=\"block text-sm font-medium text-gray-700 mb-2\"\r\n        >\r\n          {label} {required && <span>*</span>}\r\n        </label>\r\n      )}\r\n      <textarea\r\n        id={name}\r\n        name={name}\r\n        value={value}\r\n        onChange={onChange}\r\n        placeholder={placeholder}\r\n        required={required}\r\n        rows={rows}\r\n        className=\"w-full border rounded-lg p-3 text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n        style={style}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAcO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,cAAc,oBAAoB,EAClC,WAAW,KAAK,EAChB,OAAO,CAAC,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,CAAC,EACX;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,OAAO,EAAE,WAAW;;YAClC,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBAAM;oBAAE,0BAAY,6LAAC;kCAAK;;;;;;;;;;;;0BAG/B,6LAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,WAAU;gBACV,OAAO;;;;;;;;;;;;AAIf;KAlCa"}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/counselor/usePackage.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport { PackageData, PackagePayload, PackageSession, PackageSubscription, PackagesState } from \"@/app/types/counselor/package\";\r\n\r\nexport const usePackages = create<PackagesState>((set, get) => ({\r\n  packages: { total: 0, items: [] },\r\n  selectedPackage: null,\r\n  subscriptions: [],\r\n  activeSubscriptions: [],\r\n  completedSubscriptions: [],\r\n  loading: false,\r\n  error: null,\r\n\r\n  fetchPackages: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/counselor/packages\");\r\n      set({ packages: response.data });\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.message || \"Error fetching packages\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  fetchSinglePackage: async (packageId) => {\r\n    try {\r\n      // Don't set global loading state, just fetch the package\r\n      const response = await apiClient.get<PackageData>(`/counselor/packages/package/${packageId}`);\r\n      set({ selectedPackage: response.data });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.message || \"Error fetching package\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  fetchSubscriptions: async (status = \"active\") => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get<PackageSubscription[]>(\r\n        `/counselor/packages/subscriptions?status=${status}`\r\n      );\r\n\r\n      // Store subscriptions in the appropriate state based on status\r\n      if (status === \"active\") {\r\n        set({\r\n          subscriptions: response.data,\r\n          activeSubscriptions: response.data,\r\n          loading: false\r\n        });\r\n      } else if (status === \"completed\") {\r\n        set({\r\n          completedSubscriptions: response.data,\r\n          loading: false\r\n        });\r\n      } else {\r\n        set({ subscriptions: response.data, loading: false });\r\n      }\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.message || \"Error fetching package subscriptions\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  addPackage: async (packageData: PackagePayload) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.post<PackageData>(\"/counselor/packages\", packageData);\r\n      toast.success(\"Package added successfully!\");\r\n\r\n      set((state) => ({\r\n        packages: {\r\n          total: state.packages.total + 1,\r\n          items: [...state.packages.items, response.data],\r\n        },\r\n      }));\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error?.response?.data?.message ||\r\n        error?.message ||\r\n        \"Failed to add the package. Please try again.\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  updatePackage: async (packageId, packageData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.put<PackageData>(`/counselor/packages/package/${packageId}`, packageData);\r\n      toast.success(\"Package updated successfully!\");\r\n\r\n      // Update the package in the state\r\n      set((state) => ({\r\n        packages: {\r\n          total: state.packages.total,\r\n          items: state.packages.items.map((pack) =>\r\n            pack.id === packageId ? response.data : pack\r\n          ),\r\n        },\r\n        // If the selected package is the one being updated, update it too\r\n        selectedPackage: state.selectedPackage?.id === packageId ? response.data : state.selectedPackage,\r\n      }));\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.message || \"Error updating package\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  deletePackage: async (packageId) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.delete(`/counselor/packages/package/${packageId}?permanent=true`);\r\n      toast.success(\"Package deleted successfully!\");\r\n\r\n      set((state) => ({\r\n        packages: {\r\n          total: state.packages.total - 1,\r\n          items: state.packages.items.filter((pack) => pack.id !== packageId),\r\n        },\r\n        // If the selected package is the one being deleted, clear it\r\n        selectedPackage: state.selectedPackage?.id === packageId ? null : state.selectedPackage,\r\n      }));\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.message || \"Error deleting package\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  createPackageSession: async (subscriptionId, sessionData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.post<PackageSession>(\r\n        `/counselor/packages/subscription/${subscriptionId}/sessions`,\r\n        sessionData\r\n      );\r\n      toast.success(\"Package session created successfully!\");\r\n\r\n      // Update the subscription in the state\r\n      set((state) => {\r\n        const updatedSubscriptions = state.subscriptions.map((subscription) => {\r\n          if (subscription.id === subscriptionId) {\r\n            return {\r\n              ...subscription,\r\n              sessions: [...subscription.sessions, response.data],\r\n            };\r\n          }\r\n          return subscription;\r\n        });\r\n\r\n        return { subscriptions: updatedSubscriptions };\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.message || \"Error creating package session\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAKO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QAC9D,UAAU;YAAE,OAAO;YAAG,OAAO,EAAE;QAAC;QAChC,iBAAiB;QACjB,eAAe,EAAE;QACjB,qBAAqB,EAAE;QACvB,wBAAwB,EAAE;QAC1B,SAAS;QACT,OAAO;QAEP,eAAe;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,UAAU,SAAS,IAAI;gBAAC;YAChC,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW;gBACvD,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,yDAAyD;gBACzD,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAc,CAAC,4BAA4B,EAAE,WAAW;gBAC5F,IAAI;oBAAE,iBAAiB,SAAS,IAAI;gBAAC;gBACrC,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW;gBACvD,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,oBAAoB,OAAO,SAAS,QAAQ;YAC1C,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,yCAAyC,EAAE,QAAQ;gBAGtD,+DAA+D;gBAC/D,IAAI,WAAW,UAAU;oBACvB,IAAI;wBACF,eAAe,SAAS,IAAI;wBAC5B,qBAAqB,SAAS,IAAI;wBAClC,SAAS;oBACX;gBACF,OAAO,IAAI,WAAW,aAAa;oBACjC,IAAI;wBACF,wBAAwB,SAAS,IAAI;wBACrC,SAAS;oBACX;gBACF,OAAO;oBACL,IAAI;wBAAE,eAAe,SAAS,IAAI;wBAAE,SAAS;oBAAM;gBACrD;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW;gBACvD,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAc,uBAAuB;gBAC1E,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU;4BACR,OAAO,MAAM,QAAQ,CAAC,KAAK,GAAG;4BAC9B,OAAO;mCAAI,MAAM,QAAQ,CAAC,KAAK;gCAAE,SAAS,IAAI;6BAAC;wBACjD;oBACF,CAAC;gBACD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,OAAO,UAAU,MAAM,WACvB,OAAO,WACP;gBACF,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,WAAW;YAC/B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAc,CAAC,4BAA4B,EAAE,WAAW,EAAE;gBAC9F,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,kCAAkC;gBAClC,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU;4BACR,OAAO,MAAM,QAAQ,CAAC,KAAK;4BAC3B,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAC/B,KAAK,EAAE,KAAK,YAAY,SAAS,IAAI,GAAG;wBAE5C;wBACA,kEAAkE;wBAClE,iBAAiB,MAAM,eAAe,EAAE,OAAO,YAAY,SAAS,IAAI,GAAG,MAAM,eAAe;oBAClG,CAAC;gBACD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW;gBACvD,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,mHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,4BAA4B,EAAE,UAAU,eAAe,CAAC;gBAChF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU;4BACR,OAAO,MAAM,QAAQ,CAAC,KAAK,GAAG;4BAC9B,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;wBAC3D;wBACA,6DAA6D;wBAC7D,iBAAiB,MAAM,eAAe,EAAE,OAAO,YAAY,OAAO,MAAM,eAAe;oBACzF,CAAC;YACH,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW;gBACvD,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,sBAAsB,OAAO,gBAAgB;YAC3C,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,iCAAiC,EAAE,eAAe,SAAS,CAAC,EAC7D;gBAEF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,uCAAuC;gBACvC,IAAI,CAAC;oBACH,MAAM,uBAAuB,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC;wBACpD,IAAI,aAAa,EAAE,KAAK,gBAAgB;4BACtC,OAAO;gCACL,GAAG,YAAY;gCACf,UAAU;uCAAI,aAAa,QAAQ;oCAAE,SAAS,IAAI;iCAAC;4BACrD;wBACF;wBACA,OAAO;oBACT;oBAEA,OAAO;wBAAE,eAAe;oBAAqB;gBAC/C;gBAEA,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,OAAO,UAAU,MAAM,WAAW;gBACvD,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/counselor/useService.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport { ServicesState } from \"@/app/types/counselor/service\";\r\n\r\nexport const useServices = create<ServicesState>((set) => ({\r\n  services: [],\r\n  selectedService: null,\r\n  loading: false,\r\n  error: null,\r\n\r\n  fetchServices: async (id) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(`/counselor/services-offered/${id}`);\r\n      const fetchedServices = response.data.map((service: any) => ({\r\n        ...service,\r\n        service_type: service.custom_type || service.service_type,\r\n      }));\r\n      set({ services: fetchedServices });\r\n    } catch (error) {\r\n      set({ error: \"Error fetching services\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  fetchSingleService: async (serviceId) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/counselor/services-offered/service/${serviceId}`\r\n      );\r\n      set({ selectedService: response.data });\r\n    } catch (error) {\r\n      set({ error: \"Error fetching single service\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  addService: async (serviceData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.post(\r\n        \"/counselor/services-offered/\",\r\n        serviceData\r\n      );\r\n      toast.success(\"Service added successfully!\");\r\n      set((state) => ({ services: [...state.services, response.data] }));\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error?.response?.data?.message ||\r\n        error?.message ||\r\n        \"Failed to add the service. Please try again.\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  updateService: async (serviceId, serviceData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.put(\r\n        `/counselor/services-offered/${serviceId}`,\r\n        serviceData\r\n      );\r\n      toast.success(\"Service updated successfully!\");\r\n      set((state) => ({\r\n        services: state.services.map((service) =>\r\n          service.id === serviceId ? { ...service, ...serviceData } : service\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      set({ error: \"Error updating service\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  deleteService: async (serviceId) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.delete(`/counselor/services-offered/${serviceId}`);\r\n      toast.success(\"Service deleted successfully!\");\r\n      set((state) => ({\r\n        services: state.services.filter((service) => service.id !== serviceId),\r\n      }));\r\n    } catch (error) {\r\n      set({ error: \"Error deleting service\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAKO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,MAAQ,CAAC;QACzD,UAAU,EAAE;QACZ,iBAAiB;QACjB,SAAS;QACT,OAAO;QAEP,eAAe,OAAO;YACpB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI;gBACxE,MAAM,kBAAkB,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;wBAC3D,GAAG,OAAO;wBACV,cAAc,QAAQ,WAAW,IAAI,QAAQ,YAAY;oBAC3D,CAAC;gBACD,IAAI;oBAAE,UAAU;gBAAgB;YAClC,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;gBAA0B;gBACvC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,oCAAoC,EAAE,WAAW;gBAEpD,IAAI;oBAAE,iBAAiB,SAAS,IAAI;gBAAC;YACvC,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;gBAAgC;gBAC7C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,gCACA;gBAEF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,IAAI,CAAC,QAAU,CAAC;wBAAE,UAAU;+BAAI,MAAM,QAAQ;4BAAE,SAAS,IAAI;yBAAC;oBAAC,CAAC;YAClE,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,OAAO,UAAU,MAAM,WACvB,OAAO,WACP;gBACF,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,WAAW;YAC/B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CACjB,CAAC,4BAA4B,EAAE,WAAW,EAC1C;gBAEF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,UAC5B,QAAQ,EAAE,KAAK,YAAY;gCAAE,GAAG,OAAO;gCAAE,GAAG,WAAW;4BAAC,IAAI;oBAEhE,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;gBAAyB;gBACtC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,mHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,4BAA4B,EAAE,WAAW;gBACjE,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK;oBAC9D,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,OAAO;gBAAyB;gBACtC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/dropdown/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\ninterface DropdownProps {\r\n  label: string;\r\n  name: string;\r\n  options: { value: string | number; label: string }[];\r\n  value: string | number;\r\n  onChange: React.ChangeEventHandler<HTMLSelectElement>;\r\n  required?: boolean;\r\n  className?: string;\r\n  selectStyle?: string;\r\n}\r\n\r\nconst Dropdown: React.FC<DropdownProps> = ({\r\n  label,\r\n  name,\r\n  options,\r\n  value,\r\n  onChange,\r\n  required = false,\r\n  className = \"\",\r\n  selectStyle = \"\",\r\n}) => {\r\n  return (\r\n    <div className={`dropdown relative ${className}`}>\r\n      <label htmlFor={name} className=\"block font-medium mb-2 text-sm\">\r\n        {label} {required && <span>*</span>}\r\n      </label>\r\n      <div className=\"relative\">\r\n        <select\r\n          name={name}\r\n          id={name}\r\n          value={value}\r\n          onChange={onChange}\r\n          required={required}\r\n          className={`appearance-none py-3.5 px-3 pr-10 rounded border border-solid border-slate-200 w-full bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${selectStyle}`}\r\n        >\r\n          {options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n        {/* Custom Down Arrow Icon */}\r\n        <span className=\"absolute inset-y-0 right-4 flex items-center pointer-events-none\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"h-5 w-5 text-gray-500\"\r\n            viewBox=\"0 0 20 20\"\r\n            fill=\"currentColor\"\r\n          >\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\r\n              clipRule=\"evenodd\"\r\n            />\r\n          </svg>\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dropdown;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAeA,MAAM,WAAoC,CAAC,EACzC,KAAK,EACL,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,EAAE,EACjB;IACC,qBACE,6LAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;;0BAC9C,6LAAC;gBAAM,SAAS;gBAAM,WAAU;;oBAC7B;oBAAM;oBAAE,0BAAY,6LAAC;kCAAK;;;;;;;;;;;;0BAE7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAM;wBACN,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,qKAAqK,EAAE,aAAa;kCAE/L,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gCAA0B,OAAO,OAAO,KAAK;0CAC3C,OAAO,KAAK;+BADF,OAAO,KAAK;;;;;;;;;;kCAM7B,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BACC,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,MAAK;sCAEL,cAAA,6LAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;KAhDM;uCAkDS"}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,gBAAgB,sKAAgB,OAAO;AAE7C,MAAM,eAAe,sKAAgB,MAAM;AAE3C,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,sKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,sKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,8JAAM,UAAU,OAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/spinner.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface SpinnerProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n}\r\n\r\nexport const Spinner: React.FC<SpinnerProps> = ({ size = 'md', className = '' }) => {\r\n  const sizeClasses = {\r\n    sm: 'h-4 w-4',\r\n    md: 'h-8 w-8',\r\n    lg: 'h-12 w-12'\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex justify-center items-center\">\r\n      <div\r\n        className={`animate-spin rounded-full border-b-2 border-blue-950 ${sizeClasses[size]} ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"loading\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Spinner;\r\n"], "names": [], "mappings": ";;;;;;AAOO,MAAM,UAAkC,CAAC,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAE;IAC7E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAC,qDAAqD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;YACnG,MAAK;YACL,cAAW;;;;;;;;;;;AAInB;KAhBa;uCAkBE"}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/packages/AddPackageDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\nimport { InputField } from \"@/app/components/common/inputField\";\r\nimport { Textarea } from \"@/app/components/common/textarea\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport { faTrash, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { usePackages } from \"@/app/hooks/counselor/usePackage\";\r\nimport { useServices } from \"@/app/hooks/counselor/useService\";\r\nimport { useProfile } from \"@/app/hooks/counselor/useProfile\";\r\nimport Dropdown from \"@/app/components/common/dropdown\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogFooter,\r\n} from \"@/app/components/ui/dialog\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { Spinner } from \"@/app/components/ui/spinner\";\r\n\r\ninterface AddPackageDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\ninterface InitialStateProps {\r\n  title: string;\r\n  description: string;\r\n  total_price: number;\r\n  items: { service_name: string; custom_type: string; hours: number }[];\r\n}\r\n\r\nconst initialState = {\r\n  title: \"\",\r\n  description: \"\",\r\n  total_price: 0,\r\n  items: [{ service_name: \"\", custom_type: \"\", hours: 0 }],\r\n};\r\n\r\nexport default function AddPackageDialog({\r\n  isOpen,\r\n  onClose,\r\n}: AddPackageDialogProps) {\r\n  const { addPackage, fetchPackages } = usePackages();\r\n  const { services, fetchServices } = useServices();\r\n  const { userInfo } = useProfile();\r\n\r\n  const [formData, setFormData] = useState<InitialStateProps>(initialState);\r\n  const [counselorServices, setCounselorServices] = useState<\r\n    { value: string; label: string }[]\r\n  >([]);\r\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\r\n  const [isSubmitted, setIsSubmitted] = useState(false);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // Fetch counselor services when the dialog opens\r\n  useEffect(() => {\r\n    if (isOpen && userInfo?.counselor_id) {\r\n      fetchServices(userInfo.counselor_id);\r\n    }\r\n  }, [isOpen, userInfo, fetchServices]);\r\n\r\n  // Convert services to dropdown options\r\n  useEffect(() => {\r\n    if (services.length > 0) {\r\n      const serviceOptions = [\r\n        { value: \"\", label: \"Please select\" },\r\n        ...services.map((service) => ({\r\n          value: service.custom_type || service.service_type,\r\n          label: service.custom_type || service.service_type,\r\n        })),\r\n      ];\r\n      setCounselorServices(serviceOptions);\r\n    }\r\n  }, [services]);\r\n\r\n  const validateForm = (): boolean => {\r\n    const newErrors: { [key: string]: string } = {};\r\n\r\n    if (!formData.title.trim()) {\r\n      newErrors.title = \"Package title is required.\";\r\n    }\r\n    if (!formData.description.trim()) {\r\n      newErrors.description = \"Description is required.\";\r\n    }\r\n    if (!formData.total_price || formData.total_price <= 0) {\r\n      newErrors.total_price = \"Price must be a positive number.\";\r\n    }\r\n\r\n    // Validate services - must have at least one valid service\r\n    if (formData.items.length === 0) {\r\n      newErrors.services = \"At least one service is required.\";\r\n    } else {\r\n      // Check if all provided services have valid data\r\n      const hasEmptyServices = formData.items.some(\r\n        (item) => !item.service_name.trim() || item.hours <= 0\r\n      );\r\n      if (hasEmptyServices) {\r\n        newErrors.services =\r\n          \"All service fields must be filled with valid values.\";\r\n      }\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\r\n  ) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleServiceChange = (index: number, field: string, value: string) => {\r\n    const updatedItems = [...formData.items];\r\n    updatedItems[index] = {\r\n      ...updatedItems[index],\r\n      [field]: value,\r\n    };\r\n    setFormData({ ...formData, items: updatedItems });\r\n  };\r\n\r\n  const handleServiceHoursChange = (\r\n    index: number,\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const value = e.target.valueAsNumber || 0;\r\n    const updatedItems = [...formData.items];\r\n    updatedItems[index] = {\r\n      ...updatedItems[index],\r\n      hours: value,\r\n    };\r\n    setFormData({ ...formData, items: updatedItems });\r\n  };\r\n\r\n  const handleAddService = (\r\n    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<Element>\r\n  ) => {\r\n    // Prevent form submission\r\n    e.preventDefault();\r\n\r\n    setFormData({\r\n      ...formData,\r\n      items: [\r\n        ...formData.items,\r\n        { service_name: \"\", custom_type: \"\", hours: 0 },\r\n      ],\r\n    });\r\n  };\r\n\r\n  const handleRemoveService = (index: number) => {\r\n    if (formData.items.length === 1) {\r\n      return; // Don't remove the last service\r\n    }\r\n    const updatedItems = [...formData.items];\r\n    updatedItems.splice(index, 1);\r\n    setFormData({\r\n      ...formData,\r\n      items: updatedItems,\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsSubmitted(true);\r\n\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    toast.info(\"Creating package...\");\r\n\r\n    const formattedData = {\r\n      ...formData,\r\n      items: formData.items.map((item) => ({\r\n        service_name: item.service_name,\r\n        hours: item.hours,\r\n      })),\r\n    };\r\n\r\n    try {\r\n      await addPackage(formattedData);\r\n      toast.success(\"Package created successfully!\");\r\n      onClose();\r\n      setFormData(initialState);\r\n      fetchPackages();\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error?.response?.data?.message || \"Error adding package\";\r\n      toast.error(errorMessage);\r\n      console.error(\"Error adding package:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Get the list of selected services\r\n  const selectedServices = formData.items.map((item) => item.service_name);\r\n\r\n  // Filter out already selected services from the dropdown options\r\n  const getFilteredOptions = (index: number) => {\r\n    // Filter the counselor's services\r\n    const filteredOptions = counselorServices.filter(\r\n      (item) =>\r\n        item.value === \"\" || // Always include the placeholder\r\n        !selectedServices.includes(item.value) ||\r\n        formData.items[index].service_name === item.value\r\n    );\r\n\r\n    return filteredOptions;\r\n  };\r\n\r\n  return (\r\n    <Dialog\r\n      open={isOpen}\r\n      onOpenChange={(open) => {\r\n        if (!open) {\r\n          setIsSubmitted(false);\r\n          onClose();\r\n        }\r\n      }}\r\n    >\r\n      <DialogContent\r\n        className=\"max-w-4xl max-h-[90vh] overflow-y-auto\"\r\n        onInteractOutside={() => {\r\n          setIsSubmitted(false);\r\n          onClose();\r\n        }}\r\n      >\r\n        <DialogHeader>\r\n          <DialogTitle>Add Package</DialogTitle>\r\n          <p className=\"text-sm text-gray-500 mt-2\">\r\n            Fields marked with * are required\r\n          </p>\r\n        </DialogHeader>\r\n\r\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n          {formData.items.map((item, index) => (\r\n            <div key={index} className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\r\n              <div className=\"flex justify-between items-center mb-2\">\r\n                <h3 className=\"text-lg font-medium\">Service {index + 1} *</h3>\r\n                <MainButton\r\n                  variant=\"neutral\"\r\n                  icon={faTrash}\r\n                  onClick={() => handleRemoveService(index)}\r\n                  disabled={formData.items.length === 1}\r\n                >\r\n                  Remove\r\n                </MainButton>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <Dropdown\r\n                    label=\"Service Type *\"\r\n                    name={`service_${index}`}\r\n                    options={getFilteredOptions(index)}\r\n                    value={item.service_name}\r\n                    onChange={(e) =>\r\n                      handleServiceChange(index, \"service_name\", e.target.value)\r\n                    }\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <InputField\r\n                    label=\"Hours *\"\r\n                    type=\"number\"\r\n                    placeholder=\"Enter hours\"\r\n                    value={item.hours.toString()}\r\n                    min=\"0\"\r\n                    onChange={(e) => handleServiceHoursChange(index, e)}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n\r\n          <div className=\"flex justify-end\">\r\n            <MainButton\r\n              variant=\"neutral\"\r\n              icon={faPlus}\r\n              onClick={(e) => handleAddService(e)}\r\n            >\r\n              Add Service\r\n            </MainButton>\r\n          </div>\r\n\r\n          <InputField\r\n            label=\"Package Title *\"\r\n            placeholder=\"Enter package title\"\r\n            name=\"title\"\r\n            value={formData.title}\r\n            onChange={handleChange}\r\n          />\r\n          {isSubmitted && errors.title && (\r\n            <p className=\"text-red-500 text-sm\">{errors.title}</p>\r\n          )}\r\n\r\n          <Textarea\r\n            label=\"Description *\"\r\n            placeholder=\"Enter package description\"\r\n            name=\"description\"\r\n            value={formData.description}\r\n            onChange={handleChange}\r\n          />\r\n          {isSubmitted && errors.description && (\r\n            <p className=\"text-red-500 text-sm\">{errors.description}</p>\r\n          )}\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div>\r\n              <InputField\r\n                label=\"Price ($) *\"\r\n                type=\"number\"\r\n                placeholder=\"Enter price\"\r\n                name=\"total_price\"\r\n                value={formData.total_price || \"\"}\r\n                min=\"0\"\r\n                onChange={(e) => {\r\n                  const value = e.target.valueAsNumber;\r\n                  setFormData({ ...formData, total_price: value });\r\n                  if (value > 0) {\r\n                    setErrors((prev) => ({ ...prev, total_price: \"\" }));\r\n                  }\r\n                }}\r\n              />\r\n              {isSubmitted && errors.total_price && (\r\n                <p className=\"text-red-500 text-sm\">{errors.total_price}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {isSubmitted && errors.services && (\r\n            <p className=\"text-red-500 text-sm\">{errors.services}</p>\r\n          )}\r\n\r\n          <DialogFooter>\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              onClick={() => {\r\n                setIsSubmitted(false);\r\n                onClose();\r\n              }}\r\n              disabled={isSubmitting}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={isSubmitting}\r\n              className=\"min-w-[120px]\"\r\n            >\r\n              {isSubmitting ? (\r\n                <div className=\"flex items-center justify-center\">\r\n                  <Spinner size=\"sm\" className=\"mr-2\" /> Creating...\r\n                </div>\r\n              ) : (\r\n                \"Add Package\"\r\n              )}\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAOA;AACA;AAbA;;;AAPA;;;;;;;;;;;;;;AAkCA,MAAM,eAAe;IACnB,OAAO;IACP,aAAa;IACb,aAAa;IACb,OAAO;QAAC;YAAE,cAAc;YAAI,aAAa;YAAI,OAAO;QAAE;KAAE;AAC1D;AAEe,SAAS,iBAAiB,EACvC,MAAM,EACN,OAAO,EACe;;IACtB,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD;IAChD,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD;IAC9C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD;IAE9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEvD,EAAE;IACJ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,UAAU,UAAU,cAAc;gBACpC,cAAc,SAAS,YAAY;YACrC;QACF;qCAAG;QAAC;QAAQ;QAAU;KAAc;IAEpC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,MAAM,iBAAiB;oBACrB;wBAAE,OAAO;wBAAI,OAAO;oBAAgB;uBACjC,SAAS,GAAG;sDAAC,CAAC,UAAY,CAAC;gCAC5B,OAAO,QAAQ,WAAW,IAAI,QAAQ,YAAY;gCAClD,OAAO,QAAQ,WAAW,IAAI,QAAQ,YAAY;4BACpD,CAAC;;iBACF;gBACD,qBAAqB;YACvB;QACF;qCAAG;QAAC;KAAS;IAEb,MAAM,eAAe;QACnB,MAAM,YAAuC,CAAC;QAE9C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QACA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,UAAU,WAAW,GAAG;QAC1B;QACA,IAAI,CAAC,SAAS,WAAW,IAAI,SAAS,WAAW,IAAI,GAAG;YACtD,UAAU,WAAW,GAAG;QAC1B;QAEA,2DAA2D;QAC3D,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAAG;YAC/B,UAAU,QAAQ,GAAG;QACvB,OAAO;YACL,iDAAiD;YACjD,MAAM,mBAAmB,SAAS,KAAK,CAAC,IAAI,CAC1C,CAAC,OAAS,CAAC,KAAK,YAAY,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI;YAEvD,IAAI,kBAAkB;gBACpB,UAAU,QAAQ,GAChB;YACJ;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CACnB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY;YAAE,GAAG,QAAQ;YAAE,CAAC,KAAK,EAAE;QAAM;IAC3C;IAEA,MAAM,sBAAsB,CAAC,OAAe,OAAe;QACzD,MAAM,eAAe;eAAI,SAAS,KAAK;SAAC;QACxC,YAAY,CAAC,MAAM,GAAG;YACpB,GAAG,YAAY,CAAC,MAAM;YACtB,CAAC,MAAM,EAAE;QACX;QACA,YAAY;YAAE,GAAG,QAAQ;YAAE,OAAO;QAAa;IACjD;IAEA,MAAM,2BAA2B,CAC/B,OACA;QAEA,MAAM,QAAQ,EAAE,MAAM,CAAC,aAAa,IAAI;QACxC,MAAM,eAAe;eAAI,SAAS,KAAK;SAAC;QACxC,YAAY,CAAC,MAAM,GAAG;YACpB,GAAG,YAAY,CAAC,MAAM;YACtB,OAAO;QACT;QACA,YAAY;YAAE,GAAG,QAAQ;YAAE,OAAO;QAAa;IACjD;IAEA,MAAM,mBAAmB,CACvB;QAEA,0BAA0B;QAC1B,EAAE,cAAc;QAEhB,YAAY;YACV,GAAG,QAAQ;YACX,OAAO;mBACF,SAAS,KAAK;gBACjB;oBAAE,cAAc;oBAAI,aAAa;oBAAI,OAAO;gBAAE;aAC/C;QACH;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAAG;YAC/B,QAAQ,gCAAgC;QAC1C;QACA,MAAM,eAAe;eAAI,SAAS,KAAK;SAAC;QACxC,aAAa,MAAM,CAAC,OAAO;QAC3B,YAAY;YACV,GAAG,QAAQ;YACX,OAAO;QACT;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,eAAe;QAEf,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QAEX,MAAM,gBAAgB;YACpB,GAAG,QAAQ;YACX,OAAO,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;oBACnC,cAAc,KAAK,YAAY;oBAC/B,OAAO,KAAK,KAAK;gBACnB,CAAC;QACH;QAEA,IAAI;YACF,MAAM,WAAW;YACjB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,YAAY;YACZ;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eACJ,OAAO,UAAU,MAAM,WAAW;YACpC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,oCAAoC;IACpC,MAAM,mBAAmB,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,YAAY;IAEvE,iEAAiE;IACjE,MAAM,qBAAqB,CAAC;QAC1B,kCAAkC;QAClC,MAAM,kBAAkB,kBAAkB,MAAM,CAC9C,CAAC,OACC,KAAK,KAAK,KAAK,MAAM,iCAAiC;YACtD,CAAC,iBAAiB,QAAQ,CAAC,KAAK,KAAK,KACrC,SAAS,KAAK,CAAC,MAAM,CAAC,YAAY,KAAK,KAAK,KAAK;QAGrD,OAAO;IACT;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,MAAM;QACN,cAAc,CAAC;YACb,IAAI,CAAC,MAAM;gBACT,eAAe;gBACf;YACF;QACF;kBAEA,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YACZ,WAAU;YACV,mBAAmB;gBACjB,eAAe;gBACf;YACF;;8BAEA,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAsB;oDAAS,QAAQ;oDAAE;;;;;;;0DACvD,6LAAC,mJAAA,CAAA,aAAU;gDACT,SAAQ;gDACR,MAAM,2KAAA,CAAA,UAAO;gDACb,SAAS,IAAM,oBAAoB;gDACnC,UAAU,SAAS,KAAK,CAAC,MAAM,KAAK;0DACrC;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DACC,cAAA,6LAAC,oJAAA,CAAA,UAAQ;oDACP,OAAM;oDACN,MAAM,CAAC,QAAQ,EAAE,OAAO;oDACxB,SAAS,mBAAmB;oDAC5B,OAAO,KAAK,YAAY;oDACxB,UAAU,CAAC,IACT,oBAAoB,OAAO,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;0DAK/D,6LAAC;0DACC,cAAA,6LAAC,sJAAA,CAAA,aAAU;oDACT,OAAM;oDACN,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,KAAK,CAAC,QAAQ;oDAC1B,KAAI;oDACJ,UAAU,CAAC,IAAM,yBAAyB,OAAO;;;;;;;;;;;;;;;;;;+BAjC/C;;;;;sCAwCZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mJAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,MAAM,2KAAA,CAAA,SAAM;gCACZ,SAAS,CAAC,IAAM,iBAAiB;0CAClC;;;;;;;;;;;sCAKH,6LAAC,sJAAA,CAAA,aAAU;4BACT,OAAM;4BACN,aAAY;4BACZ,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU;;;;;;wBAEX,eAAe,OAAO,KAAK,kBAC1B,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,KAAK;;;;;;sCAGnD,6LAAC,oJAAA,CAAA,WAAQ;4BACP,OAAM;4BACN,aAAY;4BACZ,MAAK;4BACL,OAAO,SAAS,WAAW;4BAC3B,UAAU;;;;;;wBAEX,eAAe,OAAO,WAAW,kBAChC,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,WAAW;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC,sJAAA,CAAA,aAAU;wCACT,OAAM;wCACN,MAAK;wCACL,aAAY;wCACZ,MAAK;wCACL,OAAO,SAAS,WAAW,IAAI;wCAC/B,KAAI;wCACJ,UAAU,CAAC;4CACT,MAAM,QAAQ,EAAE,MAAM,CAAC,aAAa;4CACpC,YAAY;gDAAE,GAAG,QAAQ;gDAAE,aAAa;4CAAM;4CAC9C,IAAI,QAAQ,GAAG;gDACb,UAAU,CAAC,OAAS,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa;oDAAG,CAAC;4CACnD;wCACF;;;;;;oCAED,eAAe,OAAO,WAAW,kBAChC,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,WAAW;;;;;;;;;;;;;;;;;wBAK5D,eAAe,OAAO,QAAQ,kBAC7B,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,QAAQ;;;;;;sCAGtD,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;wCACP,eAAe;wCACf;oCACF;oCACA,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,sIAAA,CAAA,UAAO;gDAAC,MAAK;gDAAK,WAAU;;;;;;4CAAS;;;;;;+CAGxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GA1UwB;;QAIgB,0IAAA,CAAA,cAAW;QACb,0IAAA,CAAA,cAAW;QAC1B,0IAAA,CAAA,aAAU;;;KANT"}}, {"offset": {"line": 1508, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/popup/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactNode } from \"react\";\r\n\r\ninterface PopupProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title?: string;\r\n  width?: string;\r\n  height?: string;\r\n  children: ReactNode;\r\n}\r\n\r\nconst Popup: React.FC<PopupProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  width,\r\n  height = \"80vh\",\r\n  children,\r\n}) => {\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div\r\n        className=\"bg-white rounded-xl shadow-lg relative overflow-hidden w-[95vw] md:w-[80vw] lg:w-[60vw] max-w-4xl\"\r\n        style={{ width }}\r\n      >\r\n        <div className=\"bg-grayLight p-4 mb-4\">\r\n          {title && <h2 className=\"text-xl font-semibold \">{title}</h2>}\r\n          <button\r\n            onClick={onClose}\r\n            className=\"absolute top-5 right-4 text-xs text-gray-500 hover:text-black\"\r\n            aria-label=\"Close\"\r\n          >\r\n            ✖\r\n          </button>\r\n        </div>\r\n        <div\r\n          className=\"popupz p-4 pb-6 overflow-y-auto overflow-x-hidden\"\r\n          style={{ maxHeight: height }}\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Popup;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,SAAS,MAAM,EACf,QAAQ,EACT;IACC,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAM;;8BAEf,6LAAC;oBAAI,WAAU;;wBACZ,uBAAS,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAClD,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCACZ;;;;;;;;;;;;8BAIH,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,WAAW;oBAAO;8BAE1B;;;;;;;;;;;;;;;;;AAKX;KAnCM;uCAqCS"}}, {"offset": {"line": 1587, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1593, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useFormState.ts"], "sourcesContent": ["import { useState } from \"react\";\r\n\r\nfunction useFormState<T extends Record<string, any>>(initialState: T) {\r\n  const [formData, setFormData] = useState(initialState);\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<\r\n      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement\r\n    >\r\n  ) => {\r\n    const { name, type } = e.target;\r\n\r\n    // Narrow down the type to HTMLInputElement for checkboxes and radio buttons\r\n    if (\r\n      e.target instanceof HTMLInputElement &&\r\n      (type === \"checkbox\" || type === \"radio\")\r\n    ) {\r\n      const { checked } = e.target;\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        [name]: checked, // Use `checked` for checkboxes and radio buttons\r\n      }));\r\n    } else {\r\n      const { value } = e.target;\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        [name]: value, // Use `value` for other input types\r\n      }));\r\n    }\r\n  };\r\n\r\n  return { formData, setFormData, handleChange };\r\n}\r\n\r\nexport default useFormState;\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,SAAS,aAA4C,YAAe;;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe,CACnB;QAIA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QAE/B,4EAA4E;QAC5E,IACE,EAAE,MAAM,YAAY,oBACpB,CAAC,SAAS,cAAc,SAAS,OAAO,GACxC;YACA,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;YAC5B,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH,OAAO;YACL,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;YAC1B,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,OAAO;QAAE;QAAU;QAAa;IAAa;AAC/C;GA9BS;uCAgCM"}}, {"offset": {"line": 1630, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1636, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/view/popup/EditPackagePopup.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { InputField } from \"@/app/components/common/inputField\";\r\nimport { Textarea } from \"@/app/components/common/textarea\";\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport useFormState from \"@/app/hooks/useFormState\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport { faClose } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { usePackages } from \"@/app/hooks/counselor/usePackage\";\r\nimport { PackageData } from \"@/app/types/counselor/package\";\r\nimport Dropdown from \"@/app/components/common/dropdown\";\r\nimport { useServices } from \"@/app/hooks/counselor/useService\";\r\nimport { useProfile } from \"@/app/hooks/counselor/useProfile\";\r\nimport { toast } from \"react-toastify\";\r\nimport { Spinner } from \"@/app/components/ui/spinner\";\r\n\r\ninterface InitialStateProps {\r\n  title: string;\r\n  description: string;\r\n  total_price: number;\r\n  items: { service_name: string; custom_type: string; hours: number }[];\r\n}\r\n\r\nconst initialState = {\r\n  title: \"\",\r\n  description: \"\",\r\n  total_price: 0,\r\n  items: [{ service_name: \"\", custom_type: \"\", hours: 0 }],\r\n};\r\n\r\ninterface EditProps extends Omit<PopupProps, \"packageData\"> {\r\n  packageData: PackageData | null;\r\n}\r\n\r\nexport default function EditPackagePopup({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n  packageData,\r\n}: EditProps) {\r\n  const { updatePackage, fetchPackages } = usePackages();\r\n  const { services, fetchServices } = useServices();\r\n  const { userInfo } = useProfile();\r\n\r\n  const {\r\n    formData,\r\n    setFormData,\r\n    handleChange: baseHandleChange,\r\n  } = useFormState<InitialStateProps>(initialState);\r\n\r\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\r\n  const [counselorServices, setCounselorServices] = useState<\r\n    { value: string; label: string }[]\r\n  >([]);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // Fetch counselor services when the dialog opens\r\n  useEffect(() => {\r\n    if (isPopupOpen && userInfo?.counselor_id) {\r\n      fetchServices(userInfo.counselor_id);\r\n    }\r\n  }, [isPopupOpen, userInfo, fetchServices]);\r\n\r\n  // Convert services to dropdown options\r\n  useEffect(() => {\r\n    if (services.length > 0) {\r\n      const serviceOptions = [\r\n        { value: \"\", label: \"Please select\" },\r\n        ...services.map((service) => ({\r\n          value: service.custom_type || service.service_type,\r\n          label: service.custom_type || service.service_type,\r\n        })),\r\n      ];\r\n      setCounselorServices(serviceOptions);\r\n    }\r\n  }, [services]);\r\n\r\n  useEffect(() => {\r\n    if (packageData) {\r\n      setFormData({\r\n        title: packageData.title,\r\n        description: packageData.description,\r\n        total_price: packageData.total_price,\r\n        items: packageData.package_items.map((item) => ({\r\n          service_name: item.service_name,\r\n          custom_type: \"\", // This field is not returned from the API\r\n          hours: item.hours,\r\n        })),\r\n      });\r\n    }\r\n  }, [packageData, setFormData]);\r\n\r\n  const validateForm = (): boolean => {\r\n    const newErrors: { [key: string]: string } = {};\r\n\r\n    if (!formData.title.trim()) {\r\n      newErrors.title = \"Package title is required.\";\r\n    }\r\n    if (!formData.description.trim()) {\r\n      newErrors.description = \"Description is required.\";\r\n    }\r\n    if (!formData.total_price || formData.total_price <= 0) {\r\n      newErrors.total_price = \"Price must be a positive number.\";\r\n    }\r\n\r\n    // Validate services - must have at least one valid service\r\n    if (formData.items.length === 0) {\r\n      newErrors.services = \"At least one service is required.\";\r\n    } else {\r\n      // Check if all provided services have valid data\r\n      const hasEmptyServices = formData.items.some(\r\n        (item) => !item.service_name.trim() || item.hours <= 0\r\n      );\r\n      if (hasEmptyServices) {\r\n        newErrors.services =\r\n          \"All service fields must be filled with valid values.\";\r\n      }\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\r\n  ) => {\r\n    const { name } = e.target;\r\n\r\n    if (errors[name as keyof typeof errors]) {\r\n      const newErrors = { ...errors, [name]: \"\" };\r\n      setErrors(newErrors);\r\n    }\r\n\r\n    baseHandleChange(e);\r\n  };\r\n\r\n  const handleServiceChange = (\r\n    index: number,\r\n    field: \"service_name\" | \"custom_type\" | \"hours\",\r\n    value: string | number\r\n  ) => {\r\n    const updatedServices = [...formData.items];\r\n    updatedServices[index] = {\r\n      ...updatedServices[index],\r\n      [field]: value,\r\n    };\r\n\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      items: updatedServices,\r\n    }));\r\n\r\n    // Clear service-related errors\r\n    if (errors.items) {\r\n      setErrors((prev) => ({ ...prev, items: \"\" }));\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    toast.info(\"Updating package...\");\r\n\r\n    try {\r\n      const formattedData = {\r\n        ...formData,\r\n        items: formData.items.map((item) => ({\r\n          service_name: item.service_name,\r\n          hours: item.hours,\r\n        })),\r\n      };\r\n\r\n      await updatePackage(packageData?.id, formattedData);\r\n      setIsPopupOpen(false);\r\n      setFormData(initialState);\r\n      fetchPackages();\r\n    } catch (error) {\r\n      console.error(\"Error updating package:\", error);\r\n      toast.error(\"Failed to update package. Please try again.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Get the list of selected services\r\n  const selectedServices = formData.items.map((item) => item.service_name);\r\n\r\n  // Filter out already selected services from the dropdown options\r\n  const getFilteredOptions = (index: number) => {\r\n    return counselorServices.filter(\r\n      (item: { value: string; label: string }) =>\r\n        item.value === \"\" || // Always include the placeholder\r\n        !selectedServices.includes(item.value) ||\r\n        formData.items[index].service_name === item.value\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Popup\r\n      isOpen={isPopupOpen}\r\n      onClose={() => setIsPopupOpen(false)}\r\n      title=\"Update Package\"\r\n    >\r\n      <form onSubmit={handleSubmit} className=\"max-w-full overflow-x-hidden\">\r\n        <p className=\"text-sm text-gray-500 mb-4\">\r\n          Fields marked with * are required\r\n        </p>\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <h3 className=\"text-lg font-medium\">Services *</h3>\r\n            <button\r\n              type=\"button\"\r\n              className=\"px-3 py-1 bg-blue-50 text-blue-600 rounded-md text-sm font-medium hover:bg-blue-100 transition-colors\"\r\n              onClick={() => {\r\n                setFormData((prev) => ({\r\n                  ...prev,\r\n                  items: [\r\n                    ...prev.items,\r\n                    { service_name: \"\", custom_type: \"\", hours: 0 },\r\n                  ],\r\n                }));\r\n              }}\r\n            >\r\n              + Add Service\r\n            </button>\r\n          </div>\r\n\r\n          {formData.items.map((item, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"mb-6 p-4 bg-gray-50 rounded-lg relative\"\r\n            >\r\n              <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-4\">\r\n                <h3 className=\"text-lg font-medium\">Service {index + 1}</h3>\r\n                {formData.items.length > 1 && (\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"text-red-500 hover:text-red-700 transition-colors mt-2 sm:mt-0\"\r\n                    onClick={() => {\r\n                      const updatedItems = [...formData.items];\r\n                      updatedItems.splice(index, 1);\r\n                      setFormData((prev) => ({ ...prev, items: updatedItems }));\r\n                    }}\r\n                  >\r\n                    Remove\r\n                  </button>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"space-y-4\">\r\n                <Dropdown\r\n                  label=\"Service Name *\"\r\n                  name=\"service_name\"\r\n                  options={getFilteredOptions(index)}\r\n                  value={item.service_name}\r\n                  onChange={(e) =>\r\n                    handleServiceChange(index, \"service_name\", e.target.value)\r\n                  }\r\n                  selectStyle=\"w-full border p-3 bg-neutral1\"\r\n                />\r\n\r\n                {item.service_name === \"Other\" && (\r\n                  <InputField\r\n                    label=\"Specify Other\"\r\n                    name=\"custom_type\"\r\n                    value={item.custom_type}\r\n                    onChange={(e) =>\r\n                      handleServiceChange(index, \"custom_type\", e.target.value)\r\n                    }\r\n                  />\r\n                )}\r\n\r\n                <InputField\r\n                  label=\"Hours *\"\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  placeholder=\"Enter hours\"\r\n                  value={item.hours === 0 ? \"\" : item.hours}\r\n                  onChange={(e) =>\r\n                    handleServiceChange(\r\n                      index,\r\n                      \"hours\",\r\n                      parseInt(e.target.value) || 0\r\n                    )\r\n                  }\r\n                />\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {errors.services && (\r\n          <p className=\"text-red-500 text-sm mb-4\">{errors.services}</p>\r\n        )}\r\n\r\n        <InputField\r\n          label=\"Package Title *\"\r\n          type=\"text\"\r\n          placeholder=\"Eg. Essay review session for $200\"\r\n          name=\"title\"\r\n          value={formData.title}\r\n          onChange={handleChange}\r\n        />\r\n        {errors.title && <p className=\"text-red-500 text-sm\">{errors.title}</p>}\r\n\r\n        <div className=\"my-4\">\r\n          <Textarea\r\n            label=\"Description *\"\r\n            name=\"description\"\r\n            value={formData.description}\r\n            onChange={handleChange}\r\n            placeholder=\"Enter here..\"\r\n            rows={5}\r\n          />\r\n          {errors.description && (\r\n            <p className=\"text-red-500 text-sm\">{errors.description}</p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n          <div>\r\n            <InputField\r\n              label=\"Price ($) *\"\r\n              type=\"number\"\r\n              placeholder=\"Enter price\"\r\n              name=\"total_price\"\r\n              value={formData.total_price.toString()}\r\n              min=\"0\"\r\n              onChange={(e) => {\r\n                const value = parseFloat(e.target.value);\r\n                setFormData({ ...formData, total_price: value });\r\n                if (value > 0) {\r\n                  setErrors((prev) => ({ ...prev, total_price: \"\" }));\r\n                }\r\n              }}\r\n            />\r\n            {errors.total_price && (\r\n              <p className=\"text-red-500 text-sm\">{errors.total_price}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex justify-end space-x-4 mt-6\">\r\n          <MainButton\r\n            type=\"button\"\r\n            variant=\"neutral\"\r\n            children=\"Cancel\"\r\n            icon={faClose}\r\n            onClick={() => {\r\n              setIsPopupOpen(false);\r\n              setFormData(initialState);\r\n              setErrors({});\r\n            }}\r\n            disabled={isSubmitting}\r\n          />\r\n          <MainButton\r\n            type=\"submit\"\r\n            variant=\"primary\"\r\n            disabled={isSubmitting}\r\n            className=\"min-w-[100px]\"\r\n          >\r\n            {isSubmitting ? (\r\n              <div className=\"flex items-center justify-center\">\r\n                <Spinner size=\"sm\" className=\"mr-2\" /> Updating...\r\n              </div>\r\n            ) : (\r\n              \"Update\"\r\n            )}\r\n          </MainButton>\r\n        </div>\r\n      </form>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAPA;;;AATA;;;;;;;;;;;;;;AAyBA,MAAM,eAAe;IACnB,OAAO;IACP,aAAa;IACb,aAAa;IACb,OAAO;QAAC;YAAE,cAAc;YAAI,aAAa;YAAI,OAAO;QAAE;KAAE;AAC1D;AAMe,SAAS,iBAAiB,EACvC,WAAW,EACX,cAAc,EACd,WAAW,EACD;;IACV,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD;IACnD,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD;IAC9C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD;IAE9B,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,cAAc,gBAAgB,EAC/B,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD,EAAqB;IAEpC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEvD,EAAE;IACJ,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,eAAe,UAAU,cAAc;gBACzC,cAAc,SAAS,YAAY;YACrC;QACF;qCAAG;QAAC;QAAa;QAAU;KAAc;IAEzC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,MAAM,iBAAiB;oBACrB;wBAAE,OAAO;wBAAI,OAAO;oBAAgB;uBACjC,SAAS,GAAG;sDAAC,CAAC,UAAY,CAAC;gCAC5B,OAAO,QAAQ,WAAW,IAAI,QAAQ,YAAY;gCAClD,OAAO,QAAQ,WAAW,IAAI,QAAQ,YAAY;4BACpD,CAAC;;iBACF;gBACD,qBAAqB;YACvB;QACF;qCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,aAAa;gBACf,YAAY;oBACV,OAAO,YAAY,KAAK;oBACxB,aAAa,YAAY,WAAW;oBACpC,aAAa,YAAY,WAAW;oBACpC,OAAO,YAAY,aAAa,CAAC,GAAG;sDAAC,CAAC,OAAS,CAAC;gCAC9C,cAAc,KAAK,YAAY;gCAC/B,aAAa;gCACb,OAAO,KAAK,KAAK;4BACnB,CAAC;;gBACH;YACF;QACF;qCAAG;QAAC;QAAa;KAAY;IAE7B,MAAM,eAAe;QACnB,MAAM,YAAuC,CAAC;QAE9C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QACA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,UAAU,WAAW,GAAG;QAC1B;QACA,IAAI,CAAC,SAAS,WAAW,IAAI,SAAS,WAAW,IAAI,GAAG;YACtD,UAAU,WAAW,GAAG;QAC1B;QAEA,2DAA2D;QAC3D,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAAG;YAC/B,UAAU,QAAQ,GAAG;QACvB,OAAO;YACL,iDAAiD;YACjD,MAAM,mBAAmB,SAAS,KAAK,CAAC,IAAI,CAC1C,CAAC,OAAS,CAAC,KAAK,YAAY,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI;YAEvD,IAAI,kBAAkB;gBACpB,UAAU,QAAQ,GAChB;YACJ;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CACnB;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QAEzB,IAAI,MAAM,CAAC,KAA4B,EAAE;YACvC,MAAM,YAAY;gBAAE,GAAG,MAAM;gBAAE,CAAC,KAAK,EAAE;YAAG;YAC1C,UAAU;QACZ;QAEA,iBAAiB;IACnB;IAEA,MAAM,sBAAsB,CAC1B,OACA,OACA;QAEA,MAAM,kBAAkB;eAAI,SAAS,KAAK;SAAC;QAC3C,eAAe,CAAC,MAAM,GAAG;YACvB,GAAG,eAAe,CAAC,MAAM;YACzB,CAAC,MAAM,EAAE;QACX;QAEA,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,OAAO;YACT,CAAC;QAED,+BAA+B;QAC/B,IAAI,OAAO,KAAK,EAAE;YAChB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QAEX,IAAI;YACF,MAAM,gBAAgB;gBACpB,GAAG,QAAQ;gBACX,OAAO,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;wBACnC,cAAc,KAAK,YAAY;wBAC/B,OAAO,KAAK,KAAK;oBACnB,CAAC;YACH;YAEA,MAAM,cAAc,aAAa,IAAI;YACrC,eAAe;YACf,YAAY;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,oCAAoC;IACpC,MAAM,mBAAmB,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,YAAY;IAEvE,iEAAiE;IACjE,MAAM,qBAAqB,CAAC;QAC1B,OAAO,kBAAkB,MAAM,CAC7B,CAAC,OACC,KAAK,KAAK,KAAK,MAAM,iCAAiC;YACtD,CAAC,iBAAiB,QAAQ,CAAC,KAAK,KAAK,KACrC,SAAS,KAAK,CAAC,MAAM,CAAC,YAAY,KAAK,KAAK,KAAK;IAEvD;IAEA,qBACE,6LAAC,iJAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,eAAe;QAC9B,OAAM;kBAEN,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,YAAY,CAAC,OAAS,CAAC;gDACrB,GAAG,IAAI;gDACP,OAAO;uDACF,KAAK,KAAK;oDACb;wDAAE,cAAc;wDAAI,aAAa;wDAAI,OAAO;oDAAE;iDAC/C;4CACH,CAAC;oCACH;8CACD;;;;;;;;;;;;wBAKF,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAsB;oDAAS,QAAQ;;;;;;;4CACpD,SAAS,KAAK,CAAC,MAAM,GAAG,mBACvB,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;oDACP,MAAM,eAAe;2DAAI,SAAS,KAAK;qDAAC;oDACxC,aAAa,MAAM,CAAC,OAAO;oDAC3B,YAAY,CAAC,OAAS,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO;wDAAa,CAAC;gDACzD;0DACD;;;;;;;;;;;;kDAML,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oJAAA,CAAA,UAAQ;gDACP,OAAM;gDACN,MAAK;gDACL,SAAS,mBAAmB;gDAC5B,OAAO,KAAK,YAAY;gDACxB,UAAU,CAAC,IACT,oBAAoB,OAAO,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAE3D,aAAY;;;;;;4CAGb,KAAK,YAAY,KAAK,yBACrB,6LAAC,sJAAA,CAAA,aAAU;gDACT,OAAM;gDACN,MAAK;gDACL,OAAO,KAAK,WAAW;gDACvB,UAAU,CAAC,IACT,oBAAoB,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;0DAK9D,6LAAC,sJAAA,CAAA,aAAU;gDACT,OAAM;gDACN,MAAK;gDACL,KAAI;gDACJ,aAAY;gDACZ,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK;gDACzC,UAAU,CAAC,IACT,oBACE,OACA,SACA,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;+BArD/B;;;;;;;;;;;gBA8DV,OAAO,QAAQ,kBACd,6LAAC;oBAAE,WAAU;8BAA6B,OAAO,QAAQ;;;;;;8BAG3D,6LAAC,sJAAA,CAAA,aAAU;oBACT,OAAM;oBACN,MAAK;oBACL,aAAY;oBACZ,MAAK;oBACL,OAAO,SAAS,KAAK;oBACrB,UAAU;;;;;;gBAEX,OAAO,KAAK,kBAAI,6LAAC;oBAAE,WAAU;8BAAwB,OAAO,KAAK;;;;;;8BAElE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oJAAA,CAAA,WAAQ;4BACP,OAAM;4BACN,MAAK;4BACL,OAAO,SAAS,WAAW;4BAC3B,UAAU;4BACV,aAAY;4BACZ,MAAM;;;;;;wBAEP,OAAO,WAAW,kBACjB,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,WAAW;;;;;;;;;;;;8BAI3D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;0CACC,6LAAC,sJAAA,CAAA,aAAU;gCACT,OAAM;gCACN,MAAK;gCACL,aAAY;gCACZ,MAAK;gCACL,OAAO,SAAS,WAAW,CAAC,QAAQ;gCACpC,KAAI;gCACJ,UAAU,CAAC;oCACT,MAAM,QAAQ,WAAW,EAAE,MAAM,CAAC,KAAK;oCACvC,YAAY;wCAAE,GAAG,QAAQ;wCAAE,aAAa;oCAAM;oCAC9C,IAAI,QAAQ,GAAG;wCACb,UAAU,CAAC,OAAS,CAAC;gDAAE,GAAG,IAAI;gDAAE,aAAa;4CAAG,CAAC;oCACnD;gCACF;;;;;;4BAED,OAAO,WAAW,kBACjB,6LAAC;gCAAE,WAAU;0CAAwB,OAAO,WAAW;;;;;;;;;;;;;;;;;8BAK7D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,SAAQ;4BACR,UAAS;4BACT,MAAM,2KAAA,CAAA,UAAO;4BACb,SAAS;gCACP,eAAe;gCACf,YAAY;gCACZ,UAAU,CAAC;4BACb;4BACA,UAAU;;;;;;sCAEZ,6LAAC,mJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,SAAQ;4BACR,UAAU;4BACV,WAAU;sCAET,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,sIAAA,CAAA,UAAO;wCAAC,MAAK;wCAAK,WAAU;;;;;;oCAAS;;;;;;uCAGxC;;;;;;;;;;;;;;;;;;;;;;;AAOd;GAvVwB;;QAKmB,0IAAA,CAAA,cAAW;QAChB,0IAAA,CAAA,cAAW;QAC1B,0IAAA,CAAA,aAAU;QAM3B,+HAAA,CAAA,UAAY;;;KAbM"}}, {"offset": {"line": 2172, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2178, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/view/popup/DeletePackagePopup.tsx"], "sourcesContent": ["import { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport { PackageData } from \"@/app/types/counselor/package\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\nimport { faCheck, faClose } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\n\r\ninterface DeleteProps extends Omit<PopupProps, \"packageData\"> {\r\n  packageData: PackageData | null;\r\n}\r\n\r\nexport default function DeletePackagePopup({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n  packageData,\r\n  onSave,\r\n}: DeleteProps) {\r\n  const handleRemove = async () => {\r\n    if (onSave) {\r\n      await onSave();\r\n      setIsPopupOpen(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Popup\r\n      isOpen={isPopupOpen}\r\n      onClose={() => setIsPopupOpen(false)}\r\n      title=\"Remove Package\"\r\n      height=\"auto\"\r\n    >\r\n      <div className=\"mx-4 bg-neutral1 py-2 px-4 rounded-md mb-4\">\r\n        <h3 className=\"text-neutral8\">Do you want to remove this package</h3>\r\n\r\n        <p className=\"text-neutral5 text-sm\">\r\n          This service will be permanently removed from your profile.\r\n        </p>\r\n      </div>\r\n\r\n      <hr />\r\n\r\n      <div className=\"p-4\">\r\n        {/* <h2 className=\"mb-3 text-lg\">{packageData.service_type}</h2> */}\r\n        <p className=\"text-neutral8\">{packageData?.title}</p>\r\n        <p className=\"text-sm mb-3\">{packageData?.description}</p>\r\n        <p className=\"mb-1 text-neutral5\">Price</p>\r\n        <p className=\"text-3xl\">${packageData?.total_price}</p>\r\n      </div>\r\n\r\n      <hr />\r\n\r\n      <div className=\"flex justify-end gap-3 p-4\">\r\n        <MainButton variant=\"neutral\" onClick={() => setIsPopupOpen(false)}>\r\n          Cancel <FontAwesomeIcon icon={faClose} />\r\n        </MainButton>\r\n\r\n        <MainButton variant=\"secondary\" onClick={handleRemove}>\r\n          Remove <FontAwesomeIcon icon={faCheck} />\r\n        </MainButton>\r\n      </div>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAIA;AADA;;;;;;AAOe,SAAS,mBAAmB,EACzC,WAAW,EACX,cAAc,EACd,WAAW,EACX,MAAM,EACM;IACZ,MAAM,eAAe;QACnB,IAAI,QAAQ;YACV,MAAM;YACN,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC,iJAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,eAAe;QAC9B,OAAM;QACN,QAAO;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAE9B,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,6LAAC;;;;;0BAED,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAE,WAAU;kCAAiB,aAAa;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAgB,aAAa;;;;;;kCAC1C,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBAAE,WAAU;;4BAAW;4BAAE,aAAa;;;;;;;;;;;;;0BAGzC,6LAAC;;;;;0BAED,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mJAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAU,SAAS,IAAM,eAAe;;4BAAQ;0CAC3D,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,UAAO;;;;;;;;;;;;kCAGvC,6LAAC,mJAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAY,SAAS;;4BAAc;0CAC9C,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;AAK/C;KAnDwB"}}, {"offset": {"line": 2341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2347, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@components/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 2389, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2395, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-12 items-center justify-start border-b w-full overflow-x-auto\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap px-4 py-3 text-base font-medium text-gray-600 border-b-2 border-transparent transition-colors hover:text-gray-900\",\r\n      \"data-[state=active]:border-gray-900 data-[state=active]:text-gray-900\",\r\n      \"focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-4 ring-offset-background focus-visible:outline-none\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,oKAAc,IAAI;AAE/B,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,oKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,+KAC<PERSON>,yEACA,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2461, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/packages/SessionsListDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} from \"@/app/components/ui/dialog\";\r\nimport { PackageSubscription } from \"@/app/types/counselor/package\";\r\nimport { Badge } from \"@/app/components/ui/badge\";\r\nimport {\r\n  Calendar,\r\n  Clock,\r\n  User,\r\n  FileText,\r\n  CheckCircle,\r\n  XCircle,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport {\r\n  Tabs,\r\n  TabsContent,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from \"@/app/components/ui/tabs\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface SessionsListDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  subscription: PackageSubscription | null;\r\n}\r\n\r\nexport default function SessionsListDialog({\r\n  isOpen,\r\n  onClose,\r\n  subscription,\r\n}: SessionsListDialogProps) {\r\n  const [activeTab, setActiveTab] = useState(\"all\");\r\n\r\n  if (!subscription) return null;\r\n\r\n  // Filter sessions based on active tab\r\n  const filteredSessions = subscription.sessions.filter((session) => {\r\n    if (activeTab === \"all\") return true;\r\n    return session.status === activeTab;\r\n  });\r\n\r\n  // Get status color\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return \"bg-green-100 text-green-800 border-green-200\";\r\n      case \"upcoming\":\r\n        return \"bg-blue-100 text-blue-800 border-blue-200\";\r\n      case \"cancelled\":\r\n        return \"bg-red-100 text-red-800 border-red-200\";\r\n      default:\r\n        return \"bg-gray-100 text-gray-800 border-gray-200\";\r\n    }\r\n  };\r\n\r\n  // Get status icon\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return <CheckCircle className=\"w-4 h-4 mx-2 my-1 text-green-600\" />;\r\n      case \"upcoming\":\r\n        return <Clock className=\"w-4 h-4 mx-2 my-1 text-blue-600\" />;\r\n      case \"cancelled\":\r\n        return <XCircle className=\"w-4 h-4 mx-2 my-1 text-red-600\" />;\r\n      default:\r\n        return <FileText className=\"w-4 h-4 mx-2 my-1 text-gray-600\" />;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-3xl\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-2xl font-bold flex items-center gap-2\">\r\n            <User className=\"w-5 h-5\" />\r\n            {subscription.student.name}'s Sessions\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Package:{\" \"}\r\n            <span className=\"font-medium\">{subscription.package.title}</span>\r\n            <span className=\"mx-2\">•</span>\r\n            <span\r\n              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(\r\n                subscription.status\r\n              )}`}\r\n            >\r\n              {subscription.status.charAt(0).toUpperCase() +\r\n                subscription.status.slice(1)}\r\n            </span>\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"mt-4\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\r\n            {Object.entries(subscription.service_hours).map(\r\n              ([service, hours]) => (\r\n                <div key={service} className=\"bg-gray-50 p-4 rounded-lg\">\r\n                  <h4 className=\"text-sm text-gray-500 mb-1\">Service Hours</h4>\r\n                  <p className=\"font-medium\">{service}</p>\r\n                  <div className=\"mt-2 flex items-center gap-2\">\r\n                    <div className=\"bg-gray-200 h-2 flex-1 rounded-full overflow-hidden\">\r\n                      <div\r\n                        className=\"bg-blue-500 h-full\"\r\n                        style={{\r\n                          width: `${(hours.used / hours.total) * 100}%`,\r\n                        }}\r\n                      ></div>\r\n                    </div>\r\n                    <span className=\"text-xs font-medium whitespace-nowrap\">\r\n                      {hours.used}/{hours.total} hrs\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              )\r\n            )}\r\n          </div>\r\n\r\n          <Tabs\r\n            defaultValue=\"all\"\r\n            className=\"w-full\"\r\n            onValueChange={setActiveTab}\r\n          >\r\n            <TabsList className=\"mb-4\">\r\n              <TabsTrigger value=\"all\">All Sessions</TabsTrigger>\r\n              <TabsTrigger value=\"upcoming\">Upcoming</TabsTrigger>\r\n              <TabsTrigger value=\"completed\">Completed</TabsTrigger>\r\n              <TabsTrigger value=\"cancelled\">Cancelled</TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value={activeTab} className=\"mt-0\">\r\n              {filteredSessions.length === 0 ? (\r\n                <div className=\"text-center py-12 bg-gray-50 rounded-lg\">\r\n                  <h3 className=\"text-lg font-medium text-gray-700 mb-2\">\r\n                    No {activeTab !== \"all\" ? activeTab : \"\"} sessions found\r\n                  </h3>\r\n                  <p className=\"text-gray-500\">\r\n                    {activeTab === \"upcoming\"\r\n                      ? \"No upcoming sessions scheduled yet\"\r\n                      : activeTab === \"completed\"\r\n                      ? \"No completed sessions yet\"\r\n                      : activeTab === \"cancelled\"\r\n                      ? \"No cancelled sessions\"\r\n                      : \"No sessions have been scheduled yet\"}\r\n                  </p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-3\">\r\n                  {filteredSessions.map((session, index) => (\r\n                    <motion.div\r\n                      key={session.id}\r\n                      initial={{ opacity: 0, y: 10 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ duration: 0.2, delay: index * 0.05 }}\r\n                      className=\"border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow\"\r\n                    >\r\n                      <div className=\"flex flex-col md:flex-row md:items-center justify-between gap-4\">\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            <h3 className=\"font-medium text-lg\">\r\n                              {session.service_name}\r\n                            </h3>\r\n                          </div>\r\n\r\n                          <div className=\"flex flex-col sm:flex-row sm:items-center gap-x-4 gap-y-1 text-sm text-gray-600 mt-2\">\r\n                            <div className=\"flex items-center gap-1\">\r\n                              <Calendar className=\"w-4 h-4\" />\r\n                              <span>\r\n                                {new Date(session.date).toLocaleDateString()}\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"flex items-center gap-1\">\r\n                              <Clock className=\"w-4 h-4\" />\r\n                              <span>\r\n                                {new Date(\r\n                                  session.start_time\r\n                                ).toLocaleTimeString([], {\r\n                                  hour: \"2-digit\",\r\n                                  minute: \"2-digit\",\r\n                                })}\r\n                                {\" - \"}\r\n                                {new Date(session.end_time).toLocaleTimeString(\r\n                                  [],\r\n                                  {\r\n                                    hour: \"2-digit\",\r\n                                    minute: \"2-digit\",\r\n                                  }\r\n                                )}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                        <Badge\r\n                          variant=\"outline\"\r\n                          className={`ml-auto ${getStatusColor(\r\n                            session.status\r\n                          )}`}\r\n                        >\r\n                          {getStatusIcon(session.status)}\r\n                          {session.status}\r\n                        </Badge>\r\n                      </div>\r\n                    </motion.div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </TabsContent>\r\n          </Tabs>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AAUA;AATA;AAAA;AAAA;AAAA;AAAA;AAeA;AAfA;;;AAZA;;;;;;;AAmCe,SAAS,mBAAmB,EACzC,MAAM,EACN,OAAO,EACP,YAAY,EACY;;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,CAAC,cAAc,OAAO;IAE1B,sCAAsC;IACtC,MAAM,mBAAmB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,cAAc,OAAO,OAAO;QAChC,OAAO,QAAQ,MAAM,KAAK;IAC5B;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACf,aAAa,OAAO,CAAC,IAAI;gCAAC;;;;;;;sCAE7B,6LAAC,qIAAA,CAAA,oBAAiB;;gCAAC;gCACR;8CACT,6LAAC;oCAAK,WAAU;8CAAe,aAAa,OAAO,CAAC,KAAK;;;;;;8CACzD,6LAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,6LAAC;oCACC,WAAW,CAAC,wEAAwE,EAAE,eACpF,aAAa,MAAM,GAClB;8CAEF,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KACxC,aAAa,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;8BAKlC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,aAAa,aAAa,EAAE,GAAG,CAC7C,CAAC,CAAC,SAAS,MAAM,iBACf,6LAAC;oCAAkB,WAAU;;sDAC3B,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAe;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,OAAO,GAAG,AAAC,MAAM,IAAI,GAAG,MAAM,KAAK,GAAI,IAAI,CAAC,CAAC;wDAC/C;;;;;;;;;;;8DAGJ,6LAAC;oDAAK,WAAU;;wDACb,MAAM,IAAI;wDAAC;wDAAE,MAAM,KAAK;wDAAC;;;;;;;;;;;;;;mCAbtB;;;;;;;;;;sCAqBhB,6LAAC,mIAAA,CAAA,OAAI;4BACH,cAAa;4BACb,WAAU;4BACV,eAAe;;8CAEf,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAM;;;;;;sDACzB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;sDAC9B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAY;;;;;;sDAC/B,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAY;;;;;;;;;;;;8CAGjC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAO;oCAAW,WAAU;8CACtC,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAyC;oDACjD,cAAc,QAAQ,YAAY;oDAAG;;;;;;;0DAE3C,6LAAC;gDAAE,WAAU;0DACV,cAAc,aACX,uCACA,cAAc,cACd,8BACA,cAAc,cACd,0BACA;;;;;;;;;;;6DAIR,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAK;gDACjD,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAG,WAAU;kFACX,QAAQ,YAAY;;;;;;;;;;;8EAIzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,6LAAC;8FACE,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;;;;;;;;;;;;sFAG9C,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,6LAAC;;wFACE,IAAI,KACH,QAAQ,UAAU,EAClB,kBAAkB,CAAC,EAAE,EAAE;4FACvB,MAAM;4FACN,QAAQ;wFACV;wFACC;wFACA,IAAI,KAAK,QAAQ,QAAQ,EAAE,kBAAkB,CAC5C,EAAE,EACF;4FACE,MAAM;4FACN,QAAQ;wFACV;;;;;;;;;;;;;;;;;;;;;;;;;sEAMV,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAW,CAAC,QAAQ,EAAE,eACpB,QAAQ,MAAM,GACb;;gEAEF,cAAc,QAAQ,MAAM;gEAC5B,QAAQ,MAAM;;;;;;;;;;;;;+CAjDd,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DrC;GAzLwB;KAAA"}}, {"offset": {"line": 2933, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2939, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/threeDotIconMenu/index.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faEllipsisV,\r\n  faPencil,\r\n  faTrash,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\n\r\ninterface DropdownProps {\r\n  handleEdit: () => void;\r\n  handleRemove: () => void;\r\n  name?: string;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst ThreeDotIconDropdown: React.FC<DropdownProps> = ({\r\n  handleEdit,\r\n  handleRemove,\r\n  name = \"Edit\",\r\n  isLoading = false,\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  const toggleMenu = () => setIsOpen((prev) => !prev);\r\n\r\n  const handleClickOutside = (event: MouseEvent) => {\r\n    if (\r\n      dropdownRef.current &&\r\n      !dropdownRef.current.contains(event.target as Node)\r\n    ) {\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"relative\" ref={dropdownRef}>\r\n      <button\r\n        onClick={toggleMenu}\r\n        className={isLoading ? \"opacity-50 cursor-wait\" : \"\"}\r\n        disabled={isLoading}\r\n      >\r\n        <FontAwesomeIcon\r\n          icon={faEllipsisV}\r\n          className={`w-4 ${isLoading ? \"animate-pulse\" : \"\"}`}\r\n        />\r\n      </button>\r\n      {isOpen && (\r\n        <ul className=\"absolute top-full right-0 text-sm p-3 bg-white rounded-lg shadow-md w-36\">\r\n          <li\r\n            className=\"mb-2 last:mb-0 cursor-pointer flex items-center text-neutral8\"\r\n            onClick={() => {\r\n              handleEdit();\r\n              setIsOpen(false);\r\n            }}\r\n          >\r\n            <FontAwesomeIcon icon={faPencil} className=\"w-4 mr-2\" />\r\n            {name}\r\n          </li>\r\n          <li\r\n            className=\"cursor-pointer flex items-center text-[#EB5757]\"\r\n            onClick={() => {\r\n              handleRemove();\r\n              setIsOpen(false);\r\n            }}\r\n          >\r\n            <FontAwesomeIcon icon={faTrash} className=\"w-4 mr-2\" />\r\n            Remove\r\n          </li>\r\n        </ul>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ThreeDotIconDropdown;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAaA,MAAM,uBAAgD,CAAC,EACrD,UAAU,EACV,YAAY,EACZ,OAAO,MAAM,EACb,YAAY,KAAK,EAClB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,aAAa,IAAM,UAAU,CAAC,OAAS,CAAC;IAE9C,MAAM,qBAAqB,CAAC;QAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;YACA,UAAU;QACZ;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,SAAS,gBAAgB,CAAC,aAAa;YACvC;kDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;yCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,6LAAC;gBACC,SAAS;gBACT,WAAW,YAAY,2BAA2B;gBAClD,UAAU;0BAEV,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oBACd,MAAM,2KAAA,CAAA,cAAW;oBACjB,WAAW,CAAC,IAAI,EAAE,YAAY,kBAAkB,IAAI;;;;;;;;;;;YAGvD,wBACC,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC;wBACC,WAAU;wBACV,SAAS;4BACP;4BACA,UAAU;wBACZ;;0CAEA,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,WAAQ;gCAAE,WAAU;;;;;;4BAC1C;;;;;;;kCAEH,6LAAC;wBACC,WAAU;wBACV,SAAS;4BACP;4BACA,UAAU;wBACZ;;0CAEA,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,UAAO;gCAAE,WAAU;;;;;;4BAAa;;;;;;;;;;;;;;;;;;;AAOnE;GA/DM;KAAA;uCAiES"}}, {"offset": {"line": 3058, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3064, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@components/lib/utils\";\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf;KAVS"}}, {"offset": {"line": 3088, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3094, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/packages/skeletons.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@/app/components/ui/skeleton\";\r\n\r\nexport function PackageSkeleton() {\r\n  return (\r\n    <div className=\"border rounded-lg p-5 bg-white shadow-sm\">\r\n      <div className=\"flex justify-between items-start mb-4\">\r\n        <Skeleton className=\"h-7 w-48\" />\r\n        <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n      </div>\r\n      \r\n      <div className=\"space-y-2 mb-4\">\r\n        <Skeleton className=\"h-4 w-full\" />\r\n        <Skeleton className=\"h-4 w-3/4\" />\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-2 gap-3 mb-4\">\r\n        <Skeleton className=\"h-16 rounded-md\" />\r\n        <Skeleton className=\"h-16 rounded-md\" />\r\n      </div>\r\n      \r\n      <div className=\"space-y-2 mb-4\">\r\n        <Skeleton className=\"h-4 w-32\" />\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Skeleton className=\"h-4 w-4 rounded-full\" />\r\n            <Skeleton className=\"h-4 w-3/4\" />\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Skeleton className=\"h-4 w-4 rounded-full\" />\r\n            <Skeleton className=\"h-4 w-2/3\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"flex justify-between items-end pt-4 border-t\">\r\n        <div>\r\n          <Skeleton className=\"h-3 w-12 mb-1\" />\r\n          <Skeleton className=\"h-7 w-24\" />\r\n        </div>\r\n        <div>\r\n          <Skeleton className=\"h-3 w-16 mb-1\" />\r\n          <Skeleton className=\"h-6 w-20\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function SubscriptionSkeleton() {\r\n  return (\r\n    <div className=\"border rounded-lg p-5 bg-white shadow-sm\">\r\n      <div className=\"flex items-start gap-3 mb-4\">\r\n        <Skeleton className=\"h-12 w-12 rounded-full\" />\r\n        <div className=\"flex-1\">\r\n          <Skeleton className=\"h-5 w-40 mb-1\" />\r\n          <Skeleton className=\"h-4 w-32\" />\r\n        </div>\r\n        <Skeleton className=\"h-6 w-24 rounded-full\" />\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\r\n        <div className=\"border rounded-md p-3\">\r\n          <Skeleton className=\"h-5 w-32 mb-3\" />\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex justify-between items-center\">\r\n              <Skeleton className=\"h-4 w-24\" />\r\n              <div className=\"flex items-center gap-2\">\r\n                <Skeleton className=\"h-2 w-24 rounded-full\" />\r\n                <Skeleton className=\"h-4 w-16\" />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex justify-between items-center\">\r\n              <Skeleton className=\"h-4 w-32\" />\r\n              <div className=\"flex items-center gap-2\">\r\n                <Skeleton className=\"h-2 w-24 rounded-full\" />\r\n                <Skeleton className=\"h-4 w-16\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"border rounded-md p-3\">\r\n          <Skeleton className=\"h-5 w-32 mb-3\" />\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex justify-between items-center\">\r\n              <Skeleton className=\"h-4 w-24\" />\r\n              <Skeleton className=\"h-4 w-16\" />\r\n            </div>\r\n            <div className=\"flex justify-between items-center\">\r\n              <Skeleton className=\"h-4 w-32\" />\r\n              <Skeleton className=\"h-4 w-16\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"flex justify-end\">\r\n        <Skeleton className=\"h-9 w-32\" />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function PackagesSkeletonList() {\r\n  return (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n      {Array(3).fill(0).map((_, i) => (\r\n        <PackageSkeleton key={i} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function SubscriptionsSkeletonList() {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {Array(2).fill(0).map((_, i) => (\r\n        <SubscriptionSkeleton key={i} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAGtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAGtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAGtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAK1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,6LAAC;;0CACC,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAK9B;KA5CgB;AA8CT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAGtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAGxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI5B;MArDgB;AAuDT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBACxB,6LAAC,qBAAqB;;;;;;;;;;AAI9B;MARgB;AAUT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBACxB,6LAAC,0BAA0B;;;;;;;;;;AAInC;MARgB"}}, {"offset": {"line": 3603, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3609, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/packages/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, use<PERSON>allback } from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faCheck,\r\n  faCalendarAlt,\r\n  faUsers,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { toast } from \"react-toastify\";\r\nimport { MainButton } from \"../../common/mainBtn\";\r\nimport AddPackageDialog from \"@/app/components/counselor/packages/AddPackageDialog\";\r\nimport EditPackagePopup from \"@/app/components/counselor/profile/view/popup/EditPackagePopup\";\r\nimport DeletePackagePopup from \"@/app/components/counselor/profile/view/popup/DeletePackagePopup\";\r\nimport SessionsListDialog from \"@/app/components/counselor/packages/SessionsListDialog\";\r\nimport { PackageSubscription } from \"@/app/types/counselor/package\";\r\nimport { usePackages } from \"@/app/hooks/counselor/usePackage\";\r\nimport ThreeDotIconDropdown from \"../../common/threeDotIconMenu\";\r\nimport {\r\n  Ta<PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  Ta<PERSON>Trigger,\r\n} from \"@/app/components/ui/tabs\";\r\nimport { PackagesSkeletonList, SubscriptionsSkeletonList } from \"./skeletons\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\n///////////////////////////////// MAIN COMPONENT //////////////////////////////\r\nexport default function CounselorPackages() {\r\n  const {\r\n    fetchSinglePackage,\r\n    deletePackage,\r\n    selectedPackage,\r\n    fetchPackages,\r\n    fetchSubscriptions,\r\n    packages,\r\n    subscriptions,\r\n    completedSubscriptions,\r\n    loading,\r\n  } = usePackages();\r\n\r\n  // State for sessions dialog\r\n  const [sessionsDialogOpen, setSessionsDialogOpen] = useState(false);\r\n  const [selectedSubscription, setSelectedSubscription] =\r\n    useState<PackageSubscription | null>(null);\r\n\r\n  const [addPackagePopup, setAddPackagePopup] = useState(false);\r\n  const [editPackagePopup, setEditPackagePopup] = useState(false);\r\n  const [deletePackagePopup, setDeletePackagePopup] = useState(false);\r\n  const [activeTab, setActiveTab] = useState(\"packages\");\r\n  const [editLoading, setEditLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchPackages();\r\n    if (activeTab === \"subscriptions\") {\r\n      fetchSubscriptions(\"active\");\r\n    } else if (activeTab === \"completed-subscriptions\") {\r\n      fetchSubscriptions(\"completed\");\r\n    }\r\n  }, [activeTab, fetchPackages, fetchSubscriptions]);\r\n\r\n  ////////////// function handles update package items\r\n  const handleEditPackage = useCallback(\r\n    async (packageId: number | undefined) => {\r\n      if (packageId) {\r\n        try {\r\n          setEditLoading(true);\r\n          await fetchSinglePackage(packageId);\r\n          setEditPackagePopup(true);\r\n        } catch (error) {\r\n          console.error(\"Error fetching package:\", error);\r\n          toast.error(\"Failed to load package details. Please try again.\");\r\n        } finally {\r\n          setEditLoading(false);\r\n        }\r\n      }\r\n    },\r\n    [fetchSinglePackage]\r\n  );\r\n\r\n  ////////////// function handles delete package items\r\n  const fetchDeletePackage = useCallback(\r\n    async (packageId: number | undefined) => {\r\n      if (packageId) {\r\n        await fetchSinglePackage(packageId);\r\n        setDeletePackagePopup(true);\r\n      }\r\n    },\r\n    [fetchSinglePackage]\r\n  );\r\n\r\n  /////////////////////////////////////// JSX //////////////////////////////////\r\n  return (\r\n    <section className=\"bg-white rounded-xl px-5 py-9\">\r\n      <AddPackageDialog\r\n        isOpen={addPackagePopup}\r\n        onClose={() => setAddPackagePopup(false)}\r\n      />\r\n      <EditPackagePopup\r\n        isPopupOpen={editPackagePopup}\r\n        setIsPopupOpen={setEditPackagePopup}\r\n        packageData={selectedPackage}\r\n      />\r\n      <DeletePackagePopup\r\n        isPopupOpen={deletePackagePopup}\r\n        setIsPopupOpen={setDeletePackagePopup}\r\n        packageData={selectedPackage}\r\n        onSave={async () => {\r\n          if (selectedPackage?.id) {\r\n            await deletePackage(selectedPackage?.id);\r\n            fetchPackages();\r\n          }\r\n        }}\r\n      />\r\n\r\n      <div className=\"flex justify-between items-center mb-8\">\r\n        <h1 className=\"text-neutral8 text-2xl font-bold\">Package Management</h1>\r\n        <MainButton\r\n          variant=\"primary\"\r\n          onClick={() => {\r\n            console.log(\"Add Package button clicked\");\r\n            setAddPackagePopup(true);\r\n            console.log(\"addPackagePopup state after click:\", true);\r\n          }}\r\n        >\r\n          Add Package +\r\n        </MainButton>\r\n      </div>\r\n\r\n      <Tabs\r\n        defaultValue=\"packages\"\r\n        className=\"w-full\"\r\n        onValueChange={setActiveTab}\r\n      >\r\n        <TabsList className=\"mb-6\">\r\n          <TabsTrigger value=\"packages\" className=\"px-6\">\r\n            <FontAwesomeIcon icon={faCalendarAlt} className=\"mr-2\" />\r\n            My Packages\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"subscriptions\" className=\"px-6\">\r\n            <FontAwesomeIcon icon={faUsers} className=\"mr-2\" />\r\n            Student Subscriptions\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"completed-subscriptions\" className=\"px-6\">\r\n            <FontAwesomeIcon icon={faCheck} className=\"mr-2\" />\r\n            Completed Packages\r\n          </TabsTrigger>\r\n        </TabsList>\r\n\r\n        <TabsContent value=\"packages\" className=\"mt-0\">\r\n          {loading ? (\r\n            <PackagesSkeletonList />\r\n          ) : packages.items.length === 0 ? (\r\n            <div className=\"text-center py-12 bg-gray-50 rounded-lg\">\r\n              <h3 className=\"text-lg font-medium text-gray-700 mb-2\">\r\n                No packages created yet\r\n              </h3>\r\n              <p className=\"text-gray-500 mb-6\">\r\n                Create your first package to offer to students\r\n              </p>\r\n              <MainButton\r\n                variant=\"primary\"\r\n                onClick={() => setAddPackagePopup(true)}\r\n              >\r\n                Create Package\r\n              </MainButton>\r\n            </div>\r\n          ) : (\r\n            <ul className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {packages.items.map((pkg) => (\r\n                <li\r\n                  key={pkg.id}\r\n                  className=\"bg-neutral1 shadow-sm hover:shadow-md transition-shadow duration-200 rounded-lg p-5 flex flex-col\"\r\n                >\r\n                  <div className=\"flex justify-between\">\r\n                    <h2 className=\"text-neutral9 text-lg font-semibold h-12\">\r\n                      {pkg.title}\r\n                    </h2>\r\n\r\n                    <ThreeDotIconDropdown\r\n                      handleEdit={() => handleEditPackage(pkg.id)}\r\n                      handleRemove={() => fetchDeletePackage(pkg.id)}\r\n                      isLoading={editLoading}\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"my-4\">\r\n                    {pkg.package_items.slice(0, 2).map((service) => (\r\n                      <div\r\n                        key={service.id}\r\n                        className=\"bg-[#6FCF9733] px-3 py-2 rounded-lg mb-2 text-neutral8 font-medium flex justify-between gap-4\"\r\n                      >\r\n                        <span>{service.service_name}</span>\r\n                        <span className=\"whitespace-nowrap\">\r\n                          {service.hours} hours\r\n                        </span>\r\n                      </div>\r\n                    ))}\r\n                    {pkg.package_items.length > 2 && (\r\n                      <div className=\"text-sm text-blue-600 font-medium px-3 py-2\">\r\n                        + {pkg.package_items.length - 2} more service\r\n                        {pkg.package_items.length - 2 > 1 ? \"s\" : \"\"}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  <p className=\"text-sm mb-4 text-neutral7 line-clamp-2\">\r\n                    {pkg.description}\r\n                  </p>\r\n\r\n                  <div className=\"flex justify-between items-end mt-auto pt-4 border-t\">\r\n                    <div>\r\n                      <p className=\"text-sm text-neutral5 mb-1\">Price</p>\r\n                      <p className=\"text-2xl font-bold text-neutral8\">\r\n                        ${pkg.total_price}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          )}\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"subscriptions\" className=\"mt-0\">\r\n          {loading ? (\r\n            <SubscriptionsSkeletonList />\r\n          ) : subscriptions.length === 0 ? (\r\n            <div className=\"text-center py-12 bg-gray-50 rounded-lg\">\r\n              <h3 className=\"text-lg font-medium text-gray-700 mb-2\">\r\n                No active subscriptions\r\n              </h3>\r\n              <p className=\"text-gray-500\">\r\n                Students haven't purchased any of your packages yet\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-6\">\r\n              {subscriptions.map((subscription) => (\r\n                <div\r\n                  key={subscription.id}\r\n                  className=\"border rounded-lg p-5 bg-white shadow-sm\"\r\n                >\r\n                  <div className=\"flex items-start justify-between mb-4\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <img\r\n                        src={\r\n                          subscription.student.profile_picture ||\r\n                          generatePlaceholder(\r\n                            subscription.student.name.split(\" \")[0],\r\n                            subscription.student.name.split(\" \")[1]\r\n                          )\r\n                        }\r\n                        alt={subscription.student.name}\r\n                        className=\"w-12 h-12 rounded-full object-cover\"\r\n                      />\r\n                      <div>\r\n                        <h3 className=\"font-semibold text-lg\">\r\n                          {subscription.student.name}\r\n                        </h3>\r\n                        <p className=\"text-sm text-gray-500\">\r\n                          Package:{\" \"}\r\n                          <span className=\"font-medium text-gray-700\">\r\n                            {subscription.package.title}\r\n                          </span>\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"text-right\">\r\n                      <span\r\n                        className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${\r\n                          subscription.status === \"active\"\r\n                            ? \"bg-green-100 text-green-800\"\r\n                            : subscription.status === \"pending\"\r\n                            ? \"bg-yellow-100 text-yellow-800\"\r\n                            : subscription.status === \"completed\"\r\n                            ? \"bg-blue-100 text-blue-800\"\r\n                            : subscription.status === \"cancelled\"\r\n                            ? \"bg-red-100 text-red-800\"\r\n                            : \"bg-gray-100 text-gray-800\"\r\n                        }`}\r\n                      >\r\n                        {subscription.status.charAt(0).toUpperCase() +\r\n                          subscription.status.slice(1)}\r\n                      </span>\r\n                      <p className=\"text-sm text-gray-500 mt-1\">\r\n                        {subscription.end_date ? (\r\n                          <>\r\n                            {new Date(\r\n                              subscription.start_date\r\n                            ).toLocaleDateString()}{\" \"}\r\n                            -{\" \"}\r\n                            {new Date(\r\n                              subscription.end_date\r\n                            ).toLocaleDateString()}\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            Starting Date:{\" \"}\r\n                            {new Date(\r\n                              subscription.start_date\r\n                            ).toLocaleDateString()}\r\n                          </>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\r\n                    <div className=\"border rounded-md p-3\">\r\n                      <h4 className=\"font-medium mb-2\">Service Hours</h4>\r\n                      <div className=\"space-y-2\">\r\n                        {Object.entries(subscription.service_hours).map(\r\n                          ([service, hours]) => (\r\n                            <div\r\n                              key={service}\r\n                              className=\"flex justify-between items-center\"\r\n                            >\r\n                              <span className=\"text-sm\">{service}</span>\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <div className=\"bg-gray-200 h-2 w-24 rounded-full overflow-hidden\">\r\n                                  <div\r\n                                    className=\"bg-blue-500 h-full\"\r\n                                    style={{\r\n                                      width: `${\r\n                                        (hours.used / hours.total) * 100\r\n                                      }%`,\r\n                                    }}\r\n                                  ></div>\r\n                                </div>\r\n                                <span className=\"text-xs font-medium\">\r\n                                  {hours.used}/{hours.total} hrs\r\n                                </span>\r\n                              </div>\r\n                            </div>\r\n                          )\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"border rounded-md p-3\">\r\n                      <h4 className=\"font-medium mb-2\">Recent Sessions</h4>\r\n                      {subscription.sessions.length > 0 ? (\r\n                        <div className=\"space-y-2 max-h-32 overflow-y-auto\">\r\n                          {subscription.sessions.slice(0, 3).map((session) => (\r\n                            <div\r\n                              key={session.id}\r\n                              className=\"flex justify-between items-center text-sm\"\r\n                            >\r\n                              <div>\r\n                                <span className=\"font-medium\">\r\n                                  {session.service_name}\r\n                                </span>\r\n                                <p className=\"text-xs text-gray-500\">\r\n                                  {new Date(session.date).toLocaleDateString()},\r\n                                  {new Date(\r\n                                    session.start_time\r\n                                  ).toLocaleTimeString([], {\r\n                                    hour: \"2-digit\",\r\n                                    minute: \"2-digit\",\r\n                                  })}\r\n                                </p>\r\n                              </div>\r\n                              <span\r\n                                className={`text-xs px-2 py-0.5 rounded-full ${\r\n                                  session.status === \"completed\"\r\n                                    ? \"bg-green-100 text-green-800\"\r\n                                    : session.status === \"upcoming\"\r\n                                    ? \"bg-blue-100 text-blue-800\"\r\n                                    : session.status === \"cancelled\"\r\n                                    ? \"bg-red-100 text-red-800\"\r\n                                    : \"bg-gray-100 text-gray-800\"\r\n                                }`}\r\n                              >\r\n                                {session.status}\r\n                              </span>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      ) : (\r\n                        <p className=\"text-sm text-gray-500\">\r\n                          No sessions scheduled yet\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-end gap-3 mt-4\">\r\n                    <MainButton\r\n                      variant=\"primary\"\r\n                      onClick={() => {\r\n                        setSelectedSubscription(subscription);\r\n                        setSessionsDialogOpen(true);\r\n                      }}\r\n                    >\r\n                      View Details\r\n                    </MainButton>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"completed-subscriptions\" className=\"mt-0\">\r\n          {loading ? (\r\n            <SubscriptionsSkeletonList />\r\n          ) : completedSubscriptions.length === 0 ? (\r\n            <div className=\"text-center py-12 bg-gray-50 rounded-lg\">\r\n              <h3 className=\"text-lg font-medium text-gray-700 mb-2\">\r\n                No completed packages\r\n              </h3>\r\n              <p className=\"text-gray-500\">\r\n                Completed package subscriptions will appear here\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-6\">\r\n              {completedSubscriptions.map((subscription) => (\r\n                <div\r\n                  key={subscription.id}\r\n                  className=\"border rounded-lg p-5 bg-white shadow-sm\"\r\n                >\r\n                  <div className=\"flex items-start justify-between mb-4\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <img\r\n                        src={\r\n                          subscription.student.profile_picture ||\r\n                          generatePlaceholder(\r\n                            subscription.student.name.split(\" \")[0],\r\n                            subscription.student.name.split(\" \")[1]\r\n                          )\r\n                        }\r\n                        alt={subscription.student.name}\r\n                        className=\"w-12 h-12 rounded-full object-cover\"\r\n                      />\r\n                      <div>\r\n                        <h3 className=\"font-semibold text-lg\">\r\n                          {subscription.student.name}\r\n                        </h3>\r\n                        <p className=\"text-sm text-gray-500\">\r\n                          Package:{\" \"}\r\n                          <span className=\"font-medium text-gray-700\">\r\n                            {subscription.package.title}\r\n                          </span>\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"text-right\">\r\n                      <span className=\"inline-block px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\r\n                        Completed\r\n                      </span>\r\n                      <p className=\"text-sm text-gray-500 mt-1\">\r\n                        {subscription.end_date ? (\r\n                          <>\r\n                            {new Date(\r\n                              subscription.start_date\r\n                            ).toLocaleDateString()}{\" \"}\r\n                            -{\" \"}\r\n                            {new Date(\r\n                              subscription.end_date\r\n                            ).toLocaleDateString()}\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            Starting Date:{\" \"}\r\n                            {new Date(\r\n                              subscription.start_date\r\n                            ).toLocaleDateString()}\r\n                          </>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\r\n                    <div className=\"border rounded-md p-3\">\r\n                      <h4 className=\"font-medium mb-2\">Service Hours</h4>\r\n                      <div className=\"space-y-2\">\r\n                        {Object.entries(subscription.service_hours).map(\r\n                          ([service, hours]) => (\r\n                            <div\r\n                              key={service}\r\n                              className=\"flex justify-between items-center\"\r\n                            >\r\n                              <span className=\"text-sm\">{service}</span>\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <div className=\"bg-gray-200 h-2 w-24 rounded-full overflow-hidden\">\r\n                                  <div\r\n                                    className=\"bg-blue-500 h-full\"\r\n                                    style={{\r\n                                      width: `${\r\n                                        (hours.used / hours.total) * 100\r\n                                      }%`,\r\n                                    }}\r\n                                  ></div>\r\n                                </div>\r\n                                <span className=\"text-xs font-medium\">\r\n                                  {hours.used}/{hours.total} hrs\r\n                                </span>\r\n                              </div>\r\n                            </div>\r\n                          )\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"border rounded-md p-3\">\r\n                      <h4 className=\"font-medium mb-2\">Sessions</h4>\r\n                      {subscription.sessions.length > 0 ? (\r\n                        <div className=\"space-y-2 max-h-32 overflow-y-auto\">\r\n                          {subscription.sessions.slice(0, 3).map((session) => (\r\n                            <div\r\n                              key={session.id}\r\n                              className=\"flex justify-between items-center text-sm\"\r\n                            >\r\n                              <div>\r\n                                <span className=\"font-medium\">\r\n                                  {session.service_name}\r\n                                </span>\r\n                                <p className=\"text-xs text-gray-500\">\r\n                                  {new Date(session.date).toLocaleDateString()},\r\n                                  {new Date(\r\n                                    session.start_time\r\n                                  ).toLocaleTimeString([], {\r\n                                    hour: \"2-digit\",\r\n                                    minute: \"2-digit\",\r\n                                  })}\r\n                                </p>\r\n                              </div>\r\n                              <span className=\"text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-800\">\r\n                                {session.status}\r\n                              </span>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      ) : (\r\n                        <p className=\"text-sm text-gray-500\">\r\n                          No sessions recorded\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-end gap-3 mt-4\">\r\n                    <MainButton\r\n                      variant=\"primary\"\r\n                      onClick={() => {\r\n                        setSelectedSubscription(subscription);\r\n                        setSessionsDialogOpen(true);\r\n                      }}\r\n                    >\r\n                      View Details\r\n                    </MainButton>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </TabsContent>\r\n      </Tabs>\r\n\r\n      <SessionsListDialog\r\n        isOpen={sessionsDialogOpen}\r\n        onClose={() => setSessionsDialogOpen(false)}\r\n        subscription={selectedSubscription}\r\n      />\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAMA;AACA;AArBA;;;AAJA;;;;;;;;;;;;;;;AA4Be,SAAS;;IACtB,MAAM,EACJ,kBAAkB,EAClB,aAAa,EACb,eAAe,EACf,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACR,aAAa,EACb,sBAAsB,EACtB,OAAO,EACR,GAAG,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD;IAEd,4BAA4B;IAC5B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GACnD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IAEvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;YACA,IAAI,cAAc,iBAAiB;gBACjC,mBAAmB;YACrB,OAAO,IAAI,cAAc,2BAA2B;gBAClD,mBAAmB;YACrB;QACF;sCAAG;QAAC;QAAW;QAAe;KAAmB;IAEjD,oDAAoD;IACpD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAClC,OAAO;YACL,IAAI,WAAW;gBACb,IAAI;oBACF,eAAe;oBACf,MAAM,mBAAmB;oBACzB,oBAAoB;gBACtB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,SAAU;oBACR,eAAe;gBACjB;YACF;QACF;2DACA;QAAC;KAAmB;IAGtB,oDAAoD;IACpD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DACnC,OAAO;YACL,IAAI,WAAW;gBACb,MAAM,mBAAmB;gBACzB,sBAAsB;YACxB;QACF;4DACA;QAAC;KAAmB;IAGtB,8EAA8E;IAC9E,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC,kKAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;0BAEpC,6LAAC,kLAAA,CAAA,UAAgB;gBACf,aAAa;gBACb,gBAAgB;gBAChB,aAAa;;;;;;0BAEf,6LAAC,oLAAA,CAAA,UAAkB;gBACjB,aAAa;gBACb,gBAAgB;gBAChB,aAAa;gBACb,QAAQ;oBACN,IAAI,iBAAiB,IAAI;wBACvB,MAAM,cAAc,iBAAiB;wBACrC;oBACF;gBACF;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC,mJAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,SAAS;4BACP,QAAQ,GAAG,CAAC;4BACZ,mBAAmB;4BACnB,QAAQ,GAAG,CAAC,sCAAsC;wBACpD;kCACD;;;;;;;;;;;;0BAKH,6LAAC,mIAAA,CAAA,OAAI;gBACH,cAAa;gBACb,WAAU;gBACV,eAAe;;kCAEf,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,gBAAa;wCAAE,WAAU;;;;;;oCAAS;;;;;;;0CAG3D,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAgB,WAAU;;kDAC3C,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,UAAO;wCAAE,WAAU;;;;;;oCAAS;;;;;;;0CAGrD,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAA0B,WAAU;;kDACrD,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,UAAO;wCAAE,WAAU;;;;;;oCAAS;;;;;;;;;;;;;kCAKvD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACrC,wBACC,6LAAC,2JAAA,CAAA,uBAAoB;;;;mCACnB,SAAS,KAAK,CAAC,MAAM,KAAK,kBAC5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC,mJAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,SAAS,IAAM,mBAAmB;8CACnC;;;;;;;;;;;iDAKH,6LAAC;4BAAG,WAAU;sCACX,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,oBACnB,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK;;;;;;8DAGZ,6LAAC,4JAAA,CAAA,UAAoB;oDACnB,YAAY,IAAM,kBAAkB,IAAI,EAAE;oDAC1C,cAAc,IAAM,mBAAmB,IAAI,EAAE;oDAC7C,WAAW;;;;;;;;;;;;sDAIf,6LAAC;4CAAI,WAAU;;gDACZ,IAAI,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBAClC,6LAAC;wDAEC,WAAU;;0EAEV,6LAAC;0EAAM,QAAQ,YAAY;;;;;;0EAC3B,6LAAC;gEAAK,WAAU;;oEACb,QAAQ,KAAK;oEAAC;;;;;;;;uDALZ,QAAQ,EAAE;;;;;gDASlB,IAAI,aAAa,CAAC,MAAM,GAAG,mBAC1B,6LAAC;oDAAI,WAAU;;wDAA8C;wDACxD,IAAI,aAAa,CAAC,MAAM,GAAG;wDAAE;wDAC/B,IAAI,aAAa,CAAC,MAAM,GAAG,IAAI,IAAI,MAAM;;;;;;;;;;;;;sDAKhD,6LAAC;4CAAE,WAAU;sDACV,IAAI,WAAW;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,6LAAC;wDAAE,WAAU;;4DAAmC;4DAC5C,IAAI,WAAW;;;;;;;;;;;;;;;;;;;mCA3ClB,IAAI,EAAE;;;;;;;;;;;;;;;kCAqDrB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAgB,WAAU;kCAC1C,wBACC,6LAAC,2JAAA,CAAA,4BAAyB;;;;mCACxB,cAAc,MAAM,KAAK,kBAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;iDAK/B,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,KACE,aAAa,OAAO,CAAC,eAAe,IACpC,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAChB,aAAa,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EACvC,aAAa,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;4DAG3C,KAAK,aAAa,OAAO,CAAC,IAAI;4DAC9B,WAAU;;;;;;sEAEZ,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,aAAa,OAAO,CAAC,IAAI;;;;;;8EAE5B,6LAAC;oEAAE,WAAU;;wEAAwB;wEAC1B;sFACT,6LAAC;4EAAK,WAAU;sFACb,aAAa,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8DAKnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAW,CAAC,wDAAwD,EAClE,aAAa,MAAM,KAAK,WACpB,gCACA,aAAa,MAAM,KAAK,YACxB,kCACA,aAAa,MAAM,KAAK,cACxB,8BACA,aAAa,MAAM,KAAK,cACxB,4BACA,6BACJ;sEAED,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KACxC,aAAa,MAAM,CAAC,KAAK,CAAC;;;;;;sEAE9B,6LAAC;4DAAE,WAAU;sEACV,aAAa,QAAQ,iBACpB;;oEACG,IAAI,KACH,aAAa,UAAU,EACvB,kBAAkB;oEAAI;oEAAI;oEAC1B;oEACD,IAAI,KACH,aAAa,QAAQ,EACrB,kBAAkB;;6FAGtB;;oEAAE;oEACe;oEACd,IAAI,KACH,aAAa,UAAU,EACvB,kBAAkB;;;;;;;;;;;;;;;;;;;;sDAO9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEACZ,OAAO,OAAO,CAAC,aAAa,aAAa,EAAE,GAAG,CAC7C,CAAC,CAAC,SAAS,MAAM,iBACf,6LAAC;oEAEC,WAAU;;sFAEV,6LAAC;4EAAK,WAAU;sFAAW;;;;;;sFAC3B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFACC,WAAU;wFACV,OAAO;4FACL,OAAO,GACL,AAAC,MAAM,IAAI,GAAG,MAAM,KAAK,GAAI,IAC9B,CAAC,CAAC;wFACL;;;;;;;;;;;8FAGJ,6LAAC;oFAAK,WAAU;;wFACb,MAAM,IAAI;wFAAC;wFAAE,MAAM,KAAK;wFAAC;;;;;;;;;;;;;;mEAhBzB;;;;;;;;;;;;;;;;8DAyBf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;wDAChC,aAAa,QAAQ,CAAC,MAAM,GAAG,kBAC9B,6LAAC;4DAAI,WAAU;sEACZ,aAAa,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACtC,6LAAC;oEAEC,WAAU;;sFAEV,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FACb,QAAQ,YAAY;;;;;;8FAEvB,6LAAC;oFAAE,WAAU;;wFACV,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;wFAAG;wFAC5C,IAAI,KACH,QAAQ,UAAU,EAClB,kBAAkB,CAAC,EAAE,EAAE;4FACvB,MAAM;4FACN,QAAQ;wFACV;;;;;;;;;;;;;sFAGJ,6LAAC;4EACC,WAAW,CAAC,iCAAiC,EAC3C,QAAQ,MAAM,KAAK,cACf,gCACA,QAAQ,MAAM,KAAK,aACnB,8BACA,QAAQ,MAAM,KAAK,cACnB,4BACA,6BACJ;sFAED,QAAQ,MAAM;;;;;;;mEA5BZ,QAAQ,EAAE;;;;;;;;;iFAkCrB,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAO3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mJAAA,CAAA,aAAU;gDACT,SAAQ;gDACR,SAAS;oDACP,wBAAwB;oDACxB,sBAAsB;gDACxB;0DACD;;;;;;;;;;;;mCA1JE,aAAa,EAAE;;;;;;;;;;;;;;;kCAoK9B,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAA0B,WAAU;kCACpD,wBACC,6LAAC,2JAAA,CAAA,4BAAyB;;;;mCACxB,uBAAuB,MAAM,KAAK,kBACpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;iDAK/B,6LAAC;4BAAI,WAAU;sCACZ,uBAAuB,GAAG,CAAC,CAAC,6BAC3B,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,KACE,aAAa,OAAO,CAAC,eAAe,IACpC,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAChB,aAAa,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EACvC,aAAa,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;4DAG3C,KAAK,aAAa,OAAO,CAAC,IAAI;4DAC9B,WAAU;;;;;;sEAEZ,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,aAAa,OAAO,CAAC,IAAI;;;;;;8EAE5B,6LAAC;oEAAE,WAAU;;wEAAwB;wEAC1B;sFACT,6LAAC;4EAAK,WAAU;sFACb,aAAa,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8DAKnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoF;;;;;;sEAGpG,6LAAC;4DAAE,WAAU;sEACV,aAAa,QAAQ,iBACpB;;oEACG,IAAI,KACH,aAAa,UAAU,EACvB,kBAAkB;oEAAI;oEAAI;oEAC1B;oEACD,IAAI,KACH,aAAa,QAAQ,EACrB,kBAAkB;;6FAGtB;;oEAAE;oEACe;oEACd,IAAI,KACH,aAAa,UAAU,EACvB,kBAAkB;;;;;;;;;;;;;;;;;;;;sDAO9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;sEACZ,OAAO,OAAO,CAAC,aAAa,aAAa,EAAE,GAAG,CAC7C,CAAC,CAAC,SAAS,MAAM,iBACf,6LAAC;oEAEC,WAAU;;sFAEV,6LAAC;4EAAK,WAAU;sFAAW;;;;;;sFAC3B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFACC,WAAU;wFACV,OAAO;4FACL,OAAO,GACL,AAAC,MAAM,IAAI,GAAG,MAAM,KAAK,GAAI,IAC9B,CAAC,CAAC;wFACL;;;;;;;;;;;8FAGJ,6LAAC;oFAAK,WAAU;;wFACb,MAAM,IAAI;wFAAC;wFAAE,MAAM,KAAK;wFAAC;;;;;;;;;;;;;;mEAhBzB;;;;;;;;;;;;;;;;8DAyBf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmB;;;;;;wDAChC,aAAa,QAAQ,CAAC,MAAM,GAAG,kBAC9B,6LAAC;4DAAI,WAAU;sEACZ,aAAa,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACtC,6LAAC;oEAEC,WAAU;;sFAEV,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FACb,QAAQ,YAAY;;;;;;8FAEvB,6LAAC;oFAAE,WAAU;;wFACV,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;wFAAG;wFAC5C,IAAI,KACH,QAAQ,UAAU,EAClB,kBAAkB,CAAC,EAAE,EAAE;4FACvB,MAAM;4FACN,QAAQ;wFACV;;;;;;;;;;;;;sFAGJ,6LAAC;4EAAK,WAAU;sFACb,QAAQ,MAAM;;;;;;;mEAlBZ,QAAQ,EAAE;;;;;;;;;iFAwBrB,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAO3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mJAAA,CAAA,aAAU;gDACT,SAAQ;gDACR,SAAS;oDACP,wBAAwB;oDACxB,sBAAsB;gDACxB;0DACD;;;;;;;;;;;;mCAnIE,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;0BA8IhC,6LAAC,oKAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,cAAc;;;;;;;;;;;;AAItB;GA7hBwB;;QAWlB,0IAAA,CAAA,cAAW;;;KAXO"}}, {"offset": {"line": 4727, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
"use client";

import React, { useState, useRef, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEllipsisV, faTrash } from "@fortawesome/free-solid-svg-icons";

interface ProfileMenuProps {
  onDeleteAccount: () => void;
  isLoading?: boolean;
}

export const ProfileMenu: React.FC<ProfileMenuProps> = ({
  onDeleteAccount,
  isLoading = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleMenu = () => setIsOpen((prev) => !prev);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleMenu}
        className={`p-2 rounded-lg hover:bg-gray-100 transition-colors ${
          isLoading ? "opacity-50 cursor-wait" : ""
        }`}
        disabled={isLoading}
        aria-label="Profile menu"
      >
        <FontAwesomeIcon
          icon={faEllipsisV}
          className={`w-4 h-4 text-gray-600 ${
            isLoading ? "animate-pulse" : ""
          }`}
        />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-2 w-48 z-50">
          <button
            className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors"
            onClick={() => {
              onDeleteAccount();
              setIsOpen(false);
            }}
          >
            <FontAwesomeIcon
              icon={faTrash}
              className="w-4 h-4 mr-3 text-red-500"
            />
            Delete Account
          </button>
        </div>
      )}
    </div>
  );
};

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/format-webpack-messages.ts"], "sourcesContent": ["/**\nMIT License\n\nCopyright (c) 2015-present, Facebook, Inc.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n// This file is based on https://github.com/facebook/create-react-app/blob/7b1a32be6ec9f99a6c9a3c66813f3ac09c4736b9/packages/react-dev-utils/formatWebpackMessages.js\n// It's been edited to remove chalk and CRA-specific logic\n\nconst friendlySyntaxErrorLabel = 'Syntax error:'\n\nconst WEBPACK_BREAKING_CHANGE_POLYFILLS =\n  '\\n\\nBREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.'\n\nfunction isLikelyASyntaxError(message: string) {\n  return stripAnsi(message).includes(friendlySyntaxErrorLabel)\n}\n\nlet hadMissingSassError = false\n\n// Cleans up webpack error messages.\nfunction formatMessage(\n  message: any,\n  verbose?: boolean,\n  importTraceNote?: boolean\n) {\n  // TODO: Replace this once webpack 5 is stable\n  if (typeof message === 'object' && message.message) {\n    const filteredModuleTrace =\n      message.moduleTrace &&\n      message.moduleTrace.filter(\n        (trace: any) =>\n          !/next-(middleware|client-pages|route|edge-function)-loader\\.js/.test(\n            trace.originName\n          )\n      )\n\n    let body = message.message\n    const breakingChangeIndex = body.indexOf(WEBPACK_BREAKING_CHANGE_POLYFILLS)\n    if (breakingChangeIndex >= 0) {\n      body = body.slice(0, breakingChangeIndex)\n    }\n\n    message =\n      (message.moduleName ? stripAnsi(message.moduleName) + '\\n' : '') +\n      (message.file ? stripAnsi(message.file) + '\\n' : '') +\n      body +\n      (message.details && verbose ? '\\n' + message.details : '') +\n      (filteredModuleTrace && filteredModuleTrace.length\n        ? (importTraceNote || '\\n\\nImport trace for requested module:') +\n          filteredModuleTrace\n            .map((trace: any) => `\\n${trace.moduleName}`)\n            .join('')\n        : '') +\n      (message.stack && verbose ? '\\n' + message.stack : '')\n  }\n  let lines = message.split('\\n')\n\n  // Strip Webpack-added headers off errors/warnings\n  // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n  lines = lines.filter((line: string) => !/Module [A-z ]+\\(from/.test(line))\n\n  // Transform parsing error into syntax error\n  // TODO: move this to our ESLint formatter?\n  lines = lines.map((line: string) => {\n    const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(\n      line\n    )\n    if (!parsingError) {\n      return line\n    }\n    const [, errorLine, errorColumn, errorMessage] = parsingError\n    return `${friendlySyntaxErrorLabel} ${errorMessage} (${errorLine}:${errorColumn})`\n  })\n\n  message = lines.join('\\n')\n  // Smoosh syntax errors (commonly found in CSS)\n  message = message.replace(\n    /SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g,\n    `${friendlySyntaxErrorLabel} $3 ($1:$2)\\n`\n  )\n  // Clean up export errors\n  message = message.replace(\n    /^.*export '(.+?)' was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$2'.`\n  )\n  message = message.replace(\n    /^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$2' does not contain a default export (imported as '$1').`\n  )\n  message = message.replace(\n    /^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$3' (imported as '$2').`\n  )\n  lines = message.split('\\n')\n\n  // Remove leading newline\n  if (lines.length > 2 && lines[1].trim() === '') {\n    lines.splice(1, 1)\n  }\n\n  // Cleans up verbose \"module not found\" messages for files and packages.\n  if (lines[1] && lines[1].startsWith('Module not found: ')) {\n    lines = [\n      lines[0],\n      lines[1]\n        .replace('Error: ', '')\n        .replace('Module not found: Cannot find file:', 'Cannot find file:'),\n      ...lines.slice(2),\n    ]\n  }\n\n  // Add helpful message for users trying to use Sass for the first time\n  if (lines[1] && lines[1].match(/Cannot find module.+sass/)) {\n    // ./file.module.scss (<<loader info>>) => ./file.module.scss\n    const firstLine = lines[0].split('!')\n    lines[0] = firstLine[firstLine.length - 1]\n\n    lines[1] =\n      \"To use Next.js' built-in Sass support, you first need to install `sass`.\\n\"\n    lines[1] += 'Run `npm i sass` or `yarn add sass` inside your workspace.\\n'\n    lines[1] += '\\nLearn more: https://nextjs.org/docs/messages/install-sass'\n\n    // dispose of unhelpful stack trace\n    lines = lines.slice(0, 2)\n    hadMissingSassError = true\n  } else if (\n    hadMissingSassError &&\n    message.match(/(sass-loader|resolve-url-loader: CSS error)/)\n  ) {\n    // dispose of unhelpful stack trace following missing sass module\n    lines = []\n  }\n\n  if (!verbose) {\n    message = lines.join('\\n')\n    // Internal stacks are generally useless so we strip them... with the\n    // exception of stacks containing `webpack:` because they're normally\n    // from user code generated by Webpack. For more information see\n    // https://github.com/facebook/create-react-app/pull/1050\n    message = message.replace(\n      /^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm,\n      ''\n    ) // at ... ...:x:y\n    message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, '') // at <anonymous>\n\n    message = message.replace(\n      /File was processed with these loaders:\\n(.+[\\\\/](next[\\\\/]dist[\\\\/].+|@next[\\\\/]react-refresh-utils[\\\\/]loader)\\.js\\n)*You may need an additional loader to handle the result of these loaders.\\n/g,\n      ''\n    )\n\n    lines = message.split('\\n')\n  }\n\n  // Remove duplicated newlines\n  lines = (lines as string[]).filter(\n    (line, index, arr) =>\n      index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim()\n  )\n\n  // Reassemble the message\n  message = lines.join('\\n')\n  return message.trim()\n}\n\nexport default function formatWebpackMessages(json: any, verbose?: boolean) {\n  const formattedErrors = json.errors.map((message: any) => {\n    const isUnknownNextFontError = message.message.includes(\n      'An error occurred in `next/font`.'\n    )\n    return formatMessage(message, isUnknownNextFontError || verbose)\n  })\n  const formattedWarnings = json.warnings.map((message: any) => {\n    return formatMessage(message, verbose)\n  })\n\n  // Reorder errors to put the most relevant ones first.\n  let reactServerComponentsError = -1\n\n  for (let i = 0; i < formattedErrors.length; i++) {\n    const error = formattedErrors[i]\n    if (error.includes('ReactServerComponentsError')) {\n      reactServerComponentsError = i\n      break\n    }\n  }\n\n  // Move the reactServerComponentsError to the top if it exists\n  if (reactServerComponentsError !== -1) {\n    const error = formattedErrors.splice(reactServerComponentsError, 1)\n    formattedErrors.unshift(error[0])\n  }\n\n  const result = {\n    ...json,\n    errors: formattedErrors,\n    warnings: formattedWarnings,\n  }\n  if (!verbose && result.errors.some(isLikelyASyntaxError)) {\n    // If there are any syntax errors, show just them.\n    result.errors = result.errors.filter(isLikelyASyntaxError)\n    result.warnings = []\n  }\n  return result\n}\n"], "names": ["stripAnsi", "friendlySyntaxErrorLabel", "WEBPACK_BREAKING_CHANGE_POLYFILLS", "isLikelyASyntaxError", "message", "includes", "hadMissingSassError", "formatMessage", "verbose", "importTraceNote", "filteredModuleTrace", "moduleTrace", "filter", "trace", "test", "originName", "body", "breakingChangeIndex", "indexOf", "slice", "moduleName", "file", "details", "length", "map", "join", "stack", "lines", "split", "line", "parsingError", "exec", "errorLine", "errorColumn", "errorMessage", "replace", "trim", "splice", "startsWith", "match", "firstLine", "index", "arr", "formatWebpackMessages", "json", "formattedErrors", "errors", "isUnknownNextFontError", "formattedWarnings", "warnings", "reactServerComponentsError", "i", "error", "unshift", "result", "some"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;AACA,OAAOA,eAAe,gCAA+B;;AACrD,qKAAqK;AACrK,0DAA0D;AAE1D,MAAMC,2BAA2B;AAEjC,MAAMC,oCACJ;AAEF,SAASC,qBAAqBC,OAAe;IAC3C,sLAAOJ,UAAAA,EAAUI,SAASC,QAAQ,CAACJ;AACrC;AAEA,IAAIK,sBAAsB;AAE1B,oCAAoC;AACpC,SAASC,cACPH,OAAY,EACZI,OAAiB,EACjBC,eAAyB;IAEzB,8CAA8C;IAC9C,IAAI,OAAOL,YAAY,YAAYA,QAAQA,OAAO,EAAE;QAClD,MAAMM,sBACJN,QAAQO,WAAW,IACnBP,QAAQO,WAAW,CAACC,MAAM,CACxB,CAACC,QACC,CAAC,gEAAgEC,IAAI,CACnED,MAAME,UAAU;QAIxB,IAAIC,OAAOZ,QAAQA,OAAO;QAC1B,MAAMa,sBAAsBD,KAAKE,OAAO,CAAChB;QACzC,IAAIe,uBAAuB,GAAG;YAC5BD,OAAOA,KAAKG,KAAK,CAAC,GAAGF;QACvB;QAEAb,UACGA,CAAAA,QAAQgB,UAAU,kLAAGpB,UAAAA,EAAUI,QAAQgB,UAAU,IAAI,OAAO,EAAC,IAC7DhB,CAAAA,QAAQiB,IAAI,kLAAGrB,UAAAA,EAAUI,QAAQiB,IAAI,IAAI,OAAO,EAAC,IAClDL,OACCZ,CAAAA,QAAQkB,OAAO,IAAId,UAAU,OAAOJ,QAAQkB,OAAO,GAAG,EAAC,IACvDZ,CAAAA,uBAAuBA,oBAAoBa,MAAM,GAC7Cd,CAAAA,mBAAmB,wCAAuC,IAC3DC,oBACGc,GAAG,CAAC,CAACX,QAAgB,OAAIA,MAAMO,UAAU,EACzCK,IAAI,CAAC,MACR,EAAC,IACJrB,CAAAA,QAAQsB,KAAK,IAAIlB,UAAU,OAAOJ,QAAQsB,KAAK,GAAG,EAAC;IACxD;IACA,IAAIC,QAAQvB,QAAQwB,KAAK,CAAC;IAE1B,kDAAkD;IAClD,oEAAoE;IACpED,QAAQA,MAAMf,MAAM,CAAC,CAACiB,OAAiB,CAAC,uBAAuBf,IAAI,CAACe;IAEpE,4CAA4C;IAC5C,2CAA2C;IAC3CF,QAAQA,MAAMH,GAAG,CAAC,CAACK;QACjB,MAAMC,eAAe,gDAAgDC,IAAI,CACvEF;QAEF,IAAI,CAACC,cAAc;YACjB,OAAOD;QACT;QACA,MAAM,GAAGG,WAAWC,aAAaC,aAAa,GAAGJ;QACjD,OAAU7B,2BAAyB,MAAGiC,eAAa,OAAIF,YAAU,MAAGC,cAAY;IAClF;IAEA7B,UAAUuB,MAAMF,IAAI,CAAC;IACrB,+CAA+C;IAC/CrB,UAAUA,QAAQ+B,OAAO,CACvB,4CACC,KAAElC,2BAAyB;IAE9B,yBAAyB;IACzBG,UAAUA,QAAQ+B,OAAO,CACvB,mDACC;IAEH/B,UAAUA,QAAQ+B,OAAO,CACvB,6EACC;IAEH/B,UAAUA,QAAQ+B,OAAO,CACvB,2EACC;IAEHR,QAAQvB,QAAQwB,KAAK,CAAC;IAEtB,yBAAyB;IACzB,IAAID,MAAMJ,MAAM,GAAG,KAAKI,KAAK,CAAC,EAAE,CAACS,IAAI,OAAO,IAAI;QAC9CT,MAAMU,MAAM,CAAC,GAAG;IAClB;IAEA,wEAAwE;IACxE,IAAIV,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACW,UAAU,CAAC,uBAAuB;QACzDX,QAAQ;YACNA,KAAK,CAAC,EAAE;YACRA,KAAK,CAAC,EAAE,CACLQ,OAAO,CAAC,WAAW,IACnBA,OAAO,CAAC,uCAAuC;eAC/CR,MAAMR,KAAK,CAAC;SAChB;IACH;IAEA,sEAAsE;IACtE,IAAIQ,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACY,KAAK,CAAC,6BAA6B;QAC1D,6DAA6D;QAC7D,MAAMC,YAAYb,KAAK,CAAC,EAAE,CAACC,KAAK,CAAC;QACjCD,KAAK,CAAC,EAAE,GAAGa,SAAS,CAACA,UAAUjB,MAAM,GAAG,EAAE;QAE1CI,KAAK,CAAC,EAAE,GACN;QACFA,KAAK,CAAC,EAAE,IAAI;QACZA,KAAK,CAAC,EAAE,IAAI;QAEZ,mCAAmC;QACnCA,QAAQA,MAAMR,KAAK,CAAC,GAAG;QACvBb,sBAAsB;IACxB,OAAO,IACLA,uBACAF,QAAQmC,KAAK,CAAC,gDACd;QACA,iEAAiE;QACjEZ,QAAQ,EAAE;IACZ;IAEA,IAAI,CAACnB,SAAS;QACZJ,UAAUuB,MAAMF,IAAI,CAAC;QACrB,qEAAqE;QACrE,qEAAqE;QACrE,gEAAgE;QAChE,yDAAyD;QACzDrB,UAAUA,QAAQ+B,OAAO,CACvB,kDACA,IACA,iBAAiB;;QACnB/B,UAAUA,QAAQ+B,OAAO,CAAC,+BAA+B,IAAI,iBAAiB;;QAE9E/B,UAAUA,QAAQ+B,OAAO,CACvB,sMACA;QAGFR,QAAQvB,QAAQwB,KAAK,CAAC;IACxB;IAEA,6BAA6B;IAC7BD,QAASA,MAAmBf,MAAM,CAChC,CAACiB,MAAMY,OAAOC,MACZD,UAAU,KAAKZ,KAAKO,IAAI,OAAO,MAAMP,KAAKO,IAAI,OAAOM,GAAG,CAACD,QAAQ,EAAE,CAACL,IAAI;IAG5E,yBAAyB;IACzBhC,UAAUuB,MAAMF,IAAI,CAAC;IACrB,OAAOrB,QAAQgC,IAAI;AACrB;AAEe,SAASO,sBAAsBC,IAAS,EAAEpC,OAAiB;IACxE,MAAMqC,kBAAkBD,KAAKE,MAAM,CAACtB,GAAG,CAAC,CAACpB;QACvC,MAAM2C,yBAAyB3C,QAAQA,OAAO,CAACC,QAAQ,CACrD;QAEF,OAAOE,cAAcH,SAAS2C,0BAA0BvC;IAC1D;IACA,MAAMwC,oBAAoBJ,KAAKK,QAAQ,CAACzB,GAAG,CAAC,CAACpB;QAC3C,OAAOG,cAAcH,SAASI;IAChC;IAEA,sDAAsD;IACtD,IAAI0C,6BAA6B,CAAC;IAElC,IAAK,IAAIC,IAAI,GAAGA,IAAIN,gBAAgBtB,MAAM,EAAE4B,IAAK;QAC/C,MAAMC,QAAQP,eAAe,CAACM,EAAE;QAChC,IAAIC,MAAM/C,QAAQ,CAAC,+BAA+B;YAChD6C,6BAA6BC;YAC7B;QACF;IACF;IAEA,8DAA8D;IAC9D,IAAID,+BAA+B,CAAC,GAAG;QACrC,MAAME,QAAQP,gBAAgBR,MAAM,CAACa,4BAA4B;QACjEL,gBAAgBQ,OAAO,CAACD,KAAK,CAAC,EAAE;IAClC;IAEA,MAAME,SAAS;QACb,GAAGV,IAAI;QACPE,QAAQD;QACRI,UAAUD;IACZ;IACA,IAAI,CAACxC,WAAW8C,OAAOR,MAAM,CAACS,IAAI,CAACpD,uBAAuB;QACxD,kDAAkD;QAClDmD,OAAOR,MAAM,GAAGQ,OAAOR,MAAM,CAAClC,MAAM,CAACT;QACrCmD,OAAOL,QAAQ,GAAG,EAAE;IACtB;IACA,OAAOK;AACT", "ignoreList": [0]}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/shared.ts"], "sourcesContent": ["import { useReducer } from 'react'\n\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport type { VersionInfo } from '../../../server/dev/parse-version-info'\nimport type { SupportedErrorEvent } from './internal/container/Errors'\nimport type { ComponentStackFrame } from './internal/helpers/parse-component-stack'\nimport type { DebugInfo } from './types'\n\ntype FastRefreshState =\n  /** No refresh in progress. */\n  | { type: 'idle' }\n  /** The refresh process has been triggered, but the new code has not been executed yet. */\n  | { type: 'pending'; errors: SupportedErrorEvent[] }\n\nexport interface OverlayState {\n  nextId: number\n  buildError: string | null\n  errors: SupportedErrorEvent[]\n  refreshState: FastRefreshState\n  rootLayoutMissingTags: typeof window.__next_root_layout_missing_tags\n  versionInfo: VersionInfo\n  notFound: boolean\n  staticIndicator: boolean\n  debugInfo: DebugInfo | undefined\n}\n\nexport const ACTION_STATIC_INDICATOR = 'static-indicator'\nexport const ACTION_BUILD_OK = 'build-ok'\nexport const ACTION_BUILD_ERROR = 'build-error'\nexport const ACTION_BEFORE_REFRESH = 'before-fast-refresh'\nexport const ACTION_REFRESH = 'fast-refresh'\nexport const ACTION_VERSION_INFO = 'version-info'\nexport const ACTION_UNHANDLED_ERROR = 'unhandled-error'\nexport const ACTION_UNHANDLED_REJECTION = 'unhandled-rejection'\nexport const ACTION_DEBUG_INFO = 'debug-info'\n\ninterface StaticIndicatorAction {\n  type: typeof ACTION_STATIC_INDICATOR\n  staticIndicator: boolean\n}\n\ninterface BuildOkAction {\n  type: typeof ACTION_BUILD_OK\n}\ninterface BuildErrorAction {\n  type: typeof ACTION_BUILD_ERROR\n  message: string\n}\ninterface BeforeFastRefreshAction {\n  type: typeof ACTION_BEFORE_REFRESH\n}\ninterface FastRefreshAction {\n  type: typeof ACTION_REFRESH\n}\n\nexport interface UnhandledErrorAction {\n  type: typeof ACTION_UNHANDLED_ERROR\n  reason: Error\n  frames: StackFrame[]\n  componentStackFrames?: ComponentStackFrame[]\n  warning?: [string, string, string]\n}\nexport interface UnhandledRejectionAction {\n  type: typeof ACTION_UNHANDLED_REJECTION\n  reason: Error\n  frames: StackFrame[]\n}\n\nexport interface DebugInfoAction {\n  type: typeof ACTION_DEBUG_INFO\n  debugInfo: any\n}\n\ninterface VersionInfoAction {\n  type: typeof ACTION_VERSION_INFO\n  versionInfo: VersionInfo\n}\n\nexport type BusEvent =\n  | BuildOkAction\n  | BuildErrorAction\n  | BeforeFastRefreshAction\n  | FastRefreshAction\n  | UnhandledErrorAction\n  | UnhandledRejectionAction\n  | VersionInfoAction\n  | StaticIndicatorAction\n  | DebugInfoAction\n\nfunction pushErrorFilterDuplicates(\n  errors: SupportedErrorEvent[],\n  err: SupportedErrorEvent\n): SupportedErrorEvent[] {\n  return [\n    ...errors.filter((e) => {\n      // Filter out duplicate errors\n      return e.event.reason !== err.event.reason\n    }),\n    err,\n  ]\n}\n\nexport const INITIAL_OVERLAY_STATE: OverlayState = {\n  nextId: 1,\n  buildError: null,\n  errors: [],\n  notFound: false,\n  staticIndicator: false,\n  refreshState: { type: 'idle' },\n  rootLayoutMissingTags: [],\n  versionInfo: { installed: '0.0.0', staleness: 'unknown' },\n  debugInfo: undefined,\n}\n\nexport function useErrorOverlayReducer() {\n  return useReducer((_state: OverlayState, action: BusEvent): OverlayState => {\n    switch (action.type) {\n      case ACTION_DEBUG_INFO: {\n        return { ..._state, debugInfo: action.debugInfo }\n      }\n      case ACTION_STATIC_INDICATOR: {\n        return { ..._state, staticIndicator: action.staticIndicator }\n      }\n      case ACTION_BUILD_OK: {\n        return { ..._state, buildError: null }\n      }\n      case ACTION_BUILD_ERROR: {\n        return { ..._state, buildError: action.message }\n      }\n      case ACTION_BEFORE_REFRESH: {\n        return { ..._state, refreshState: { type: 'pending', errors: [] } }\n      }\n      case ACTION_REFRESH: {\n        return {\n          ..._state,\n          buildError: null,\n          errors:\n            // Errors can come in during updates. In this case, UNHANDLED_ERROR\n            // and UNHANDLED_REJECTION events might be dispatched between the\n            // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n            // around until the next refresh. Otherwise we run into a race\n            // condition where those errors would be cleared on refresh completion\n            // before they can be displayed.\n            _state.refreshState.type === 'pending'\n              ? _state.refreshState.errors\n              : [],\n          refreshState: { type: 'idle' },\n        }\n      }\n      case ACTION_UNHANDLED_ERROR:\n      case ACTION_UNHANDLED_REJECTION: {\n        switch (_state.refreshState.type) {\n          case 'idle': {\n            return {\n              ..._state,\n              nextId: _state.nextId + 1,\n              errors: pushErrorFilterDuplicates(_state.errors, {\n                id: _state.nextId,\n                event: action,\n              }),\n            }\n          }\n          case 'pending': {\n            return {\n              ..._state,\n              nextId: _state.nextId + 1,\n              refreshState: {\n                ..._state.refreshState,\n                errors: pushErrorFilterDuplicates(_state.refreshState.errors, {\n                  id: _state.nextId,\n                  event: action,\n                }),\n              },\n            }\n          }\n          default:\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            const _: never = _state.refreshState\n            return _state\n        }\n      }\n      case ACTION_VERSION_INFO: {\n        return { ..._state, versionInfo: action.versionInfo }\n      }\n      default: {\n        return _state\n      }\n    }\n  }, INITIAL_OVERLAY_STATE)\n}\n\nexport const REACT_REFRESH_FULL_RELOAD_FROM_ERROR =\n  '[Fast Refresh] performing full reload because your application had an unrecoverable error'\n"], "names": ["useReducer", "ACTION_STATIC_INDICATOR", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_VERSION_INFO", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_DEBUG_INFO", "pushErrorFilterDuplicates", "errors", "err", "filter", "e", "event", "reason", "INITIAL_OVERLAY_STATE", "nextId", "buildError", "notFound", "staticIndicator", "refreshState", "type", "rootLayoutMissingTags", "versionInfo", "installed", "staleness", "debugInfo", "undefined", "useErrorOverlayReducer", "_state", "action", "message", "id", "_", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR"], "mappings": ";;;;;;;;;;;;;;AAAA,SAASA,UAAU,QAAQ,QAAO;;AA0B3B,MAAMC,0BAA0B,mBAAkB;AAClD,MAAMC,kBAAkB,WAAU;AAClC,MAAMC,qBAAqB,cAAa;AACxC,MAAMC,wBAAwB,sBAAqB;AACnD,MAAMC,iBAAiB,eAAc;AACrC,MAAMC,sBAAsB,eAAc;AAC1C,MAAMC,yBAAyB,kBAAiB;AAChD,MAAMC,6BAA6B,sBAAqB;AACxD,MAAMC,oBAAoB,aAAY;AAuD7C,SAASC,0BACPC,MAA6B,EAC7BC,GAAwB;IAExB,OAAO;WACFD,OAAOE,MAAM,CAAC,CAACC;YAChB,8BAA8B;YAC9B,OAAOA,EAAEC,KAAK,CAACC,MAAM,KAAKJ,IAAIG,KAAK,CAACC,MAAM;QAC5C;QACAJ;KACD;AACH;AAEO,MAAMK,wBAAsC;IACjDC,QAAQ;IACRC,YAAY;IACZR,QAAQ,EAAE;IACVS,UAAU;IACVC,iBAAiB;IACjBC,cAAc;QAAEC,MAAM;IAAO;IAC7BC,uBAAuB,EAAE;IACzBC,aAAa;QAAEC,WAAW;QAASC,WAAW;IAAU;IACxDC,WAAWC;AACb,EAAC;AAEM,SAASC;IACd,8KAAO9B,aAAAA,EAAW,CAAC+B,QAAsBC;QACvC,OAAQA,OAAOT,IAAI;YACjB,KAAKd;gBAAmB;oBACtB,OAAO;wBAAE,GAAGsB,MAAM;wBAAEH,WAAWI,OAAOJ,SAAS;oBAAC;gBAClD;YACA,KAAK3B;gBAAyB;oBAC5B,OAAO;wBAAE,GAAG8B,MAAM;wBAAEV,iBAAiBW,OAAOX,eAAe;oBAAC;gBAC9D;YACA,KAAKnB;gBAAiB;oBACpB,OAAO;wBAAE,GAAG6B,MAAM;wBAAEZ,YAAY;oBAAK;gBACvC;YACA,KAAKhB;gBAAoB;oBACvB,OAAO;wBAAE,GAAG4B,MAAM;wBAAEZ,YAAYa,OAAOC,OAAO;oBAAC;gBACjD;YACA,KAAK7B;gBAAuB;oBAC1B,OAAO;wBAAE,GAAG2B,MAAM;wBAAET,cAAc;4BAAEC,MAAM;4BAAWZ,QAAQ,EAAE;wBAAC;oBAAE;gBACpE;YACA,KAAKN;gBAAgB;oBACnB,OAAO;wBACL,GAAG0B,MAAM;wBACTZ,YAAY;wBACZR,QACE,AACA,iEAAiE,EADE;wBAEnE,qEAAqE;wBACrE,8DAA8D;wBAC9D,sEAAsE;wBACtE,gCAAgC;wBAChCoB,OAAOT,YAAY,CAACC,IAAI,KAAK,YACzBQ,OAAOT,YAAY,CAACX,MAAM,GAC1B,EAAE;wBACRW,cAAc;4BAAEC,MAAM;wBAAO;oBAC/B;gBACF;YACA,KAAKhB;YACL,KAAKC;gBAA4B;oBAC/B,OAAQuB,OAAOT,YAAY,CAACC,IAAI;wBAC9B,KAAK;4BAAQ;gCACX,OAAO;oCACL,GAAGQ,MAAM;oCACTb,QAAQa,OAAOb,MAAM,GAAG;oCACxBP,QAAQD,0BAA0BqB,OAAOpB,MAAM,EAAE;wCAC/CuB,IAAIH,OAAOb,MAAM;wCACjBH,OAAOiB;oCACT;gCACF;4BACF;wBACA,KAAK;4BAAW;gCACd,OAAO;oCACL,GAAGD,MAAM;oCACTb,QAAQa,OAAOb,MAAM,GAAG;oCACxBI,cAAc;wCACZ,GAAGS,OAAOT,YAAY;wCACtBX,QAAQD,0BAA0BqB,OAAOT,YAAY,CAACX,MAAM,EAAE;4CAC5DuB,IAAIH,OAAOb,MAAM;4CACjBH,OAAOiB;wCACT;oCACF;gCACF;4BACF;wBACA;4BACE,6DAA6D;4BAC7D,MAAMG,IAAWJ,OAAOT,YAAY;4BACpC,OAAOS;oBACX;gBACF;YACA,KAAKzB;gBAAqB;oBACxB,OAAO;wBAAE,GAAGyB,MAAM;wBAAEN,aAAaO,OAAOP,WAAW;oBAAC;gBACtD;YACA;gBAAS;oBACP,OAAOM;gBACT;QACF;IACF,GAAGd;AACL;AAEO,MAAMmB,uCACX,4FAA2F", "ignoreList": [0]}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/parse-stack.ts"], "sourcesContent": ["import { parse } from 'next/dist/compiled/stacktrace-parser'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  getHydrationErrorStackInfo,\n  isReactHydrationErrorMessage,\n} from '../../../is-hydration-error'\n\nconst regexNextStatic = /\\/_next(\\/static\\/.+)/\n\nexport function parseStack(stack: string | undefined): StackFrame[] {\n  if (!stack) return []\n  const messageAndStack = stack.replace(/^Error: /, '')\n  if (isReactHydrationErrorMessage(messageAndStack)) {\n    const { stack: parsedStack } = getHydrationErrorStackInfo(messageAndStack)\n    if (parsedStack) {\n      stack = parsedStack\n    }\n  }\n\n  // throw away eval information that stacktrace-parser doesn't support\n  // adapted from https://github.com/stacktracejs/error-stack-parser/blob/9f33c224b5d7b607755eb277f9d51fcdb7287e24/error-stack-parser.js#L59C33-L59C62\n  stack = stack\n    .split('\\n')\n    .map((line) => {\n      if (line.includes('(eval ')) {\n        line = line\n          .replace(/eval code/g, 'eval')\n          .replace(/\\(eval at [^()]* \\(/, '(file://')\n          .replace(/\\),.*$/g, ')')\n      }\n\n      return line\n    })\n    .join('\\n')\n\n  const frames = parse(stack)\n  return frames.map((frame) => {\n    try {\n      const url = new URL(frame.file!)\n      const res = regexNextStatic.exec(url.pathname)\n      if (res) {\n        const distDir = process.env.__NEXT_DIST_DIR\n          ?.replace(/\\\\/g, '/')\n          ?.replace(/\\/$/, '')\n        if (distDir) {\n          frame.file = 'file://' + distDir.concat(res.pop()!) + url.search\n        }\n      }\n    } catch {}\n    return frame\n  })\n}\n"], "names": ["parse", "getHydrationErrorStackInfo", "isReactHydrationErrorMessage", "regexNextStatic", "parseStack", "stack", "messageAndStack", "replace", "parsedStack", "split", "map", "line", "includes", "join", "frames", "frame", "url", "URL", "file", "res", "exec", "pathname", "process", "distDir", "env", "__NEXT_DIST_DIR", "concat", "pop", "search"], "mappings": ";;;AAAA,SAASA,KAAK,QAAQ,uCAAsC;AAE5D,SACEC,0BAA0B,EAC1BC,4BAA4B,QACvB,8BAA6B;;;AAEpC,MAAMC,kBAAkB;AAEjB,SAASC,WAAWC,KAAyB;IAClD,IAAI,CAACA,OAAO,OAAO,EAAE;IACrB,MAAMC,kBAAkBD,MAAME,OAAO,CAAC,YAAY;IAClD,QAAIL,+NAAAA,EAA6BI,kBAAkB;QACjD,MAAM,EAAED,OAAOG,WAAW,EAAE,uMAAGP,6BAAAA,EAA2BK;QAC1D,IAAIE,aAAa;YACfH,QAAQG;QACV;IACF;IAEA,qEAAqE;IACrE,oJAAoJ;IACpJH,QAAQA,MACLI,KAAK,CAAC,MACNC,GAAG,CAAC,CAACC;QACJ,IAAIA,KAAKC,QAAQ,CAAC,WAAW;YAC3BD,OAAOA,KACJJ,OAAO,CAAC,cAAc,QACtBA,OAAO,CAAC,uBAAuB,YAC/BA,OAAO,CAAC,WAAW;QACxB;QAEA,OAAOI;IACT,GACCE,IAAI,CAAC;IAER,MAAMC,yNAASd,QAAAA,EAAMK;IACrB,OAAOS,OAAOJ,GAAG,CAAC,CAACK;QACjB,IAAI;YACF,MAAMC,MAAM,IAAIC,IAAIF,MAAMG,IAAI;YAC9B,MAAMC,MAAMhB,gBAAgBiB,IAAI,CAACJ,IAAIK,QAAQ;YAC7C,IAAIF,KAAK;oBACSG,sCAAAA;gBAAhB,MAAMC,UAAAA,CAAUD,+BAAAA,QAAQE,GAAG,CAACC,eAAe,KAAA,OAAA,KAAA,IAAA,CAA3BH,uCAAAA,6BACZf,OAAO,CAAC,OAAO,IAAA,KAAA,OAAA,KAAA,IADHe,qCAEZf,OAAO,CAAC,OAAO;gBACnB,IAAIgB,SAAS;oBACXR,MAAMG,IAAI,GAAG,YAAYK,QAAQG,MAAM,CAACP,IAAIQ,GAAG,MAAOX,IAAIY,MAAM;gBAClE;YACF;QACF,EAAE,OAAA,GAAM,CAAC;QACT,OAAOb;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/ShadowPortal.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { createPortal } from 'react-dom'\n\nexport function ShadowPortal({ children }: { children: React.ReactNode }) {\n  let portalNode = React.useRef<HTMLElement | null>(null)\n  let shadowNode = React.useRef<ShadowRoot | null>(null)\n  let [, forceUpdate] = React.useState<{} | undefined>()\n\n  React.useLayoutEffect(() => {\n    const ownerDocument = document\n    portalNode.current = ownerDocument.createElement('nextjs-portal')\n    shadowNode.current = portalNode.current.attachShadow({ mode: 'open' })\n    ownerDocument.body.appendChild(portalNode.current)\n    forceUpdate({})\n    return () => {\n      if (portalNode.current && portalNode.current.ownerDocument) {\n        portalNode.current.ownerDocument.body.removeChild(portalNode.current)\n      }\n    }\n  }, [])\n\n  return shadowNode.current\n    ? createPortal(children, shadowNode.current as any)\n    : null\n}\n"], "names": ["React", "createPortal", "ShadowPort<PERSON>", "children", "portalNode", "useRef", "shadowNode", "forceUpdate", "useState", "useLayoutEffect", "ownerDocument", "document", "current", "createElement", "attachShadow", "mode", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,YAAY,QAAQ,YAAW;;;AAEjC,SAASC,aAAa,KAA2C;IAA3C,IAAA,EAAEC,QAAQ,EAAiC,GAA3C;IAC3B,IAAIC,aAAaJ,mKAAMK,MAAM,CAAqB;IAClD,IAAIC,aAAaN,mKAAMK,MAAM,CAAoB;IACjD,IAAI,GAAGE,YAAY,GAAGP,mKAAMQ,QAAQ;IAEpCR,mKAAMS,eAAe,CAAC;QACpB,MAAMC,gBAAgBC;QACtBP,WAAWQ,OAAO,GAAGF,cAAcG,aAAa,CAAC;QACjDP,WAAWM,OAAO,GAAGR,WAAWQ,OAAO,CAACE,YAAY,CAAC;YAAEC,MAAM;QAAO;QACpEL,cAAcM,IAAI,CAACC,WAAW,CAACb,WAAWQ,OAAO;QACjDL,YAAY,CAAC;QACb,OAAO;YACL,IAAIH,WAAWQ,OAAO,IAAIR,WAAWQ,OAAO,CAACF,aAAa,EAAE;gBAC1DN,WAAWQ,OAAO,CAACF,aAAa,CAACM,IAAI,CAACE,WAAW,CAACd,WAAWQ,OAAO;YACtE;QACF;IACF,GAAG,EAAE;IAEL,OAAON,WAAWM,OAAO,GAAA,WAAA,iLACrBX,eAAAA,EAAaE,UAAUG,WAAWM,OAAO,IACzC;AACN", "ignoreList": [0]}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/hooks/use-on-click-outside.ts"], "sourcesContent": ["import * as React from 'react'\n\nexport function useOnClickOutside(\n  el: Node | null,\n  handler: ((e: MouseEvent | TouchEvent) => void) | undefined\n) {\n  React.useEffect(() => {\n    if (el == null || handler == null) {\n      return\n    }\n\n    const listener = (e: MouseEvent | TouchEvent) => {\n      // Do nothing if clicking ref's element or descendent elements\n      if (!el || el.contains(e.target as Element)) {\n        return\n      }\n\n      handler(e)\n    }\n\n    const root = el.getRootNode()\n    root.addEventListener('mousedown', listener as EventListener)\n    root.addEventListener('touchstart', listener as EventListener, {\n      passive: false,\n    })\n    return function () {\n      root.removeEventListener('mousedown', listener as EventListener)\n      root.removeEventListener('touchstart', listener as EventListener)\n    }\n  }, [handler, el])\n}\n"], "names": ["React", "useOnClickOutside", "el", "handler", "useEffect", "listener", "e", "contains", "target", "root", "getRootNode", "addEventListener", "passive", "removeEventListener"], "mappings": ";;;AAAA,YAAYA,WAAW,QAAO;;AAEvB,SAASC,kBACdC,EAAe,EACfC,OAA2D;IAE3DH,mKAAMI,SAAS,CAAC;QACd,IAAIF,MAAM,QAAQC,WAAW,MAAM;YACjC;QACF;QAEA,MAAME,WAAW,CAACC;YAChB,8DAA8D;YAC9D,IAAI,CAACJ,MAAMA,GAAGK,QAAQ,CAACD,EAAEE,MAAM,GAAc;gBAC3C;YACF;YAEAL,QAAQG;QACV;QAEA,MAAMG,OAAOP,GAAGQ,WAAW;QAC3BD,KAAKE,gBAAgB,CAAC,aAAaN;QACnCI,KAAKE,gBAAgB,CAAC,cAAcN,UAA2B;YAC7DO,SAAS;QACX;QACA,OAAO;YACLH,KAAKI,mBAAmB,CAAC,aAAaR;YACtCI,KAAKI,mBAAmB,CAAC,cAAcR;QACzC;IACF,GAAG;QAACF;QAASD;KAAG;AAClB", "ignoreList": [0]}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Dialog/Dialog.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { useOnClickOutside } from '../../hooks/use-on-click-outside'\n\nexport type DialogProps = {\n  children?: React.ReactNode\n  type: 'error' | 'warning'\n  'aria-labelledby': string\n  'aria-describedby': string\n  onClose?: () => void\n}\n\nconst Dialog: React.FC<DialogProps> = function Dialog({\n  children,\n  type,\n  onClose,\n  ...props\n}) {\n  const [dialog, setDialog] = React.useState<HTMLDivElement | null>(null)\n  const [role, setRole] = React.useState<string | undefined>(\n    typeof document !== 'undefined' && document.hasFocus()\n      ? 'dialog'\n      : undefined\n  )\n  const onDialog = React.useCallback((node: HTMLDivElement | null) => {\n    setDialog(node)\n  }, [])\n  useOnClickOutside(dialog, (e) => {\n    e.preventDefault()\n    return onClose?.()\n  })\n\n  // Make HTMLElements with `role=link` accessible to be triggered by the\n  // keyboard, i.e. [Enter].\n  React.useEffect(() => {\n    if (dialog == null) {\n      return\n    }\n\n    const root = dialog.getRootNode()\n    // Always true, but we do this for TypeScript:\n    if (!(root instanceof ShadowRoot)) {\n      return\n    }\n    const shadowRoot = root\n    function handler(e: KeyboardEvent) {\n      const el = shadowRoot.activeElement\n      if (\n        e.key === 'Enter' &&\n        el instanceof HTMLElement &&\n        el.getAttribute('role') === 'link'\n      ) {\n        e.preventDefault()\n        e.stopPropagation()\n\n        el.click()\n      }\n    }\n\n    function handleFocus() {\n      // safari will force itself as the active application when a background page triggers any sort of autofocus\n      // this is a workaround to only set the dialog role if the document has focus\n      setRole(document.hasFocus() ? 'dialog' : undefined)\n    }\n\n    shadowRoot.addEventListener('keydown', handler as EventListener)\n    window.addEventListener('focus', handleFocus)\n    window.addEventListener('blur', handleFocus)\n    return () => {\n      shadowRoot.removeEventListener('keydown', handler as EventListener)\n      window.removeEventListener('focus', handleFocus)\n      window.removeEventListener('blur', handleFocus)\n    }\n  }, [dialog])\n\n  return (\n    <div\n      ref={onDialog}\n      data-nextjs-dialog\n      tabIndex={-1}\n      role={role}\n      aria-labelledby={props['aria-labelledby']}\n      aria-describedby={props['aria-describedby']}\n      aria-modal=\"true\"\n    >\n      <div data-nextjs-dialog-banner className={`banner-${type}`} />\n      {children}\n    </div>\n  )\n}\n\nexport { Dialog }\n"], "names": ["React", "useOnClickOutside", "Dialog", "children", "type", "onClose", "props", "dialog", "setDialog", "useState", "role", "setRole", "document", "hasFocus", "undefined", "onDialog", "useCallback", "node", "e", "preventDefault", "useEffect", "root", "getRootNode", "ShadowRoot", "shadowRoot", "handler", "el", "activeElement", "key", "HTMLElement", "getAttribute", "stopPropagation", "click", "handleFocus", "addEventListener", "window", "removeEventListener", "div", "ref", "data-nextjs-dialog", "tabIndex", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "aria-modal", "data-nextjs-dialog-banner", "className"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,iBAAiB,QAAQ,mCAAkC;;;;AAUpE,MAAMC,SAAgC,SAASA,OAAO,KAKrD;IALqD,IAAA,EACpDC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACP,GAAGC,OACJ,GALqD;IAMpD,MAAM,CAACC,QAAQC,UAAU,GAAGR,mKAAMS,QAAQ,CAAwB;IAClE,MAAM,CAACC,MAAMC,QAAQ,GAAGX,mKAAMS,QAAQ,CACpC,OAAOG,aAAa,eAAeA,SAASC,QAAQ,KAChD,WACAC;IAEN,MAAMC,WAAWf,mKAAMgB,WAAW,CAAC,CAACC;QAClCT,UAAUS;IACZ,GAAG,EAAE;QACLhB,yQAAAA,EAAkBM,QAAQ,CAACW;QACzBA,EAAEC,cAAc;QAChB,OAAOd,WAAAA,OAAAA,KAAAA,IAAAA;IACT;IAEA,uEAAuE;IACvE,0BAA0B;IAC1BL,mKAAMoB,SAAS,CAAC;QACd,IAAIb,UAAU,MAAM;YAClB;QACF;QAEA,MAAMc,OAAOd,OAAOe,WAAW;QAC/B,8CAA8C;QAC9C,IAAI,CAAED,CAAAA,gBAAgBE,UAAS,GAAI;YACjC;QACF;QACA,MAAMC,aAAaH;QACnB,SAASI,QAAQP,CAAgB;YAC/B,MAAMQ,KAAKF,WAAWG,aAAa;YACnC,IACET,EAAEU,GAAG,KAAK,WACVF,cAAcG,eACdH,GAAGI,YAAY,CAAC,YAAY,QAC5B;gBACAZ,EAAEC,cAAc;gBAChBD,EAAEa,eAAe;gBAEjBL,GAAGM,KAAK;YACV;QACF;QAEA,SAASC;YACP,2GAA2G;YAC3G,6EAA6E;YAC7EtB,QAAQC,SAASC,QAAQ,KAAK,WAAWC;QAC3C;QAEAU,WAAWU,gBAAgB,CAAC,WAAWT;QACvCU,OAAOD,gBAAgB,CAAC,SAASD;QACjCE,OAAOD,gBAAgB,CAAC,QAAQD;QAChC,OAAO;YACLT,WAAWY,mBAAmB,CAAC,WAAWX;YAC1CU,OAAOC,mBAAmB,CAAC,SAASH;YACpCE,OAAOC,mBAAmB,CAAC,QAAQH;QACrC;IACF,GAAG;QAAC1B;KAAO;IAEX,OAAA,WAAA,IACE,sLAAA,EAAC8B,OAAAA;QACCC,KAAKvB;QACLwB,oBAAkB,EAAA;QAClBC,UAAU,CAAC;QACX9B,MAAMA;QACN+B,mBAAiBnC,KAAK,CAAC,kBAAkB;QACzCoC,oBAAkBpC,KAAK,CAAC,mBAAmB;QAC3CqC,cAAW;;0MAEX,MAAA,EAACN,OAAAA;gBAAIO,2BAAyB,EAAA;gBAACC,WAAY,YAASzC;;YACnDD;;;AAGP", "ignoreList": [0]}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Dialog/DialogBody.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport type DialogBodyProps = {\n  children?: React.ReactNode\n  className?: string\n}\n\nconst DialogBody: React.FC<DialogBodyProps> = function DialogBody({\n  children,\n  className,\n}) {\n  return (\n    <div data-nextjs-dialog-body className={className}>\n      {children}\n    </div>\n  )\n}\n\nexport { DialogBody }\n"], "names": ["React", "DialogBody", "children", "className", "div", "data-nextjs-dialog-body"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;;;AAO9B,MAAMC,aAAwC,SAASA,WAAW,KAGjE;IAHiE,IAAA,EAChEC,QAAQ,EACRC,SAAS,EACV,GAHiE;IAIhE,OAAA,WAAA,mLACE,MAAA,EAACC,OAAAA;QAAIC,yBAAuB,EAAA;QAACF,WAAWA;kBACrCD;;AAGP", "ignoreList": [0]}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Dialog/DialogContent.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport type DialogContentProps = {\n  children?: React.ReactNode\n  className?: string\n}\n\nconst DialogContent: React.FC<DialogContentProps> = function DialogContent({\n  children,\n  className,\n}) {\n  return (\n    <div data-nextjs-dialog-content className={className}>\n      {children}\n    </div>\n  )\n}\n\nexport { DialogContent }\n"], "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "className", "div", "data-nextjs-dialog-content"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;;;AAO9B,MAAMC,gBAA8C,SAASA,cAAc,KAG1E;IAH0E,IAAA,EACzEC,QAAQ,EACRC,SAAS,EACV,GAH0E;IAIzE,OAAA,WAAA,mLACE,MAAA,EAACC,OAAAA;QAAIC,4BAA0B,EAAA;QAACF,WAAWA;kBACxCD;;AAGP", "ignoreList": [0]}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport type DialogHeaderProps = {\n  children?: React.ReactNode\n  className?: string\n}\n\nconst DialogHeader: React.FC<DialogHeaderProps> = function DialogHeader({\n  children,\n  className,\n}) {\n  return (\n    <div data-nextjs-dialog-header className={className}>\n      {children}\n    </div>\n  )\n}\n\nexport { DialogHeader }\n"], "names": ["React", "DialogHeader", "children", "className", "div", "data-nextjs-dialog-header"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;;;AAO9B,MAAMC,eAA4C,SAASA,aAAa,KAGvE;IAHuE,IAAA,EACtEC,QAAQ,EACRC,SAAS,EACV,GAHuE;IAItE,OAAA,WAAA,mLACE,MAAA,EAACC,OAAAA;QAAIC,2BAAyB,EAAA;QAACF,WAAWA;kBACvCD;;AAGP", "ignoreList": [0]}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/noop-template.ts"], "sourcesContent": ["export function noop(\n  strings: TemplateStringsArray,\n  ...keys: readonly string[]\n) {\n  const lastIndex = strings.length - 1\n  return (\n    strings.slice(0, lastIndex).reduce((p, s, i) => p + s + keys[i], '') +\n    strings[lastIndex]\n  )\n}\n"], "names": ["noop", "strings", "keys", "lastIndex", "length", "slice", "reduce", "p", "s", "i"], "mappings": ";;;AAAO,SAASA,KACdC,OAA6B;IAC7B,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,OAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,IAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAA0B;;IAE1B,MAAMC,YAAYF,QAAQG,MAAM,GAAG;IACnC,OACEH,QAAQI,KAAK,CAAC,GAAGF,WAAWG,MAAM,CAAC,CAACC,GAAGC,GAAGC,IAAMF,IAAIC,IAAIN,IAAI,CAACO,EAAE,EAAE,MACjER,OAAO,CAACE,UAAU;AAEtB", "ignoreList": [0]}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 609, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Dialog/styles.ts"], "sourcesContent": ["import { noop as css } from '../../helpers/noop-template'\n\nconst styles = css`\n  [data-nextjs-dialog] {\n    display: flex;\n    flex-direction: column;\n    width: 100%;\n    margin-right: auto;\n    margin-left: auto;\n    outline: none;\n    background: var(--color-background);\n    border-radius: var(--size-gap);\n    box-shadow: 0 var(--size-gap-half) var(--size-gap-double)\n      rgba(0, 0, 0, 0.25);\n    max-height: calc(100% - 56px);\n    overflow-y: hidden;\n  }\n\n  @media (max-height: 812px) {\n    [data-nextjs-dialog-overlay] {\n      max-height: calc(100% - 15px);\n    }\n  }\n\n  @media (min-width: 576px) {\n    [data-nextjs-dialog] {\n      max-width: 540px;\n      box-shadow: 0 var(--size-gap) var(--size-gap-quad) rgba(0, 0, 0, 0.25);\n    }\n  }\n\n  @media (min-width: 768px) {\n    [data-nextjs-dialog] {\n      max-width: 720px;\n    }\n  }\n\n  @media (min-width: 992px) {\n    [data-nextjs-dialog] {\n      max-width: 960px;\n    }\n  }\n\n  [data-nextjs-dialog-banner] {\n    position: relative;\n  }\n  [data-nextjs-dialog-banner].banner-warning {\n    border-color: var(--color-ansi-yellow);\n  }\n  [data-nextjs-dialog-banner].banner-error {\n    border-color: var(--color-ansi-red);\n  }\n\n  [data-nextjs-dialog-banner]::after {\n    z-index: 2;\n    content: '';\n    position: absolute;\n    top: 0;\n    right: 0;\n    width: 100%;\n    /* banner width: */\n    border-top-width: var(--size-gap-half);\n    border-bottom-width: 0;\n    border-top-style: solid;\n    border-bottom-style: solid;\n    border-top-color: inherit;\n    border-bottom-color: transparent;\n  }\n\n  [data-nextjs-dialog-content] {\n    overflow-y: auto;\n    border: none;\n    margin: 0;\n    /* calc(padding + banner width offset) */\n    padding: calc(var(--size-gap-double) + var(--size-gap-half))\n      var(--size-gap-double);\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n  }\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {\n    flex-shrink: 0;\n    margin-bottom: var(--size-gap-double);\n  }\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {\n    position: relative;\n    flex: 1 1 auto;\n  }\n`\n\nexport { styles }\n"], "names": ["noop", "css", "styles"], "mappings": ";;;AAAA,SAASA,QAAQC,GAAG,QAAQ,8BAA6B;;;;;;;;;;;;;AAEzD,MAAMC,uPAASD,OAAAA,EAAAA", "ignoreList": [0]}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Dialog/index.ts"], "sourcesContent": ["export { Dialog } from './Dialog'\nexport { DialogBody } from './DialogBody'\nexport { DialogContent } from './DialogContent'\nexport { DialogHeader } from './DialogHeader'\nexport { styles } from './styles'\n"], "names": ["Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "styles"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Overlay/maintain--tab-focus.ts"], "sourcesContent": ["/* eslint-disable */\n// @ts-nocheck\n// Copied from https://github.com/medialize/ally.js\n// License: MIT\n// Copyright (c) 2015 <PERSON>\n//\n// Entrypoint: ally.js/maintain/tab-focus\n\nimport _platform from 'next/dist/compiled/platform'\nimport cssEscape from 'next/dist/compiled/css.escape'\n\n// input may be undefined, selector-tring, Node, NodeList, HTMLCollection, array of Nodes\n// yes, to some extent this is a bad replica of j<PERSON><PERSON><PERSON>'s constructor function\nfunction nodeArray(input) {\n  if (!input) {\n    return []\n  }\n\n  if (Array.isArray(input)) {\n    return input\n  }\n\n  // instanceof Node - does not work with iframes\n  if (input.nodeType !== undefined) {\n    return [input]\n  }\n\n  if (typeof input === 'string') {\n    input = document.querySelectorAll(input)\n  }\n\n  if (input.length !== undefined) {\n    return [].slice.call(input, 0)\n  }\n\n  throw new TypeError('unexpected input ' + String(input))\n}\n\nfunction contextToElement(_ref) {\n  var context = _ref.context,\n    _ref$label = _ref.label,\n    label = _ref$label === undefined ? 'context-to-element' : _ref$label,\n    resolveDocument = _ref.resolveDocument,\n    defaultToDocument = _ref.defaultToDocument\n\n  var element = nodeArray(context)[0]\n\n  if (resolveDocument && element && element.nodeType === Node.DOCUMENT_NODE) {\n    element = element.documentElement\n  }\n\n  if (!element && defaultToDocument) {\n    return document.documentElement\n  }\n\n  if (!element) {\n    throw new TypeError(label + ' requires valid options.context')\n  }\n\n  if (\n    element.nodeType !== Node.ELEMENT_NODE &&\n    element.nodeType !== Node.DOCUMENT_FRAGMENT_NODE\n  ) {\n    throw new TypeError(label + ' requires options.context to be an Element')\n  }\n\n  return element\n}\n\nfunction getShadowHost() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context\n\n  var element = contextToElement({\n    label: 'get/shadow-host',\n    context: context,\n  })\n\n  // walk up to the root\n  var container = null\n\n  while (element) {\n    container = element\n    element = element.parentNode\n  }\n\n  // https://developer.mozilla.org/docs/Web/API/Node.nodeType\n  // NOTE: Firefox 34 does not expose ShadowRoot.host (but 37 does)\n  if (\n    container.nodeType === container.DOCUMENT_FRAGMENT_NODE &&\n    container.host\n  ) {\n    // the root is attached to a fragment node that has a host\n    return container.host\n  }\n\n  return null\n}\n\nfunction getDocument(node) {\n  if (!node) {\n    return document\n  }\n\n  if (node.nodeType === Node.DOCUMENT_NODE) {\n    return node\n  }\n\n  return node.ownerDocument || document\n}\n\nfunction isActiveElement(context) {\n  var element = contextToElement({\n    label: 'is/active-element',\n    resolveDocument: true,\n    context: context,\n  })\n\n  var _document = getDocument(element)\n  if (_document.activeElement === element) {\n    return true\n  }\n\n  var shadowHost = getShadowHost({ context: element })\n  if (shadowHost && shadowHost.shadowRoot.activeElement === element) {\n    return true\n  }\n\n  return false\n}\n\n// [elem, elem.parent, elem.parent.parent, …, html]\n// will not contain the shadowRoot (DOCUMENT_FRAGMENT_NODE) and shadowHost\nfunction getParents() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context\n\n  var list = []\n  var element = contextToElement({\n    label: 'get/parents',\n    context: context,\n  })\n\n  while (element) {\n    list.push(element)\n    // IE does know support parentElement on SVGElement\n    element = element.parentNode\n    if (element && element.nodeType !== Node.ELEMENT_NODE) {\n      element = null\n    }\n  }\n\n  return list\n}\n\n// Element.prototype.matches may be available at a different name\n// https://developer.mozilla.org/en/docs/Web/API/Element/matches\n\nvar names = [\n  'matches',\n  'webkitMatchesSelector',\n  'mozMatchesSelector',\n  'msMatchesSelector',\n]\nvar name = null\n\nfunction findMethodName(element) {\n  names.some(function (_name) {\n    if (!element[_name]) {\n      return false\n    }\n\n    name = _name\n    return true\n  })\n}\n\nfunction elementMatches(element, selector) {\n  if (!name) {\n    findMethodName(element)\n  }\n\n  return element[name](selector)\n}\n\n// deep clone of original platform\nvar platform = JSON.parse(JSON.stringify(_platform))\n\n// operating system\nvar os = platform.os.family || ''\nvar ANDROID = os === 'Android'\nvar WINDOWS = os.slice(0, 7) === 'Windows'\nvar OSX = os === 'OS X'\nvar IOS = os === 'iOS'\n\n// layout\nvar BLINK = platform.layout === 'Blink'\nvar GECKO = platform.layout === 'Gecko'\nvar TRIDENT = platform.layout === 'Trident'\nvar EDGE = platform.layout === 'EdgeHTML'\nvar WEBKIT = platform.layout === 'WebKit'\n\n// browser version (not layout engine version!)\nvar version = parseFloat(platform.version)\nvar majorVersion = Math.floor(version)\nplatform.majorVersion = majorVersion\n\nplatform.is = {\n  // operating system\n  ANDROID: ANDROID,\n  WINDOWS: WINDOWS,\n  OSX: OSX,\n  IOS: IOS,\n  // layout\n  BLINK: BLINK, // \"Chrome\", \"Chrome Mobile\", \"Opera\"\n  GECKO: GECKO, // \"Firefox\"\n  TRIDENT: TRIDENT, // \"Internet Explorer\"\n  EDGE: EDGE, // \"Microsoft Edge\"\n  WEBKIT: WEBKIT, // \"Safari\"\n  // INTERNET EXPLORERS\n  IE9: TRIDENT && majorVersion === 9,\n  IE10: TRIDENT && majorVersion === 10,\n  IE11: TRIDENT && majorVersion === 11,\n}\n\nfunction before() {\n  var data = {\n    // remember what had focus to restore after test\n    activeElement: document.activeElement,\n    // remember scroll positions to restore after test\n    windowScrollTop: window.scrollTop,\n    windowScrollLeft: window.scrollLeft,\n    bodyScrollTop: document.body.scrollTop,\n    bodyScrollLeft: document.body.scrollLeft,\n  }\n\n  // wrap tests in an element hidden from screen readers to prevent them\n  // from announcing focus, which can be quite irritating to the user\n  var iframe = document.createElement('iframe')\n  iframe.setAttribute(\n    'style',\n    'position:absolute; position:fixed; top:0; left:-2px; width:1px; height:1px; overflow:hidden;'\n  )\n  iframe.setAttribute('aria-live', 'off')\n  iframe.setAttribute('aria-busy', 'true')\n  iframe.setAttribute('aria-hidden', 'true')\n  document.body.appendChild(iframe)\n\n  var _window = iframe.contentWindow\n  var _document = _window.document\n\n  _document.open()\n  _document.close()\n  var wrapper = _document.createElement('div')\n  _document.body.appendChild(wrapper)\n\n  data.iframe = iframe\n  data.wrapper = wrapper\n  data.window = _window\n  data.document = _document\n\n  return data\n}\n\n// options.element:\n//  {string} element name\n//  {function} callback(wrapper, document) to generate an element\n// options.mutate: (optional)\n//  {function} callback(element, wrapper, document) to manipulate element prior to focus-test.\n//             Can return DOMElement to define focus target (default: element)\n// options.validate: (optional)\n//  {function} callback(element, focusTarget, document) to manipulate test-result\nfunction test(data, options) {\n  // make sure we operate on a clean slate\n  data.wrapper.innerHTML = ''\n  // create dummy element to test focusability of\n  var element =\n    typeof options.element === 'string'\n      ? data.document.createElement(options.element)\n      : options.element(data.wrapper, data.document)\n  // allow callback to further specify dummy element\n  // and optionally define element to focus\n  var focus =\n    options.mutate && options.mutate(element, data.wrapper, data.document)\n  if (!focus && focus !== false) {\n    focus = element\n  }\n  // element needs to be part of the DOM to be focusable\n  !element.parentNode && data.wrapper.appendChild(element)\n  // test if the element with invalid tabindex can be focused\n  focus && focus.focus && focus.focus()\n  // validate test's result\n  return options.validate\n    ? options.validate(element, focus, data.document)\n    : data.document.activeElement === focus\n}\n\nfunction after(data) {\n  // restore focus to what it was before test and cleanup\n  if (data.activeElement === document.body) {\n    document.activeElement &&\n      document.activeElement.blur &&\n      document.activeElement.blur()\n    if (platform.is.IE10) {\n      // IE10 does not redirect focus to <body> when the activeElement is removed\n      document.body.focus()\n    }\n  } else {\n    data.activeElement && data.activeElement.focus && data.activeElement.focus()\n  }\n\n  document.body.removeChild(data.iframe)\n\n  // restore scroll position\n  window.scrollTop = data.windowScrollTop\n  window.scrollLeft = data.windowScrollLeft\n  document.body.scrollTop = data.bodyScrollTop\n  document.body.scrollLeft = data.bodyScrollLeft\n}\n\nfunction detectFocus(tests) {\n  var data = before()\n\n  var results = {}\n  Object.keys(tests).map(function (key) {\n    results[key] = test(data, tests[key])\n  })\n\n  after(data)\n  return results\n}\n\n// this file is overwritten by `npm run build:pre`\nvar version$1 = '1.4.1'\n\n/*\n    Facility to cache test results in localStorage.\n\n    USAGE:\n      cache.get('key');\n      cache.set('key', 'value');\n */\n\nfunction readLocalStorage(key) {\n  // allow reading from storage to retrieve previous support results\n  // even while the document does not have focus\n  var data = void 0\n\n  try {\n    data = window.localStorage && window.localStorage.getItem(key)\n    data = data ? JSON.parse(data) : {}\n  } catch (e) {\n    data = {}\n  }\n\n  return data\n}\n\nfunction writeLocalStorage(key, value) {\n  if (!document.hasFocus()) {\n    // if the document does not have focus when tests are executed, focus() may\n    // not be handled properly and events may not be dispatched immediately.\n    // This can happen when a document is reloaded while Developer Tools have focus.\n    try {\n      window.localStorage && window.localStorage.removeItem(key)\n    } catch (e) {\n      // ignore\n    }\n\n    return\n  }\n\n  try {\n    window.localStorage &&\n      window.localStorage.setItem(key, JSON.stringify(value))\n  } catch (e) {\n    // ignore\n  }\n}\n\nvar userAgent =\n  (typeof window !== 'undefined' && window.navigator.userAgent) || ''\nvar cacheKey = 'ally-supports-cache'\nvar cache = readLocalStorage(cacheKey)\n\n// update the cache if ally or the user agent changed (newer version, etc)\nif (cache.userAgent !== userAgent || cache.version !== version$1) {\n  cache = {}\n}\n\ncache.userAgent = userAgent\ncache.version = version$1\n\nvar cache$1 = {\n  get: function get() {\n    return cache\n  },\n  set: function set(values) {\n    Object.keys(values).forEach(function (key) {\n      cache[key] = values[key]\n    })\n\n    cache.time = new Date().toISOString()\n    writeLocalStorage(cacheKey, cache)\n  },\n}\n\nfunction cssShadowPiercingDeepCombinator() {\n  var combinator = void 0\n\n  // see https://dev.w3.org/csswg/css-scoping-1/#deep-combinator\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1117572\n  // https://code.google.com/p/chromium/issues/detail?id=446051\n  try {\n    document.querySelector('html >>> :first-child')\n    combinator = '>>>'\n  } catch (noArrowArrowArrow) {\n    try {\n      // old syntax supported at least up to Chrome 41\n      // https://code.google.com/p/chromium/issues/detail?id=446051\n      document.querySelector('html /deep/ :first-child')\n      combinator = '/deep/'\n    } catch (noDeep) {\n      combinator = ''\n    }\n  }\n\n  return combinator\n}\n\nvar gif =\n  'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'\n\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusAreaImgTabindex = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML =\n      '<map name=\"image-map-tabindex-test\">' +\n      '<area shape=\"rect\" coords=\"63,19,144,45\"></map>' +\n      '<img usemap=\"#image-map-tabindex-test\" tabindex=\"-1\" alt=\"\" src=\"' +\n      gif +\n      '\">'\n\n    return element.querySelector('area')\n  },\n}\n\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusAreaTabindex = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML =\n      '<map name=\"image-map-tabindex-test\">' +\n      '<area href=\"#void\" tabindex=\"-1\" shape=\"rect\" coords=\"63,19,144,45\"></map>' +\n      '<img usemap=\"#image-map-tabindex-test\" alt=\"\" src=\"' +\n      gif +\n      '\">'\n\n    return false\n  },\n  validate: function validate(element, focusTarget, _document) {\n    if (platform.is.GECKO) {\n      // fixes https://github.com/medialize/ally.js/issues/35\n      // Firefox loads the DataURI asynchronously, causing a false-negative\n      return true\n    }\n\n    var focus = element.querySelector('area')\n    focus.focus()\n    return _document.activeElement === focus\n  },\n}\n\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusAreaWithoutHref = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML =\n      '<map name=\"image-map-area-href-test\">' +\n      '<area shape=\"rect\" coords=\"63,19,144,45\"></map>' +\n      '<img usemap=\"#image-map-area-href-test\" alt=\"\" src=\"' +\n      gif +\n      '\">'\n\n    return element.querySelector('area')\n  },\n  validate: function validate(element, focusTarget, _document) {\n    if (platform.is.GECKO) {\n      // fixes https://github.com/medialize/ally.js/issues/35\n      // Firefox loads the DataURI asynchronously, causing a false-negative\n      return true\n    }\n\n    return _document.activeElement === focusTarget\n  },\n}\n\nvar focusAudioWithoutControls = {\n  name: 'can-focus-audio-without-controls',\n  element: 'audio',\n  mutate: function mutate(element) {\n    try {\n      // invalid media file can trigger warning in console, data-uri to prevent HTTP request\n      element.setAttribute('src', gif)\n    } catch (e) {\n      // IE9 may throw \"Error: Not implemented\"\n    }\n  },\n}\n\nvar invalidGif =\n  'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ'\n\n// NOTE: https://github.com/medialize/ally.js/issues/35\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusBrokenImageMap = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML =\n      '<map name=\"broken-image-map-test\"><area href=\"#void\" shape=\"rect\" coords=\"63,19,144,45\"></map>' +\n      '<img usemap=\"#broken-image-map-test\" alt=\"\" src=\"' +\n      invalidGif +\n      '\">'\n\n    return element.querySelector('area')\n  },\n}\n\n// Children of focusable elements with display:flex are focusable in IE10-11\nvar focusChildrenOfFocusableFlexbox = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.setAttribute('tabindex', '-1')\n    element.setAttribute(\n      'style',\n      'display: -webkit-flex; display: -ms-flexbox; display: flex;'\n    )\n    element.innerHTML = '<span style=\"display: block;\">hello</span>'\n    return element.querySelector('span')\n  },\n}\n\n// fieldset[tabindex=0][disabled] should not be focusable, but Blink and WebKit disagree\n// @specification https://www.w3.org/TR/html5/disabled-elements.html#concept-element-disabled\n// @browser-issue Chromium https://crbug.com/453847\n// @browser-issue WebKit https://bugs.webkit.org/show_bug.cgi?id=141086\nvar focusFieldsetDisabled = {\n  element: 'fieldset',\n  mutate: function mutate(element) {\n    element.setAttribute('tabindex', 0)\n    element.setAttribute('disabled', 'disabled')\n  },\n}\n\nvar focusFieldset = {\n  element: 'fieldset',\n  mutate: function mutate(element) {\n    element.innerHTML = '<legend>legend</legend><p>content</p>'\n  },\n}\n\n// elements with display:flex are focusable in IE10-11\nvar focusFlexboxContainer = {\n  element: 'span',\n  mutate: function mutate(element) {\n    element.setAttribute(\n      'style',\n      'display: -webkit-flex; display: -ms-flexbox; display: flex;'\n    )\n    element.innerHTML = '<span style=\"display: block;\">hello</span>'\n  },\n}\n\n// form[tabindex=0][disabled] should be focusable as the\n// specification doesn't know the disabled attribute on the form element\n// @specification https://www.w3.org/TR/html5/forms.html#the-form-element\nvar focusFormDisabled = {\n  element: 'form',\n  mutate: function mutate(element) {\n    element.setAttribute('tabindex', 0)\n    element.setAttribute('disabled', 'disabled')\n  },\n}\n\n// NOTE: https://github.com/medialize/ally.js/issues/35\n// fixes https://github.com/medialize/ally.js/issues/20\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-ismap\nvar focusImgIsmap = {\n  element: 'a',\n  mutate: function mutate(element) {\n    element.href = '#void'\n    element.innerHTML = '<img ismap src=\"' + gif + '\" alt=\"\">'\n    return element.querySelector('img')\n  },\n}\n\n// NOTE: https://github.com/medialize/ally.js/issues/35\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\nvar focusImgUsemapTabindex = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML =\n      '<map name=\"image-map-tabindex-test\"><area href=\"#void\" shape=\"rect\" coords=\"63,19,144,45\"></map>' +\n      '<img usemap=\"#image-map-tabindex-test\" tabindex=\"-1\" alt=\"\" ' +\n      'src=\"' +\n      gif +\n      '\">'\n\n    return element.querySelector('img')\n  },\n}\n\nvar focusInHiddenIframe = {\n  element: function element(wrapper, _document) {\n    var iframe = _document.createElement('iframe')\n\n    // iframe must be part of the DOM before accessing the contentWindow is possible\n    wrapper.appendChild(iframe)\n\n    // create the iframe's default document (<html><head></head><body></body></html>)\n    var iframeDocument = iframe.contentWindow.document\n    iframeDocument.open()\n    iframeDocument.close()\n    return iframe\n  },\n  mutate: function mutate(iframe) {\n    iframe.style.visibility = 'hidden'\n\n    var iframeDocument = iframe.contentWindow.document\n    var input = iframeDocument.createElement('input')\n    iframeDocument.body.appendChild(input)\n    return input\n  },\n  validate: function validate(iframe) {\n    var iframeDocument = iframe.contentWindow.document\n    var focus = iframeDocument.querySelector('input')\n    return iframeDocument.activeElement === focus\n  },\n}\n\nvar result = !platform.is.WEBKIT\n\nfunction focusInZeroDimensionObject() {\n  return result\n}\n\n// Firefox allows *any* value and treats invalid values like tabindex=\"-1\"\n// @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\nvar focusInvalidTabindex = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.setAttribute('tabindex', 'invalid-value')\n  },\n}\n\nvar focusLabelTabindex = {\n  element: 'label',\n  mutate: function mutate(element) {\n    element.setAttribute('tabindex', '-1')\n  },\n  validate: function validate(element, focusTarget, _document) {\n    // force layout in Chrome 49, otherwise the element won't be focusable\n    /* eslint-disable no-unused-vars */\n    var variableToPreventDeadCodeElimination = element.offsetHeight\n    /* eslint-enable no-unused-vars */\n    element.focus()\n    return _document.activeElement === element\n  },\n}\n\nvar svg =\n  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtb' +\n  'G5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBpZD0ic3ZnIj48dGV4dCB4PSIxMCIgeT0iMjAiIGlkPSJ' +\n  'zdmctbGluay10ZXh0Ij50ZXh0PC90ZXh0Pjwvc3ZnPg=='\n\n// Note: IE10 on BrowserStack does not like this test\n\nvar focusObjectSvgHidden = {\n  element: 'object',\n  mutate: function mutate(element) {\n    element.setAttribute('type', 'image/svg+xml')\n    element.setAttribute('data', svg)\n    element.setAttribute('width', '200')\n    element.setAttribute('height', '50')\n    element.style.visibility = 'hidden'\n  },\n}\n\n// Note: IE10 on BrowserStack does not like this test\n\nvar focusObjectSvg = {\n  name: 'can-focus-object-svg',\n  element: 'object',\n  mutate: function mutate(element) {\n    element.setAttribute('type', 'image/svg+xml')\n    element.setAttribute('data', svg)\n    element.setAttribute('width', '200')\n    element.setAttribute('height', '50')\n  },\n  validate: function validate(element, focusTarget, _document) {\n    if (platform.is.GECKO) {\n      // Firefox seems to be handling the object creation asynchronously and thereby produces a false negative test result.\n      // Because we know Firefox is able to focus object elements referencing SVGs, we simply cheat by sniffing the user agent string\n      return true\n    }\n\n    return _document.activeElement === element\n  },\n}\n\n// Every Environment except IE9 considers SWF objects focusable\nvar result$1 = !platform.is.IE9\n\nfunction focusObjectSwf() {\n  return result$1\n}\n\nvar focusRedirectImgUsemap = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML =\n      '<map name=\"focus-redirect-img-usemap\"><area href=\"#void\" shape=\"rect\" coords=\"63,19,144,45\"></map>' +\n      '<img usemap=\"#focus-redirect-img-usemap\" alt=\"\" ' +\n      'src=\"' +\n      gif +\n      '\">'\n\n    // focus the <img>, not the <div>\n    return element.querySelector('img')\n  },\n  validate: function validate(element, focusTarget, _document) {\n    var target = element.querySelector('area')\n    return _document.activeElement === target\n  },\n}\n\n// see https://jsbin.com/nenirisage/edit?html,js,console,output\n\nvar focusRedirectLegend = {\n  element: 'fieldset',\n  mutate: function mutate(element) {\n    element.innerHTML =\n      '<legend>legend</legend><input tabindex=\"-1\"><input tabindex=\"0\">'\n    // take care of focus in validate();\n    return false\n  },\n  validate: function validate(element, focusTarget, _document) {\n    var focusable = element.querySelector('input[tabindex=\"-1\"]')\n    var tabbable = element.querySelector('input[tabindex=\"0\"]')\n\n    // Firefox requires this test to focus the <fieldset> first, while this is not necessary in\n    // https://jsbin.com/nenirisage/edit?html,js,console,output\n    element.focus()\n\n    element.querySelector('legend').focus()\n    return (\n      (_document.activeElement === focusable && 'focusable') ||\n      (_document.activeElement === tabbable && 'tabbable') ||\n      ''\n    )\n  },\n}\n\n// https://github.com/medialize/ally.js/issues/21\nvar focusScrollBody = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.setAttribute('style', 'width: 100px; height: 50px; overflow: auto;')\n    element.innerHTML =\n      '<div style=\"width: 500px; height: 40px;\">scrollable content</div>'\n    return element.querySelector('div')\n  },\n}\n\n// https://github.com/medialize/ally.js/issues/21\nvar focusScrollContainerWithoutOverflow = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.setAttribute('style', 'width: 100px; height: 50px;')\n    element.innerHTML =\n      '<div style=\"width: 500px; height: 40px;\">scrollable content</div>'\n  },\n}\n\n// https://github.com/medialize/ally.js/issues/21\nvar focusScrollContainer = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.setAttribute('style', 'width: 100px; height: 50px; overflow: auto;')\n    element.innerHTML =\n      '<div style=\"width: 500px; height: 40px;\">scrollable content</div>'\n  },\n}\n\nvar focusSummary = {\n  element: 'details',\n  mutate: function mutate(element) {\n    element.innerHTML = '<summary>foo</summary><p>content</p>'\n    return element.firstElementChild\n  },\n}\n\nfunction makeFocusableForeignObject() {\n  // Constructs <foreignObject width=\"30\" height=\"30\"><input type=\"text\"/></foreignObject>\n  // without raising a Trusted Types violation\n  var foreignObject = document.createElementNS(\n    'http://www.w3.org/2000/svg',\n    'foreignObject'\n  )\n  foreignObject.width.baseVal.value = 30\n  foreignObject.height.baseVal.value = 30\n  foreignObject.appendChild(document.createElement('input'))\n  foreignObject.lastChild.type = 'text'\n\n  return foreignObject\n}\n\nfunction focusSvgForeignObjectHack(element) {\n  // Edge13, Edge14: foreignObject focus hack\n  // https://jsbin.com/kunehinugi/edit?html,js,output\n  // https://jsbin.com/fajagi/3/edit?html,js,output\n  var isSvgElement =\n    element.ownerSVGElement || element.nodeName.toLowerCase() === 'svg'\n  if (!isSvgElement) {\n    return false\n  }\n\n  // inject and focus an <input> element into the SVG element to receive focus\n  var foreignObject = makeFocusableForeignObject()\n  element.appendChild(foreignObject)\n  var input = foreignObject.querySelector('input')\n  input.focus()\n\n  // upon disabling the activeElement, IE and Edge\n  // will not shift focus to <body> like all the other\n  // browsers, but instead find the first focusable\n  // ancestor and shift focus to that\n  input.disabled = true\n\n  // clean up\n  element.removeChild(foreignObject)\n  return true\n}\n\nfunction generate(element) {\n  return (\n    '<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">' +\n    element +\n    '</svg>'\n  )\n}\n\nfunction focus(element) {\n  if (element.focus) {\n    return\n  }\n\n  try {\n    HTMLElement.prototype.focus.call(element)\n  } catch (e) {\n    focusSvgForeignObjectHack(element)\n  }\n}\n\nfunction validate(element, focusTarget, _document) {\n  focus(focusTarget)\n  return _document.activeElement === focusTarget\n}\n\nvar focusSvgFocusableAttribute = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML = generate('<text focusable=\"true\">a</text>')\n    return element.querySelector('text')\n  },\n  validate: validate,\n}\n\nvar focusSvgTabindexAttribute = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML = generate('<text tabindex=\"0\">a</text>')\n    return element.querySelector('text')\n  },\n  validate: validate,\n}\n\nvar focusSvgNegativeTabindexAttribute = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML = generate('<text tabindex=\"-1\">a</text>')\n    return element.querySelector('text')\n  },\n  validate: validate,\n}\n\nvar focusSvgUseTabindex = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML = generate(\n      [\n        '<g id=\"ally-test-target\"><a xlink:href=\"#void\"><text>link</text></a></g>',\n        '<use xlink:href=\"#ally-test-target\" x=\"0\" y=\"0\" tabindex=\"-1\" />',\n      ].join('')\n    )\n\n    return element.querySelector('use')\n  },\n  validate: validate,\n}\n\nvar focusSvgForeignobjectTabindex = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML = generate(\n      '<foreignObject tabindex=\"-1\"><input type=\"text\" /></foreignObject>'\n    )\n    // Safari 8's querySelector() can't identify foreignObject, but getElementsByTagName() can\n    return (\n      element.querySelector('foreignObject') ||\n      element.getElementsByTagName('foreignObject')[0]\n    )\n  },\n  validate: validate,\n}\n\n// Firefox seems to be handling the SVG-document-in-iframe creation asynchronously\n// and thereby produces a false negative test result. Thus the test is pointless\n// and we resort to UA sniffing once again.\n// see http://jsbin.com/vunadohoko/1/edit?js,console,output\n\nvar result$2 = Boolean(\n  platform.is.GECKO &&\n    typeof SVGElement !== 'undefined' &&\n    SVGElement.prototype.focus\n)\n\nfunction focusSvgInIframe() {\n  return result$2\n}\n\nvar focusSvg = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.innerHTML = generate('')\n    return element.firstChild\n  },\n  validate: validate,\n}\n\n// Firefox allows *any* value and treats invalid values like tabindex=\"-1\"\n// @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\nvar focusTabindexTrailingCharacters = {\n  element: 'div',\n  mutate: function mutate(element) {\n    element.setAttribute('tabindex', '3x')\n  },\n}\n\nvar focusTable = {\n  element: 'table',\n  mutate: function mutate(element, wrapper, _document) {\n    // IE9 has a problem replacing TBODY contents with innerHTML.\n    // https://stackoverflow.com/a/8097055/515124\n    // element.innerHTML = '<tr><td>cell</td></tr>';\n    var fragment = _document.createDocumentFragment()\n    fragment.innerHTML = '<tr><td>cell</td></tr>'\n    element.appendChild(fragment)\n  },\n}\n\nvar focusVideoWithoutControls = {\n  element: 'video',\n  mutate: function mutate(element) {\n    try {\n      // invalid media file can trigger warning in console, data-uri to prevent HTTP request\n      element.setAttribute('src', gif)\n    } catch (e) {\n      // IE9 may throw \"Error: Not implemented\"\n    }\n  },\n}\n\n// https://jsbin.com/vafaba/3/edit?html,js,console,output\nvar result$3 = platform.is.GECKO || platform.is.TRIDENT || platform.is.EDGE\n\nfunction tabsequenceAreaAtImgPosition() {\n  return result$3\n}\n\nvar testCallbacks = {\n  cssShadowPiercingDeepCombinator: cssShadowPiercingDeepCombinator,\n  focusInZeroDimensionObject: focusInZeroDimensionObject,\n  focusObjectSwf: focusObjectSwf,\n  focusSvgInIframe: focusSvgInIframe,\n  tabsequenceAreaAtImgPosition: tabsequenceAreaAtImgPosition,\n}\n\nvar testDescriptions = {\n  focusAreaImgTabindex: focusAreaImgTabindex,\n  focusAreaTabindex: focusAreaTabindex,\n  focusAreaWithoutHref: focusAreaWithoutHref,\n  focusAudioWithoutControls: focusAudioWithoutControls,\n  focusBrokenImageMap: focusBrokenImageMap,\n  focusChildrenOfFocusableFlexbox: focusChildrenOfFocusableFlexbox,\n  focusFieldsetDisabled: focusFieldsetDisabled,\n  focusFieldset: focusFieldset,\n  focusFlexboxContainer: focusFlexboxContainer,\n  focusFormDisabled: focusFormDisabled,\n  focusImgIsmap: focusImgIsmap,\n  focusImgUsemapTabindex: focusImgUsemapTabindex,\n  focusInHiddenIframe: focusInHiddenIframe,\n  focusInvalidTabindex: focusInvalidTabindex,\n  focusLabelTabindex: focusLabelTabindex,\n  focusObjectSvg: focusObjectSvg,\n  focusObjectSvgHidden: focusObjectSvgHidden,\n  focusRedirectImgUsemap: focusRedirectImgUsemap,\n  focusRedirectLegend: focusRedirectLegend,\n  focusScrollBody: focusScrollBody,\n  focusScrollContainerWithoutOverflow: focusScrollContainerWithoutOverflow,\n  focusScrollContainer: focusScrollContainer,\n  focusSummary: focusSummary,\n  focusSvgFocusableAttribute: focusSvgFocusableAttribute,\n  focusSvgTabindexAttribute: focusSvgTabindexAttribute,\n  focusSvgNegativeTabindexAttribute: focusSvgNegativeTabindexAttribute,\n  focusSvgUseTabindex: focusSvgUseTabindex,\n  focusSvgForeignobjectTabindex: focusSvgForeignobjectTabindex,\n  focusSvg: focusSvg,\n  focusTabindexTrailingCharacters: focusTabindexTrailingCharacters,\n  focusTable: focusTable,\n  focusVideoWithoutControls: focusVideoWithoutControls,\n}\n\nfunction executeTests() {\n  var results = detectFocus(testDescriptions)\n  Object.keys(testCallbacks).forEach(function (key) {\n    results[key] = testCallbacks[key]()\n  })\n\n  return results\n}\n\nvar supportsCache = null\n\nfunction _supports() {\n  if (supportsCache) {\n    return supportsCache\n  }\n\n  supportsCache = cache$1.get()\n  if (!supportsCache.time) {\n    cache$1.set(executeTests())\n    supportsCache = cache$1.get()\n  }\n\n  return supportsCache\n}\n\nvar supports = void 0\n\n// https://www.w3.org/TR/html5/infrastructure.html#rules-for-parsing-integers\n// NOTE: all browsers agree to allow trailing spaces as well\nvar validIntegerPatternNoTrailing = /^\\s*(-|\\+)?[0-9]+\\s*$/\nvar validIntegerPatternWithTrailing = /^\\s*(-|\\+)?[0-9]+.*$/\n\nfunction isValidTabindex(context) {\n  if (!supports) {\n    supports = _supports()\n  }\n\n  var validIntegerPattern = supports.focusTabindexTrailingCharacters\n    ? validIntegerPatternWithTrailing\n    : validIntegerPatternNoTrailing\n\n  var element = contextToElement({\n    label: 'is/valid-tabindex',\n    resolveDocument: true,\n    context: context,\n  })\n\n  // Edge 14 has a capitalization problem on SVG elements,\n  // see https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/9282058/\n  var hasTabindex = element.hasAttribute('tabindex')\n  var hasTabIndex = element.hasAttribute('tabIndex')\n\n  if (!hasTabindex && !hasTabIndex) {\n    return false\n  }\n\n  // older Firefox and Internet Explorer don't support tabindex on SVG elements\n  var isSvgElement =\n    element.ownerSVGElement || element.nodeName.toLowerCase() === 'svg'\n  if (isSvgElement && !supports.focusSvgTabindexAttribute) {\n    return false\n  }\n\n  // @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  if (supports.focusInvalidTabindex) {\n    return true\n  }\n\n  // an element matches the tabindex selector even if its value is invalid\n  var tabindex = element.getAttribute(hasTabindex ? 'tabindex' : 'tabIndex')\n  // IE11 parses tabindex=\"\" as the value \"-32768\"\n  // @browser-issue Trident https://connect.microsoft.com/IE/feedback/details/1072965\n  if (tabindex === '-32768') {\n    return false\n  }\n\n  return Boolean(tabindex && validIntegerPattern.test(tabindex))\n}\n\nfunction tabindexValue(element) {\n  if (!isValidTabindex(element)) {\n    return null\n  }\n\n  // Edge 14 has a capitalization problem on SVG elements,\n  // see https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/9282058/\n  var hasTabindex = element.hasAttribute('tabindex')\n  var attributeName = hasTabindex ? 'tabindex' : 'tabIndex'\n\n  // @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  var tabindex = parseInt(element.getAttribute(attributeName), 10)\n  return isNaN(tabindex) ? -1 : tabindex\n}\n\n// this is a shared utility file for focus-relevant.js and tabbable.js\n// separate testing of this file's functions is not necessary,\n// as they're implicitly tested by way of the consumers\n\nfunction isUserModifyWritable(style) {\n  // https://www.w3.org/TR/1999/WD-css3-userint-19990916#user-modify\n  // https://github.com/medialize/ally.js/issues/17\n  var userModify = style.webkitUserModify || ''\n  return Boolean(userModify && userModify.indexOf('write') !== -1)\n}\n\nfunction hasCssOverflowScroll(style) {\n  return [\n    style.getPropertyValue('overflow'),\n    style.getPropertyValue('overflow-x'),\n    style.getPropertyValue('overflow-y'),\n  ].some(function (overflow) {\n    return overflow === 'auto' || overflow === 'scroll'\n  })\n}\n\nfunction hasCssDisplayFlex(style) {\n  return style.display.indexOf('flex') > -1\n}\n\nfunction isScrollableContainer(element, nodeName, parentNodeName, parentStyle) {\n  if (nodeName !== 'div' && nodeName !== 'span') {\n    // Internet Explorer advances scrollable containers and bodies to focusable\n    // only if the scrollable container is <div> or <span> - this does *not*\n    // happen for <section>, <article>, …\n    return false\n  }\n\n  if (\n    parentNodeName &&\n    parentNodeName !== 'div' &&\n    parentNodeName !== 'span' &&\n    !hasCssOverflowScroll(parentStyle)\n  ) {\n    return false\n  }\n\n  return (\n    element.offsetHeight < element.scrollHeight ||\n    element.offsetWidth < element.scrollWidth\n  )\n}\n\nvar supports$1 = void 0\n\nfunction isFocusRelevantRules() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    _ref$except = _ref.except,\n    except =\n      _ref$except === undefined\n        ? {\n            flexbox: false,\n            scrollable: false,\n            shadow: false,\n          }\n        : _ref$except\n\n  if (!supports$1) {\n    supports$1 = _supports()\n  }\n\n  var element = contextToElement({\n    label: 'is/focus-relevant',\n    resolveDocument: true,\n    context: context,\n  })\n\n  if (!except.shadow && element.shadowRoot) {\n    // a ShadowDOM host receives focus when the focus moves to its content\n    return true\n  }\n\n  var nodeName = element.nodeName.toLowerCase()\n\n  if (nodeName === 'input' && element.type === 'hidden') {\n    // input[type=\"hidden\"] supports.cannot be focused\n    return false\n  }\n\n  if (\n    nodeName === 'input' ||\n    nodeName === 'select' ||\n    nodeName === 'button' ||\n    nodeName === 'textarea'\n  ) {\n    return true\n  }\n\n  if (nodeName === 'legend' && supports$1.focusRedirectLegend) {\n    // specifics filtered in is/focusable\n    return true\n  }\n\n  if (nodeName === 'label') {\n    // specifics filtered in is/focusable\n    return true\n  }\n\n  if (nodeName === 'area') {\n    // specifics filtered in is/focusable\n    return true\n  }\n\n  if (nodeName === 'a' && element.hasAttribute('href')) {\n    return true\n  }\n\n  if (nodeName === 'object' && element.hasAttribute('usemap')) {\n    // object[usemap] is not focusable in any browser\n    return false\n  }\n\n  if (nodeName === 'object') {\n    var svgType = element.getAttribute('type')\n    if (!supports$1.focusObjectSvg && svgType === 'image/svg+xml') {\n      // object[type=\"image/svg+xml\"] is not focusable in Internet Explorer\n      return false\n    } else if (\n      !supports$1.focusObjectSwf &&\n      svgType === 'application/x-shockwave-flash'\n    ) {\n      // object[type=\"application/x-shockwave-flash\"] is not focusable in Internet Explorer 9\n      return false\n    }\n  }\n\n  if (nodeName === 'iframe' || nodeName === 'object') {\n    // browsing context containers\n    return true\n  }\n\n  if (nodeName === 'embed' || nodeName === 'keygen') {\n    // embed is considered focus-relevant but not focusable\n    // see https://github.com/medialize/ally.js/issues/82\n    return true\n  }\n\n  if (element.hasAttribute('contenteditable')) {\n    // also see CSS property user-modify below\n    return true\n  }\n\n  if (\n    nodeName === 'audio' &&\n    (supports$1.focusAudioWithoutControls || element.hasAttribute('controls'))\n  ) {\n    return true\n  }\n\n  if (\n    nodeName === 'video' &&\n    (supports$1.focusVideoWithoutControls || element.hasAttribute('controls'))\n  ) {\n    return true\n  }\n\n  if (supports$1.focusSummary && nodeName === 'summary') {\n    return true\n  }\n\n  var validTabindex = isValidTabindex(element)\n\n  if (nodeName === 'img' && element.hasAttribute('usemap')) {\n    // Gecko, Trident and Edge do not allow an image with an image map and tabindex to be focused,\n    // it appears the tabindex is overruled so focus is still forwarded to the <map>\n    return (\n      (validTabindex && supports$1.focusImgUsemapTabindex) ||\n      supports$1.focusRedirectImgUsemap\n    )\n  }\n\n  if (supports$1.focusTable && (nodeName === 'table' || nodeName === 'td')) {\n    // IE10-11 supports.can focus <table> and <td>\n    return true\n  }\n\n  if (supports$1.focusFieldset && nodeName === 'fieldset') {\n    // IE10-11 supports.can focus <fieldset>\n    return true\n  }\n\n  var isSvgElement = nodeName === 'svg'\n  var isSvgContent = element.ownerSVGElement\n  var focusableAttribute = element.getAttribute('focusable')\n  var tabindex = tabindexValue(element)\n\n  if (\n    nodeName === 'use' &&\n    tabindex !== null &&\n    !supports$1.focusSvgUseTabindex\n  ) {\n    // <use> cannot be made focusable by adding a tabindex attribute anywhere but Blink and WebKit\n    return false\n  }\n\n  if (nodeName === 'foreignobject') {\n    // <use> can only be made focusable in Blink and WebKit\n    return tabindex !== null && supports$1.focusSvgForeignobjectTabindex\n  }\n\n  if (elementMatches(element, 'svg a') && element.hasAttribute('xlink:href')) {\n    return true\n  }\n\n  if (\n    (isSvgElement || isSvgContent) &&\n    element.focus &&\n    !supports$1.focusSvgNegativeTabindexAttribute &&\n    tabindex < 0\n  ) {\n    // Firefox 51 and 52 treat any natively tabbable SVG element with\n    // tabindex=\"-1\" as tabbable and everything else as inert\n    // see https://bugzilla.mozilla.org/show_bug.cgi?id=1302340\n    return false\n  }\n\n  if (isSvgElement) {\n    return (\n      validTabindex ||\n      supports$1.focusSvg ||\n      supports$1.focusSvgInIframe ||\n      // Internet Explorer understands the focusable attribute introduced in SVG Tiny 1.2\n      Boolean(\n        supports$1.focusSvgFocusableAttribute &&\n          focusableAttribute &&\n          focusableAttribute === 'true'\n      )\n    )\n  }\n\n  if (isSvgContent) {\n    if (supports$1.focusSvgTabindexAttribute && validTabindex) {\n      return true\n    }\n\n    if (supports$1.focusSvgFocusableAttribute) {\n      // Internet Explorer understands the focusable attribute introduced in SVG Tiny 1.2\n      return focusableAttribute === 'true'\n    }\n  }\n\n  // https://www.w3.org/TR/html5/editing.html#sequential-focus-navigation-and-the-tabindex-attribute\n  if (validTabindex) {\n    return true\n  }\n\n  var style = window.getComputedStyle(element, null)\n  if (isUserModifyWritable(style)) {\n    return true\n  }\n\n  if (\n    supports$1.focusImgIsmap &&\n    nodeName === 'img' &&\n    element.hasAttribute('ismap')\n  ) {\n    // IE10-11 considers the <img> in <a href><img ismap> focusable\n    // https://github.com/medialize/ally.js/issues/20\n    var hasLinkParent = getParents({ context: element }).some(\n      function (parent) {\n        return (\n          parent.nodeName.toLowerCase() === 'a' && parent.hasAttribute('href')\n        )\n      }\n    )\n\n    if (hasLinkParent) {\n      return true\n    }\n  }\n\n  // https://github.com/medialize/ally.js/issues/21\n  if (!except.scrollable && supports$1.focusScrollContainer) {\n    if (supports$1.focusScrollContainerWithoutOverflow) {\n      // Internet Explorer does will consider the scrollable area focusable\n      // if the element is a <div> or a <span> and it is in fact scrollable,\n      // regardless of the CSS overflow property\n      if (isScrollableContainer(element, nodeName)) {\n        return true\n      }\n    } else if (hasCssOverflowScroll(style)) {\n      // Firefox requires proper overflow setting, IE does not necessarily\n      // https://developer.mozilla.org/docs/Web/CSS/overflow\n      return true\n    }\n  }\n\n  if (\n    !except.flexbox &&\n    supports$1.focusFlexboxContainer &&\n    hasCssDisplayFlex(style)\n  ) {\n    // elements with display:flex are focusable in IE10-11\n    return true\n  }\n\n  var parent = element.parentElement\n  if (!except.scrollable && parent) {\n    var parentNodeName = parent.nodeName.toLowerCase()\n    var parentStyle = window.getComputedStyle(parent, null)\n    if (\n      supports$1.focusScrollBody &&\n      isScrollableContainer(parent, nodeName, parentNodeName, parentStyle)\n    ) {\n      // scrollable bodies are focusable Internet Explorer\n      // https://github.com/medialize/ally.js/issues/21\n      return true\n    }\n\n    // Children of focusable elements with display:flex are focusable in IE10-11\n    if (supports$1.focusChildrenOfFocusableFlexbox) {\n      if (hasCssDisplayFlex(parentStyle)) {\n        return true\n      }\n    }\n  }\n\n  // NOTE: elements marked as inert are not focusable,\n  // but that property is not exposed to the DOM\n  // https://www.w3.org/TR/html5/editing.html#inert\n\n  return false\n}\n\n// bind exceptions to an iterator callback\nisFocusRelevantRules.except = function () {\n  var except =\n    arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}\n\n  var isFocusRelevant = function isFocusRelevant(context) {\n    return isFocusRelevantRules({\n      context: context,\n      except: except,\n    })\n  }\n\n  isFocusRelevant.rules = isFocusRelevantRules\n  return isFocusRelevant\n}\n\n// provide isFocusRelevant(context) as default iterator callback\nvar isFocusRelevant = isFocusRelevantRules.except({})\n\nfunction findIndex(array, callback) {\n  // attempt to use native or polyfilled Array#findIndex first\n  if (array.findIndex) {\n    return array.findIndex(callback)\n  }\n\n  var length = array.length\n\n  // shortcut if the array is empty\n  if (length === 0) {\n    return -1\n  }\n\n  // otherwise loop over array\n  for (var i = 0; i < length; i++) {\n    if (callback(array[i], i, array)) {\n      return i\n    }\n  }\n\n  return -1\n}\n\nfunction getContentDocument(node) {\n  try {\n    // works on <object> and <iframe>\n    return (\n      node.contentDocument ||\n      // works on <object> and <iframe>\n      (node.contentWindow && node.contentWindow.document) ||\n      // works on <object> and <iframe> that contain SVG\n      (node.getSVGDocument && node.getSVGDocument()) ||\n      null\n    )\n  } catch (e) {\n    // SecurityError: Failed to read the 'contentDocument' property from 'HTMLObjectElement'\n    // also IE may throw member not found exception e.g. on <object type=\"image/png\">\n    return null\n  }\n}\n\nfunction getWindow(node) {\n  var _document = getDocument(node)\n  return _document.defaultView || window\n}\n\nvar shadowPrefix = void 0\n\nfunction selectInShadows(selector) {\n  if (typeof shadowPrefix !== 'string') {\n    var operator = cssShadowPiercingDeepCombinator()\n    if (operator) {\n      shadowPrefix = ', html ' + operator + ' '\n    }\n  }\n\n  if (!shadowPrefix) {\n    return selector\n  }\n\n  return (\n    selector +\n    shadowPrefix +\n    selector\n      .replace(/\\s*,\\s*/g, ',')\n      .split(',')\n      .join(shadowPrefix)\n  )\n}\n\nvar selector = void 0\n\nfunction findDocumentHostElement(_window) {\n  if (!selector) {\n    selector = selectInShadows('object, iframe')\n  }\n\n  if (_window._frameElement !== undefined) {\n    return _window._frameElement\n  }\n\n  _window._frameElement = null\n\n  var potentialHosts = _window.parent.document.querySelectorAll(selector)\n  ;[].some.call(potentialHosts, function (element) {\n    var _document = getContentDocument(element)\n    if (_document !== _window.document) {\n      return false\n    }\n\n    _window._frameElement = element\n    return true\n  })\n\n  return _window._frameElement\n}\n\nfunction getFrameElement(element) {\n  var _window = getWindow(element)\n  if (!_window.parent || _window.parent === _window) {\n    // if there is no parent browsing context,\n    // we're not going to get a frameElement either way\n    return null\n  }\n\n  try {\n    // see https://developer.mozilla.org/docs/Web/API/Window/frameElement\n    // does not work within <embed> anywhere, and not within in <object> in IE\n    return _window.frameElement || findDocumentHostElement(_window)\n  } catch (e) {\n    return null\n  }\n}\n\n// https://www.w3.org/TR/html5/rendering.html#being-rendered\n// <area> is not rendered, but we *consider* it visible to simplfiy this function's usage\nvar notRenderedElementsPattern = /^(area)$/\n\nfunction computedStyle(element, property) {\n  return window.getComputedStyle(element, null).getPropertyValue(property)\n}\n\nfunction notDisplayed(_path) {\n  return _path.some(function (element) {\n    // display:none is not visible (optimized away at layout)\n    return computedStyle(element, 'display') === 'none'\n  })\n}\n\nfunction notVisible(_path) {\n  // https://github.com/jquery/jquery-ui/blob/master/ui/core.js#L109-L114\n  // NOTE: a nested element can reverse visibility:hidden|collapse by explicitly setting visibility:visible\n  // NOTE: visibility can be [\"\", \"visible\", \"hidden\", \"collapse\"]\n  var hidden = findIndex(_path, function (element) {\n    var visibility = computedStyle(element, 'visibility')\n    return visibility === 'hidden' || visibility === 'collapse'\n  })\n\n  if (hidden === -1) {\n    // there is no hidden element\n    return false\n  }\n\n  var visible = findIndex(_path, function (element) {\n    return computedStyle(element, 'visibility') === 'visible'\n  })\n\n  if (visible === -1) {\n    // there is no visible element (but a hidden element)\n    return true\n  }\n\n  if (hidden < visible) {\n    // there is a hidden element and it's closer than the first visible element\n    return true\n  }\n\n  // there may be a hidden element, but the closest element is visible\n  return false\n}\n\nfunction collapsedParent(_path) {\n  var offset = 1\n  if (_path[0].nodeName.toLowerCase() === 'summary') {\n    offset = 2\n  }\n\n  return _path.slice(offset).some(function (element) {\n    // \"content children\" of a closed details element are not visible\n    return (\n      element.nodeName.toLowerCase() === 'details' && element.open === false\n    )\n  })\n}\n\nfunction isVisibleRules() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    _ref$except = _ref.except,\n    except =\n      _ref$except === undefined\n        ? {\n            notRendered: false,\n            cssDisplay: false,\n            cssVisibility: false,\n            detailsElement: false,\n            browsingContext: false,\n          }\n        : _ref$except\n\n  var element = contextToElement({\n    label: 'is/visible',\n    resolveDocument: true,\n    context: context,\n  })\n\n  var nodeName = element.nodeName.toLowerCase()\n  if (!except.notRendered && notRenderedElementsPattern.test(nodeName)) {\n    return true\n  }\n\n  var _path = getParents({ context: element })\n\n  // in Internet Explorer <audio> has a default display: none, where others have display: inline\n  // but IE allows focusing <audio style=\"display:none\">, but not <div display:none><audio>\n  // this is irrelevant to other browsers, as the controls attribute is required to make <audio> focusable\n  var isAudioWithoutControls =\n    nodeName === 'audio' && !element.hasAttribute('controls')\n  if (\n    !except.cssDisplay &&\n    notDisplayed(isAudioWithoutControls ? _path.slice(1) : _path)\n  ) {\n    return false\n  }\n\n  if (!except.cssVisibility && notVisible(_path)) {\n    return false\n  }\n\n  if (!except.detailsElement && collapsedParent(_path)) {\n    return false\n  }\n\n  if (!except.browsingContext) {\n    // elements within a browsing context are affected by the\n    // browsing context host element's visibility and tabindex\n    var frameElement = getFrameElement(element)\n    var _isVisible = isVisibleRules.except(except)\n    if (frameElement && !_isVisible(frameElement)) {\n      return false\n    }\n  }\n\n  return true\n}\n\n// bind exceptions to an iterator callback\nisVisibleRules.except = function () {\n  var except =\n    arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}\n\n  var isVisible = function isVisible(context) {\n    return isVisibleRules({\n      context: context,\n      except: except,\n    })\n  }\n\n  isVisible.rules = isVisibleRules\n  return isVisible\n}\n\n// provide isVisible(context) as default iterator callback\nvar isVisible = isVisibleRules.except({})\n\nfunction getMapByName(name, _document) {\n  // apparently getElementsByName() also considers id attribute in IE & opera\n  // https://developer.mozilla.org/docs/Web/API/Document/getElementsByName\n  var map = _document.querySelector('map[name=\"' + cssEscape(name) + '\"]')\n  return map || null\n}\n\nfunction getImageOfArea(element) {\n  var map = element.parentElement\n\n  if (!map.name || map.nodeName.toLowerCase() !== 'map') {\n    return null\n  }\n\n  // NOTE: image maps can also be applied to <object> with image content,\n  // but no browser supports this at the moment\n\n  // HTML5 specifies HTMLMapElement.images to be an HTMLCollection of all\n  // <img> and <object> referencing the <map> element, but no browser implements this\n  //   https://www.w3.org/TR/html5/embedded-content-0.html#the-map-element\n  //   https://developer.mozilla.org/docs/Web/API/HTMLMapElement\n  // the image must be valid and loaded for the map to take effect\n  var _document = getDocument(element)\n  return (\n    _document.querySelector('img[usemap=\"#' + cssEscape(map.name) + '\"]') ||\n    null\n  )\n}\n\nvar supports$2 = void 0\n\n// https://developer.mozilla.org/docs/Web/HTML/Element/map\n// https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\n// https://github.com/jquery/jquery-ui/blob/master/ui/core.js#L88-L107\nfunction isValidArea(context) {\n  if (!supports$2) {\n    supports$2 = _supports()\n  }\n\n  var element = contextToElement({\n    label: 'is/valid-area',\n    context: context,\n  })\n\n  var nodeName = element.nodeName.toLowerCase()\n  if (nodeName !== 'area') {\n    return false\n  }\n\n  var hasTabindex = element.hasAttribute('tabindex')\n  if (!supports$2.focusAreaTabindex && hasTabindex) {\n    // Blink and WebKit do not consider <area tabindex=\"-1\" href=\"#void\"> focusable\n    return false\n  }\n\n  var img = getImageOfArea(element)\n  if (!img || !isVisible(img)) {\n    return false\n  }\n\n  // Firefox only allows fully loaded images to reference image maps\n  // https://stereochro.me/ideas/detecting-broken-images-js\n  if (\n    !supports$2.focusBrokenImageMap &&\n    (!img.complete ||\n      !img.naturalHeight ||\n      img.offsetWidth <= 0 ||\n      img.offsetHeight <= 0)\n  ) {\n    return false\n  }\n\n  // Firefox supports.can focus area elements even if they don't have an href attribute\n  if (!supports$2.focusAreaWithoutHref && !element.href) {\n    // Internet explorer supports.can focus area elements without href if either\n    // the area element or the image element has a tabindex attribute\n    return (\n      (supports$2.focusAreaTabindex && hasTabindex) ||\n      (supports$2.focusAreaImgTabindex && img.hasAttribute('tabindex'))\n    )\n  }\n\n  // https://developer.mozilla.org/docs/Web/HTML/Element/img#attr-usemap\n  var childOfInteractive = getParents({ context: img })\n    .slice(1)\n    .some(function (_element) {\n      var name = _element.nodeName.toLowerCase()\n      return name === 'button' || name === 'a'\n    })\n\n  if (childOfInteractive) {\n    return false\n  }\n\n  return true\n}\n\nvar supports$3 = void 0\n\n// https://www.w3.org/TR/html5/disabled-elements.html#concept-element-disabled\nvar disabledElementsPattern = void 0\nvar disabledElements = {\n  input: true,\n  select: true,\n  textarea: true,\n  button: true,\n  fieldset: true,\n  form: true,\n}\n\nfunction isNativeDisabledSupported(context) {\n  if (!supports$3) {\n    supports$3 = _supports()\n\n    if (supports$3.focusFieldsetDisabled) {\n      delete disabledElements.fieldset\n    }\n\n    if (supports$3.focusFormDisabled) {\n      delete disabledElements.form\n    }\n\n    disabledElementsPattern = new RegExp(\n      '^(' + Object.keys(disabledElements).join('|') + ')$'\n    )\n  }\n\n  var element = contextToElement({\n    label: 'is/native-disabled-supported',\n    context: context,\n  })\n\n  var nodeName = element.nodeName.toLowerCase()\n  return Boolean(disabledElementsPattern.test(nodeName))\n}\n\nvar supports$4 = void 0\n\nfunction isDisabledFieldset(element) {\n  var nodeName = element.nodeName.toLowerCase()\n  return nodeName === 'fieldset' && element.disabled\n}\n\nfunction isDisabledForm(element) {\n  var nodeName = element.nodeName.toLowerCase()\n  return nodeName === 'form' && element.disabled\n}\n\nfunction isDisabled(context) {\n  if (!supports$4) {\n    supports$4 = _supports()\n  }\n\n  var element = contextToElement({\n    label: 'is/disabled',\n    context: context,\n  })\n\n  if (element.hasAttribute('data-ally-disabled')) {\n    // treat ally's element/disabled like the DOM native element.disabled\n    return true\n  }\n\n  if (!isNativeDisabledSupported(element)) {\n    // non-form elements do not support the disabled attribute\n    return false\n  }\n\n  if (element.disabled) {\n    // the element itself is disabled\n    return true\n  }\n\n  var parents = getParents({ context: element })\n  if (parents.some(isDisabledFieldset)) {\n    // a parental <fieldset> is disabld and inherits the state onto this element\n    return true\n  }\n\n  if (!supports$4.focusFormDisabled && parents.some(isDisabledForm)) {\n    // a parental <form> is disabld and inherits the state onto this element\n    return true\n  }\n\n  return false\n}\n\nfunction isOnlyTabbableRules() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    _ref$except = _ref.except,\n    except =\n      _ref$except === undefined\n        ? {\n            onlyFocusableBrowsingContext: false,\n            visible: false,\n          }\n        : _ref$except\n\n  var element = contextToElement({\n    label: 'is/only-tabbable',\n    resolveDocument: true,\n    context: context,\n  })\n\n  if (!except.visible && !isVisible(element)) {\n    return false\n  }\n\n  if (\n    !except.onlyFocusableBrowsingContext &&\n    (platform.is.GECKO || platform.is.TRIDENT || platform.is.EDGE)\n  ) {\n    var frameElement = getFrameElement(element)\n    if (frameElement) {\n      if (tabindexValue(frameElement) < 0) {\n        // iframe[tabindex=\"-1\"] and object[tabindex=\"-1\"] inherit the\n        // tabbable demotion onto elements of their browsing contexts\n        return false\n      }\n    }\n  }\n\n  var nodeName = element.nodeName.toLowerCase()\n  var tabindex = tabindexValue(element)\n\n  if (nodeName === 'label' && platform.is.GECKO) {\n    // Firefox cannot focus, but tab to: label[tabindex=0]\n    return tabindex !== null && tabindex >= 0\n  }\n\n  // SVG Elements were keyboard focusable but not script focusable before Firefox 51.\n  // Firefox 51 added the focus management DOM API (.focus and .blur) to SVGElement,\n  // see https://bugzilla.mozilla.org/show_bug.cgi?id=778654\n  if (platform.is.GECKO && element.ownerSVGElement && !element.focus) {\n    if (nodeName === 'a' && element.hasAttribute('xlink:href')) {\n      // any focusable child of <svg> cannot be focused, but tabbed to\n      if (platform.is.GECKO) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n\n// bind exceptions to an iterator callback\nisOnlyTabbableRules.except = function () {\n  var except =\n    arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}\n\n  var isOnlyTabbable = function isOnlyTabbable(context) {\n    return isOnlyTabbableRules({\n      context: context,\n      except: except,\n    })\n  }\n\n  isOnlyTabbable.rules = isOnlyTabbableRules\n  return isOnlyTabbable\n}\n\n// provide isOnlyTabbable(context) as default iterator callback\nvar isOnlyTabbable = isOnlyTabbableRules.except({})\n\nvar supports$5 = void 0\n\nfunction isOnlyFocusRelevant(element) {\n  var nodeName = element.nodeName.toLowerCase()\n  if (nodeName === 'embed' || nodeName === 'keygen') {\n    // embed is considered focus-relevant but not focusable\n    // see https://github.com/medialize/ally.js/issues/82\n    return true\n  }\n\n  var _tabindex = tabindexValue(element)\n  if (element.shadowRoot && _tabindex === null) {\n    // ShadowDOM host elements *may* receive focus\n    // even though they are not considered focuable\n    return true\n  }\n\n  if (nodeName === 'label') {\n    // <label tabindex=\"0\"> is only tabbable in Firefox, not script-focusable\n    // there's no way to make an element focusable other than by adding a tabindex,\n    // and focus behavior of the label element seems hard-wired to ignore tabindex\n    // in some browsers (like Gecko, Blink and WebKit)\n    return !supports$5.focusLabelTabindex || _tabindex === null\n  }\n\n  if (nodeName === 'legend') {\n    return _tabindex === null\n  }\n\n  if (\n    supports$5.focusSvgFocusableAttribute &&\n    (element.ownerSVGElement || nodeName === 'svg')\n  ) {\n    // Internet Explorer understands the focusable attribute introduced in SVG Tiny 1.2\n    var focusableAttribute = element.getAttribute('focusable')\n    return focusableAttribute && focusableAttribute === 'false'\n  }\n\n  if (nodeName === 'img' && element.hasAttribute('usemap')) {\n    // Gecko, Trident and Edge do not allow an image with an image map and tabindex to be focused,\n    // it appears the tabindex is overruled so focus is still forwarded to the <map>\n    return _tabindex === null || !supports$5.focusImgUsemapTabindex\n  }\n\n  if (nodeName === 'area') {\n    // all <area>s are considered relevant,\n    // but only the valid <area>s are focusable\n    return !isValidArea(element)\n  }\n\n  return false\n}\n\nfunction isFocusableRules() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    _ref$except = _ref.except,\n    except =\n      _ref$except === undefined\n        ? {\n            disabled: false,\n            visible: false,\n            onlyTabbable: false,\n          }\n        : _ref$except\n\n  if (!supports$5) {\n    supports$5 = _supports()\n  }\n\n  var _isOnlyTabbable = isOnlyTabbable.rules.except({\n    onlyFocusableBrowsingContext: true,\n    visible: except.visible,\n  })\n\n  var element = contextToElement({\n    label: 'is/focusable',\n    resolveDocument: true,\n    context: context,\n  })\n\n  var focusRelevant = isFocusRelevant.rules({\n    context: element,\n    except: except,\n  })\n\n  if (!focusRelevant || isOnlyFocusRelevant(element)) {\n    return false\n  }\n\n  if (!except.disabled && isDisabled(element)) {\n    return false\n  }\n\n  if (!except.onlyTabbable && _isOnlyTabbable(element)) {\n    // some elements may be keyboard focusable, but not script focusable\n    return false\n  }\n\n  // elements that are not rendered, cannot be focused\n  if (!except.visible) {\n    var visibilityOptions = {\n      context: element,\n      except: {},\n    }\n\n    if (supports$5.focusInHiddenIframe) {\n      // WebKit and Blink can focus content in hidden <iframe> and <object>\n      visibilityOptions.except.browsingContext = true\n    }\n\n    if (supports$5.focusObjectSvgHidden) {\n      // Blink allows focusing the object element, even if it has visibility: hidden;\n      // @browser-issue Blink https://code.google.com/p/chromium/issues/detail?id=586191\n      var _nodeName2 = element.nodeName.toLowerCase()\n      if (_nodeName2 === 'object') {\n        visibilityOptions.except.cssVisibility = true\n      }\n    }\n\n    if (!isVisible.rules(visibilityOptions)) {\n      return false\n    }\n  }\n\n  var frameElement = getFrameElement(element)\n  if (frameElement) {\n    var _nodeName = frameElement.nodeName.toLowerCase()\n    if (_nodeName === 'object' && !supports$5.focusInZeroDimensionObject) {\n      if (!frameElement.offsetWidth || !frameElement.offsetHeight) {\n        // WebKit can not focus content in <object> if it doesn't have dimensions\n        return false\n      }\n    }\n  }\n\n  var nodeName = element.nodeName.toLowerCase()\n  if (\n    nodeName === 'svg' &&\n    supports$5.focusSvgInIframe &&\n    !frameElement &&\n    element.getAttribute('tabindex') === null\n  ) {\n    return false\n  }\n\n  return true\n}\n\n// bind exceptions to an iterator callback\nisFocusableRules.except = function () {\n  var except =\n    arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}\n\n  var isFocusable = function isFocusable(context) {\n    return isFocusableRules({\n      context: context,\n      except: except,\n    })\n  }\n\n  isFocusable.rules = isFocusableRules\n  return isFocusable\n}\n\n// provide isFocusRelevant(context) as default iterator callback\nvar isFocusable = isFocusableRules.except({})\n\nfunction createFilter(condition) {\n  // see https://developer.mozilla.org/docs/Web/API/NodeFilter\n  var filter = function filter(node) {\n    if (node.shadowRoot) {\n      // return ShadowRoot elements regardless of them being focusable,\n      // so they can be walked recursively later\n      return NodeFilter.FILTER_ACCEPT\n    }\n\n    if (condition(node)) {\n      // finds elements that could have been found by document.querySelectorAll()\n      return NodeFilter.FILTER_ACCEPT\n    }\n\n    return NodeFilter.FILTER_SKIP\n  }\n  // IE requires a function, Browsers require {acceptNode: function}\n  // see http://www.bennadel.com/blog/2607-finding-html-comment-nodes-in-the-dom-using-treewalker.htm\n  filter.acceptNode = filter\n  return filter\n}\n\nvar PossiblyFocusableFilter = createFilter(isFocusRelevant)\n\nfunction queryFocusableStrict() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    includeContext = _ref.includeContext,\n    includeOnlyTabbable = _ref.includeOnlyTabbable,\n    strategy = _ref.strategy\n\n  if (!context) {\n    context = document.documentElement\n  }\n\n  var _isFocusable = isFocusable.rules.except({\n    onlyTabbable: includeOnlyTabbable,\n  })\n\n  var _document = getDocument(context)\n  // see https://developer.mozilla.org/docs/Web/API/Document/createTreeWalker\n  var walker = _document.createTreeWalker(\n    // root element to start search in\n    context,\n    // element type filter\n    NodeFilter.SHOW_ELEMENT,\n    // custom NodeFilter filter\n    strategy === 'all' ? PossiblyFocusableFilter : createFilter(_isFocusable),\n    // deprecated, but IE requires it\n    false\n  )\n\n  var list = []\n\n  while (walker.nextNode()) {\n    if (walker.currentNode.shadowRoot) {\n      if (_isFocusable(walker.currentNode)) {\n        list.push(walker.currentNode)\n      }\n\n      list = list.concat(\n        queryFocusableStrict({\n          context: walker.currentNode.shadowRoot,\n          includeOnlyTabbable: includeOnlyTabbable,\n          strategy: strategy,\n        })\n      )\n    } else {\n      list.push(walker.currentNode)\n    }\n  }\n\n  // add context if requested and focusable\n  if (includeContext) {\n    if (strategy === 'all') {\n      if (isFocusRelevant(context)) {\n        list.unshift(context)\n      }\n    } else if (_isFocusable(context)) {\n      list.unshift(context)\n    }\n  }\n\n  return list\n}\n\n// NOTE: this selector MUST *never* be used directly,\nvar supports$6 = void 0\n\nvar selector$1 = void 0\n\nfunction selector$2() {\n  if (!supports$6) {\n    supports$6 = _supports()\n  }\n\n  if (typeof selector$1 === 'string') {\n    return selector$1\n  }\n\n  // https://www.w3.org/TR/html5/editing.html#sequential-focus-navigation-and-the-tabindex-attribute\n  selector$1 =\n    '' +\n    // IE11 supports.can focus <table> and <td>\n    (supports$6.focusTable ? 'table, td,' : '') +\n    // IE11 supports.can focus <fieldset>\n    (supports$6.focusFieldset ? 'fieldset,' : '') +\n    // Namespace problems of [xlink:href] explained in https://stackoverflow.com/a/23047888/515124\n    // svg a[*|href] does not match in IE9, but since we're filtering\n    // through is/focusable we can include all <a> from SVG\n    'svg a,' +\n    // may behave as 'svg, svg *,' in chrome as *every* svg element with a focus event listener is focusable\n    // navigational elements\n    'a[href],' +\n    // validity determined by is/valid-area.js\n    'area[href],' +\n    // validity determined by is/disabled.js\n    'input, select, textarea, button,' +\n    // browsing context containers\n    'iframe, object, embed,' +\n    // interactive content\n    'keygen,' +\n    (supports$6.focusAudioWithoutControls ? 'audio,' : 'audio[controls],') +\n    (supports$6.focusVideoWithoutControls ? 'video,' : 'video[controls],') +\n    (supports$6.focusSummary ? 'summary,' : '') +\n    // validity determined by is/valid-tabindex.js\n    '[tabindex],' +\n    // editing hosts\n    '[contenteditable]'\n\n  // where ShadowDOM is supported, we also want the shadowed focusable elements (via \">>>\" or \"/deep/\")\n  selector$1 = selectInShadows(selector$1)\n\n  return selector$1\n}\n\nfunction queryFocusableQuick() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    includeContext = _ref.includeContext,\n    includeOnlyTabbable = _ref.includeOnlyTabbable\n\n  var _selector = selector$2()\n  var elements = context.querySelectorAll(_selector)\n  // the selector potentially matches more than really is focusable\n\n  var _isFocusable = isFocusable.rules.except({\n    onlyTabbable: includeOnlyTabbable,\n  })\n\n  var result = [].filter.call(elements, _isFocusable)\n\n  // add context if requested and focusable\n  if (includeContext && _isFocusable(context)) {\n    result.unshift(context)\n  }\n\n  return result\n}\n\nfunction queryFocusable() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    includeContext = _ref.includeContext,\n    includeOnlyTabbable = _ref.includeOnlyTabbable,\n    _ref$strategy = _ref.strategy,\n    strategy = _ref$strategy === undefined ? 'quick' : _ref$strategy\n\n  var element = contextToElement({\n    label: 'query/focusable',\n    resolveDocument: true,\n    defaultToDocument: true,\n    context: context,\n  })\n\n  var options = {\n    context: element,\n    includeContext: includeContext,\n    includeOnlyTabbable: includeOnlyTabbable,\n    strategy: strategy,\n  }\n\n  if (strategy === 'quick') {\n    return queryFocusableQuick(options)\n  } else if (strategy === 'strict' || strategy === 'all') {\n    return queryFocusableStrict(options)\n  }\n\n  throw new TypeError(\n    'query/focusable requires option.strategy to be one of [\"quick\", \"strict\", \"all\"]'\n  )\n}\n\nvar supports$7 = void 0\n\n// Internet Explorer 11 considers fieldset, table, td focusable, but not tabbable\n// Internet Explorer 11 considers body to have [tabindex=0], but does not allow tabbing to it\nvar focusableElementsPattern = /^(fieldset|table|td|body)$/\n\nfunction isTabbableRules() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    _ref$except = _ref.except,\n    except =\n      _ref$except === undefined\n        ? {\n            flexbox: false,\n            scrollable: false,\n            shadow: false,\n            visible: false,\n            onlyTabbable: false,\n          }\n        : _ref$except\n\n  if (!supports$7) {\n    supports$7 = _supports()\n  }\n\n  var element = contextToElement({\n    label: 'is/tabbable',\n    resolveDocument: true,\n    context: context,\n  })\n\n  if (platform.is.BLINK && platform.is.ANDROID && platform.majorVersion > 42) {\n    // External keyboard support worked fine in CHrome 42, but stopped working in Chrome 45.\n    // The on-screen keyboard does not provide a way to focus the next input element (like iOS does).\n    // That leaves us with no option to advance focus by keyboard, ergo nothing is tabbable (keyboard focusable).\n    return false\n  }\n\n  var frameElement = getFrameElement(element)\n  if (frameElement) {\n    if (platform.is.WEBKIT && platform.is.IOS) {\n      // iOS only does not consider anything from another browsing context keyboard focusable\n      return false\n    }\n\n    // iframe[tabindex=\"-1\"] and object[tabindex=\"-1\"] inherit the\n    // tabbable demotion onto elements of their browsing contexts\n    if (tabindexValue(frameElement) < 0) {\n      return false\n    }\n\n    if (\n      !except.visible &&\n      (platform.is.BLINK || platform.is.WEBKIT) &&\n      !isVisible(frameElement)\n    ) {\n      // Blink and WebKit consider elements in hidden browsing contexts focusable, but not tabbable\n      return false\n    }\n\n    // Webkit and Blink don't consider anything in <object> tabbable\n    // Blink fixed that fixed in Chrome 54, Opera 41\n    var frameNodeName = frameElement.nodeName.toLowerCase()\n    if (frameNodeName === 'object') {\n      var isFixedBlink =\n        (platform.name === 'Chrome' && platform.majorVersion >= 54) ||\n        (platform.name === 'Opera' && platform.majorVersion >= 41)\n\n      if (platform.is.WEBKIT || (platform.is.BLINK && !isFixedBlink)) {\n        return false\n      }\n    }\n  }\n\n  var nodeName = element.nodeName.toLowerCase()\n  var _tabindex = tabindexValue(element)\n  var tabindex = _tabindex === null ? null : _tabindex >= 0\n\n  if (\n    platform.is.EDGE &&\n    platform.majorVersion >= 14 &&\n    frameElement &&\n    element.ownerSVGElement &&\n    _tabindex < 0\n  ) {\n    // Edge 14+ considers <a xlink:href=\"…\" tabindex=\"-1\"> keyboard focusable\n    // if the element is in a nested browsing context\n    return true\n  }\n\n  var hasTabbableTabindexOrNone = tabindex !== false\n  var hasTabbableTabindex = _tabindex !== null && _tabindex >= 0\n\n  // NOTE: Firefox 31 considers [contenteditable] to have [tabindex=-1], but allows tabbing to it\n  // fixed in Firefox 40 the latest - https://bugzilla.mozilla.org/show_bug.cgi?id=1185657\n  if (element.hasAttribute('contenteditable')) {\n    // tabbing can still be disabled by explicitly providing [tabindex=\"-1\"]\n    return hasTabbableTabindexOrNone\n  }\n\n  if (focusableElementsPattern.test(nodeName) && tabindex !== true) {\n    return false\n  }\n\n  if (platform.is.WEBKIT && platform.is.IOS) {\n    // iOS only considers a hand full of elements tabbable (keyboard focusable)\n    // this holds true even with external keyboards\n    var potentiallyTabbable =\n      (nodeName === 'input' && element.type === 'text') ||\n      element.type === 'password' ||\n      nodeName === 'select' ||\n      nodeName === 'textarea' ||\n      element.hasAttribute('contenteditable')\n\n    if (!potentiallyTabbable) {\n      var style = window.getComputedStyle(element, null)\n      potentiallyTabbable = isUserModifyWritable(style)\n    }\n\n    if (!potentiallyTabbable) {\n      return false\n    }\n  }\n\n  if (nodeName === 'use' && _tabindex !== null) {\n    if (\n      platform.is.BLINK ||\n      (platform.is.WEBKIT && platform.majorVersion === 9)\n    ) {\n      // In Chrome and Safari 9 the <use> element is keyboard focusable even for tabindex=\"-1\"\n      return true\n    }\n  }\n\n  if (elementMatches(element, 'svg a') && element.hasAttribute('xlink:href')) {\n    if (hasTabbableTabindexOrNone) {\n      // in Trident and Gecko SVGElement does not handle the tabIndex property properly\n      return true\n    }\n\n    if (element.focus && !supports$7.focusSvgNegativeTabindexAttribute) {\n      // Firefox 51 and 52 treat any natively tabbable SVG element with\n      // tabindex=\"-1\" as tabbable and everything else as inert\n      // see https://bugzilla.mozilla.org/show_bug.cgi?id=1302340\n      return true\n    }\n  }\n\n  if (\n    nodeName === 'svg' &&\n    supports$7.focusSvgInIframe &&\n    hasTabbableTabindexOrNone\n  ) {\n    return true\n  }\n\n  if (platform.is.TRIDENT || platform.is.EDGE) {\n    if (nodeName === 'svg') {\n      if (supports$7.focusSvg) {\n        // older Internet Explorers consider <svg> keyboard focusable\n        // unless they have focsable=\"false\", but then they wouldn't\n        // be focusable and thus not even reach this filter\n        return true\n      }\n\n      // elements that have [focusable] are automatically keyboard focusable regardless of the attribute's value\n      return element.hasAttribute('focusable') || hasTabbableTabindex\n    }\n\n    if (element.ownerSVGElement) {\n      if (supports$7.focusSvgTabindexAttribute && hasTabbableTabindex) {\n        return true\n      }\n\n      // elements that have [focusable] are automatically keyboard focusable regardless of the attribute's value\n      return element.hasAttribute('focusable')\n    }\n  }\n  if (element.tabIndex === undefined) {\n    return Boolean(except.onlyTabbable)\n  }\n\n  if (nodeName === 'audio') {\n    if (!element.hasAttribute('controls')) {\n      // In Internet Explorer the <audio> element is focusable, but not tabbable, and tabIndex property is wrong\n      return false\n    } else if (platform.is.BLINK) {\n      // In Chrome <audio controls tabindex=\"-1\"> remains keyboard focusable\n      return true\n    }\n  }\n\n  if (nodeName === 'video') {\n    if (!element.hasAttribute('controls')) {\n      if (platform.is.TRIDENT || platform.is.EDGE) {\n        // In Internet Explorer and Edge the <video> element is focusable, but not tabbable, and tabIndex property is wrong\n        return false\n      }\n    } else if (platform.is.BLINK || platform.is.GECKO) {\n      // In Chrome and Firefox <video controls tabindex=\"-1\"> remains keyboard focusable\n      return true\n    }\n  }\n\n  if (nodeName === 'object') {\n    if (platform.is.BLINK || platform.is.WEBKIT) {\n      // In all Blink and WebKit based browsers <embed> and <object> are never keyboard focusable, even with tabindex=\"0\" set\n      return false\n    }\n  }\n\n  if (nodeName === 'iframe') {\n    // In Internet Explorer all iframes are only focusable\n    // In WebKit, Blink and Gecko iframes may be tabbable depending on content.\n    // Since we can't reliably investigate iframe documents because of the\n    // SameOriginPolicy, we're declaring everything only focusable.\n    return false\n  }\n\n  if (!except.scrollable && platform.is.GECKO) {\n    // Firefox considers scrollable containers keyboard focusable,\n    // even though their tabIndex property is -1\n    var _style = window.getComputedStyle(element, null)\n    if (hasCssOverflowScroll(_style)) {\n      return hasTabbableTabindexOrNone\n    }\n  }\n\n  if (platform.is.TRIDENT || platform.is.EDGE) {\n    // IE and Edge degrade <area> to script focusable, if the image\n    // using the <map> has been given tabindex=\"-1\"\n    if (nodeName === 'area') {\n      var img = getImageOfArea(element)\n      if (img && tabindexValue(img) < 0) {\n        return false\n      }\n    }\n\n    var _style2 = window.getComputedStyle(element, null)\n    if (isUserModifyWritable(_style2)) {\n      // prevent being swallowed by the overzealous isScrollableContainer() below\n      return element.tabIndex >= 0\n    }\n\n    if (!except.flexbox && hasCssDisplayFlex(_style2)) {\n      if (_tabindex !== null) {\n        return hasTabbableTabindex\n      }\n\n      return (\n        isFocusRelevantWithoutFlexbox(element) &&\n        isTabbableWithoutFlexbox(element)\n      )\n    }\n\n    // IE considers scrollable containers script focusable only,\n    // even though their tabIndex property is 0\n    if (isScrollableContainer(element, nodeName)) {\n      return false\n    }\n\n    var parent = element.parentElement\n    if (parent) {\n      var parentNodeName = parent.nodeName.toLowerCase()\n      var parentStyle = window.getComputedStyle(parent, null)\n      // IE considers scrollable bodies script focusable only,\n      if (\n        isScrollableContainer(parent, nodeName, parentNodeName, parentStyle)\n      ) {\n        return false\n      }\n\n      // Children of focusable elements with display:flex are focusable in IE10-11,\n      // even though their tabIndex property suggests otherwise\n      if (hasCssDisplayFlex(parentStyle)) {\n        // value of tabindex takes precedence\n        return hasTabbableTabindex\n      }\n    }\n  }\n\n  // https://www.w3.org/WAI/PF/aria-practices/#focus_tabindex\n  return element.tabIndex >= 0\n}\n\n// bind exceptions to an iterator callback\nisTabbableRules.except = function () {\n  var except =\n    arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}\n\n  var isTabbable = function isTabbable(context) {\n    return isTabbableRules({\n      context: context,\n      except: except,\n    })\n  }\n\n  isTabbable.rules = isTabbableRules\n  return isTabbable\n}\n\nvar isFocusRelevantWithoutFlexbox = isFocusRelevant.rules.except({\n  flexbox: true,\n})\nvar isTabbableWithoutFlexbox = isTabbableRules.except({ flexbox: true })\n\n// provide isTabbable(context) as default iterator callback\nvar isTabbable = isTabbableRules.except({})\n\nfunction queryTabbable() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    includeContext = _ref.includeContext,\n    includeOnlyTabbable = _ref.includeOnlyTabbable,\n    strategy = _ref.strategy\n\n  var _isTabbable = isTabbable.rules.except({\n    onlyTabbable: includeOnlyTabbable,\n  })\n\n  return queryFocusable({\n    context: context,\n    includeContext: includeContext,\n    includeOnlyTabbable: includeOnlyTabbable,\n    strategy: strategy,\n  }).filter(_isTabbable)\n}\n\n// sorts a list of elements according to their order in the DOM\n\nfunction compareDomPosition(a, b) {\n  return a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING\n    ? -1\n    : 1\n}\n\nfunction sortDomOrder(elements) {\n  return elements.sort(compareDomPosition)\n}\n\nfunction getFirstSuccessorOffset(list, target) {\n  // find the first element that comes AFTER the target element\n  return findIndex(list, function (element) {\n    return (\n      target.compareDocumentPosition(element) & Node.DOCUMENT_POSITION_FOLLOWING\n    )\n  })\n}\n\nfunction findInsertionOffsets(list, elements, resolveElement) {\n  // instead of mutating the elements list directly, remember position and map\n  // to inject later, when we can do this more efficiently\n  var insertions = []\n  elements.forEach(function (element) {\n    var replace = true\n    var offset = list.indexOf(element)\n\n    if (offset === -1) {\n      // element is not in target list\n      offset = getFirstSuccessorOffset(list, element)\n      replace = false\n    }\n\n    if (offset === -1) {\n      // there is no successor in the tabsequence,\n      // meaning the image must be the last element\n      offset = list.length\n    }\n\n    // allow the consumer to replace the injected element\n    var injections = nodeArray(\n      resolveElement ? resolveElement(element) : element\n    )\n    if (!injections.length) {\n      // we can't inject zero elements\n      return\n    }\n\n    insertions.push({\n      offset: offset,\n      replace: replace,\n      elements: injections,\n    })\n  })\n\n  return insertions\n}\n\nfunction insertElementsAtOffsets(list, insertions) {\n  // remember the number of elements we have already injected\n  // so we account for the caused index offset\n  var inserted = 0\n  // make sure that we insert the elements in sequence,\n  // otherwise the offset compensation won't work\n  insertions.sort(function (a, b) {\n    return a.offset - b.offset\n  })\n  insertions.forEach(function (insertion) {\n    // array.splice has an annoying function signature :(\n    var remove = insertion.replace ? 1 : 0\n    var args = [insertion.offset + inserted, remove].concat(insertion.elements)\n    list.splice.apply(list, args)\n    inserted += insertion.elements.length - remove\n  })\n}\n\nfunction mergeInDomOrder() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    list = _ref.list,\n    elements = _ref.elements,\n    resolveElement = _ref.resolveElement\n\n  // operate on a copy so we don't mutate the original array\n  var _list = list.slice(0)\n  // make sure the elements we're injecting are provided in DOM order\n  var _elements = nodeArray(elements).slice(0)\n  sortDomOrder(_elements)\n  // find the offsets within the target array (list) at which to inject\n  // each individual element (from elements)\n  var insertions = findInsertionOffsets(_list, _elements, resolveElement)\n  // actually inject the elements into the target array at the identified positions\n  insertElementsAtOffsets(_list, insertions)\n  return _list\n}\n\nvar _createClass = (function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i]\n      descriptor.enumerable = descriptor.enumerable || false\n      descriptor.configurable = true\n      if ('value' in descriptor) descriptor.writable = true\n      Object.defineProperty(target, descriptor.key, descriptor)\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps)\n    if (staticProps) defineProperties(Constructor, staticProps)\n    return Constructor\n  }\n})()\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError('Cannot call a class as a function')\n  }\n}\n\nvar Maps = (function () {\n  function Maps(context) {\n    _classCallCheck(this, Maps)\n\n    this._document = getDocument(context)\n    this.maps = {}\n  }\n\n  _createClass(Maps, [\n    {\n      key: 'getAreasFor',\n      value: function getAreasFor(name) {\n        if (!this.maps[name]) {\n          // the map is not defined within the context, so we\n          // have to go find it elsewhere in the document\n          this.addMapByName(name)\n        }\n\n        return this.maps[name]\n      },\n    },\n    {\n      key: 'addMapByName',\n      value: function addMapByName(name) {\n        var map = getMapByName(name, this._document)\n        if (!map) {\n          // if there is no map, the img[usemap] wasn't doing anything anyway\n          return\n        }\n\n        this.maps[map.name] = queryTabbable({ context: map })\n      },\n    },\n    {\n      key: 'extractAreasFromList',\n      value: function extractAreasFromList(elements) {\n        // remove all <area> elements from the elements list,\n        // but put them the map for later retrieval\n        return elements.filter(function (element) {\n          var nodeName = element.nodeName.toLowerCase()\n          if (nodeName !== 'area') {\n            return true\n          }\n\n          var map = element.parentNode\n          if (!this.maps[map.name]) {\n            this.maps[map.name] = []\n          }\n\n          this.maps[map.name].push(element)\n          return false\n        }, this)\n      },\n    },\n  ])\n\n  return Maps\n})()\n\nfunction sortArea(elements, context) {\n  // images - unless they are focusable themselves, likely not\n  // part of the elements list, so we'll have to find them and\n  // sort them into the elements list manually\n  var usemaps = context.querySelectorAll('img[usemap]')\n  var maps = new Maps(context)\n\n  // remove all <area> elements from the elements list,\n  // but put them the map for later retrieval\n  var _elements = maps.extractAreasFromList(elements)\n\n  if (!usemaps.length) {\n    // the context does not contain any <area>s so no need\n    // to replace anything, just remove any maps\n    return _elements\n  }\n\n  return mergeInDomOrder({\n    list: _elements,\n    elements: usemaps,\n    resolveElement: function resolveElement(image) {\n      var name = image.getAttribute('usemap').slice(1)\n      return maps.getAreasFor(name)\n    },\n  })\n}\n\nvar _createClass$1 = (function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i]\n      descriptor.enumerable = descriptor.enumerable || false\n      descriptor.configurable = true\n      if ('value' in descriptor) descriptor.writable = true\n      Object.defineProperty(target, descriptor.key, descriptor)\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps)\n    if (staticProps) defineProperties(Constructor, staticProps)\n    return Constructor\n  }\n})()\n\nfunction _classCallCheck$1(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError('Cannot call a class as a function')\n  }\n}\n\nvar Shadows = (function () {\n  function Shadows(context, sortElements) {\n    _classCallCheck$1(this, Shadows)\n\n    // document context we're working with\n    this.context = context\n    // callback that sorts an array of elements\n    this.sortElements = sortElements\n    // reference to create unique IDs for each ShadowHost\n    this.hostCounter = 1\n    // reference map for child-ShadowHosts of a ShadowHost\n    this.inHost = {}\n    // reference map for child-ShadowHost of the document\n    this.inDocument = []\n    // reference map for ShadowHosts\n    this.hosts = {}\n    // reference map for tabbable elements of a ShadowHost\n    this.elements = {}\n  }\n\n  // remember which hosts we have to sort within later\n\n  _createClass$1(Shadows, [\n    {\n      key: '_registerHost',\n      value: function _registerHost(host) {\n        if (host._sortingId) {\n          return\n        }\n\n        // make the ShadowHost identifiable (see cleanup() for undo)\n        host._sortingId = 'shadow-' + this.hostCounter++\n        this.hosts[host._sortingId] = host\n\n        // hosts may contain other hosts\n        var parentHost = getShadowHost({ context: host })\n        if (parentHost) {\n          this._registerHost(parentHost)\n          this._registerHostParent(host, parentHost)\n        } else {\n          this.inDocument.push(host)\n        }\n      },\n\n      // remember which host is the child of which other host\n    },\n    {\n      key: '_registerHostParent',\n      value: function _registerHostParent(host, parent) {\n        if (!this.inHost[parent._sortingId]) {\n          this.inHost[parent._sortingId] = []\n        }\n\n        this.inHost[parent._sortingId].push(host)\n      },\n\n      // remember which elements a host contains\n    },\n    {\n      key: '_registerElement',\n      value: function _registerElement(element, host) {\n        if (!this.elements[host._sortingId]) {\n          this.elements[host._sortingId] = []\n        }\n\n        this.elements[host._sortingId].push(element)\n      },\n\n      // remove shadowed elements from the sequence and register\n      // the ShadowHosts they belong to so we know what to sort\n      // later on\n    },\n    {\n      key: 'extractElements',\n      value: function extractElements(elements) {\n        return elements.filter(function (element) {\n          var host = getShadowHost({ context: element })\n          if (!host) {\n            return true\n          }\n\n          this._registerHost(host)\n          this._registerElement(element, host)\n          return false\n        }, this)\n      },\n\n      // inject hosts into the sequence, sort everything,\n      // and recoursively replace hosts by its descendants\n    },\n    {\n      key: 'sort',\n      value: function sort(elements) {\n        var _elements = this._injectHosts(elements)\n        _elements = this._replaceHosts(_elements)\n        this._cleanup()\n        return _elements\n      },\n\n      // merge ShadowHosts into the element lists of other ShadowHosts\n      // or the document, then sort the individual lists\n    },\n    {\n      key: '_injectHosts',\n      value: function _injectHosts(elements) {\n        Object.keys(this.hosts).forEach(function (_sortingId) {\n          var _list = this.elements[_sortingId]\n          var _elements = this.inHost[_sortingId]\n          var _context = this.hosts[_sortingId].shadowRoot\n          this.elements[_sortingId] = this._merge(_list, _elements, _context)\n        }, this)\n\n        return this._merge(elements, this.inDocument, this.context)\n      },\n    },\n    {\n      key: '_merge',\n      value: function _merge(list, elements, context) {\n        var merged = mergeInDomOrder({\n          list: list,\n          elements: elements,\n        })\n\n        return this.sortElements(merged, context)\n      },\n    },\n    {\n      key: '_replaceHosts',\n      value: function _replaceHosts(elements) {\n        return mergeInDomOrder({\n          list: elements,\n          elements: this.inDocument,\n          resolveElement: this._resolveHostElement.bind(this),\n        })\n      },\n    },\n    {\n      key: '_resolveHostElement',\n      value: function _resolveHostElement(host) {\n        var merged = mergeInDomOrder({\n          list: this.elements[host._sortingId],\n          elements: this.inHost[host._sortingId],\n          resolveElement: this._resolveHostElement.bind(this),\n        })\n\n        var _tabindex = tabindexValue(host)\n        if (_tabindex !== null && _tabindex > -1) {\n          return [host].concat(merged)\n        }\n\n        return merged\n      },\n    },\n    {\n      key: '_cleanup',\n      value: function _cleanup() {\n        // remove those identifers we put on the ShadowHost to avoid using Map()\n        Object.keys(this.hosts).forEach(function (key) {\n          delete this.hosts[key]._sortingId\n        }, this)\n      },\n    },\n  ])\n\n  return Shadows\n})()\n\nfunction sortShadowed(elements, context, sortElements) {\n  var shadows = new Shadows(context, sortElements)\n  var _elements = shadows.extractElements(elements)\n\n  if (_elements.length === elements.length) {\n    // no shadowed content found, no need to continue\n    return sortElements(elements)\n  }\n\n  return shadows.sort(_elements)\n}\n\nfunction sortTabindex(elements) {\n  // https://developer.mozilla.org/docs/Web/API/HTMLElement.tabIndex\n  // elements with tabIndex \"0\" (including tabbableElements without tabIndex) should be navigated in the order they appear.\n  // elements with a positive tabIndex:\n  //   Elements that have identical tabIndexes should be navigated in the order they appear.\n  //   Navigation proceeds from the lowest tabIndex to the highest tabIndex.\n\n  // NOTE: sort implementation may be unstable and thus mess up DOM order,\n  // that's why we build a map that's being sorted instead. If we were able to rely\n  // on a stable sorting algorithm, sortTabindex() could be as simple as\n  // elements.sort(function(a, b) { return a.tabIndex - b.tabIndex; });\n  // at this time Chrome does not use a stable sorting algorithm\n  // see http://blog.rodneyrehm.de/archives/14-Sorting-Were-Doing-It-Wrong.html#stability\n\n  // NOTE: compareDocumentPosition seemed like more overhead than just sorting this with buckets\n  // https://developer.mozilla.org/docs/Web/API/Node.compareDocumentPosition\n\n  var map = {}\n  var indexes = []\n  var normal = elements.filter(function (element) {\n    // in Trident and Gecko SVGElement does not know about the tabIndex property\n    var tabIndex = element.tabIndex\n    if (tabIndex === undefined) {\n      tabIndex = tabindexValue(element)\n    }\n\n    // extract elements that don't need sorting\n    if (tabIndex <= 0 || tabIndex === null || tabIndex === undefined) {\n      return true\n    }\n\n    if (!map[tabIndex]) {\n      // create sortable bucket for dom-order-preservation of elements with the same tabIndex\n      map[tabIndex] = []\n      // maintain a list of unique tabIndexes\n      indexes.push(tabIndex)\n    }\n\n    // sort element into the proper bucket\n    map[tabIndex].push(element)\n    // element moved to sorting map, so not \"normal\" anymore\n    return false\n  })\n\n  // sort the tabindex ascending,\n  // then resolve them to their appropriate buckets,\n  // then flatten the array of arrays to an array\n  var _elements = indexes\n    .sort()\n    .map(function (tabIndex) {\n      return map[tabIndex]\n    })\n    .reduceRight(function (previous, current) {\n      return current.concat(previous)\n    }, normal)\n\n  return _elements\n}\n\nvar supports$8 = void 0\n\nfunction moveContextToBeginning(elements, context) {\n  var pos = elements.indexOf(context)\n  if (pos > 0) {\n    var tmp = elements.splice(pos, 1)\n    return tmp.concat(elements)\n  }\n\n  return elements\n}\n\nfunction sortElements(elements, _context) {\n  if (supports$8.tabsequenceAreaAtImgPosition) {\n    // Some browsers sort <area> in DOM order, some place the <area>s\n    // where the <img> referecing them would've been in DOM order.\n    // https://github.com/medialize/ally.js/issues/5\n    elements = sortArea(elements, _context)\n  }\n\n  elements = sortTabindex(elements)\n  return elements\n}\n\nfunction queryTabsequence() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    context = _ref.context,\n    includeContext = _ref.includeContext,\n    includeOnlyTabbable = _ref.includeOnlyTabbable,\n    strategy = _ref.strategy\n\n  if (!supports$8) {\n    supports$8 = _supports()\n  }\n\n  var _context = nodeArray(context)[0] || document.documentElement\n  var elements = queryTabbable({\n    context: _context,\n    includeContext: includeContext,\n    includeOnlyTabbable: includeOnlyTabbable,\n    strategy: strategy,\n  })\n\n  if (document.body.createShadowRoot && platform.is.BLINK) {\n    // sort tabindex localized to shadow dom\n    // see https://github.com/medialize/ally.js/issues/6\n    elements = sortShadowed(elements, _context, sortElements)\n  } else {\n    elements = sortElements(elements, _context)\n  }\n\n  if (includeContext) {\n    // if we include the context itself, it has to be the first\n    // element of the sequence\n    elements = moveContextToBeginning(elements, _context)\n  }\n\n  return elements\n}\n\n// codes mostly cloned from https://github.com/keithamus/jwerty/blob/master/jwerty.js\n// deliberately not exposing characters like <,.-#* because they vary *wildly*\n// across keyboard layouts and may cause various problems\n// (e.g. \"*\" is \"Shift +\" on a German Mac keyboard)\n// (e.g. \"@\" is \"Alt L\" on a German Mac keyboard)\n\nvar keycode = {\n  // Element Focus\n  tab: 9,\n\n  // Navigation\n  left: 37,\n  up: 38,\n  right: 39,\n  down: 40,\n  pageUp: 33,\n  'page-up': 33,\n  pageDown: 34,\n  'page-down': 34,\n  end: 35,\n  home: 36,\n\n  // Action\n  enter: 13,\n  escape: 27,\n  space: 32,\n\n  // Modifier\n  shift: 16,\n  capsLock: 20,\n  'caps-lock': 20,\n  ctrl: 17,\n  alt: 18,\n  meta: 91,\n  // in firefox: 224\n  // on mac (chrome): meta-left=91, meta-right=93\n  // on win (IE11): meta-left=91, meta-right=92\n  pause: 19,\n\n  // Content Manipulation\n  insert: 45,\n  delete: 46,\n  backspace: 8,\n\n  // the same logical key may be identified through different keyCodes\n  _alias: {\n    91: [92, 93, 224],\n  },\n}\n\n// Function keys (112 - 137)\n// NOTE: not every keyboard knows F13+\nfor (var n = 1; n < 26; n++) {\n  keycode['f' + n] = n + 111\n}\n\n// Number keys (48-57, numpad 96-105)\n// NOTE: not every keyboard knows num-0+\nfor (var _n = 0; _n < 10; _n++) {\n  var code = _n + 48\n  var numCode = _n + 96\n  keycode[_n] = code\n  keycode['num-' + _n] = numCode\n  keycode._alias[code] = [numCode]\n}\n\n// Latin characters (65 - 90)\nfor (var _n2 = 0; _n2 < 26; _n2++) {\n  var _code = _n2 + 65\n  var name$1 = String.fromCharCode(_code).toLowerCase()\n  keycode[name$1] = _code\n}\n\nvar modifier = {\n  alt: 'altKey',\n  ctrl: 'ctrlKey',\n  meta: 'metaKey',\n  shift: 'shiftKey',\n}\n\nvar modifierSequence = Object.keys(modifier).map(function (name) {\n  return modifier[name]\n})\n\nfunction createExpectedModifiers(ignoreModifiers) {\n  var value = ignoreModifiers ? null : false\n  return {\n    altKey: value,\n    ctrlKey: value,\n    metaKey: value,\n    shiftKey: value,\n  }\n}\n\nfunction resolveModifiers(modifiers) {\n  var ignoreModifiers = modifiers.indexOf('*') !== -1\n  var expected = createExpectedModifiers(ignoreModifiers)\n\n  modifiers.forEach(function (token) {\n    if (token === '*') {\n      // we've already covered the all-in operator\n      return\n    }\n\n    // we want the modifier pressed\n    var value = true\n    var operator = token.slice(0, 1)\n    if (operator === '?') {\n      // we don't care if the modifier is pressed\n      value = null\n    } else if (operator === '!') {\n      // we do not want the modifier pressed\n      value = false\n    }\n\n    if (value !== true) {\n      // compensate for the modifier's operator\n      token = token.slice(1)\n    }\n\n    var propertyName = modifier[token]\n    if (!propertyName) {\n      throw new TypeError('Unknown modifier \"' + token + '\"')\n    }\n\n    expected[propertyName] = value\n  })\n\n  return expected\n}\n\nfunction resolveKey(key) {\n  var code = keycode[key] || parseInt(key, 10)\n  if (!code || typeof code !== 'number' || isNaN(code)) {\n    throw new TypeError('Unknown key \"' + key + '\"')\n  }\n\n  return [code].concat(keycode._alias[code] || [])\n}\n\nfunction matchModifiers(expected, event) {\n  // returns true on match\n  return !modifierSequence.some(function (prop) {\n    // returns true on mismatch\n    return (\n      typeof expected[prop] === 'boolean' &&\n      Boolean(event[prop]) !== expected[prop]\n    )\n  })\n}\n\nfunction keyBinding(text) {\n  return text.split(/\\s+/).map(function (_text) {\n    var tokens = _text.split('+')\n    var _modifiers = resolveModifiers(tokens.slice(0, -1))\n    var _keyCodes = resolveKey(tokens.slice(-1))\n    return {\n      keyCodes: _keyCodes,\n      modifiers: _modifiers,\n      matchModifiers: matchModifiers.bind(null, _modifiers),\n    }\n  })\n}\n\n// Node.compareDocumentPosition is available since IE9\n// see https://developer.mozilla.org/docs/Web/API/Node.compareDocumentPosition\n\n// callback returns true when element is contained by parent or is the parent suited for use with Array.some()\n/*\n  USAGE:\n    var isChildOf = getParentComparator({parent: someNode});\n    listOfElements.some(isChildOf)\n*/\n\nfunction getParentComparator() {\n  var _ref =\n      arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    parent = _ref.parent,\n    element = _ref.element,\n    includeSelf = _ref.includeSelf\n\n  if (parent) {\n    return function isChildOf(node) {\n      return Boolean(\n        (includeSelf && node === parent) ||\n          parent.compareDocumentPosition(node) &\n            Node.DOCUMENT_POSITION_CONTAINED_BY\n      )\n    }\n  } else if (element) {\n    return function isParentOf(node) {\n      return Boolean(\n        (includeSelf && element === node) ||\n          node.compareDocumentPosition(element) &\n            Node.DOCUMENT_POSITION_CONTAINED_BY\n      )\n    }\n  }\n\n  throw new TypeError(\n    'util/compare-position#getParentComparator required either options.parent or options.element'\n  )\n}\n\n// Bug 286933 - Key events in the autocomplete popup should be hidden from page scripts\n// @browser-issue Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=286933\n\nfunction whenKey() {\n  var map =\n    arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}\n\n  var bindings = {}\n\n  var context = nodeArray(map.context)[0] || document.documentElement\n  delete map.context\n  var filter = nodeArray(map.filter)\n  delete map.filter\n\n  var mapKeys = Object.keys(map)\n  if (!mapKeys.length) {\n    throw new TypeError('when/key requires at least one option key')\n  }\n\n  var registerBinding = function registerBinding(event) {\n    event.keyCodes.forEach(function (code) {\n      if (!bindings[code]) {\n        bindings[code] = []\n      }\n\n      bindings[code].push(event)\n    })\n  }\n\n  mapKeys.forEach(function (text) {\n    if (typeof map[text] !== 'function') {\n      throw new TypeError(\n        'when/key requires option[\"' + text + '\"] to be a function'\n      )\n    }\n\n    var addCallback = function addCallback(event) {\n      event.callback = map[text]\n      return event\n    }\n\n    keyBinding(text).map(addCallback).forEach(registerBinding)\n  })\n\n  var handleKeyDown = function handleKeyDown(event) {\n    if (event.defaultPrevented) {\n      return\n    }\n\n    if (filter.length) {\n      // ignore elements within the exempted sub-trees\n      var isParentOfElement = getParentComparator({\n        element: event.target,\n        includeSelf: true,\n      })\n      if (filter.some(isParentOfElement)) {\n        return\n      }\n    }\n\n    var key = event.keyCode || event.which\n    if (!bindings[key]) {\n      return\n    }\n\n    bindings[key].forEach(function (_event) {\n      if (!_event.matchModifiers(event)) {\n        return\n      }\n\n      _event.callback.call(context, event, disengage)\n    })\n  }\n\n  context.addEventListener('keydown', handleKeyDown, false)\n\n  var disengage = function disengage() {\n    context.removeEventListener('keydown', handleKeyDown, false)\n  }\n\n  return { disengage: disengage }\n}\n\nexport default function ({ context } = {}) {\n  if (!context) {\n    context = document.documentElement\n  }\n\n  // Make sure the supports tests are run before intercepting the Tab key,\n  // or IE10 and IE11 will fail to process the first Tab key event. Not\n  // limiting this warm-up to IE because it may be a problem elsewhere, too.\n  queryTabsequence()\n\n  return whenKey({\n    // Safari on OSX may require ALT+TAB to reach links,\n    // see https://github.com/medialize/ally.js/issues/146\n    '?alt+?shift+tab': function altShiftTab(event) {\n      // we're completely taking over the Tab key handling\n      event.preventDefault()\n\n      var sequence = queryTabsequence({\n        context: context,\n      })\n\n      var backward = event.shiftKey\n      var first = sequence[0]\n      var last = sequence[sequence.length - 1]\n\n      // wrap around first to last, last to first\n      var source = backward ? first : last\n      var target = backward ? last : first\n      if (isActiveElement(source)) {\n        target.focus()\n        return\n      }\n\n      // find current position in tabsequence\n      var currentIndex = void 0\n      var found = sequence.some(function (element, index) {\n        if (!isActiveElement(element)) {\n          return false\n        }\n\n        currentIndex = index\n        return true\n      })\n\n      if (!found) {\n        // redirect to first as we're not in our tabsequence\n        first.focus()\n        return\n      }\n\n      // shift focus to previous/next element in the sequence\n      var offset = backward ? -1 : 1\n      sequence[currentIndex + offset].focus()\n    },\n  })\n}\n"], "names": ["_platform", "cssEscape", "nodeArray", "input", "Array", "isArray", "nodeType", "undefined", "document", "querySelectorAll", "length", "slice", "call", "TypeError", "String", "contextToElement", "_ref", "context", "_ref$label", "label", "resolveDocument", "defaultToDocument", "element", "Node", "DOCUMENT_NODE", "documentElement", "ELEMENT_NODE", "DOCUMENT_FRAGMENT_NODE", "getShadowHost", "arguments", "container", "parentNode", "host", "getDocument", "node", "ownerDocument", "isActiveElement", "_document", "activeElement", "shadowHost", "shadowRoot", "getParents", "list", "push", "names", "name", "findMethodName", "some", "_name", "elementMatches", "selector", "platform", "JSON", "parse", "stringify", "os", "family", "ANDROID", "WINDOWS", "OSX", "IOS", "BLINK", "layout", "GECKO", "TRIDENT", "EDGE", "WEBKIT", "version", "parseFloat", "majorVersion", "Math", "floor", "is", "IE9", "IE10", "IE11", "before", "data", "windowScrollTop", "window", "scrollTop", "windowScrollLeft", "scrollLeft", "bodyScrollTop", "body", "bodyScrollLeft", "iframe", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "_window", "contentWindow", "open", "close", "wrapper", "test", "options", "innerHTML", "focus", "mutate", "validate", "after", "blur", "<PERSON><PERSON><PERSON><PERSON>", "detectFocus", "tests", "results", "Object", "keys", "map", "key", "version$1", "readLocalStorage", "localStorage", "getItem", "e", "writeLocalStorage", "value", "hasFocus", "removeItem", "setItem", "userAgent", "navigator", "cache<PERSON>ey", "cache", "cache$1", "get", "set", "values", "for<PERSON>ach", "time", "Date", "toISOString", "cssShadowPiercingDeepCombinator", "combinator", "querySelector", "noArrowArrowArrow", "noDeep", "gif", "focusAreaImgTabindex", "focusAreaTabindex", "focusTarget", "focusAreaWithoutHref", "focusAudioWithoutControls", "invalidGif", "focusBrokenImageMap", "focusChildrenOfFocusableFlexbox", "focusFieldsetDisabled", "focusFieldset", "focusFlexboxContainer", "focusFormDisabled", "focusImgIsmap", "href", "focusImgUsemapTabindex", "focusInHiddenIframe", "iframeDocument", "style", "visibility", "result", "focusInZeroDimensionObject", "focusInvalidTabindex", "focusLabelTabindex", "variableToPreventDeadCodeElimination", "offsetHeight", "svg", "focusObjectSvgHidden", "focusObjectSvg", "result$1", "focusObjectSwf", "focusRedirectImgUsemap", "target", "focusRedirectLegend", "focusable", "tabbable", "focusScrollBody", "focusScrollContainerWithoutOverflow", "focusScrollContainer", "focusSummary", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "makeFocusableForeignObject", "foreignObject", "createElementNS", "width", "baseVal", "height", "<PERSON><PERSON><PERSON><PERSON>", "type", "focusSvgForeignObjectHack", "isSvgElement", "ownerSVGElement", "nodeName", "toLowerCase", "disabled", "generate", "HTMLElement", "prototype", "focusSvgFocusableAttribute", "focusSvgTabindexAttribute", "focusSvgNegativeTabindexAttribute", "focusSvgUseTabindex", "join", "focusSvgForeignobjectTabindex", "getElementsByTagName", "result$2", "Boolean", "SVGElement", "focusSvgInIframe", "focusSvg", "<PERSON><PERSON><PERSON><PERSON>", "focusTabindexTrailingCharacters", "focusTable", "fragment", "createDocumentFragment", "focusVideoWithoutControls", "result$3", "tabsequenceAreaAtImgPosition", "testCallbacks", "testDescriptions", "executeTests", "supportsCache", "_supports", "supports", "validIntegerPatternNoTrailing", "validIntegerPatternWithTrailing", "isValidTabindex", "validIntegerPattern", "hasTabindex", "hasAttribute", "hasTabIndex", "tabindex", "getAttribute", "tabindexValue", "attributeName", "parseInt", "isNaN", "isUserModifyWritable", "userModify", "webkitUserModify", "indexOf", "hasCssOverflowScroll", "getPropertyValue", "overflow", "hasCssDisplayFlex", "display", "isScrollableContainer", "parentNodeName", "parentStyle", "scrollHeight", "offsetWidth", "scrollWidth", "supports$1", "isFocusRelevantRules", "_ref$except", "except", "flexbox", "scrollable", "shadow", "svgType", "validTabindex", "isSvgContent", "focusableAttribute", "getComputedStyle", "hasLinkParent", "parent", "parentElement", "isFocusRelevant", "rules", "findIndex", "array", "callback", "i", "getContentDocument", "contentDocument", "getSVGDocument", "getWindow", "defaultView", "shadowPrefix", "selectInShadows", "operator", "replace", "split", "findDocumentHostElement", "_frameElement", "potentialHosts", "getFrameElement", "frameElement", "notRenderedElementsPattern", "computedStyle", "property", "notDisplayed", "_path", "notVisible", "hidden", "visible", "collapsedParent", "offset", "isVisibleRules", "notRendered", "cssDisplay", "cssVisibility", "detailsElement", "browsingContext", "isAudioWithoutControls", "_isVisible", "isVisible", "getMapByName", "getImageOfArea", "supports$2", "isValidArea", "img", "complete", "naturalHeight", "childOfInteractive", "_element", "supports$3", "disabledElementsPattern", "disabledElements", "select", "textarea", "button", "fieldset", "form", "isNativeDisabledSupported", "RegExp", "supports$4", "isDisabledFieldset", "isDisabledForm", "isDisabled", "parents", "isOnlyTabbableRules", "onlyFocusableBrowsingContext", "isOnlyTabbable", "supports$5", "isOnlyFocusRelevant", "_tabindex", "isFocusableRules", "onlyTabbable", "_isOnlyTabbable", "focusRelevant", "visibilityOptions", "_nodeName2", "_nodeName", "isFocusable", "createFilter", "condition", "filter", "Node<PERSON><PERSON><PERSON>", "FILTER_ACCEPT", "FILTER_SKIP", "acceptNode", "PossiblyFocusableFilter", "queryFocusableStrict", "includeContext", "includeOnlyTabbable", "strategy", "_isFocusable", "walker", "createTreeWalker", "SHOW_ELEMENT", "nextNode", "currentNode", "concat", "unshift", "supports$6", "selector$1", "selector$2", "queryFocusableQuick", "_selector", "elements", "queryFocusable", "_ref$strategy", "supports$7", "focusableElementsPattern", "isTabbableRules", "frameNodeName", "isFixedBlink", "hasTabbableTabindexOrNone", "hasTabbableTabindex", "potentiallyTabbable", "tabIndex", "_style", "_style2", "isFocusRelevantWithoutFlexbox", "isTabbableWithoutFlexbox", "isTabbable", "queryTabbable", "_isTabbable", "compareDomPosition", "a", "b", "compareDocumentPosition", "DOCUMENT_POSITION_FOLLOWING", "sortDomOrder", "sort", "getFirstSuccessorOffset", "findInsertionOffsets", "resolveElement", "insertions", "injections", "insertElementsAtOffsets", "inserted", "insertion", "remove", "args", "splice", "apply", "mergeInDomOrder", "_list", "_elements", "_createClass", "defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_classCallCheck", "instance", "Maps", "maps", "getAreasFor", "addMapByName", "extractAreasFromList", "sortArea", "usemaps", "image", "_createClass$1", "_classCallCheck$1", "Shadows", "sortElements", "<PERSON><PERSON><PERSON><PERSON>", "inHost", "inDocument", "hosts", "_registerHost", "_sortingId", "parentHost", "_registerHostParent", "_registerElement", "extractElements", "_injectHosts", "_replaceHosts", "_cleanup", "_context", "_merge", "merged", "_resolveHostElement", "bind", "sortShadowed", "shadows", "sortTabindex", "indexes", "normal", "reduceRight", "previous", "current", "supports$8", "moveContextToBeginning", "pos", "tmp", "queryTabsequence", "createShadowRoot", "keycode", "tab", "left", "up", "right", "down", "pageUp", "pageDown", "end", "home", "enter", "escape", "space", "shift", "capsLock", "ctrl", "alt", "meta", "pause", "insert", "delete", "backspace", "_alias", "n", "_n", "code", "numCode", "_n2", "_code", "name$1", "fromCharCode", "modifier", "modifierSequence", "createExpectedModifiers", "ignoreModifiers", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "resolveModifiers", "modifiers", "expected", "token", "propertyName", "<PERSON><PERSON><PERSON>", "matchModifiers", "event", "prop", "keyBinding", "text", "_text", "tokens", "_modifiers", "_keyCodes", "keyCodes", "getParentComparator", "includeSelf", "isChildOf", "DOCUMENT_POSITION_CONTAINED_BY", "isParentOf", "when<PERSON><PERSON>", "bindings", "mapKeys", "registerBinding", "addCallback", "handleKeyDown", "defaultPrevented", "isParentOfElement", "keyCode", "which", "_event", "disengage", "addEventListener", "removeEventListener", "altShiftTab", "preventDefault", "sequence", "backward", "first", "last", "source", "currentIndex", "found", "index"], "mappings": "AAAA,kBAAkB,GAClB,cAAc;AACd,mDAAmD;AACnD,eAAe;AACf,iCAAiC;AACjC,EAAE;AACF,yCAAyC;;;;AAEzC,OAAOA,eAAe,8BAA6B;AACnD,OAAOC,eAAe,gCAA+B;;;AAErD,yFAAyF;AACzF,6EAA6E;AAC7E,SAASC,UAAUC,KAAK;IACtB,IAAI,CAACA,OAAO;QACV,OAAO,EAAE;IACX;IAEA,IAAIC,MAAMC,OAAO,CAACF,QAAQ;QACxB,OAAOA;IACT;IAEA,+CAA+C;IAC/C,IAAIA,MAAMG,QAAQ,KAAKC,WAAW;QAChC,OAAO;YAACJ;SAAM;IAChB;IAEA,IAAI,OAAOA,UAAU,UAAU;QAC7BA,QAAQK,SAASC,gBAAgB,CAACN;IACpC;IAEA,IAAIA,MAAMO,MAAM,KAAKH,WAAW;QAC9B,OAAO,EAAE,CAACI,KAAK,CAACC,IAAI,CAACT,OAAO;IAC9B;IAEA,MAAM,IAAIU,UAAU,sBAAsBC,OAAOX;AACnD;AAEA,SAASY,iBAAiBC,IAAI;IAC5B,IAAIC,UAAUD,KAAKC,OAAO,EACxBC,aAAaF,KAAKG,KAAK,EACvBA,QAAQD,eAAeX,YAAY,uBAAuBW,YAC1DE,kBAAkBJ,KAAKI,eAAe,EACtCC,oBAAoBL,KAAKK,iBAAiB;IAE5C,IAAIC,UAAUpB,UAAUe,QAAQ,CAAC,EAAE;IAEnC,IAAIG,mBAAmBE,WAAWA,QAAQhB,QAAQ,KAAKiB,KAAKC,aAAa,EAAE;QACzEF,UAAUA,QAAQG,eAAe;IACnC;IAEA,IAAI,CAACH,WAAWD,mBAAmB;QACjC,OAAOb,SAASiB,eAAe;IACjC;IAEA,IAAI,CAACH,SAAS;QACZ,MAAM,IAAIT,UAAUM,QAAQ;IAC9B;IAEA,IACEG,QAAQhB,QAAQ,KAAKiB,KAAKG,YAAY,IACtCJ,QAAQhB,QAAQ,KAAKiB,KAAKI,sBAAsB,EAChD;QACA,MAAM,IAAId,UAAUM,QAAQ;IAC9B;IAEA,OAAOG;AACT;AAEA,SAASM;IACP,IAAIZ,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO;IAExB,IAAIK,UAAUP,iBAAiB;QAC7BI,OAAO;QACPF,SAASA;IACX;IAEA,sBAAsB;IACtB,IAAIa,YAAY;IAEhB,MAAOR,QAAS;QACdQ,YAAYR;QACZA,UAAUA,QAAQS,UAAU;IAC9B;IAEA,2DAA2D;IAC3D,iEAAiE;IACjE,IACED,UAAUxB,QAAQ,KAAKwB,UAAUH,sBAAsB,IACvDG,UAAUE,IAAI,EACd;QACA,0DAA0D;QAC1D,OAAOF,UAAUE,IAAI;IACvB;IAEA,OAAO;AACT;AAEA,SAASC,YAAYC,IAAI;IACvB,IAAI,CAACA,MAAM;QACT,OAAO1B;IACT;IAEA,IAAI0B,KAAK5B,QAAQ,KAAKiB,KAAKC,aAAa,EAAE;QACxC,OAAOU;IACT;IAEA,OAAOA,KAAKC,aAAa,IAAI3B;AAC/B;AAEA,SAAS4B,gBAAgBnB,OAAO;IAC9B,IAAIK,UAAUP,iBAAiB;QAC7BI,OAAO;QACPC,iBAAiB;QACjBH,SAASA;IACX;IAEA,IAAIoB,YAAYJ,YAAYX;IAC5B,IAAIe,UAAUC,aAAa,KAAKhB,SAAS;QACvC,OAAO;IACT;IAEA,IAAIiB,aAAaX,cAAc;QAAEX,SAASK;IAAQ;IAClD,IAAIiB,cAAcA,WAAWC,UAAU,CAACF,aAAa,KAAKhB,SAAS;QACjE,OAAO;IACT;IAEA,OAAO;AACT;AAEA,mDAAmD;AACnD,0EAA0E;AAC1E,SAASmB;IACP,IAAIzB,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO;IAExB,IAAIyB,OAAO,EAAE;IACb,IAAIpB,UAAUP,iBAAiB;QAC7BI,OAAO;QACPF,SAASA;IACX;IAEA,MAAOK,QAAS;QACdoB,KAAKC,IAAI,CAACrB;QACV,mDAAmD;QACnDA,UAAUA,QAAQS,UAAU;QAC5B,IAAIT,WAAWA,QAAQhB,QAAQ,KAAKiB,KAAKG,YAAY,EAAE;YACrDJ,UAAU;QACZ;IACF;IAEA,OAAOoB;AACT;AAEA,iEAAiE;AACjE,gEAAgE;AAEhE,IAAIE,QAAQ;IACV;IACA;IACA;IACA;CACD;AACD,IAAIC,OAAO;AAEX,SAASC,eAAexB,OAAO;IAC7BsB,MAAMG,IAAI,CAAC,SAAUC,KAAK;QACxB,IAAI,CAAC1B,OAAO,CAAC0B,MAAM,EAAE;YACnB,OAAO;QACT;QAEAH,OAAOG;QACP,OAAO;IACT;AACF;AAEA,SAASC,eAAe3B,OAAO,EAAE4B,QAAQ;IACvC,IAAI,CAACL,MAAM;QACTC,eAAexB;IACjB;IAEA,OAAOA,OAAO,CAACuB,KAAK,CAACK;AACvB;AAEA,kCAAkC;AAClC,IAAIC,WAAWC,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACtD,mLAAAA;AAEzC,mBAAmB;AACnB,IAAIuD,KAAKJ,SAASI,EAAE,CAACC,MAAM,IAAI;AAC/B,IAAIC,UAAUF,OAAO;AACrB,IAAIG,UAAUH,GAAG5C,KAAK,CAAC,GAAG,OAAO;AACjC,IAAIgD,MAAMJ,OAAO;AACjB,IAAIK,MAAML,OAAO;AAEjB,SAAS;AACT,IAAIM,QAAQV,SAASW,MAAM,KAAK;AAChC,IAAIC,QAAQZ,SAASW,MAAM,KAAK;AAChC,IAAIE,UAAUb,SAASW,MAAM,KAAK;AAClC,IAAIG,OAAOd,SAASW,MAAM,KAAK;AAC/B,IAAII,SAASf,SAASW,MAAM,KAAK;AAEjC,+CAA+C;AAC/C,IAAIK,UAAUC,WAAWjB,SAASgB,OAAO;AACzC,IAAIE,eAAeC,KAAKC,KAAK,CAACJ;AAC9BhB,SAASkB,YAAY,GAAGA;AAExBlB,SAASqB,EAAE,GAAG;IACZ,mBAAmB;IACnBf,SAASA;IACTC,SAASA;IACTC,KAAKA;IACLC,KAAKA;IACL,SAAS;IACTC,OAAOA;IACPE,OAAOA;IACPC,SAASA;IACTC,MAAMA;IACNC,QAAQA;IACR,qBAAqB;IACrBO,KAAKT,WAAWK,iBAAiB;IACjCK,MAAMV,WAAWK,iBAAiB;IAClCM,MAAMX,WAAWK,iBAAiB;AACpC;AAEA,SAASO;IACP,IAAIC,OAAO;QACT,gDAAgD;QAChDvC,eAAe9B,SAAS8B,aAAa;QACrC,kDAAkD;QAClDwC,iBAAiBC,OAAOC,SAAS;QACjCC,kBAAkBF,OAAOG,UAAU;QACnCC,eAAe3E,SAAS4E,IAAI,CAACJ,SAAS;QACtCK,gBAAgB7E,SAAS4E,IAAI,CAACF,UAAU;IAC1C;IAEA,sEAAsE;IACtE,mEAAmE;IACnE,IAAII,SAAS9E,SAAS+E,aAAa,CAAC;IACpCD,OAAOE,YAAY,CACjB,SACA;IAEFF,OAAOE,YAAY,CAAC,aAAa;IACjCF,OAAOE,YAAY,CAAC,aAAa;IACjCF,OAAOE,YAAY,CAAC,eAAe;IACnChF,SAAS4E,IAAI,CAACK,WAAW,CAACH;IAE1B,IAAII,UAAUJ,OAAOK,aAAa;IAClC,IAAItD,YAAYqD,QAAQlF,QAAQ;IAEhC6B,UAAUuD,IAAI;IACdvD,UAAUwD,KAAK;IACf,IAAIC,UAAUzD,UAAUkD,aAAa,CAAC;IACtClD,UAAU+C,IAAI,CAACK,WAAW,CAACK;IAE3BjB,KAAKS,MAAM,GAAGA;IACdT,KAAKiB,OAAO,GAAGA;IACfjB,KAAKE,MAAM,GAAGW;IACdb,KAAKrE,QAAQ,GAAG6B;IAEhB,OAAOwC;AACT;AAEA,mBAAmB;AACnB,yBAAyB;AACzB,iEAAiE;AACjE,6BAA6B;AAC7B,8FAA8F;AAC9F,8EAA8E;AAC9E,+BAA+B;AAC/B,iFAAiF;AACjF,SAASkB,KAAKlB,IAAI,EAAEmB,OAAO;IACzB,wCAAwC;IACxCnB,KAAKiB,OAAO,CAACG,SAAS,GAAG;IACzB,+CAA+C;IAC/C,IAAI3E,UACF,OAAO0E,QAAQ1E,OAAO,KAAK,WACvBuD,KAAKrE,QAAQ,CAAC+E,aAAa,CAACS,QAAQ1E,OAAO,IAC3C0E,QAAQ1E,OAAO,CAACuD,KAAKiB,OAAO,EAAEjB,KAAKrE,QAAQ;IACjD,kDAAkD;IAClD,yCAAyC;IACzC,IAAI0F,QACFF,QAAQG,MAAM,IAAIH,QAAQG,MAAM,CAAC7E,SAASuD,KAAKiB,OAAO,EAAEjB,KAAKrE,QAAQ;IACvE,IAAI,CAAC0F,SAASA,UAAU,OAAO;QAC7BA,QAAQ5E;IACV;IACA,sDAAsD;IACtD,CAACA,QAAQS,UAAU,IAAI8C,KAAKiB,OAAO,CAACL,WAAW,CAACnE;IAChD,2DAA2D;IAC3D4E,SAASA,MAAMA,KAAK,IAAIA,MAAMA,KAAK;IACnC,yBAAyB;IACzB,OAAOF,QAAQI,QAAQ,GACnBJ,QAAQI,QAAQ,CAAC9E,SAAS4E,OAAOrB,KAAKrE,QAAQ,IAC9CqE,KAAKrE,QAAQ,CAAC8B,aAAa,KAAK4D;AACtC;AAEA,SAASG,MAAMxB,IAAI;IACjB,uDAAuD;IACvD,IAAIA,KAAKvC,aAAa,KAAK9B,SAAS4E,IAAI,EAAE;QACxC5E,SAAS8B,aAAa,IACpB9B,SAAS8B,aAAa,CAACgE,IAAI,IAC3B9F,SAAS8B,aAAa,CAACgE,IAAI;QAC7B,IAAInD,SAASqB,EAAE,CAACE,IAAI,EAAE;YACpB,2EAA2E;YAC3ElE,SAAS4E,IAAI,CAACc,KAAK;QACrB;IACF,OAAO;QACLrB,KAAKvC,aAAa,IAAIuC,KAAKvC,aAAa,CAAC4D,KAAK,IAAIrB,KAAKvC,aAAa,CAAC4D,KAAK;IAC5E;IAEA1F,SAAS4E,IAAI,CAACmB,WAAW,CAAC1B,KAAKS,MAAM;IAErC,0BAA0B;IAC1BP,OAAOC,SAAS,GAAGH,KAAKC,eAAe;IACvCC,OAAOG,UAAU,GAAGL,KAAKI,gBAAgB;IACzCzE,SAAS4E,IAAI,CAACJ,SAAS,GAAGH,KAAKM,aAAa;IAC5C3E,SAAS4E,IAAI,CAACF,UAAU,GAAGL,KAAKQ,cAAc;AAChD;AAEA,SAASmB,YAAYC,KAAK;IACxB,IAAI5B,OAAOD;IAEX,IAAI8B,UAAU,CAAC;IACfC,OAAOC,IAAI,CAACH,OAAOI,GAAG,CAAC,SAAUC,GAAG;QAClCJ,OAAO,CAACI,IAAI,GAAGf,KAAKlB,MAAM4B,KAAK,CAACK,IAAI;IACtC;IAEAT,MAAMxB;IACN,OAAO6B;AACT;AAEA,kDAAkD;AAClD,IAAIK,YAAY;AAEhB;;;;;;CAMC,GAED,SAASC,iBAAiBF,GAAG;IAC3B,kEAAkE;IAClE,8CAA8C;IAC9C,IAAIjC,OAAO,KAAK;IAEhB,IAAI;QACFA,OAAOE,OAAOkC,YAAY,IAAIlC,OAAOkC,YAAY,CAACC,OAAO,CAACJ;QAC1DjC,OAAOA,OAAOzB,KAAKC,KAAK,CAACwB,QAAQ,CAAC;IACpC,EAAE,OAAOsC,GAAG;QACVtC,OAAO,CAAC;IACV;IAEA,OAAOA;AACT;AAEA,SAASuC,kBAAkBN,GAAG,EAAEO,KAAK;IACnC,IAAI,CAAC7G,SAAS8G,QAAQ,IAAI;QACxB,2EAA2E;QAC3E,wEAAwE;QACxE,gFAAgF;QAChF,IAAI;YACFvC,OAAOkC,YAAY,IAAIlC,OAAOkC,YAAY,CAACM,UAAU,CAACT;QACxD,EAAE,OAAOK,GAAG;QACV,SAAS;QACX;QAEA;IACF;IAEA,IAAI;QACFpC,OAAOkC,YAAY,IACjBlC,OAAOkC,YAAY,CAACO,OAAO,CAACV,KAAK1D,KAAKE,SAAS,CAAC+D;IACpD,EAAE,OAAOF,GAAG;IACV,SAAS;IACX;AACF;AAEA,IAAIM,YACD,OAAO1C,WAAW,eAAeA,OAAO2C,SAAS,CAACD,SAAS,IAAK;AACnE,IAAIE,WAAW;AACf,IAAIC,QAAQZ,iBAAiBW;AAE7B,0EAA0E;AAC1E,IAAIC,MAAMH,SAAS,KAAKA,aAAaG,MAAMzD,OAAO,KAAK4C,WAAW;IAChEa,QAAQ,CAAC;AACX;AAEAA,MAAMH,SAAS,GAAGA;AAClBG,MAAMzD,OAAO,GAAG4C;AAEhB,IAAIc,UAAU;IACZC,KAAK,SAASA;QACZ,OAAOF;IACT;IACAG,KAAK,SAASA,IAAIC,MAAM;QACtBrB,OAAOC,IAAI,CAACoB,QAAQC,OAAO,CAAC,SAAUnB,GAAG;YACvCc,KAAK,CAACd,IAAI,GAAGkB,MAAM,CAAClB,IAAI;QAC1B;QAEAc,MAAMM,IAAI,GAAG,IAAIC,OAAOC,WAAW;QACnChB,kBAAkBO,UAAUC;IAC9B;AACF;AAEA,SAASS;IACP,IAAIC,aAAa,KAAK;IAEtB,8DAA8D;IAC9D,uDAAuD;IACvD,6DAA6D;IAC7D,IAAI;QACF9H,SAAS+H,aAAa,CAAC;QACvBD,aAAa;IACf,EAAE,OAAOE,mBAAmB;QAC1B,IAAI;YACF,gDAAgD;YAChD,6DAA6D;YAC7DhI,SAAS+H,aAAa,CAAC;YACvBD,aAAa;QACf,EAAE,OAAOG,QAAQ;YACfH,aAAa;QACf;IACF;IAEA,OAAOA;AACT;AAEA,IAAII,MACF;AAEF,sEAAsE;AACtE,IAAIC,uBAAuB;IACzBrH,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GACf,yCACA,oDACA,sEACAyC,MACA;QAEF,OAAOpH,QAAQiH,aAAa,CAAC;IAC/B;AACF;AAEA,sEAAsE;AACtE,IAAIK,oBAAoB;IACtBtH,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GACf,yCACA,+EACA,wDACAyC,MACA;QAEF,OAAO;IACT;IACAtC,UAAU,SAASA,SAAS9E,OAAO,EAAEuH,WAAW,EAAExG,SAAS;QACzD,IAAIc,SAASqB,EAAE,CAACT,KAAK,EAAE;YACrB,uDAAuD;YACvD,qEAAqE;YACrE,OAAO;QACT;QAEA,IAAImC,QAAQ5E,QAAQiH,aAAa,CAAC;QAClCrC,MAAMA,KAAK;QACX,OAAO7D,UAAUC,aAAa,KAAK4D;IACrC;AACF;AAEA,sEAAsE;AACtE,IAAI4C,uBAAuB;IACzBxH,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GACf,0CACA,oDACA,yDACAyC,MACA;QAEF,OAAOpH,QAAQiH,aAAa,CAAC;IAC/B;IACAnC,UAAU,SAASA,SAAS9E,OAAO,EAAEuH,WAAW,EAAExG,SAAS;QACzD,IAAIc,SAASqB,EAAE,CAACT,KAAK,EAAE;YACrB,uDAAuD;YACvD,qEAAqE;YACrE,OAAO;QACT;QAEA,OAAO1B,UAAUC,aAAa,KAAKuG;IACrC;AACF;AAEA,IAAIE,4BAA4B;IAC9BlG,MAAM;IACNvB,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7B,IAAI;YACF,sFAAsF;YACtFA,QAAQkE,YAAY,CAAC,OAAOkD;QAC9B,EAAE,OAAOvB,GAAG;QACV,yCAAyC;QAC3C;IACF;AACF;AAEA,IAAI6B,aACF;AAEF,uDAAuD;AACvD,sEAAsE;AACtE,IAAIC,sBAAsB;IACxB3H,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GACf,mGACA,sDACA+C,aACA;QAEF,OAAO1H,QAAQiH,aAAa,CAAC;IAC/B;AACF;AAEA,4EAA4E;AAC5E,IAAIW,kCAAkC;IACpC5H,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,YAAY;QACjClE,QAAQkE,YAAY,CAClB,SACA;QAEFlE,QAAQ2E,SAAS,GAAG;QACpB,OAAO3E,QAAQiH,aAAa,CAAC;IAC/B;AACF;AAEA,wFAAwF;AACxF,6FAA6F;AAC7F,mDAAmD;AACnD,uEAAuE;AACvE,IAAIY,wBAAwB;IAC1B7H,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,YAAY;QACjClE,QAAQkE,YAAY,CAAC,YAAY;IACnC;AACF;AAEA,IAAI4D,gBAAgB;IAClB9H,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GAAG;IACtB;AACF;AAEA,sDAAsD;AACtD,IAAIoD,wBAAwB;IAC1B/H,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAClB,SACA;QAEFlE,QAAQ2E,SAAS,GAAG;IACtB;AACF;AAEA,wDAAwD;AACxD,wEAAwE;AACxE,yEAAyE;AACzE,IAAIqD,oBAAoB;IACtBhI,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,YAAY;QACjClE,QAAQkE,YAAY,CAAC,YAAY;IACnC;AACF;AAEA,uDAAuD;AACvD,uDAAuD;AACvD,qEAAqE;AACrE,IAAI+D,gBAAgB;IAClBjI,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkI,IAAI,GAAG;QACflI,QAAQ2E,SAAS,GAAG,qBAAqByC,MAAM;QAC/C,OAAOpH,QAAQiH,aAAa,CAAC;IAC/B;AACF;AAEA,uDAAuD;AACvD,sEAAsE;AACtE,IAAIkB,yBAAyB;IAC3BnI,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GACf,qGACA,iEACA,UACAyC,MACA;QAEF,OAAOpH,QAAQiH,aAAa,CAAC;IAC/B;AACF;AAEA,IAAImB,sBAAsB;IACxBpI,SAAS,SAASA,QAAQwE,OAAO,EAAEzD,SAAS;QAC1C,IAAIiD,SAASjD,UAAUkD,aAAa,CAAC;QAErC,gFAAgF;QAChFO,QAAQL,WAAW,CAACH;QAEpB,iFAAiF;QACjF,IAAIqE,iBAAiBrE,OAAOK,aAAa,CAACnF,QAAQ;QAClDmJ,eAAe/D,IAAI;QACnB+D,eAAe9D,KAAK;QACpB,OAAOP;IACT;IACAa,QAAQ,SAASA,OAAOb,MAAM;QAC5BA,OAAOsE,KAAK,CAACC,UAAU,GAAG;QAE1B,IAAIF,iBAAiBrE,OAAOK,aAAa,CAACnF,QAAQ;QAClD,IAAIL,QAAQwJ,eAAepE,aAAa,CAAC;QACzCoE,eAAevE,IAAI,CAACK,WAAW,CAACtF;QAChC,OAAOA;IACT;IACAiG,UAAU,SAASA,SAASd,MAAM;QAChC,IAAIqE,iBAAiBrE,OAAOK,aAAa,CAACnF,QAAQ;QAClD,IAAI0F,QAAQyD,eAAepB,aAAa,CAAC;QACzC,OAAOoB,eAAerH,aAAa,KAAK4D;IAC1C;AACF;AAEA,IAAI4D,SAAS,CAAC3G,SAASqB,EAAE,CAACN,MAAM;AAEhC,SAAS6F;IACP,OAAOD;AACT;AAEA,0EAA0E;AAC1E,4EAA4E;AAC5E,IAAIE,uBAAuB;IACzB1I,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,YAAY;IACnC;AACF;AAEA,IAAIyE,qBAAqB;IACvB3I,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,YAAY;IACnC;IACAY,UAAU,SAASA,SAAS9E,OAAO,EAAEuH,WAAW,EAAExG,SAAS;QACzD,sEAAsE;QACtE,iCAAiC,GACjC,IAAI6H,uCAAuC5I,QAAQ6I,YAAY;QAC/D,gCAAgC,GAChC7I,QAAQ4E,KAAK;QACb,OAAO7D,UAAUC,aAAa,KAAKhB;IACrC;AACF;AAEA,IAAI8I,MACF,wFACA,uGACA;AAEF,qDAAqD;AAErD,IAAIC,uBAAuB;IACzB/I,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,QAAQ;QAC7BlE,QAAQkE,YAAY,CAAC,QAAQ4E;QAC7B9I,QAAQkE,YAAY,CAAC,SAAS;QAC9BlE,QAAQkE,YAAY,CAAC,UAAU;QAC/BlE,QAAQsI,KAAK,CAACC,UAAU,GAAG;IAC7B;AACF;AAEA,qDAAqD;AAErD,IAAIS,iBAAiB;IACnBzH,MAAM;IACNvB,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,QAAQ;QAC7BlE,QAAQkE,YAAY,CAAC,QAAQ4E;QAC7B9I,QAAQkE,YAAY,CAAC,SAAS;QAC9BlE,QAAQkE,YAAY,CAAC,UAAU;IACjC;IACAY,UAAU,SAASA,SAAS9E,OAAO,EAAEuH,WAAW,EAAExG,SAAS;QACzD,IAAIc,SAASqB,EAAE,CAACT,KAAK,EAAE;YACrB,qHAAqH;YACrH,+HAA+H;YAC/H,OAAO;QACT;QAEA,OAAO1B,UAAUC,aAAa,KAAKhB;IACrC;AACF;AAEA,+DAA+D;AAC/D,IAAIiJ,WAAW,CAACpH,SAASqB,EAAE,CAACC,GAAG;AAE/B,SAAS+F;IACP,OAAOD;AACT;AAEA,IAAIE,yBAAyB;IAC3BnJ,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GACf,uGACA,qDACA,UACAyC,MACA;QAEF,iCAAiC;QACjC,OAAOpH,QAAQiH,aAAa,CAAC;IAC/B;IACAnC,UAAU,SAASA,SAAS9E,OAAO,EAAEuH,WAAW,EAAExG,SAAS;QACzD,IAAIqI,SAASpJ,QAAQiH,aAAa,CAAC;QACnC,OAAOlG,UAAUC,aAAa,KAAKoI;IACrC;AACF;AAEA,+DAA+D;AAE/D,IAAIC,sBAAsB;IACxBrJ,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GACf;QACF,oCAAoC;QACpC,OAAO;IACT;IACAG,UAAU,SAASA,SAAS9E,OAAO,EAAEuH,WAAW,EAAExG,SAAS;QACzD,IAAIuI,YAAYtJ,QAAQiH,aAAa,CAAC;QACtC,IAAIsC,WAAWvJ,QAAQiH,aAAa,CAAC;QAErC,2FAA2F;QAC3F,2DAA2D;QAC3DjH,QAAQ4E,KAAK;QAEb5E,QAAQiH,aAAa,CAAC,UAAUrC,KAAK;QACrC,OACG7D,UAAUC,aAAa,KAAKsI,aAAa,eACzCvI,UAAUC,aAAa,KAAKuI,YAAY,cACzC;IAEJ;AACF;AAEA,iDAAiD;AACjD,IAAIC,kBAAkB;IACpBxJ,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,SAAS;QAC9BlE,QAAQ2E,SAAS,GACf;QACF,OAAO3E,QAAQiH,aAAa,CAAC;IAC/B;AACF;AAEA,iDAAiD;AACjD,IAAIwC,sCAAsC;IACxCzJ,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,SAAS;QAC9BlE,QAAQ2E,SAAS,GACf;IACJ;AACF;AAEA,iDAAiD;AACjD,IAAI+E,uBAAuB;IACzB1J,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,SAAS;QAC9BlE,QAAQ2E,SAAS,GACf;IACJ;AACF;AAEA,IAAIgF,eAAe;IACjB3J,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GAAG;QACpB,OAAO3E,QAAQ4J,iBAAiB;IAClC;AACF;AAEA,SAASC;IACP,wFAAwF;IACxF,4CAA4C;IAC5C,IAAIC,gBAAgB5K,SAAS6K,eAAe,CAC1C,8BACA;IAEFD,cAAcE,KAAK,CAACC,OAAO,CAAClE,KAAK,GAAG;IACpC+D,cAAcI,MAAM,CAACD,OAAO,CAAClE,KAAK,GAAG;IACrC+D,cAAc3F,WAAW,CAACjF,SAAS+E,aAAa,CAAC;IACjD6F,cAAcK,SAAS,CAACC,IAAI,GAAG;IAE/B,OAAON;AACT;AAEA,SAASO,0BAA0BrK,OAAO;IACxC,2CAA2C;IAC3C,mDAAmD;IACnD,iDAAiD;IACjD,IAAIsK,eACFtK,QAAQuK,eAAe,IAAIvK,QAAQwK,QAAQ,CAACC,WAAW,OAAO;IAChE,IAAI,CAACH,cAAc;QACjB,OAAO;IACT;IAEA,4EAA4E;IAC5E,IAAIR,gBAAgBD;IACpB7J,QAAQmE,WAAW,CAAC2F;IACpB,IAAIjL,QAAQiL,cAAc7C,aAAa,CAAC;IACxCpI,MAAM+F,KAAK;IAEX,gDAAgD;IAChD,oDAAoD;IACpD,iDAAiD;IACjD,mCAAmC;IACnC/F,MAAM6L,QAAQ,GAAG;IAEjB,WAAW;IACX1K,QAAQiF,WAAW,CAAC6E;IACpB,OAAO;AACT;AAEA,SAASa,SAAS3K,OAAO;IACvB,OACE,wFACAA,UACA;AAEJ;AAEA,SAAS4E,MAAM5E,OAAO;IACpB,IAAIA,QAAQ4E,KAAK,EAAE;QACjB;IACF;IAEA,IAAI;QACFgG,YAAYC,SAAS,CAACjG,KAAK,CAACtF,IAAI,CAACU;IACnC,EAAE,OAAO6F,GAAG;QACVwE,0BAA0BrK;IAC5B;AACF;AAEA,SAAS8E,SAAS9E,OAAO,EAAEuH,WAAW,EAAExG,SAAS;IAC/C6D,MAAM2C;IACN,OAAOxG,UAAUC,aAAa,KAAKuG;AACrC;AAEA,IAAIuD,6BAA6B;IAC/B9K,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GAAGgG,SAAS;QAC7B,OAAO3K,QAAQiH,aAAa,CAAC;IAC/B;IACAnC,UAAUA;AACZ;AAEA,IAAIiG,4BAA4B;IAC9B/K,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GAAGgG,SAAS;QAC7B,OAAO3K,QAAQiH,aAAa,CAAC;IAC/B;IACAnC,UAAUA;AACZ;AAEA,IAAIkG,oCAAoC;IACtChL,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GAAGgG,SAAS;QAC7B,OAAO3K,QAAQiH,aAAa,CAAC;IAC/B;IACAnC,UAAUA;AACZ;AAEA,IAAImG,sBAAsB;IACxBjL,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GAAGgG,SAClB;YACE;YACA;SACD,CAACO,IAAI,CAAC;QAGT,OAAOlL,QAAQiH,aAAa,CAAC;IAC/B;IACAnC,UAAUA;AACZ;AAEA,IAAIqG,gCAAgC;IAClCnL,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GAAGgG,SAClB;QAEF,0FAA0F;QAC1F,OACE3K,QAAQiH,aAAa,CAAC,oBACtBjH,QAAQoL,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;IAEpD;IACAtG,UAAUA;AACZ;AAEA,kFAAkF;AAClF,gFAAgF;AAChF,2CAA2C;AAC3C,2DAA2D;AAE3D,IAAIuG,WAAWC,QACbzJ,SAASqB,EAAE,CAACT,KAAK,IACf,OAAO8I,eAAe,eACtBA,WAAWV,SAAS,CAACjG,KAAK;AAG9B,SAAS4G;IACP,OAAOH;AACT;AAEA,IAAII,WAAW;IACbzL,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQ2E,SAAS,GAAGgG,SAAS;QAC7B,OAAO3K,QAAQ0L,UAAU;IAC3B;IACA5G,UAAUA;AACZ;AAEA,0EAA0E;AAC1E,4EAA4E;AAC5E,IAAI6G,kCAAkC;IACpC3L,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7BA,QAAQkE,YAAY,CAAC,YAAY;IACnC;AACF;AAEA,IAAI0H,aAAa;IACf5L,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO,EAAEwE,OAAO,EAAEzD,SAAS;QACjD,6DAA6D;QAC7D,6CAA6C;QAC7C,gDAAgD;QAChD,IAAI8K,WAAW9K,UAAU+K,sBAAsB;QAC/CD,SAASlH,SAAS,GAAG;QACrB3E,QAAQmE,WAAW,CAAC0H;IACtB;AACF;AAEA,IAAIE,4BAA4B;IAC9B/L,SAAS;IACT6E,QAAQ,SAASA,OAAO7E,OAAO;QAC7B,IAAI;YACF,sFAAsF;YACtFA,QAAQkE,YAAY,CAAC,OAAOkD;QAC9B,EAAE,OAAOvB,GAAG;QACV,yCAAyC;QAC3C;IACF;AACF;AAEA,yDAAyD;AACzD,IAAImG,WAAWnK,SAASqB,EAAE,CAACT,KAAK,IAAIZ,SAASqB,EAAE,CAACR,OAAO,IAAIb,SAASqB,EAAE,CAACP,IAAI;AAE3E,SAASsJ;IACP,OAAOD;AACT;AAEA,IAAIE,gBAAgB;IAClBnF,iCAAiCA;IACjC0B,4BAA4BA;IAC5BS,gBAAgBA;IAChBsC,kBAAkBA;IAClBS,8BAA8BA;AAChC;AAEA,IAAIE,mBAAmB;IACrB9E,sBAAsBA;IACtBC,mBAAmBA;IACnBE,sBAAsBA;IACtBC,2BAA2BA;IAC3BE,qBAAqBA;IACrBC,iCAAiCA;IACjCC,uBAAuBA;IACvBC,eAAeA;IACfC,uBAAuBA;IACvBC,mBAAmBA;IACnBC,eAAeA;IACfE,wBAAwBA;IACxBC,qBAAqBA;IACrBM,sBAAsBA;IACtBC,oBAAoBA;IACpBK,gBAAgBA;IAChBD,sBAAsBA;IACtBI,wBAAwBA;IACxBE,qBAAqBA;IACrBG,iBAAiBA;IACjBC,qCAAqCA;IACrCC,sBAAsBA;IACtBC,cAAcA;IACdmB,4BAA4BA;IAC5BC,2BAA2BA;IAC3BC,mCAAmCA;IACnCC,qBAAqBA;IACrBE,+BAA+BA;IAC/BM,UAAUA;IACVE,iCAAiCA;IACjCC,YAAYA;IACZG,2BAA2BA;AAC7B;AAEA,SAASK;IACP,IAAIhH,UAAUF,YAAYiH;IAC1B9G,OAAOC,IAAI,CAAC4G,eAAevF,OAAO,CAAC,SAAUnB,GAAG;QAC9CJ,OAAO,CAACI,IAAI,GAAG0G,aAAa,CAAC1G,IAAI;IACnC;IAEA,OAAOJ;AACT;AAEA,IAAIiH,gBAAgB;AAEpB,SAASC;IACP,IAAID,eAAe;QACjB,OAAOA;IACT;IAEAA,gBAAgB9F,QAAQC,GAAG;IAC3B,IAAI,CAAC6F,cAAczF,IAAI,EAAE;QACvBL,QAAQE,GAAG,CAAC2F;QACZC,gBAAgB9F,QAAQC,GAAG;IAC7B;IAEA,OAAO6F;AACT;AAEA,IAAIE,WAAW,KAAK;AAEpB,6EAA6E;AAC7E,4DAA4D;AAC5D,IAAIC,gCAAgC;AACpC,IAAIC,kCAAkC;AAEtC,SAASC,gBAAgB/M,OAAO;IAC9B,IAAI,CAAC4M,UAAU;QACbA,WAAWD;IACb;IAEA,IAAIK,sBAAsBJ,SAASZ,+BAA+B,GAC9Dc,kCACAD;IAEJ,IAAIxM,UAAUP,iBAAiB;QAC7BI,OAAO;QACPC,iBAAiB;QACjBH,SAASA;IACX;IAEA,wDAAwD;IACxD,oFAAoF;IACpF,IAAIiN,cAAc5M,QAAQ6M,YAAY,CAAC;IACvC,IAAIC,cAAc9M,QAAQ6M,YAAY,CAAC;IAEvC,IAAI,CAACD,eAAe,CAACE,aAAa;QAChC,OAAO;IACT;IAEA,6EAA6E;IAC7E,IAAIxC,eACFtK,QAAQuK,eAAe,IAAIvK,QAAQwK,QAAQ,CAACC,WAAW,OAAO;IAChE,IAAIH,gBAAgB,CAACiC,SAASxB,yBAAyB,EAAE;QACvD,OAAO;IACT;IAEA,4EAA4E;IAC5E,IAAIwB,SAAS7D,oBAAoB,EAAE;QACjC,OAAO;IACT;IAEA,wEAAwE;IACxE,IAAIqE,WAAW/M,QAAQgN,YAAY,CAACJ,cAAc,aAAa;IAC/D,gDAAgD;IAChD,mFAAmF;IACnF,IAAIG,aAAa,UAAU;QACzB,OAAO;IACT;IAEA,OAAOzB,QAAQyB,YAAYJ,oBAAoBlI,IAAI,CAACsI;AACtD;AAEA,SAASE,cAAcjN,OAAO;IAC5B,IAAI,CAAC0M,gBAAgB1M,UAAU;QAC7B,OAAO;IACT;IAEA,wDAAwD;IACxD,oFAAoF;IACpF,IAAI4M,cAAc5M,QAAQ6M,YAAY,CAAC;IACvC,IAAIK,gBAAgBN,cAAc,aAAa;IAE/C,4EAA4E;IAC5E,IAAIG,WAAWI,SAASnN,QAAQgN,YAAY,CAACE,gBAAgB;IAC7D,OAAOE,MAAML,YAAY,CAAC,IAAIA;AAChC;AAEA,sEAAsE;AACtE,8DAA8D;AAC9D,uDAAuD;AAEvD,SAASM,qBAAqB/E,KAAK;IACjC,kEAAkE;IAClE,iDAAiD;IACjD,IAAIgF,aAAahF,MAAMiF,gBAAgB,IAAI;IAC3C,OAAOjC,QAAQgC,cAAcA,WAAWE,OAAO,CAAC,aAAa,CAAC;AAChE;AAEA,SAASC,qBAAqBnF,KAAK;IACjC,OAAO;QACLA,MAAMoF,gBAAgB,CAAC;QACvBpF,MAAMoF,gBAAgB,CAAC;QACvBpF,MAAMoF,gBAAgB,CAAC;KACxB,CAACjM,IAAI,CAAC,SAAUkM,QAAQ;QACvB,OAAOA,aAAa,UAAUA,aAAa;IAC7C;AACF;AAEA,SAASC,kBAAkBtF,KAAK;IAC9B,OAAOA,MAAMuF,OAAO,CAACL,OAAO,CAAC,UAAU,CAAC;AAC1C;AAEA,SAASM,sBAAsB9N,OAAO,EAAEwK,QAAQ,EAAEuD,cAAc,EAAEC,WAAW;IAC3E,IAAIxD,aAAa,SAASA,aAAa,QAAQ;QAC7C,2EAA2E;QAC3E,wEAAwE;QACxE,qCAAqC;QACrC,OAAO;IACT;IAEA,IACEuD,kBACAA,mBAAmB,SACnBA,mBAAmB,UACnB,CAACN,qBAAqBO,cACtB;QACA,OAAO;IACT;IAEA,OACEhO,QAAQ6I,YAAY,GAAG7I,QAAQiO,YAAY,IAC3CjO,QAAQkO,WAAW,GAAGlO,QAAQmO,WAAW;AAE7C;AAEA,IAAIC,aAAa,KAAK;AAEtB,SAASC;IACP,IAAI3O,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB2O,cAAc5O,KAAK6O,MAAM,EACzBA,SACED,gBAAgBrP,YACZ;QACEuP,SAAS;QACTC,YAAY;QACZC,QAAQ;IACV,IACAJ;IAER,IAAI,CAACF,YAAY;QACfA,aAAa9B;IACf;IAEA,IAAItM,UAAUP,iBAAiB;QAC7BI,OAAO;QACPC,iBAAiB;QACjBH,SAASA;IACX;IAEA,IAAI,CAAC4O,OAAOG,MAAM,IAAI1O,QAAQkB,UAAU,EAAE;QACxC,sEAAsE;QACtE,OAAO;IACT;IAEA,IAAIsJ,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAE3C,IAAID,aAAa,WAAWxK,QAAQoK,IAAI,KAAK,UAAU;QACrD,kDAAkD;QAClD,OAAO;IACT;IAEA,IACEI,aAAa,WACbA,aAAa,YACbA,aAAa,YACbA,aAAa,YACb;QACA,OAAO;IACT;IAEA,IAAIA,aAAa,YAAY4D,WAAW/E,mBAAmB,EAAE;QAC3D,qCAAqC;QACrC,OAAO;IACT;IAEA,IAAImB,aAAa,SAAS;QACxB,qCAAqC;QACrC,OAAO;IACT;IAEA,IAAIA,aAAa,QAAQ;QACvB,qCAAqC;QACrC,OAAO;IACT;IAEA,IAAIA,aAAa,OAAOxK,QAAQ6M,YAAY,CAAC,SAAS;QACpD,OAAO;IACT;IAEA,IAAIrC,aAAa,YAAYxK,QAAQ6M,YAAY,CAAC,WAAW;QAC3D,iDAAiD;QACjD,OAAO;IACT;IAEA,IAAIrC,aAAa,UAAU;QACzB,IAAImE,UAAU3O,QAAQgN,YAAY,CAAC;QACnC,IAAI,CAACoB,WAAWpF,cAAc,IAAI2F,YAAY,iBAAiB;YAC7D,qEAAqE;YACrE,OAAO;QACT,OAAO,IACL,CAACP,WAAWlF,cAAc,IAC1ByF,YAAY,iCACZ;YACA,uFAAuF;YACvF,OAAO;QACT;IACF;IAEA,IAAInE,aAAa,YAAYA,aAAa,UAAU;QAClD,8BAA8B;QAC9B,OAAO;IACT;IAEA,IAAIA,aAAa,WAAWA,aAAa,UAAU;QACjD,uDAAuD;QACvD,qDAAqD;QACrD,OAAO;IACT;IAEA,IAAIxK,QAAQ6M,YAAY,CAAC,oBAAoB;QAC3C,0CAA0C;QAC1C,OAAO;IACT;IAEA,IACErC,aAAa,WACZ4D,CAAAA,WAAW3G,yBAAyB,IAAIzH,QAAQ6M,YAAY,CAAC,WAAU,GACxE;QACA,OAAO;IACT;IAEA,IACErC,aAAa,WACZ4D,CAAAA,WAAWrC,yBAAyB,IAAI/L,QAAQ6M,YAAY,CAAC,WAAU,GACxE;QACA,OAAO;IACT;IAEA,IAAIuB,WAAWzE,YAAY,IAAIa,aAAa,WAAW;QACrD,OAAO;IACT;IAEA,IAAIoE,gBAAgBlC,gBAAgB1M;IAEpC,IAAIwK,aAAa,SAASxK,QAAQ6M,YAAY,CAAC,WAAW;QACxD,8FAA8F;QAC9F,gFAAgF;QAChF,OACG+B,iBAAiBR,WAAWjG,sBAAsB,IACnDiG,WAAWjF,sBAAsB;IAErC;IAEA,IAAIiF,WAAWxC,UAAU,IAAKpB,CAAAA,aAAa,WAAWA,aAAa,IAAG,GAAI;QACxE,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI4D,WAAWtG,aAAa,IAAI0C,aAAa,YAAY;QACvD,wCAAwC;QACxC,OAAO;IACT;IAEA,IAAIF,eAAeE,aAAa;IAChC,IAAIqE,eAAe7O,QAAQuK,eAAe;IAC1C,IAAIuE,qBAAqB9O,QAAQgN,YAAY,CAAC;IAC9C,IAAID,WAAWE,cAAcjN;IAE7B,IACEwK,aAAa,SACbuC,aAAa,QACb,CAACqB,WAAWnD,mBAAmB,EAC/B;QACA,8FAA8F;QAC9F,OAAO;IACT;IAEA,IAAIT,aAAa,iBAAiB;QAChC,uDAAuD;QACvD,OAAOuC,aAAa,QAAQqB,WAAWjD,6BAA6B;IACtE;IAEA,IAAIxJ,eAAe3B,SAAS,YAAYA,QAAQ6M,YAAY,CAAC,eAAe;QAC1E,OAAO;IACT;IAEA,IACGvC,CAAAA,gBAAgBuE,YAAW,KAC5B7O,QAAQ4E,KAAK,IACb,CAACwJ,WAAWpD,iCAAiC,IAC7C+B,WAAW,GACX;QACA,iEAAiE;QACjE,yDAAyD;QACzD,2DAA2D;QAC3D,OAAO;IACT;IAEA,IAAIzC,cAAc;QAChB,OACEsE,iBACAR,WAAW3C,QAAQ,IACnB2C,WAAW5C,gBAAgB,IAC3B,mFAAmF;QACnFF,QACE8C,WAAWtD,0BAA0B,IACnCgE,sBACAA,uBAAuB;IAG/B;IAEA,IAAID,cAAc;QAChB,IAAIT,WAAWrD,yBAAyB,IAAI6D,eAAe;YACzD,OAAO;QACT;QAEA,IAAIR,WAAWtD,0BAA0B,EAAE;YACzC,mFAAmF;YACnF,OAAOgE,uBAAuB;QAChC;IACF;IAEA,kGAAkG;IAClG,IAAIF,eAAe;QACjB,OAAO;IACT;IAEA,IAAItG,QAAQ7E,OAAOsL,gBAAgB,CAAC/O,SAAS;IAC7C,IAAIqN,qBAAqB/E,QAAQ;QAC/B,OAAO;IACT;IAEA,IACE8F,WAAWnG,aAAa,IACxBuC,aAAa,SACbxK,QAAQ6M,YAAY,CAAC,UACrB;QACA,+DAA+D;QAC/D,iDAAiD;QACjD,IAAImC,gBAAgB7N,WAAW;YAAExB,SAASK;QAAQ,GAAGyB,IAAI,CACvD,SAAUwN,MAAM;YACd,OACEA,OAAOzE,QAAQ,CAACC,WAAW,OAAO,OAAOwE,OAAOpC,YAAY,CAAC;QAEjE;QAGF,IAAImC,eAAe;YACjB,OAAO;QACT;IACF;IAEA,iDAAiD;IACjD,IAAI,CAACT,OAAOE,UAAU,IAAIL,WAAW1E,oBAAoB,EAAE;QACzD,IAAI0E,WAAW3E,mCAAmC,EAAE;YAClD,qEAAqE;YACrE,sEAAsE;YACtE,0CAA0C;YAC1C,IAAIqE,sBAAsB9N,SAASwK,WAAW;gBAC5C,OAAO;YACT;QACF,OAAO,IAAIiD,qBAAqBnF,QAAQ;YACtC,oEAAoE;YACpE,sDAAsD;YACtD,OAAO;QACT;IACF;IAEA,IACE,CAACiG,OAAOC,OAAO,IACfJ,WAAWrG,qBAAqB,IAChC6F,kBAAkBtF,QAClB;QACA,sDAAsD;QACtD,OAAO;IACT;IAEA,IAAI2G,SAASjP,QAAQkP,aAAa;IAClC,IAAI,CAACX,OAAOE,UAAU,IAAIQ,QAAQ;QAChC,IAAIlB,iBAAiBkB,OAAOzE,QAAQ,CAACC,WAAW;QAChD,IAAIuD,cAAcvK,OAAOsL,gBAAgB,CAACE,QAAQ;QAClD,IACEb,WAAW5E,eAAe,IAC1BsE,sBAAsBmB,QAAQzE,UAAUuD,gBAAgBC,cACxD;YACA,oDAAoD;YACpD,iDAAiD;YACjD,OAAO;QACT;QAEA,4EAA4E;QAC5E,IAAII,WAAWxG,+BAA+B,EAAE;YAC9C,IAAIgG,kBAAkBI,cAAc;gBAClC,OAAO;YACT;QACF;IACF;IAEA,oDAAoD;IACpD,8CAA8C;IAC9C,iDAAiD;IAEjD,OAAO;AACT;AAEA,0CAA0C;AAC1CK,qBAAqBE,MAAM,GAAG;IAC5B,IAAIA,SACFhO,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC;IAEvE,IAAI4O,kBAAkB,SAASA,gBAAgBxP,OAAO;QACpD,OAAO0O,qBAAqB;YAC1B1O,SAASA;YACT4O,QAAQA;QACV;IACF;IAEAY,gBAAgBC,KAAK,GAAGf;IACxB,OAAOc;AACT;AAEA,gEAAgE;AAChE,IAAIA,kBAAkBd,qBAAqBE,MAAM,CAAC,CAAC;AAEnD,SAASc,UAAUC,KAAK,EAAEC,QAAQ;IAChC,4DAA4D;IAC5D,IAAID,MAAMD,SAAS,EAAE;QACnB,OAAOC,MAAMD,SAAS,CAACE;IACzB;IAEA,IAAInQ,SAASkQ,MAAMlQ,MAAM;IAEzB,iCAAiC;IACjC,IAAIA,WAAW,GAAG;QAChB,OAAO,CAAC;IACV;IAEA,4BAA4B;IAC5B,IAAK,IAAIoQ,IAAI,GAAGA,IAAIpQ,QAAQoQ,IAAK;QAC/B,IAAID,SAASD,KAAK,CAACE,EAAE,EAAEA,GAAGF,QAAQ;YAChC,OAAOE;QACT;IACF;IAEA,OAAO,CAAC;AACV;AAEA,SAASC,mBAAmB7O,IAAI;IAC9B,IAAI;QACF,iCAAiC;QACjC,OACEA,KAAK8O,eAAe,IACpB,iCAAiC;QAChC9O,KAAKyD,aAAa,IAAIzD,KAAKyD,aAAa,CAACnF,QAAQ,IAClD,kDAAkD;QACjD0B,KAAK+O,cAAc,IAAI/O,KAAK+O,cAAc,MAC3C;IAEJ,EAAE,OAAO9J,GAAG;QACV,wFAAwF;QACxF,iFAAiF;QACjF,OAAO;IACT;AACF;AAEA,SAAS+J,UAAUhP,IAAI;IACrB,IAAIG,YAAYJ,YAAYC;IAC5B,OAAOG,UAAU8O,WAAW,IAAIpM;AAClC;AAEA,IAAIqM,eAAe,KAAK;AAExB,SAASC,gBAAgBnO,QAAQ;IAC/B,IAAI,OAAOkO,iBAAiB,UAAU;QACpC,IAAIE,WAAWjJ;QACf,IAAIiJ,UAAU;YACZF,eAAe,YAAYE,WAAW;QACxC;IACF;IAEA,IAAI,CAACF,cAAc;QACjB,OAAOlO;IACT;IAEA,OACEA,WACAkO,eACAlO,SACGqO,OAAO,CAAC,YAAY,KACpBC,KAAK,CAAC,KACNhF,IAAI,CAAC4E;AAEZ;AAEA,IAAIlO,WAAW,KAAK;AAEpB,SAASuO,wBAAwB/L,OAAO;IACtC,IAAI,CAACxC,UAAU;QACbA,WAAWmO,gBAAgB;IAC7B;IAEA,IAAI3L,QAAQgM,aAAa,KAAKnR,WAAW;QACvC,OAAOmF,QAAQgM,aAAa;IAC9B;IAEAhM,QAAQgM,aAAa,GAAG;IAExB,IAAIC,iBAAiBjM,QAAQ6K,MAAM,CAAC/P,QAAQ,CAACC,gBAAgB,CAACyC;IAC7D,EAAE,CAACH,IAAI,CAACnC,IAAI,CAAC+Q,gBAAgB,SAAUrQ,OAAO;QAC7C,IAAIe,YAAY0O,mBAAmBzP;QACnC,IAAIe,cAAcqD,QAAQlF,QAAQ,EAAE;YAClC,OAAO;QACT;QAEAkF,QAAQgM,aAAa,GAAGpQ;QACxB,OAAO;IACT;IAEA,OAAOoE,QAAQgM,aAAa;AAC9B;AAEA,SAASE,gBAAgBtQ,OAAO;IAC9B,IAAIoE,UAAUwL,UAAU5P;IACxB,IAAI,CAACoE,QAAQ6K,MAAM,IAAI7K,QAAQ6K,MAAM,KAAK7K,SAAS;QACjD,0CAA0C;QAC1C,mDAAmD;QACnD,OAAO;IACT;IAEA,IAAI;QACF,qEAAqE;QACrE,0EAA0E;QAC1E,OAAOA,QAAQmM,YAAY,IAAIJ,wBAAwB/L;IACzD,EAAE,OAAOyB,GAAG;QACV,OAAO;IACT;AACF;AAEA,4DAA4D;AAC5D,yFAAyF;AACzF,IAAI2K,6BAA6B;AAEjC,SAASC,cAAczQ,OAAO,EAAE0Q,QAAQ;IACtC,OAAOjN,OAAOsL,gBAAgB,CAAC/O,SAAS,MAAM0N,gBAAgB,CAACgD;AACjE;AAEA,SAASC,aAAaC,KAAK;IACzB,OAAOA,MAAMnP,IAAI,CAAC,SAAUzB,OAAO;QACjC,yDAAyD;QACzD,OAAOyQ,cAAczQ,SAAS,eAAe;IAC/C;AACF;AAEA,SAAS6Q,WAAWD,KAAK;IACvB,uEAAuE;IACvE,yGAAyG;IACzG,gEAAgE;IAChE,IAAIE,SAASzB,UAAUuB,OAAO,SAAU5Q,OAAO;QAC7C,IAAIuI,aAAakI,cAAczQ,SAAS;QACxC,OAAOuI,eAAe,YAAYA,eAAe;IACnD;IAEA,IAAIuI,WAAW,CAAC,GAAG;QACjB,6BAA6B;QAC7B,OAAO;IACT;IAEA,IAAIC,UAAU1B,UAAUuB,OAAO,SAAU5Q,OAAO;QAC9C,OAAOyQ,cAAczQ,SAAS,kBAAkB;IAClD;IAEA,IAAI+Q,YAAY,CAAC,GAAG;QAClB,qDAAqD;QACrD,OAAO;IACT;IAEA,IAAID,SAASC,SAAS;QACpB,2EAA2E;QAC3E,OAAO;IACT;IAEA,oEAAoE;IACpE,OAAO;AACT;AAEA,SAASC,gBAAgBJ,KAAK;IAC5B,IAAIK,SAAS;IACb,IAAIL,KAAK,CAAC,EAAE,CAACpG,QAAQ,CAACC,WAAW,OAAO,WAAW;QACjDwG,SAAS;IACX;IAEA,OAAOL,MAAMvR,KAAK,CAAC4R,QAAQxP,IAAI,CAAC,SAAUzB,OAAO;QAC/C,iEAAiE;QACjE,OACEA,QAAQwK,QAAQ,CAACC,WAAW,OAAO,aAAazK,QAAQsE,IAAI,KAAK;IAErE;AACF;AAEA,SAAS4M;IACP,IAAIxR,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB2O,cAAc5O,KAAK6O,MAAM,EACzBA,SACED,gBAAgBrP,YACZ;QACEkS,aAAa;QACbC,YAAY;QACZC,eAAe;QACfC,gBAAgB;QAChBC,iBAAiB;IACnB,IACAjD;IAER,IAAItO,UAAUP,iBAAiB;QAC7BI,OAAO;QACPC,iBAAiB;QACjBH,SAASA;IACX;IAEA,IAAI6K,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,IAAI,CAAC8D,OAAO4C,WAAW,IAAIX,2BAA2B/L,IAAI,CAAC+F,WAAW;QACpE,OAAO;IACT;IAEA,IAAIoG,QAAQzP,WAAW;QAAExB,SAASK;IAAQ;IAE1C,8FAA8F;IAC9F,yFAAyF;IACzF,wGAAwG;IACxG,IAAIwR,yBACFhH,aAAa,WAAW,CAACxK,QAAQ6M,YAAY,CAAC;IAChD,IACE,CAAC0B,OAAO6C,UAAU,IAClBT,aAAaa,yBAAyBZ,MAAMvR,KAAK,CAAC,KAAKuR,QACvD;QACA,OAAO;IACT;IAEA,IAAI,CAACrC,OAAO8C,aAAa,IAAIR,WAAWD,QAAQ;QAC9C,OAAO;IACT;IAEA,IAAI,CAACrC,OAAO+C,cAAc,IAAIN,gBAAgBJ,QAAQ;QACpD,OAAO;IACT;IAEA,IAAI,CAACrC,OAAOgD,eAAe,EAAE;QAC3B,yDAAyD;QACzD,0DAA0D;QAC1D,IAAIhB,eAAeD,gBAAgBtQ;QACnC,IAAIyR,aAAaP,eAAe3C,MAAM,CAACA;QACvC,IAAIgC,gBAAgB,CAACkB,WAAWlB,eAAe;YAC7C,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,0CAA0C;AAC1CW,eAAe3C,MAAM,GAAG;IACtB,IAAIA,SACFhO,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC;IAEvE,IAAImR,YAAY,SAASA,UAAU/R,OAAO;QACxC,OAAOuR,eAAe;YACpBvR,SAASA;YACT4O,QAAQA;QACV;IACF;IAEAmD,UAAUtC,KAAK,GAAG8B;IAClB,OAAOQ;AACT;AAEA,0DAA0D;AAC1D,IAAIA,YAAYR,eAAe3C,MAAM,CAAC,CAAC;AAEvC,SAASoD,aAAapQ,IAAI,EAAER,SAAS;IACnC,2EAA2E;IAC3E,wEAAwE;IACxE,IAAIwE,MAAMxE,UAAUkG,aAAa,CAAC,eAAetI,iMAAAA,EAAU4C,QAAQ;IACnE,OAAOgE,OAAO;AAChB;AAEA,SAASqM,eAAe5R,OAAO;IAC7B,IAAIuF,MAAMvF,QAAQkP,aAAa;IAE/B,IAAI,CAAC3J,IAAIhE,IAAI,IAAIgE,IAAIiF,QAAQ,CAACC,WAAW,OAAO,OAAO;QACrD,OAAO;IACT;IAEA,uEAAuE;IACvE,6CAA6C;IAE7C,uEAAuE;IACvE,mFAAmF;IACnF,wEAAwE;IACxE,8DAA8D;IAC9D,gEAAgE;IAChE,IAAI1J,YAAYJ,YAAYX;IAC5B,OACEe,UAAUkG,aAAa,CAAC,yMAAkBtI,UAAAA,EAAU4G,IAAIhE,IAAI,IAAI,SAChE;AAEJ;AAEA,IAAIsQ,aAAa,KAAK;AAEtB,0DAA0D;AAC1D,sEAAsE;AACtE,sEAAsE;AACtE,SAASC,YAAYnS,OAAO;IAC1B,IAAI,CAACkS,YAAY;QACfA,aAAavF;IACf;IAEA,IAAItM,UAAUP,iBAAiB;QAC7BI,OAAO;QACPF,SAASA;IACX;IAEA,IAAI6K,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,IAAID,aAAa,QAAQ;QACvB,OAAO;IACT;IAEA,IAAIoC,cAAc5M,QAAQ6M,YAAY,CAAC;IACvC,IAAI,CAACgF,WAAWvK,iBAAiB,IAAIsF,aAAa;QAChD,+EAA+E;QAC/E,OAAO;IACT;IAEA,IAAImF,MAAMH,eAAe5R;IACzB,IAAI,CAAC+R,OAAO,CAACL,UAAUK,MAAM;QAC3B,OAAO;IACT;IAEA,kEAAkE;IAClE,yDAAyD;IACzD,IACE,CAACF,WAAWlK,mBAAmB,IAC9B,CAAA,CAACoK,IAAIC,QAAQ,IACZ,CAACD,IAAIE,aAAa,IAClBF,IAAI7D,WAAW,IAAI,KACnB6D,IAAIlJ,YAAY,IAAI,CAAA,GACtB;QACA,OAAO;IACT;IAEA,qFAAqF;IACrF,IAAI,CAACgJ,WAAWrK,oBAAoB,IAAI,CAACxH,QAAQkI,IAAI,EAAE;QACrD,4EAA4E;QAC5E,iEAAiE;QACjE,OACG2J,WAAWvK,iBAAiB,IAAIsF,eAChCiF,WAAWxK,oBAAoB,IAAI0K,IAAIlF,YAAY,CAAC;IAEzD;IAEA,sEAAsE;IACtE,IAAIqF,qBAAqB/Q,WAAW;QAAExB,SAASoS;IAAI,GAChD1S,KAAK,CAAC,GACNoC,IAAI,CAAC,SAAU0Q,QAAQ;QACtB,IAAI5Q,OAAO4Q,SAAS3H,QAAQ,CAACC,WAAW;QACxC,OAAOlJ,SAAS,YAAYA,SAAS;IACvC;IAEF,IAAI2Q,oBAAoB;QACtB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,IAAIE,aAAa,KAAK;AAEtB,8EAA8E;AAC9E,IAAIC,0BAA0B,KAAK;AACnC,IAAIC,mBAAmB;IACrBzT,OAAO;IACP0T,QAAQ;IACRC,UAAU;IACVC,QAAQ;IACRC,UAAU;IACVC,MAAM;AACR;AAEA,SAASC,0BAA0BjT,OAAO;IACxC,IAAI,CAACyS,YAAY;QACfA,aAAa9F;QAEb,IAAI8F,WAAWvK,qBAAqB,EAAE;YACpC,OAAOyK,iBAAiBI,QAAQ;QAClC;QAEA,IAAIN,WAAWpK,iBAAiB,EAAE;YAChC,OAAOsK,iBAAiBK,IAAI;QAC9B;QAEAN,0BAA0B,IAAIQ,OAC5B,OAAOxN,OAAOC,IAAI,CAACgN,kBAAkBpH,IAAI,CAAC,OAAO;IAErD;IAEA,IAAIlL,UAAUP,iBAAiB;QAC7BI,OAAO;QACPF,SAASA;IACX;IAEA,IAAI6K,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,OAAOa,QAAQ+G,wBAAwB5N,IAAI,CAAC+F;AAC9C;AAEA,IAAIsI,aAAa,KAAK;AAEtB,SAASC,mBAAmB/S,OAAO;IACjC,IAAIwK,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,OAAOD,aAAa,cAAcxK,QAAQ0K,QAAQ;AACpD;AAEA,SAASsI,eAAehT,OAAO;IAC7B,IAAIwK,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,OAAOD,aAAa,UAAUxK,QAAQ0K,QAAQ;AAChD;AAEA,SAASuI,WAAWtT,OAAO;IACzB,IAAI,CAACmT,YAAY;QACfA,aAAaxG;IACf;IAEA,IAAItM,UAAUP,iBAAiB;QAC7BI,OAAO;QACPF,SAASA;IACX;IAEA,IAAIK,QAAQ6M,YAAY,CAAC,uBAAuB;QAC9C,qEAAqE;QACrE,OAAO;IACT;IAEA,IAAI,CAAC+F,0BAA0B5S,UAAU;QACvC,0DAA0D;QAC1D,OAAO;IACT;IAEA,IAAIA,QAAQ0K,QAAQ,EAAE;QACpB,iCAAiC;QACjC,OAAO;IACT;IAEA,IAAIwI,UAAU/R,WAAW;QAAExB,SAASK;IAAQ;IAC5C,IAAIkT,QAAQzR,IAAI,CAACsR,qBAAqB;QACpC,4EAA4E;QAC5E,OAAO;IACT;IAEA,IAAI,CAACD,WAAW9K,iBAAiB,IAAIkL,QAAQzR,IAAI,CAACuR,iBAAiB;QACjE,wEAAwE;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASG;IACP,IAAIzT,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB2O,cAAc5O,KAAK6O,MAAM,EACzBA,SACED,gBAAgBrP,YACZ;QACEmU,8BAA8B;QAC9BrC,SAAS;IACX,IACAzC;IAER,IAAItO,UAAUP,iBAAiB;QAC7BI,OAAO;QACPC,iBAAiB;QACjBH,SAASA;IACX;IAEA,IAAI,CAAC4O,OAAOwC,OAAO,IAAI,CAACW,UAAU1R,UAAU;QAC1C,OAAO;IACT;IAEA,IACE,CAACuO,OAAO6E,4BAA4B,IACnCvR,CAAAA,SAASqB,EAAE,CAACT,KAAK,IAAIZ,SAASqB,EAAE,CAACR,OAAO,IAAIb,SAASqB,EAAE,CAACP,IAAG,GAC5D;QACA,IAAI4N,eAAeD,gBAAgBtQ;QACnC,IAAIuQ,cAAc;YAChB,IAAItD,cAAcsD,gBAAgB,GAAG;gBACnC,8DAA8D;gBAC9D,6DAA6D;gBAC7D,OAAO;YACT;QACF;IACF;IAEA,IAAI/F,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,IAAIsC,WAAWE,cAAcjN;IAE7B,IAAIwK,aAAa,WAAW3I,SAASqB,EAAE,CAACT,KAAK,EAAE;QAC7C,sDAAsD;QACtD,OAAOsK,aAAa,QAAQA,YAAY;IAC1C;IAEA,mFAAmF;IACnF,kFAAkF;IAClF,0DAA0D;IAC1D,IAAIlL,SAASqB,EAAE,CAACT,KAAK,IAAIzC,QAAQuK,eAAe,IAAI,CAACvK,QAAQ4E,KAAK,EAAE;QAClE,IAAI4F,aAAa,OAAOxK,QAAQ6M,YAAY,CAAC,eAAe;YAC1D,gEAAgE;YAChE,IAAIhL,SAASqB,EAAE,CAACT,KAAK,EAAE;gBACrB,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AAEA,0CAA0C;AAC1C0Q,oBAAoB5E,MAAM,GAAG;IAC3B,IAAIA,SACFhO,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC;IAEvE,IAAI8S,iBAAiB,SAASA,eAAe1T,OAAO;QAClD,OAAOwT,oBAAoB;YACzBxT,SAASA;YACT4O,QAAQA;QACV;IACF;IAEA8E,eAAejE,KAAK,GAAG+D;IACvB,OAAOE;AACT;AAEA,+DAA+D;AAC/D,IAAIA,iBAAiBF,oBAAoB5E,MAAM,CAAC,CAAC;AAEjD,IAAI+E,aAAa,KAAK;AAEtB,SAASC,oBAAoBvT,OAAO;IAClC,IAAIwK,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,IAAID,aAAa,WAAWA,aAAa,UAAU;QACjD,uDAAuD;QACvD,qDAAqD;QACrD,OAAO;IACT;IAEA,IAAIgJ,YAAYvG,cAAcjN;IAC9B,IAAIA,QAAQkB,UAAU,IAAIsS,cAAc,MAAM;QAC5C,8CAA8C;QAC9C,+CAA+C;QAC/C,OAAO;IACT;IAEA,IAAIhJ,aAAa,SAAS;QACxB,yEAAyE;QACzE,+EAA+E;QAC/E,8EAA8E;QAC9E,kDAAkD;QAClD,OAAO,CAAC8I,WAAW3K,kBAAkB,IAAI6K,cAAc;IACzD;IAEA,IAAIhJ,aAAa,UAAU;QACzB,OAAOgJ,cAAc;IACvB;IAEA,IACEF,WAAWxI,0BAA0B,IACpC9K,CAAAA,QAAQuK,eAAe,IAAIC,aAAa,KAAI,GAC7C;QACA,mFAAmF;QACnF,IAAIsE,qBAAqB9O,QAAQgN,YAAY,CAAC;QAC9C,OAAO8B,sBAAsBA,uBAAuB;IACtD;IAEA,IAAItE,aAAa,SAASxK,QAAQ6M,YAAY,CAAC,WAAW;QACxD,8FAA8F;QAC9F,gFAAgF;QAChF,OAAO2G,cAAc,QAAQ,CAACF,WAAWnL,sBAAsB;IACjE;IAEA,IAAIqC,aAAa,QAAQ;QACvB,uCAAuC;QACvC,2CAA2C;QAC3C,OAAO,CAACsH,YAAY9R;IACtB;IAEA,OAAO;AACT;AAEA,SAASyT;IACP,IAAI/T,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB2O,cAAc5O,KAAK6O,MAAM,EACzBA,SACED,gBAAgBrP,YACZ;QACEyL,UAAU;QACVqG,SAAS;QACT2C,cAAc;IAChB,IACApF;IAER,IAAI,CAACgF,YAAY;QACfA,aAAahH;IACf;IAEA,IAAIqH,kBAAkBN,eAAejE,KAAK,CAACb,MAAM,CAAC;QAChD6E,8BAA8B;QAC9BrC,SAASxC,OAAOwC,OAAO;IACzB;IAEA,IAAI/Q,UAAUP,iBAAiB;QAC7BI,OAAO;QACPC,iBAAiB;QACjBH,SAASA;IACX;IAEA,IAAIiU,gBAAgBzE,gBAAgBC,KAAK,CAAC;QACxCzP,SAASK;QACTuO,QAAQA;IACV;IAEA,IAAI,CAACqF,iBAAiBL,oBAAoBvT,UAAU;QAClD,OAAO;IACT;IAEA,IAAI,CAACuO,OAAO7D,QAAQ,IAAIuI,WAAWjT,UAAU;QAC3C,OAAO;IACT;IAEA,IAAI,CAACuO,OAAOmF,YAAY,IAAIC,gBAAgB3T,UAAU;QACpD,oEAAoE;QACpE,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,CAACuO,OAAOwC,OAAO,EAAE;QACnB,IAAI8C,oBAAoB;YACtBlU,SAASK;YACTuO,QAAQ,CAAC;QACX;QAEA,IAAI+E,WAAWlL,mBAAmB,EAAE;YAClC,qEAAqE;YACrEyL,kBAAkBtF,MAAM,CAACgD,eAAe,GAAG;QAC7C;QAEA,IAAI+B,WAAWvK,oBAAoB,EAAE;YACnC,+EAA+E;YAC/E,kFAAkF;YAClF,IAAI+K,aAAa9T,QAAQwK,QAAQ,CAACC,WAAW;YAC7C,IAAIqJ,eAAe,UAAU;gBAC3BD,kBAAkBtF,MAAM,CAAC8C,aAAa,GAAG;YAC3C;QACF;QAEA,IAAI,CAACK,UAAUtC,KAAK,CAACyE,oBAAoB;YACvC,OAAO;QACT;IACF;IAEA,IAAItD,eAAeD,gBAAgBtQ;IACnC,IAAIuQ,cAAc;QAChB,IAAIwD,YAAYxD,aAAa/F,QAAQ,CAACC,WAAW;QACjD,IAAIsJ,cAAc,YAAY,CAACT,WAAW7K,0BAA0B,EAAE;YACpE,IAAI,CAAC8H,aAAarC,WAAW,IAAI,CAACqC,aAAa1H,YAAY,EAAE;gBAC3D,yEAAyE;gBACzE,OAAO;YACT;QACF;IACF;IAEA,IAAI2B,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,IACED,aAAa,SACb8I,WAAW9H,gBAAgB,IAC3B,CAAC+E,gBACDvQ,QAAQgN,YAAY,CAAC,gBAAgB,MACrC;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,0CAA0C;AAC1CyG,iBAAiBlF,MAAM,GAAG;IACxB,IAAIA,SACFhO,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC;IAEvE,IAAIyT,cAAc,SAASA,YAAYrU,OAAO;QAC5C,OAAO8T,iBAAiB;YACtB9T,SAASA;YACT4O,QAAQA;QACV;IACF;IAEAyF,YAAY5E,KAAK,GAAGqE;IACpB,OAAOO;AACT;AAEA,gEAAgE;AAChE,IAAIA,cAAcP,iBAAiBlF,MAAM,CAAC,CAAC;AAE3C,SAAS0F,aAAaC,SAAS;IAC7B,4DAA4D;IAC5D,IAAIC,SAAS,SAASA,OAAOvT,IAAI;QAC/B,IAAIA,KAAKM,UAAU,EAAE;YACnB,iEAAiE;YACjE,0CAA0C;YAC1C,OAAOkT,WAAWC,aAAa;QACjC;QAEA,IAAIH,UAAUtT,OAAO;YACnB,2EAA2E;YAC3E,OAAOwT,WAAWC,aAAa;QACjC;QAEA,OAAOD,WAAWE,WAAW;IAC/B;IACA,kEAAkE;IAClE,mGAAmG;IACnGH,OAAOI,UAAU,GAAGJ;IACpB,OAAOA;AACT;AAEA,IAAIK,0BAA0BP,aAAa9E;AAE3C,SAASsF;IACP,IAAI/U,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB+U,iBAAiBhV,KAAKgV,cAAc,EACpCC,sBAAsBjV,KAAKiV,mBAAmB,EAC9CC,WAAWlV,KAAKkV,QAAQ;IAE1B,IAAI,CAACjV,SAAS;QACZA,UAAUT,SAASiB,eAAe;IACpC;IAEA,IAAI0U,eAAeb,YAAY5E,KAAK,CAACb,MAAM,CAAC;QAC1CmF,cAAciB;IAChB;IAEA,IAAI5T,YAAYJ,YAAYhB;IAC5B,2EAA2E;IAC3E,IAAImV,SAAS/T,UAAUgU,gBAAgB,CACrC,AACApV,SACA,AACAyU,WAAWY,WADW,CACC,EAHW,AAIlC,AACAJ,aAAa,QAAQJ,MADM,oBACoBP,aAAaY,eAC5D,AACA,iCADiC;IAInC,IAAIzT,OAAO,EAAE;IAEb,MAAO0T,OAAOG,QAAQ,GAAI;QACxB,IAAIH,OAAOI,WAAW,CAAChU,UAAU,EAAE;YACjC,IAAI2T,aAAaC,OAAOI,WAAW,GAAG;gBACpC9T,KAAKC,IAAI,CAACyT,OAAOI,WAAW;YAC9B;YAEA9T,OAAOA,KAAK+T,MAAM,CAChBV,qBAAqB;gBACnB9U,SAASmV,OAAOI,WAAW,CAAChU,UAAU;gBACtCyT,qBAAqBA;gBACrBC,UAAUA;YACZ;QAEJ,OAAO;YACLxT,KAAKC,IAAI,CAACyT,OAAOI,WAAW;QAC9B;IACF;IAEA,yCAAyC;IACzC,IAAIR,gBAAgB;QAClB,IAAIE,aAAa,OAAO;YACtB,IAAIzF,gBAAgBxP,UAAU;gBAC5ByB,KAAKgU,OAAO,CAACzV;YACf;QACF,OAAO,IAAIkV,aAAalV,UAAU;YAChCyB,KAAKgU,OAAO,CAACzV;QACf;IACF;IAEA,OAAOyB;AACT;AAEA,qDAAqD;AACrD,IAAIiU,aAAa,KAAK;AAEtB,IAAIC,aAAa,KAAK;AAEtB,SAASC;IACP,IAAI,CAACF,YAAY;QACfA,aAAa/I;IACf;IAEA,IAAI,OAAOgJ,eAAe,UAAU;QAClC,OAAOA;IACT;IAEA,kGAAkG;IAClGA,aACE,KACA,2CAA2C;IAC1CD,CAAAA,WAAWzJ,UAAU,GAAG,eAAe,EAAC,IACzC,qCAAqC;IACpCyJ,CAAAA,WAAWvN,aAAa,GAAG,cAAc,EAAC,IAC3C,8FAA8F;IAC9F,iEAAiE;IACjE,uDAAuD;IACvD,WACA,wGAAwG;IACxG,wBAAwB;IACxB,aACA,0CAA0C;IAC1C,gBACA,wCAAwC;IACxC,qCACA,8BAA8B;IAC9B,2BACA,sBAAsB;IACtB,YACCuN,CAAAA,WAAW5N,yBAAyB,GAAG,WAAW,kBAAiB,IACnE4N,CAAAA,WAAWtJ,yBAAyB,GAAG,WAAW,kBAAiB,IACnEsJ,CAAAA,WAAW1L,YAAY,GAAG,aAAa,EAAC,IACzC,8CAA8C;IAC9C,gBACA,gBAAgB;IAChB;IAEF,qGAAqG;IACrG2L,aAAavF,gBAAgBuF;IAE7B,OAAOA;AACT;AAEA,SAASE;IACP,IAAI9V,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB+U,iBAAiBhV,KAAKgV,cAAc,EACpCC,sBAAsBjV,KAAKiV,mBAAmB;IAEhD,IAAIc,YAAYF;IAChB,IAAIG,WAAW/V,QAAQR,gBAAgB,CAACsW;IACxC,iEAAiE;IAEjE,IAAIZ,eAAeb,YAAY5E,KAAK,CAACb,MAAM,CAAC;QAC1CmF,cAAciB;IAChB;IAEA,IAAInM,SAAS,EAAE,CAAC2L,MAAM,CAAC7U,IAAI,CAACoW,UAAUb;IAEtC,yCAAyC;IACzC,IAAIH,kBAAkBG,aAAalV,UAAU;QAC3C6I,OAAO4M,OAAO,CAACzV;IACjB;IAEA,OAAO6I;AACT;AAEA,SAASmN;IACP,IAAIjW,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB+U,iBAAiBhV,KAAKgV,cAAc,EACpCC,sBAAsBjV,KAAKiV,mBAAmB,EAC9CiB,gBAAgBlW,KAAKkV,QAAQ,EAC7BA,WAAWgB,kBAAkB3W,YAAY,UAAU2W;IAErD,IAAI5V,UAAUP,iBAAiB;QAC7BI,OAAO;QACPC,iBAAiB;QACjBC,mBAAmB;QACnBJ,SAASA;IACX;IAEA,IAAI+E,UAAU;QACZ/E,SAASK;QACT0U,gBAAgBA;QAChBC,qBAAqBA;QACrBC,UAAUA;IACZ;IAEA,IAAIA,aAAa,SAAS;QACxB,OAAOY,oBAAoB9Q;IAC7B,OAAO,IAAIkQ,aAAa,YAAYA,aAAa,OAAO;QACtD,OAAOH,qBAAqB/P;IAC9B;IAEA,MAAM,IAAInF,UACR;AAEJ;AAEA,IAAIsW,aAAa,KAAK;AAEtB,iFAAiF;AACjF,6FAA6F;AAC7F,IAAIC,2BAA2B;AAE/B,SAASC;IACP,IAAIrW,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB2O,cAAc5O,KAAK6O,MAAM,EACzBA,SACED,gBAAgBrP,YACZ;QACEuP,SAAS;QACTC,YAAY;QACZC,QAAQ;QACRqC,SAAS;QACT2C,cAAc;IAChB,IACApF;IAER,IAAI,CAACuH,YAAY;QACfA,aAAavJ;IACf;IAEA,IAAItM,UAAUP,iBAAiB;QAC7BI,OAAO;QACPC,iBAAiB;QACjBH,SAASA;IACX;IAEA,IAAIkC,SAASqB,EAAE,CAACX,KAAK,IAAIV,SAASqB,EAAE,CAACf,OAAO,IAAIN,SAASkB,YAAY,GAAG,IAAI;QAC1E,wFAAwF;QACxF,iGAAiG;QACjG,6GAA6G;QAC7G,OAAO;IACT;IAEA,IAAIwN,eAAeD,gBAAgBtQ;IACnC,IAAIuQ,cAAc;QAChB,IAAI1O,SAASqB,EAAE,CAACN,MAAM,IAAIf,SAASqB,EAAE,CAACZ,GAAG,EAAE;YACzC,uFAAuF;YACvF,OAAO;QACT;QAEA,8DAA8D;QAC9D,6DAA6D;QAC7D,IAAI2K,cAAcsD,gBAAgB,GAAG;YACnC,OAAO;QACT;QAEA,IACE,CAAChC,OAAOwC,OAAO,IACdlP,CAAAA,SAASqB,EAAE,CAACX,KAAK,IAAIV,SAASqB,EAAE,CAACN,MAAK,KACvC,CAAC8O,UAAUnB,eACX;YACA,6FAA6F;YAC7F,OAAO;QACT;QAEA,gEAAgE;QAChE,gDAAgD;QAChD,IAAIyF,gBAAgBzF,aAAa/F,QAAQ,CAACC,WAAW;QACrD,IAAIuL,kBAAkB,UAAU;YAC9B,IAAIC,eACDpU,SAASN,IAAI,KAAK,YAAYM,SAASkB,YAAY,IAAI,MACvDlB,SAASN,IAAI,KAAK,WAAWM,SAASkB,YAAY,IAAI;YAEzD,IAAIlB,SAASqB,EAAE,CAACN,MAAM,IAAKf,SAASqB,EAAE,CAACX,KAAK,IAAI,CAAC0T,cAAe;gBAC9D,OAAO;YACT;QACF;IACF;IAEA,IAAIzL,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;IAC3C,IAAI+I,YAAYvG,cAAcjN;IAC9B,IAAI+M,WAAWyG,cAAc,OAAO,OAAOA,aAAa;IAExD,IACE3R,SAASqB,EAAE,CAACP,IAAI,IAChBd,SAASkB,YAAY,IAAI,MACzBwN,gBACAvQ,QAAQuK,eAAe,IACvBiJ,YAAY,GACZ;QACA,yEAAyE;QACzE,iDAAiD;QACjD,OAAO;IACT;IAEA,IAAI0C,4BAA4BnJ,aAAa;IAC7C,IAAIoJ,sBAAsB3C,cAAc,QAAQA,aAAa;IAE7D,+FAA+F;IAC/F,wFAAwF;IACxF,IAAIxT,QAAQ6M,YAAY,CAAC,oBAAoB;QAC3C,wEAAwE;QACxE,OAAOqJ;IACT;IAEA,IAAIJ,yBAAyBrR,IAAI,CAAC+F,aAAauC,aAAa,MAAM;QAChE,OAAO;IACT;IAEA,IAAIlL,SAASqB,EAAE,CAACN,MAAM,IAAIf,SAASqB,EAAE,CAACZ,GAAG,EAAE;QACzC,2EAA2E;QAC3E,+CAA+C;QAC/C,IAAI8T,sBACD5L,aAAa,WAAWxK,QAAQoK,IAAI,KAAK,UAC1CpK,QAAQoK,IAAI,KAAK,cACjBI,aAAa,YACbA,aAAa,cACbxK,QAAQ6M,YAAY,CAAC;QAEvB,IAAI,CAACuJ,qBAAqB;YACxB,IAAI9N,QAAQ7E,OAAOsL,gBAAgB,CAAC/O,SAAS;YAC7CoW,sBAAsB/I,qBAAqB/E;QAC7C;QAEA,IAAI,CAAC8N,qBAAqB;YACxB,OAAO;QACT;IACF;IAEA,IAAI5L,aAAa,SAASgJ,cAAc,MAAM;QAC5C,IACE3R,SAASqB,EAAE,CAACX,KAAK,IAChBV,SAASqB,EAAE,CAACN,MAAM,IAAIf,SAASkB,YAAY,KAAK,GACjD;YACA,wFAAwF;YACxF,OAAO;QACT;IACF;IAEA,IAAIpB,eAAe3B,SAAS,YAAYA,QAAQ6M,YAAY,CAAC,eAAe;QAC1E,IAAIqJ,2BAA2B;YAC7B,iFAAiF;YACjF,OAAO;QACT;QAEA,IAAIlW,QAAQ4E,KAAK,IAAI,CAACiR,WAAW7K,iCAAiC,EAAE;YAClE,iEAAiE;YACjE,yDAAyD;YACzD,2DAA2D;YAC3D,OAAO;QACT;IACF;IAEA,IACER,aAAa,SACbqL,WAAWrK,gBAAgB,IAC3B0K,2BACA;QACA,OAAO;IACT;IAEA,IAAIrU,SAASqB,EAAE,CAACR,OAAO,IAAIb,SAASqB,EAAE,CAACP,IAAI,EAAE;QAC3C,IAAI6H,aAAa,OAAO;YACtB,IAAIqL,WAAWpK,QAAQ,EAAE;gBACvB,6DAA6D;gBAC7D,4DAA4D;gBAC5D,mDAAmD;gBACnD,OAAO;YACT;YAEA,0GAA0G;YAC1G,OAAOzL,QAAQ6M,YAAY,CAAC,gBAAgBsJ;QAC9C;QAEA,IAAInW,QAAQuK,eAAe,EAAE;YAC3B,IAAIsL,WAAW9K,yBAAyB,IAAIoL,qBAAqB;gBAC/D,OAAO;YACT;YAEA,0GAA0G;YAC1G,OAAOnW,QAAQ6M,YAAY,CAAC;QAC9B;IACF;IACA,IAAI7M,QAAQqW,QAAQ,KAAKpX,WAAW;QAClC,OAAOqM,QAAQiD,OAAOmF,YAAY;IACpC;IAEA,IAAIlJ,aAAa,SAAS;QACxB,IAAI,CAACxK,QAAQ6M,YAAY,CAAC,aAAa;YACrC,0GAA0G;YAC1G,OAAO;QACT,OAAO,IAAIhL,SAASqB,EAAE,CAACX,KAAK,EAAE;YAC5B,sEAAsE;YACtE,OAAO;QACT;IACF;IAEA,IAAIiI,aAAa,SAAS;QACxB,IAAI,CAACxK,QAAQ6M,YAAY,CAAC,aAAa;YACrC,IAAIhL,SAASqB,EAAE,CAACR,OAAO,IAAIb,SAASqB,EAAE,CAACP,IAAI,EAAE;gBAC3C,mHAAmH;gBACnH,OAAO;YACT;QACF,OAAO,IAAId,SAASqB,EAAE,CAACX,KAAK,IAAIV,SAASqB,EAAE,CAACT,KAAK,EAAE;YACjD,kFAAkF;YAClF,OAAO;QACT;IACF;IAEA,IAAI+H,aAAa,UAAU;QACzB,IAAI3I,SAASqB,EAAE,CAACX,KAAK,IAAIV,SAASqB,EAAE,CAACN,MAAM,EAAE;YAC3C,uHAAuH;YACvH,OAAO;QACT;IACF;IAEA,IAAI4H,aAAa,UAAU;QACzB,sDAAsD;QACtD,2EAA2E;QAC3E,sEAAsE;QACtE,+DAA+D;QAC/D,OAAO;IACT;IAEA,IAAI,CAAC+D,OAAOE,UAAU,IAAI5M,SAASqB,EAAE,CAACT,KAAK,EAAE;QAC3C,8DAA8D;QAC9D,4CAA4C;QAC5C,IAAI6T,SAAS7S,OAAOsL,gBAAgB,CAAC/O,SAAS;QAC9C,IAAIyN,qBAAqB6I,SAAS;YAChC,OAAOJ;QACT;IACF;IAEA,IAAIrU,SAASqB,EAAE,CAACR,OAAO,IAAIb,SAASqB,EAAE,CAACP,IAAI,EAAE;QAC3C,+DAA+D;QAC/D,+CAA+C;QAC/C,IAAI6H,aAAa,QAAQ;YACvB,IAAIuH,MAAMH,eAAe5R;YACzB,IAAI+R,OAAO9E,cAAc8E,OAAO,GAAG;gBACjC,OAAO;YACT;QACF;QAEA,IAAIwE,UAAU9S,OAAOsL,gBAAgB,CAAC/O,SAAS;QAC/C,IAAIqN,qBAAqBkJ,UAAU;YACjC,2EAA2E;YAC3E,OAAOvW,QAAQqW,QAAQ,IAAI;QAC7B;QAEA,IAAI,CAAC9H,OAAOC,OAAO,IAAIZ,kBAAkB2I,UAAU;YACjD,IAAI/C,cAAc,MAAM;gBACtB,OAAO2C;YACT;YAEA,OACEK,8BAA8BxW,YAC9ByW,yBAAyBzW;QAE7B;QAEA,4DAA4D;QAC5D,2CAA2C;QAC3C,IAAI8N,sBAAsB9N,SAASwK,WAAW;YAC5C,OAAO;QACT;QAEA,IAAIyE,SAASjP,QAAQkP,aAAa;QAClC,IAAID,QAAQ;YACV,IAAIlB,iBAAiBkB,OAAOzE,QAAQ,CAACC,WAAW;YAChD,IAAIuD,cAAcvK,OAAOsL,gBAAgB,CAACE,QAAQ;YAClD,wDAAwD;YACxD,IACEnB,sBAAsBmB,QAAQzE,UAAUuD,gBAAgBC,cACxD;gBACA,OAAO;YACT;YAEA,6EAA6E;YAC7E,yDAAyD;YACzD,IAAIJ,kBAAkBI,cAAc;gBAClC,qCAAqC;gBACrC,OAAOmI;YACT;QACF;IACF;IAEA,2DAA2D;IAC3D,OAAOnW,QAAQqW,QAAQ,IAAI;AAC7B;AAEA,0CAA0C;AAC1CN,gBAAgBxH,MAAM,GAAG;IACvB,IAAIA,SACFhO,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC;IAEvE,IAAImW,aAAa,SAASA,WAAW/W,OAAO;QAC1C,OAAOoW,gBAAgB;YACrBpW,SAASA;YACT4O,QAAQA;QACV;IACF;IAEAmI,WAAWtH,KAAK,GAAG2G;IACnB,OAAOW;AACT;AAEA,IAAIF,gCAAgCrH,gBAAgBC,KAAK,CAACb,MAAM,CAAC;IAC/DC,SAAS;AACX;AACA,IAAIiI,2BAA2BV,gBAAgBxH,MAAM,CAAC;IAAEC,SAAS;AAAK;AAEtE,2DAA2D;AAC3D,IAAIkI,aAAaX,gBAAgBxH,MAAM,CAAC,CAAC;AAEzC,SAASoI;IACP,IAAIjX,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB+U,iBAAiBhV,KAAKgV,cAAc,EACpCC,sBAAsBjV,KAAKiV,mBAAmB,EAC9CC,WAAWlV,KAAKkV,QAAQ;IAE1B,IAAIgC,cAAcF,WAAWtH,KAAK,CAACb,MAAM,CAAC;QACxCmF,cAAciB;IAChB;IAEA,OAAOgB,eAAe;QACpBhW,SAASA;QACT+U,gBAAgBA;QAChBC,qBAAqBA;QACrBC,UAAUA;IACZ,GAAGT,MAAM,CAACyC;AACZ;AAEA,+DAA+D;AAE/D,SAASC,mBAAmBC,CAAC,EAAEC,CAAC;IAC9B,OAAOD,EAAEE,uBAAuB,CAACD,KAAK9W,KAAKgX,2BAA2B,GAClE,CAAC,IACD;AACN;AAEA,SAASC,aAAaxB,QAAQ;IAC5B,OAAOA,SAASyB,IAAI,CAACN;AACvB;AAEA,SAASO,wBAAwBhW,IAAI,EAAEgI,MAAM;IAC3C,6DAA6D;IAC7D,OAAOiG,UAAUjO,MAAM,SAAUpB,OAAO;QACtC,OACEoJ,OAAO4N,uBAAuB,CAAChX,WAAWC,KAAKgX,2BAA2B;IAE9E;AACF;AAEA,SAASI,qBAAqBjW,IAAI,EAAEsU,QAAQ,EAAE4B,cAAc;IAC1D,4EAA4E;IAC5E,wDAAwD;IACxD,IAAIC,aAAa,EAAE;IACnB7B,SAAS/O,OAAO,CAAC,SAAU3G,OAAO;QAChC,IAAIiQ,UAAU;QACd,IAAIgB,SAAS7P,KAAKoM,OAAO,CAACxN;QAE1B,IAAIiR,WAAW,CAAC,GAAG;YACjB,gCAAgC;YAChCA,SAASmG,wBAAwBhW,MAAMpB;YACvCiQ,UAAU;QACZ;QAEA,IAAIgB,WAAW,CAAC,GAAG;YACjB,4CAA4C;YAC5C,6CAA6C;YAC7CA,SAAS7P,KAAKhC,MAAM;QACtB;QAEA,qDAAqD;QACrD,IAAIoY,aAAa5Y,UACf0Y,iBAAiBA,eAAetX,WAAWA;QAE7C,IAAI,CAACwX,WAAWpY,MAAM,EAAE;YACtB,gCAAgC;YAChC;QACF;QAEAmY,WAAWlW,IAAI,CAAC;YACd4P,QAAQA;YACRhB,SAASA;YACTyF,UAAU8B;QACZ;IACF;IAEA,OAAOD;AACT;AAEA,SAASE,wBAAwBrW,IAAI,EAAEmW,UAAU;IAC/C,2DAA2D;IAC3D,4CAA4C;IAC5C,IAAIG,WAAW;IACf,qDAAqD;IACrD,+CAA+C;IAC/CH,WAAWJ,IAAI,CAAC,SAAUL,CAAC,EAAEC,CAAC;QAC5B,OAAOD,EAAE7F,MAAM,GAAG8F,EAAE9F,MAAM;IAC5B;IACAsG,WAAW5Q,OAAO,CAAC,SAAUgR,SAAS;QACpC,qDAAqD;QACrD,IAAIC,SAASD,UAAU1H,OAAO,GAAG,IAAI;QACrC,IAAI4H,OAAO;YAACF,UAAU1G,MAAM,GAAGyG;YAAUE;SAAO,CAACzC,MAAM,CAACwC,UAAUjC,QAAQ;QAC1EtU,KAAK0W,MAAM,CAACC,KAAK,CAAC3W,MAAMyW;QACxBH,YAAYC,UAAUjC,QAAQ,CAACtW,MAAM,GAAGwY;IAC1C;AACF;AAEA,SAASI;IACP,IAAItY,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEa,OAAO1B,KAAK0B,IAAI,EAChBsU,WAAWhW,KAAKgW,QAAQ,EACxB4B,iBAAiB5X,KAAK4X,cAAc;IAEtC,0DAA0D;IAC1D,IAAIW,QAAQ7W,KAAK/B,KAAK,CAAC;IACvB,mEAAmE;IACnE,IAAI6Y,YAAYtZ,UAAU8W,UAAUrW,KAAK,CAAC;IAC1C6X,aAAagB;IACb,qEAAqE;IACrE,0CAA0C;IAC1C,IAAIX,aAAaF,qBAAqBY,OAAOC,WAAWZ;IACxD,iFAAiF;IACjFG,wBAAwBQ,OAAOV;IAC/B,OAAOU;AACT;AAEA,IAAIE,eAAgB;IAClB,SAASC,iBAAiBhP,MAAM,EAAEiP,KAAK;QACrC,IAAK,IAAI7I,IAAI,GAAGA,IAAI6I,MAAMjZ,MAAM,EAAEoQ,IAAK;YACrC,IAAI8I,aAAaD,KAAK,CAAC7I,EAAE;YACzB8I,WAAWC,UAAU,GAAGD,WAAWC,UAAU,IAAI;YACjDD,WAAWE,YAAY,GAAG;YAC1B,IAAI,WAAWF,YAAYA,WAAWG,QAAQ,GAAG;YACjDpT,OAAOqT,cAAc,CAACtP,QAAQkP,WAAW9S,GAAG,EAAE8S;QAChD;IACF;IACA,OAAO,SAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW;QACnD,IAAID,YAAYR,iBAAiBO,YAAY9N,SAAS,EAAE+N;QACxD,IAAIC,aAAaT,iBAAiBO,aAAaE;QAC/C,OAAOF;IACT;AACF;AAEA,SAASG,gBAAgBC,QAAQ,EAAEJ,WAAW;IAC5C,IAAI,CAAEI,CAAAA,oBAAoBJ,WAAU,GAAI;QACtC,MAAM,IAAIpZ,UAAU;IACtB;AACF;AAEA,IAAIyZ,OAAQ;IACV,SAASA,KAAKrZ,OAAO;QACnBmZ,gBAAgB,IAAI,EAAEE;QAEtB,IAAI,CAACjY,SAAS,GAAGJ,YAAYhB;QAC7B,IAAI,CAACsZ,IAAI,GAAG,CAAC;IACf;IAEAd,aAAaa,MAAM;QACjB;YACExT,KAAK;YACLO,OAAO,SAASmT,YAAY3X,IAAI;gBAC9B,IAAI,CAAC,IAAI,CAAC0X,IAAI,CAAC1X,KAAK,EAAE;oBACpB,mDAAmD;oBACnD,+CAA+C;oBAC/C,IAAI,CAAC4X,YAAY,CAAC5X;gBACpB;gBAEA,OAAO,IAAI,CAAC0X,IAAI,CAAC1X,KAAK;YACxB;QACF;QACA;YACEiE,KAAK;YACLO,OAAO,SAASoT,aAAa5X,IAAI;gBAC/B,IAAIgE,MAAMoM,aAAapQ,MAAM,IAAI,CAACR,SAAS;gBAC3C,IAAI,CAACwE,KAAK;oBACR,mEAAmE;oBACnE;gBACF;gBAEA,IAAI,CAAC0T,IAAI,CAAC1T,IAAIhE,IAAI,CAAC,GAAGoV,cAAc;oBAAEhX,SAAS4F;gBAAI;YACrD;QACF;QACA;YACEC,KAAK;YACLO,OAAO,SAASqT,qBAAqB1D,QAAQ;gBAC3C,qDAAqD;gBACrD,2CAA2C;gBAC3C,OAAOA,SAASvB,MAAM,CAAC,SAAUnU,OAAO;oBACtC,IAAIwK,WAAWxK,QAAQwK,QAAQ,CAACC,WAAW;oBAC3C,IAAID,aAAa,QAAQ;wBACvB,OAAO;oBACT;oBAEA,IAAIjF,MAAMvF,QAAQS,UAAU;oBAC5B,IAAI,CAAC,IAAI,CAACwY,IAAI,CAAC1T,IAAIhE,IAAI,CAAC,EAAE;wBACxB,IAAI,CAAC0X,IAAI,CAAC1T,IAAIhE,IAAI,CAAC,GAAG,EAAE;oBAC1B;oBAEA,IAAI,CAAC0X,IAAI,CAAC1T,IAAIhE,IAAI,CAAC,CAACF,IAAI,CAACrB;oBACzB,OAAO;gBACT,GAAG,IAAI;YACT;QACF;KACD;IAED,OAAOgZ;AACT;AAEA,SAASK,SAAS3D,QAAQ,EAAE/V,OAAO;IACjC,4DAA4D;IAC5D,4DAA4D;IAC5D,4CAA4C;IAC5C,IAAI2Z,UAAU3Z,QAAQR,gBAAgB,CAAC;IACvC,IAAI8Z,OAAO,IAAID,KAAKrZ;IAEpB,qDAAqD;IACrD,2CAA2C;IAC3C,IAAIuY,YAAYe,KAAKG,oBAAoB,CAAC1D;IAE1C,IAAI,CAAC4D,QAAQla,MAAM,EAAE;QACnB,sDAAsD;QACtD,4CAA4C;QAC5C,OAAO8Y;IACT;IAEA,OAAOF,gBAAgB;QACrB5W,MAAM8W;QACNxC,UAAU4D;QACVhC,gBAAgB,SAASA,eAAeiC,KAAK;YAC3C,IAAIhY,OAAOgY,MAAMvM,YAAY,CAAC,UAAU3N,KAAK,CAAC;YAC9C,OAAO4Z,KAAKC,WAAW,CAAC3X;QAC1B;IACF;AACF;AAEA,IAAIiY,iBAAkB;IACpB,SAASpB,iBAAiBhP,MAAM,EAAEiP,KAAK;QACrC,IAAK,IAAI7I,IAAI,GAAGA,IAAI6I,MAAMjZ,MAAM,EAAEoQ,IAAK;YACrC,IAAI8I,aAAaD,KAAK,CAAC7I,EAAE;YACzB8I,WAAWC,UAAU,GAAGD,WAAWC,UAAU,IAAI;YACjDD,WAAWE,YAAY,GAAG;YAC1B,IAAI,WAAWF,YAAYA,WAAWG,QAAQ,GAAG;YACjDpT,OAAOqT,cAAc,CAACtP,QAAQkP,WAAW9S,GAAG,EAAE8S;QAChD;IACF;IACA,OAAO,SAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW;QACnD,IAAID,YAAYR,iBAAiBO,YAAY9N,SAAS,EAAE+N;QACxD,IAAIC,aAAaT,iBAAiBO,aAAaE;QAC/C,OAAOF;IACT;AACF;AAEA,SAASc,kBAAkBV,QAAQ,EAAEJ,WAAW;IAC9C,IAAI,CAAEI,CAAAA,oBAAoBJ,WAAU,GAAI;QACtC,MAAM,IAAIpZ,UAAU;IACtB;AACF;AAEA,IAAIma,UAAW;IACb,SAASA,QAAQ/Z,OAAO,EAAEga,YAAY;QACpCF,kBAAkB,IAAI,EAAEC;QAExB,sCAAsC;QACtC,IAAI,CAAC/Z,OAAO,GAAGA;QACf,2CAA2C;QAC3C,IAAI,CAACga,YAAY,GAAGA;QACpB,qDAAqD;QACrD,IAAI,CAACC,WAAW,GAAG;QACnB,sDAAsD;QACtD,IAAI,CAACC,MAAM,GAAG,CAAC;QACf,qDAAqD;QACrD,IAAI,CAACC,UAAU,GAAG,EAAE;QACpB,gCAAgC;QAChC,IAAI,CAACC,KAAK,GAAG,CAAC;QACd,sDAAsD;QACtD,IAAI,CAACrE,QAAQ,GAAG,CAAC;IACnB;IAEA,oDAAoD;IAEpD8D,eAAeE,SAAS;QACtB;YACElU,KAAK;YACLO,OAAO,SAASiU,cAActZ,IAAI;gBAChC,IAAIA,KAAKuZ,UAAU,EAAE;oBACnB;gBACF;gBAEA,4DAA4D;gBAC5DvZ,KAAKuZ,UAAU,GAAG,YAAY,IAAI,CAACL,WAAW;gBAC9C,IAAI,CAACG,KAAK,CAACrZ,KAAKuZ,UAAU,CAAC,GAAGvZ;gBAE9B,gCAAgC;gBAChC,IAAIwZ,aAAa5Z,cAAc;oBAAEX,SAASe;gBAAK;gBAC/C,IAAIwZ,YAAY;oBACd,IAAI,CAACF,aAAa,CAACE;oBACnB,IAAI,CAACC,mBAAmB,CAACzZ,MAAMwZ;gBACjC,OAAO;oBACL,IAAI,CAACJ,UAAU,CAACzY,IAAI,CAACX;gBACvB;YACF;QAGF;QACA;YACE8E,KAAK;YACLO,OAAO,SAASoU,oBAAoBzZ,IAAI,EAAEuO,MAAM;gBAC9C,IAAI,CAAC,IAAI,CAAC4K,MAAM,CAAC5K,OAAOgL,UAAU,CAAC,EAAE;oBACnC,IAAI,CAACJ,MAAM,CAAC5K,OAAOgL,UAAU,CAAC,GAAG,EAAE;gBACrC;gBAEA,IAAI,CAACJ,MAAM,CAAC5K,OAAOgL,UAAU,CAAC,CAAC5Y,IAAI,CAACX;YACtC;QAGF;QACA;YACE8E,KAAK;YACLO,OAAO,SAASqU,iBAAiBpa,OAAO,EAAEU,IAAI;gBAC5C,IAAI,CAAC,IAAI,CAACgV,QAAQ,CAAChV,KAAKuZ,UAAU,CAAC,EAAE;oBACnC,IAAI,CAACvE,QAAQ,CAAChV,KAAKuZ,UAAU,CAAC,GAAG,EAAE;gBACrC;gBAEA,IAAI,CAACvE,QAAQ,CAAChV,KAAKuZ,UAAU,CAAC,CAAC5Y,IAAI,CAACrB;YACtC;QAKF;QACA;YACEwF,KAAK;YACLO,OAAO,SAASsU,gBAAgB3E,QAAQ;gBACtC,OAAOA,SAASvB,MAAM,CAAC,SAAUnU,OAAO;oBACtC,IAAIU,OAAOJ,cAAc;wBAAEX,SAASK;oBAAQ;oBAC5C,IAAI,CAACU,MAAM;wBACT,OAAO;oBACT;oBAEA,IAAI,CAACsZ,aAAa,CAACtZ;oBACnB,IAAI,CAAC0Z,gBAAgB,CAACpa,SAASU;oBAC/B,OAAO;gBACT,GAAG,IAAI;YACT;QAIF;QACA;YACE8E,KAAK;YACLO,OAAO,SAASoR,KAAKzB,QAAQ;gBAC3B,IAAIwC,YAAY,IAAI,CAACoC,YAAY,CAAC5E;gBAClCwC,YAAY,IAAI,CAACqC,aAAa,CAACrC;gBAC/B,IAAI,CAACsC,QAAQ;gBACb,OAAOtC;YACT;QAIF;QACA;YACE1S,KAAK;YACLO,OAAO,SAASuU,aAAa5E,QAAQ;gBACnCrQ,OAAOC,IAAI,CAAC,IAAI,CAACyU,KAAK,EAAEpT,OAAO,CAAC,SAAUsT,UAAU;oBAClD,IAAIhC,QAAQ,IAAI,CAACvC,QAAQ,CAACuE,WAAW;oBACrC,IAAI/B,YAAY,IAAI,CAAC2B,MAAM,CAACI,WAAW;oBACvC,IAAIQ,WAAW,IAAI,CAACV,KAAK,CAACE,WAAW,CAAC/Y,UAAU;oBAChD,IAAI,CAACwU,QAAQ,CAACuE,WAAW,GAAG,IAAI,CAACS,MAAM,CAACzC,OAAOC,WAAWuC;gBAC5D,GAAG,IAAI;gBAEP,OAAO,IAAI,CAACC,MAAM,CAAChF,UAAU,IAAI,CAACoE,UAAU,EAAE,IAAI,CAACna,OAAO;YAC5D;QACF;QACA;YACE6F,KAAK;YACLO,OAAO,SAAS2U,OAAOtZ,IAAI,EAAEsU,QAAQ,EAAE/V,OAAO;gBAC5C,IAAIgb,SAAS3C,gBAAgB;oBAC3B5W,MAAMA;oBACNsU,UAAUA;gBACZ;gBAEA,OAAO,IAAI,CAACiE,YAAY,CAACgB,QAAQhb;YACnC;QACF;QACA;YACE6F,KAAK;YACLO,OAAO,SAASwU,cAAc7E,QAAQ;gBACpC,OAAOsC,gBAAgB;oBACrB5W,MAAMsU;oBACNA,UAAU,IAAI,CAACoE,UAAU;oBACzBxC,gBAAgB,IAAI,CAACsD,mBAAmB,CAACC,IAAI,CAAC,IAAI;gBACpD;YACF;QACF;QACA;YACErV,KAAK;YACLO,OAAO,SAAS6U,oBAAoBla,IAAI;gBACtC,IAAIia,SAAS3C,gBAAgB;oBAC3B5W,MAAM,IAAI,CAACsU,QAAQ,CAAChV,KAAKuZ,UAAU,CAAC;oBACpCvE,UAAU,IAAI,CAACmE,MAAM,CAACnZ,KAAKuZ,UAAU,CAAC;oBACtC3C,gBAAgB,IAAI,CAACsD,mBAAmB,CAACC,IAAI,CAAC,IAAI;gBACpD;gBAEA,IAAIrH,YAAYvG,cAAcvM;gBAC9B,IAAI8S,cAAc,QAAQA,YAAY,CAAC,GAAG;oBACxC,OAAO;wBAAC9S;qBAAK,CAACyU,MAAM,CAACwF;gBACvB;gBAEA,OAAOA;YACT;QACF;QACA;YACEnV,KAAK;YACLO,OAAO,SAASyU;gBACd,wEAAwE;gBACxEnV,OAAOC,IAAI,CAAC,IAAI,CAACyU,KAAK,EAAEpT,OAAO,CAAC,SAAUnB,GAAG;oBAC3C,OAAO,IAAI,CAACuU,KAAK,CAACvU,IAAI,CAACyU,UAAU;gBACnC,GAAG,IAAI;YACT;QACF;KACD;IAED,OAAOP;AACT;AAEA,SAASoB,aAAapF,QAAQ,EAAE/V,OAAO,EAAEga,YAAY;IACnD,IAAIoB,UAAU,IAAIrB,QAAQ/Z,SAASga;IACnC,IAAIzB,YAAY6C,QAAQV,eAAe,CAAC3E;IAExC,IAAIwC,UAAU9Y,MAAM,KAAKsW,SAAStW,MAAM,EAAE;QACxC,iDAAiD;QACjD,OAAOua,aAAajE;IACtB;IAEA,OAAOqF,QAAQ5D,IAAI,CAACe;AACtB;AAEA,SAAS8C,aAAatF,QAAQ;IAC5B,kEAAkE;IAClE,yHAAyH;IACzH,qCAAqC;IACrC,0FAA0F;IAC1F,0EAA0E;IAE1E,wEAAwE;IACxE,iFAAiF;IACjF,sEAAsE;IACtE,qEAAqE;IACrE,8DAA8D;IAC9D,uFAAuF;IAEvF,8FAA8F;IAC9F,0EAA0E;IAE1E,IAAInQ,MAAM,CAAC;IACX,IAAI0V,UAAU,EAAE;IAChB,IAAIC,SAASxF,SAASvB,MAAM,CAAC,SAAUnU,OAAO;QAC5C,4EAA4E;QAC5E,IAAIqW,WAAWrW,QAAQqW,QAAQ;QAC/B,IAAIA,aAAapX,WAAW;YAC1BoX,WAAWpJ,cAAcjN;QAC3B;QAEA,2CAA2C;QAC3C,IAAIqW,YAAY,KAAKA,aAAa,QAAQA,aAAapX,WAAW;YAChE,OAAO;QACT;QAEA,IAAI,CAACsG,GAAG,CAAC8Q,SAAS,EAAE;YAClB,uFAAuF;YACvF9Q,GAAG,CAAC8Q,SAAS,GAAG,EAAE;YAClB,uCAAuC;YACvC4E,QAAQ5Z,IAAI,CAACgV;QACf;QAEA,sCAAsC;QACtC9Q,GAAG,CAAC8Q,SAAS,CAAChV,IAAI,CAACrB;QACnB,wDAAwD;QACxD,OAAO;IACT;IAEA,+BAA+B;IAC/B,kDAAkD;IAClD,+CAA+C;IAC/C,IAAIkY,YAAY+C,QACb9D,IAAI,GACJ5R,GAAG,CAAC,SAAU8Q,QAAQ;QACrB,OAAO9Q,GAAG,CAAC8Q,SAAS;IACtB,GACC8E,WAAW,CAAC,SAAUC,QAAQ,EAAEC,OAAO;QACtC,OAAOA,QAAQlG,MAAM,CAACiG;IACxB,GAAGF;IAEL,OAAOhD;AACT;AAEA,IAAIoD,aAAa,KAAK;AAEtB,SAASC,uBAAuB7F,QAAQ,EAAE/V,OAAO;IAC/C,IAAI6b,MAAM9F,SAASlI,OAAO,CAAC7N;IAC3B,IAAI6b,MAAM,GAAG;QACX,IAAIC,MAAM/F,SAASoC,MAAM,CAAC0D,KAAK;QAC/B,OAAOC,IAAItG,MAAM,CAACO;IACpB;IAEA,OAAOA;AACT;AAEA,SAASiE,aAAajE,QAAQ,EAAE+E,QAAQ;IACtC,IAAIa,WAAWrP,4BAA4B,EAAE;QAC3C,iEAAiE;QACjE,8DAA8D;QAC9D,gDAAgD;QAChDyJ,WAAW2D,SAAS3D,UAAU+E;IAChC;IAEA/E,WAAWsF,aAAatF;IACxB,OAAOA;AACT;AAEA,SAASgG;IACP,IAAIhc,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvEZ,UAAUD,KAAKC,OAAO,EACtB+U,iBAAiBhV,KAAKgV,cAAc,EACpCC,sBAAsBjV,KAAKiV,mBAAmB,EAC9CC,WAAWlV,KAAKkV,QAAQ;IAE1B,IAAI,CAAC0G,YAAY;QACfA,aAAahP;IACf;IAEA,IAAImO,WAAW7b,UAAUe,QAAQ,CAAC,EAAE,IAAIT,SAASiB,eAAe;IAChE,IAAIuV,WAAWiB,cAAc;QAC3BhX,SAAS8a;QACT/F,gBAAgBA;QAChBC,qBAAqBA;QACrBC,UAAUA;IACZ;IAEA,IAAI1V,SAAS4E,IAAI,CAAC6X,gBAAgB,IAAI9Z,SAASqB,EAAE,CAACX,KAAK,EAAE;QACvD,wCAAwC;QACxC,oDAAoD;QACpDmT,WAAWoF,aAAapF,UAAU+E,UAAUd;IAC9C,OAAO;QACLjE,WAAWiE,aAAajE,UAAU+E;IACpC;IAEA,IAAI/F,gBAAgB;QAClB,2DAA2D;QAC3D,0BAA0B;QAC1BgB,WAAW6F,uBAAuB7F,UAAU+E;IAC9C;IAEA,OAAO/E;AACT;AAEA,qFAAqF;AACrF,8EAA8E;AAC9E,yDAAyD;AACzD,mDAAmD;AACnD,iDAAiD;AAEjD,IAAIkG,UAAU;IACZ,gBAAgB;IAChBC,KAAK;IAEL,aAAa;IACbC,MAAM;IACNC,IAAI;IACJC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACR,WAAW;IACXC,UAAU;IACV,aAAa;IACbC,KAAK;IACLC,MAAM;IAEN,SAAS;IACTC,OAAO;IACPC,QAAQ;IACRC,OAAO;IAEP,WAAW;IACXC,OAAO;IACPC,UAAU;IACV,aAAa;IACbC,MAAM;IACNC,KAAK;IACLC,MAAM;IACN,kBAAkB;IAClB,+CAA+C;IAC/C,6CAA6C;IAC7CC,OAAO;IAEP,uBAAuB;IACvBC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IAEX,oEAAoE;IACpEC,QAAQ;QACN,IAAI;YAAC;YAAI;YAAI;SAAI;IACnB;AACF;AAEA,4BAA4B;AAC5B,sCAAsC;AACtC,IAAK,IAAIC,IAAI,GAAGA,IAAI,IAAIA,IAAK;IAC3BvB,OAAO,CAAC,MAAMuB,EAAE,GAAGA,IAAI;AACzB;AAEA,qCAAqC;AACrC,wCAAwC;AACxC,IAAK,IAAIC,KAAK,GAAGA,KAAK,IAAIA,KAAM;IAC9B,IAAIC,OAAOD,KAAK;IAChB,IAAIE,UAAUF,KAAK;IACnBxB,OAAO,CAACwB,GAAG,GAAGC;IACdzB,OAAO,CAAC,SAASwB,GAAG,GAAGE;IACvB1B,QAAQsB,MAAM,CAACG,KAAK,GAAG;QAACC;KAAQ;AAClC;AAEA,6BAA6B;AAC7B,IAAK,IAAIC,MAAM,GAAGA,MAAM,IAAIA,MAAO;IACjC,IAAIC,QAAQD,MAAM;IAClB,IAAIE,SAASje,OAAOke,YAAY,CAACF,OAAO/S,WAAW;IACnDmR,OAAO,CAAC6B,OAAO,GAAGD;AACpB;AAEA,IAAIG,WAAW;IACbf,KAAK;IACLD,MAAM;IACNE,MAAM;IACNJ,OAAO;AACT;AAEA,IAAImB,mBAAmBvY,OAAOC,IAAI,CAACqY,UAAUpY,GAAG,CAAC,SAAUhE,IAAI;IAC7D,OAAOoc,QAAQ,CAACpc,KAAK;AACvB;AAEA,SAASsc,wBAAwBC,eAAe;IAC9C,IAAI/X,QAAQ+X,kBAAkB,OAAO;IACrC,OAAO;QACLC,QAAQhY;QACRiY,SAASjY;QACTkY,SAASlY;QACTmY,UAAUnY;IACZ;AACF;AAEA,SAASoY,iBAAiBC,SAAS;IACjC,IAAIN,kBAAkBM,UAAU5Q,OAAO,CAAC,SAAS,CAAC;IAClD,IAAI6Q,WAAWR,wBAAwBC;IAEvCM,UAAUzX,OAAO,CAAC,SAAU2X,KAAK;QAC/B,IAAIA,UAAU,KAAK;YACjB,4CAA4C;YAC5C;QACF;QAEA,+BAA+B;QAC/B,IAAIvY,QAAQ;QACZ,IAAIiK,WAAWsO,MAAMjf,KAAK,CAAC,GAAG;QAC9B,IAAI2Q,aAAa,KAAK;YACpB,2CAA2C;YAC3CjK,QAAQ;QACV,OAAO,IAAIiK,aAAa,KAAK;YAC3B,sCAAsC;YACtCjK,QAAQ;QACV;QAEA,IAAIA,UAAU,MAAM;YAClB,yCAAyC;YACzCuY,QAAQA,MAAMjf,KAAK,CAAC;QACtB;QAEA,IAAIkf,eAAeZ,QAAQ,CAACW,MAAM;QAClC,IAAI,CAACC,cAAc;YACjB,MAAM,IAAIhf,UAAU,uBAAuB+e,QAAQ;QACrD;QAEAD,QAAQ,CAACE,aAAa,GAAGxY;IAC3B;IAEA,OAAOsY;AACT;AAEA,SAASG,WAAWhZ,GAAG;IACrB,IAAI6X,OAAOzB,OAAO,CAACpW,IAAI,IAAI2H,SAAS3H,KAAK;IACzC,IAAI,CAAC6X,QAAQ,OAAOA,SAAS,YAAYjQ,MAAMiQ,OAAO;QACpD,MAAM,IAAI9d,UAAU,kBAAkBiG,MAAM;IAC9C;IAEA,OAAO;QAAC6X;KAAK,CAAClI,MAAM,CAACyG,QAAQsB,MAAM,CAACG,KAAK,IAAI,EAAE;AACjD;AAEA,SAASoB,eAAeJ,QAAQ,EAAEK,KAAK;IACrC,wBAAwB;IACxB,OAAO,CAACd,iBAAiBnc,IAAI,CAAC,SAAUkd,IAAI;QAC1C,2BAA2B;QAC3B,OACE,OAAON,QAAQ,CAACM,KAAK,KAAK,aAC1BrT,QAAQoT,KAAK,CAACC,KAAK,MAAMN,QAAQ,CAACM,KAAK;IAE3C;AACF;AAEA,SAASC,WAAWC,IAAI;IACtB,OAAOA,KAAK3O,KAAK,CAAC,OAAO3K,GAAG,CAAC,SAAUuZ,KAAK;QAC1C,IAAIC,SAASD,MAAM5O,KAAK,CAAC;QACzB,IAAI8O,aAAab,iBAAiBY,OAAO1f,KAAK,CAAC,GAAG,CAAC;QACnD,IAAI4f,YAAYT,WAAWO,OAAO1f,KAAK,CAAC,CAAC;QACzC,OAAO;YACL6f,UAAUD;YACVb,WAAWY;YACXP,gBAAgBA,eAAe5D,IAAI,CAAC,MAAMmE;QAC5C;IACF;AACF;AAEA,sDAAsD;AACtD,8EAA8E;AAE9E,8GAA8G;AAC9G;;;;AAIA,GAEA,SAASG;IACP,IAAIzf,OACAa,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC,GACvE0O,SAASvP,KAAKuP,MAAM,EACpBjP,UAAUN,KAAKM,OAAO,EACtBof,cAAc1f,KAAK0f,WAAW;IAEhC,IAAInQ,QAAQ;QACV,OAAO,SAASoQ,UAAUze,IAAI;YAC5B,OAAO0K,QACJ8T,eAAexe,SAASqO,UACvBA,OAAO+H,uBAAuB,CAACpW,QAC7BX,KAAKqf,8BAA8B;QAE3C;IACF,OAAO,IAAItf,SAAS;QAClB,OAAO,SAASuf,WAAW3e,IAAI;YAC7B,OAAO0K,QACJ8T,eAAepf,YAAYY,QAC1BA,KAAKoW,uBAAuB,CAAChX,WAC3BC,KAAKqf,8BAA8B;QAE3C;IACF;IAEA,MAAM,IAAI/f,UACR;AAEJ;AAEA,uFAAuF;AACvF,2EAA2E;AAE3E,SAASigB;IACP,IAAIja,MACFhF,UAAUnB,MAAM,GAAG,KAAKmB,SAAS,CAAC,EAAE,KAAKtB,YAAYsB,SAAS,CAAC,EAAE,GAAG,CAAC;IAEvE,IAAIkf,WAAW,CAAC;IAEhB,IAAI9f,UAAUf,UAAU2G,IAAI5F,OAAO,CAAC,CAAC,EAAE,IAAIT,SAASiB,eAAe;IACnE,OAAOoF,IAAI5F,OAAO;IAClB,IAAIwU,SAASvV,UAAU2G,IAAI4O,MAAM;IACjC,OAAO5O,IAAI4O,MAAM;IAEjB,IAAIuL,UAAUra,OAAOC,IAAI,CAACC;IAC1B,IAAI,CAACma,QAAQtgB,MAAM,EAAE;QACnB,MAAM,IAAIG,UAAU;IACtB;IAEA,IAAIogB,kBAAkB,SAASA,gBAAgBjB,KAAK;QAClDA,MAAMQ,QAAQ,CAACvY,OAAO,CAAC,SAAU0W,IAAI;YACnC,IAAI,CAACoC,QAAQ,CAACpC,KAAK,EAAE;gBACnBoC,QAAQ,CAACpC,KAAK,GAAG,EAAE;YACrB;YAEAoC,QAAQ,CAACpC,KAAK,CAAChc,IAAI,CAACqd;QACtB;IACF;IAEAgB,QAAQ/Y,OAAO,CAAC,SAAUkY,IAAI;QAC5B,IAAI,OAAOtZ,GAAG,CAACsZ,KAAK,KAAK,YAAY;YACnC,MAAM,IAAItf,UACR,+BAA+Bsf,OAAO;QAE1C;QAEA,IAAIe,cAAc,SAASA,YAAYlB,KAAK;YAC1CA,MAAMnP,QAAQ,GAAGhK,GAAG,CAACsZ,KAAK;YAC1B,OAAOH;QACT;QAEAE,WAAWC,MAAMtZ,GAAG,CAACqa,aAAajZ,OAAO,CAACgZ;IAC5C;IAEA,IAAIE,gBAAgB,SAASA,cAAcnB,KAAK;QAC9C,IAAIA,MAAMoB,gBAAgB,EAAE;YAC1B;QACF;QAEA,IAAI3L,OAAO/U,MAAM,EAAE;YACjB,gDAAgD;YAChD,IAAI2gB,oBAAoBZ,oBAAoB;gBAC1Cnf,SAAS0e,MAAMtV,MAAM;gBACrBgW,aAAa;YACf;YACA,IAAIjL,OAAO1S,IAAI,CAACse,oBAAoB;gBAClC;YACF;QACF;QAEA,IAAIva,MAAMkZ,MAAMsB,OAAO,IAAItB,MAAMuB,KAAK;QACtC,IAAI,CAACR,QAAQ,CAACja,IAAI,EAAE;YAClB;QACF;QAEAia,QAAQ,CAACja,IAAI,CAACmB,OAAO,CAAC,SAAUuZ,MAAM;YACpC,IAAI,CAACA,OAAOzB,cAAc,CAACC,QAAQ;gBACjC;YACF;YAEAwB,OAAO3Q,QAAQ,CAACjQ,IAAI,CAACK,SAAS+e,OAAOyB;QACvC;IACF;IAEAxgB,QAAQygB,gBAAgB,CAAC,WAAWP,eAAe;IAEnD,IAAIM,YAAY,SAASA;QACvBxgB,QAAQ0gB,mBAAmB,CAAC,WAAWR,eAAe;IACxD;IAEA,OAAO;QAAEM,WAAWA;IAAU;AAChC;AAEe,wCAAU,KAAA;IAAA,IAAA,EAAExgB,OAAO,EAAE,GAAX,UAAA,KAAA,IAAc,CAAC,IAAf;IACvB,IAAI,CAACA,SAAS;QACZA,UAAUT,SAASiB,eAAe;IACpC;IAEA,wEAAwE;IACxE,qEAAqE;IACrE,0EAA0E;IAC1Eub;IAEA,OAAO8D,QAAQ;QACb,oDAAoD;QACpD,sDAAsD;QACtD,mBAAmB,SAASc,YAAY5B,KAAK;YAC3C,oDAAoD;YACpDA,MAAM6B,cAAc;YAEpB,IAAIC,WAAW9E,iBAAiB;gBAC9B/b,SAASA;YACX;YAEA,IAAI8gB,WAAW/B,MAAMR,QAAQ;YAC7B,IAAIwC,QAAQF,QAAQ,CAAC,EAAE;YACvB,IAAIG,OAAOH,QAAQ,CAACA,SAASphB,MAAM,GAAG,EAAE;YAExC,2CAA2C;YAC3C,IAAIwhB,SAASH,WAAWC,QAAQC;YAChC,IAAIvX,SAASqX,WAAWE,OAAOD;YAC/B,IAAI5f,gBAAgB8f,SAAS;gBAC3BxX,OAAOxE,KAAK;gBACZ;YACF;YAEA,uCAAuC;YACvC,IAAIic,eAAe,KAAK;YACxB,IAAIC,QAAQN,SAAS/e,IAAI,CAAC,SAAUzB,OAAO,EAAE+gB,KAAK;gBAChD,IAAI,CAACjgB,gBAAgBd,UAAU;oBAC7B,OAAO;gBACT;gBAEA6gB,eAAeE;gBACf,OAAO;YACT;YAEA,IAAI,CAACD,OAAO;gBACV,oDAAoD;gBACpDJ,MAAM9b,KAAK;gBACX;YACF;YAEA,uDAAuD;YACvD,IAAIqM,SAASwP,WAAW,CAAC,IAAI;YAC7BD,QAAQ,CAACK,eAAe5P,OAAO,CAACrM,KAAK;QACvC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 3358, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3364, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Overlay/body-locker.ts"], "sourcesContent": ["let previousBodyPaddingRight: string | undefined\nlet previousBodyOverflowSetting: string | undefined\n\nlet activeLocks = 0\n\nexport function lock() {\n  setTimeout(() => {\n    if (activeLocks++ > 0) {\n      return\n    }\n\n    const scrollBarGap =\n      window.innerWidth - document.documentElement.clientWidth\n\n    if (scrollBarGap > 0) {\n      previousBodyPaddingRight = document.body.style.paddingRight\n      document.body.style.paddingRight = `${scrollBarGap}px`\n    }\n\n    previousBodyOverflowSetting = document.body.style.overflow\n    document.body.style.overflow = 'hidden'\n  })\n}\n\nexport function unlock() {\n  setTimeout(() => {\n    if (activeLocks === 0 || --activeLocks !== 0) {\n      return\n    }\n\n    if (previousBodyPaddingRight !== undefined) {\n      document.body.style.paddingRight = previousBodyPaddingRight\n      previousBodyPaddingRight = undefined\n    }\n\n    if (previousBodyOverflowSetting !== undefined) {\n      document.body.style.overflow = previousBodyOverflowSetting\n      previousBodyOverflowSetting = undefined\n    }\n  })\n}\n"], "names": ["previousBodyPaddingRight", "previousBodyOverflowSetting", "activeLocks", "lock", "setTimeout", "scrollBarGap", "window", "innerWidth", "document", "documentElement", "clientWidth", "body", "style", "paddingRight", "overflow", "unlock", "undefined"], "mappings": ";;;;AAAA,IAAIA;AACJ,IAAIC;AAEJ,IAAIC,cAAc;AAEX,SAASC;IACdC,WAAW;QACT,IAAIF,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAMG,eACJC,OAAOC,UAAU,GAAGC,SAASC,eAAe,CAACC,WAAW;QAE1D,IAAIL,eAAe,GAAG;YACpBL,2BAA2BQ,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY;YAC3DL,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAI,KAAER,eAAa;QACrD;QAEAJ,8BAA8BO,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ;QAC1DN,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG;IACjC;AACF;AAEO,SAASC;IACdX,WAAW;QACT,IAAIF,gBAAgB,KAAK,EAAEA,gBAAgB,GAAG;YAC5C;QACF;QAEA,IAAIF,6BAA6BgB,WAAW;YAC1CR,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAGb;YACnCA,2BAA2BgB;QAC7B;QAEA,IAAIf,gCAAgCe,WAAW;YAC7CR,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAGb;YAC/BA,8BAA8Be;QAChC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 3400, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3406, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Overlay/Overlay.tsx"], "sourcesContent": ["import allyTrap from './maintain--tab-focus'\nimport * as React from 'react'\nimport { lock, unlock } from './body-locker'\n\nexport type OverlayProps = {\n  children?: React.ReactNode\n  className?: string\n  fixed?: boolean\n}\n\nconst Overlay: React.FC<OverlayProps> = function Overlay({\n  className,\n  children,\n  fixed,\n}) {\n  React.useEffect(() => {\n    lock()\n    return () => {\n      unlock()\n    }\n  }, [])\n\n  const [overlay, setOverlay] = React.useState<HTMLDivElement | null>(null)\n  const onOverlay = React.useCallback((el: HTMLDivElement) => {\n    setOverlay(el)\n  }, [])\n\n  React.useEffect(() => {\n    if (overlay == null) {\n      return\n    }\n\n    const handle2 = allyTrap({ context: overlay })\n    return () => {\n      handle2.disengage()\n    }\n  }, [overlay])\n\n  return (\n    <div data-nextjs-dialog-overlay className={className} ref={onOverlay}>\n      <div\n        data-nextjs-dialog-backdrop\n        data-nextjs-dialog-backdrop-fixed={fixed ? true : undefined}\n      />\n      {children}\n    </div>\n  )\n}\n\nexport { Overlay }\n"], "names": ["allyTrap", "React", "lock", "unlock", "Overlay", "className", "children", "fixed", "useEffect", "overlay", "set<PERSON><PERSON>lay", "useState", "onOverlay", "useCallback", "el", "handle2", "context", "disengage", "div", "data-nextjs-dialog-overlay", "ref", "data-nextjs-dialog-backdrop", "data-nextjs-dialog-backdrop-fixed", "undefined"], "mappings": ";;;;AAAA,OAAOA,cAAc,wBAAuB;AAC5C,YAAYC,WAAW,QAAO;AAC9B,SAASC,IAAI,EAAEC,MAAM,QAAQ,gBAAe;;;;;AAQ5C,MAAMC,UAAkC,SAASA,QAAQ,KAIxD;IAJwD,IAAA,EACvDC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACN,GAJwD;IAKvDN,mKAAMO,SAAS,CAAC;QACdN,iQAAAA;QACA,OAAO;sQACLC,SAAAA;QACF;IACF,GAAG,EAAE;IAEL,MAAM,CAACM,SAASC,WAAW,GAAGT,mKAAMU,QAAQ,CAAwB;IACpE,MAAMC,YAAYX,mKAAMY,WAAW,CAAC,CAACC;QACnCJ,WAAWI;IACb,GAAG,EAAE;IAELb,mKAAMO,SAAS,CAAC;QACd,IAAIC,WAAW,MAAM;YACnB;QACF;QAEA,MAAMM,UAAUf,gRAAAA,EAAS;YAAEgB,SAASP;QAAQ;QAC5C,OAAO;YACLM,QAAQE,SAAS;QACnB;IACF,GAAG;QAACR;KAAQ;IAEZ,OAAA,WAAA,mLACE,OAAA,EAACS,OAAAA;QAAIC,4BAA0B,EAAA;QAACd,WAAWA;QAAWe,KAAKR;;0MACzD,MAAA,EAACM,OAAAA;gBACCG,6BAA2B,EAAA;gBAC3BC,qCAAmCf,QAAQ,OAAOgB;;YAEnDjB;;;AAGP", "ignoreList": [0]}}, {"offset": {"line": 3457, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3463, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Overlay/index.tsx"], "sourcesContent": ["export { Overlay } from './Overlay'\n"], "names": ["Overlay"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 3466, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3481, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/hot-linked-text/index.tsx"], "sourcesContent": ["import React from 'react'\nimport {\n  decodeMagicIdentifier,\n  MAGIC_IDENTIFIER_REGEX,\n} from '../../../../../../shared/lib/magic-identifier'\n\nconst linkRegex = /https?:\\/\\/[^\\s/$.?#].[^\\s)'\"]*/i\n\nconst splitRegexp = new RegExp(`(${MAGIC_IDENTIFIER_REGEX.source}|\\\\s+)`)\n\nexport const HotlinkedText: React.FC<{\n  text: string\n  matcher?: (text: string) => boolean\n}> = function HotlinkedText(props) {\n  const { text, matcher } = props\n\n  const wordsAndWhitespaces = text.split(splitRegexp)\n\n  return (\n    <>\n      {wordsAndWhitespaces.map((word, index) => {\n        if (linkRegex.test(word)) {\n          const link = linkRegex.exec(word)!\n          const href = link[0]\n          // If link matcher is present but the link doesn't match, don't turn it into a link\n          if (typeof matcher === 'function' && !matcher(href)) {\n            return word\n          }\n          return (\n            <React.Fragment key={`link-${index}`}>\n              <a href={href} target=\"_blank\" rel=\"noreferrer noopener\">\n                {word}\n              </a>\n            </React.Fragment>\n          )\n        }\n        try {\n          const decodedWord = decodeMagicIdentifier(word)\n          if (decodedWord !== word) {\n            return (\n              <i key={`ident-${index}`}>\n                {'{'}\n                {decodedWord}\n                {'}'}\n              </i>\n            )\n          }\n        } catch (e) {\n          return (\n            <i key={`ident-${index}`}>\n              {'{'}\n              {word} (decoding failed: {'' + e}){'}'}\n            </i>\n          )\n        }\n        return <React.Fragment key={`text-${index}`}>{word}</React.Fragment>\n      })}\n    </>\n  )\n}\n"], "names": ["React", "decodeMagicIdentifier", "MAGIC_IDENTIFIER_REGEX", "linkRegex", "splitRegexp", "RegExp", "source", "HotlinkedText", "props", "text", "matcher", "wordsAndWhitespaces", "split", "map", "word", "index", "test", "link", "exec", "href", "Fragment", "a", "target", "rel", "decodedWord", "i", "e"], "mappings": ";;;;AAAA,OAAOA,WAAW,QAAO;AACzB,SACEC,qBAAqB,EACrBC,sBAAsB,QACjB,gDAA+C;;;;AAEtD,MAAMC,YAAY;AAElB,MAAMC,cAAc,IAAIC,OAAQ,0LAAGH,yBAAAA,CAAuBI,MAAM,GAAC;AAE1D,MAAMC,gBAGR,SAASA,cAAcC,KAAK;IAC/B,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE,GAAGF;IAE1B,MAAMG,sBAAsBF,KAAKG,KAAK,CAACR;IAEvC,OAAA,WAAA,mLACE,MAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;kBACGO,oBAAoBE,GAAG,CAAC,CAACC,MAAMC;YAC9B,IAAIZ,UAAUa,IAAI,CAACF,OAAO;gBACxB,MAAMG,OAAOd,UAAUe,IAAI,CAACJ;gBAC5B,MAAMK,OAAOF,IAAI,CAAC,EAAE;gBACpB,mFAAmF;gBACnF,IAAI,OAAOP,YAAY,cAAc,CAACA,QAAQS,OAAO;oBACnD,OAAOL;gBACT;gBACA,OAAA,WAAA,GACE,sLAAA,qKAACd,UAAAA,CAAMoB,QAAQ,EAAA;8BACb,WAAA,mLAAA,MAAA,EAACC,KAAAA;wBAAEF,MAAMA;wBAAMG,QAAO;wBAASC,KAAI;kCAChCT;;mBAFiB,UAAOC;YAMjC;YACA,IAAI;gBACF,MAAMS,sMAAcvB,wBAAAA,EAAsBa;gBAC1C,IAAIU,gBAAgBV,MAAM;oBACxB,OAAA,WAAA,mLACE,OAAA,EAACW,KAAAA;;4BACE;4BACAD;4BACA;;uBAHM,WAAQT;gBAMrB;YACF,EAAE,OAAOW,GAAG;gBACV,OAAA,WAAA,GACE,uLAAA,EAACD,KAAAA;;wBACE;wBACAX;wBAAK;wBAAoB,KAAKY;wBAAE;wBAAE;;mBAF5B,WAAQX;YAKrB;YACA,OAAA,WAAA,mLAAO,MAAA,qKAACf,UAAAA,CAAMoB,QAAQ,EAAA;0BAAwBN;eAAjB,UAAOC;QACtC;;AAGN,EAAC", "ignoreList": [0]}}, {"offset": {"line": 3542, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3548, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.ts"], "sourcesContent": ["import { useCallback } from 'react'\n\nexport function useOpenInEditor({\n  file,\n  lineNumber,\n  column,\n}: {\n  file?: string | null\n  lineNumber?: number | null\n  column?: number | null\n} = {}) {\n  const openInEditor = useCallback(() => {\n    if (file == null || lineNumber == null || column == null) return\n\n    const params = new URLSearchParams()\n    params.append('file', file)\n    params.append('lineNumber', String(lineNumber))\n    params.append('column', String(column))\n\n    self\n      .fetch(\n        `${\n          process.env.__NEXT_ROUTER_BASEPATH || ''\n        }/__nextjs_launch-editor?${params.toString()}`\n      )\n      .then(\n        () => {},\n        () => {\n          console.error('There was an issue opening this code in your editor.')\n        }\n      )\n  }, [file, lineNumber, column])\n\n  return openInEditor\n}\n"], "names": ["useCallback", "useOpenInEditor", "file", "lineNumber", "column", "openInEditor", "params", "URLSearchParams", "append", "String", "self", "fetch", "process", "env", "__NEXT_ROUTER_BASEPATH", "toString", "then", "console", "error"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,QAAO;;AAE5B,SAASC,gBAAgB,KAAA;IAAA,IAAA,EAC9BC,IAAI,EACJC,UAAU,EACVC,MAAM,EAKP,GAR+B,UAAA,KAAA,IAQ5B,CAAC,IAR2B;IAS9B,MAAMC,gBAAeL,oLAAAA,EAAY;QAC/B,IAAIE,QAAQ,QAAQC,cAAc,QAAQC,UAAU,MAAM;QAE1D,MAAME,SAAS,IAAIC;QACnBD,OAAOE,MAAM,CAAC,QAAQN;QACtBI,OAAOE,MAAM,CAAC,cAAcC,OAAON;QACnCG,OAAOE,MAAM,CAAC,UAAUC,OAAOL;QAE/BM,KACGC,KAAK,CAEFC,CAAAA,QAAQC,GAAG,CAACC,sBAAsB,MAAI,EAAC,IACxC,6BAA0BR,OAAOS,QAAQ,IAE3CC,IAAI,CACH,KAAO,GACP;YACEC,QAAQC,KAAK,CAAC;QAChB;IAEN,GAAG;QAAChB;QAAMC;QAAYC;KAAO;IAE7B,OAAOC;AACT", "ignoreList": [0]}}, {"offset": {"line": 3571, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3577, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Terminal/EditorLink.tsx"], "sourcesContent": ["import React from 'react'\nimport { useOpenInEditor } from '../../helpers/use-open-in-editor'\n\ntype EditorLinkProps = {\n  file: string\n  isSourceFile: boolean\n  location?: {\n    line: number\n    column: number\n  }\n}\nexport function EditorLink({ file, isSourceFile, location }: EditorLinkProps) {\n  const open = useOpenInEditor({\n    file,\n    lineNumber: location?.line ?? 1,\n    column: location?.column ?? 0,\n  })\n\n  return (\n    <div\n      data-with-open-in-editor-link\n      data-with-open-in-editor-link-source-file={\n        isSourceFile ? true : undefined\n      }\n      data-with-open-in-editor-link-import-trace={\n        isSourceFile ? undefined : true\n      }\n      tabIndex={10}\n      role={'link'}\n      onClick={open}\n      title={'Click to open in your editor'}\n    >\n      {file}\n      {location ? `:${location.line}:${location.column}` : null}\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      >\n        <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n        <polyline points=\"15 3 21 3 21 9\"></polyline>\n        <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n      </svg>\n    </div>\n  )\n}\n"], "names": ["React", "useOpenInEditor", "EditorLink", "file", "isSourceFile", "location", "open", "lineNumber", "line", "column", "div", "data-with-open-in-editor-link", "data-with-open-in-editor-link-source-file", "undefined", "data-with-open-in-editor-link-import-trace", "tabIndex", "role", "onClick", "title", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2"], "mappings": ";;;;AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,eAAe,QAAQ,mCAAkC;;;;AAU3D,SAASC,WAAW,KAAiD;IAAjD,IAAA,EAAEC,IAAI,EAAEC,YAAY,EAAEC,QAAQ,EAAmB,GAAjD;QAGXA,gBACJA;IAHV,MAAMC,WAAOL,uQAAAA,EAAgB;QAC3BE;QACAI,YAAYF,CAAAA,iBAAAA,YAAAA,OAAAA,KAAAA,IAAAA,SAAUG,IAAI,KAAA,OAAdH,iBAAkB;QAC9BI,QAAQJ,CAAAA,mBAAAA,YAAAA,OAAAA,KAAAA,IAAAA,SAAUI,MAAM,KAAA,OAAhBJ,mBAAoB;IAC9B;IAEA,OAAA,WAAA,mLACE,OAAA,EAACK,OAAAA;QACCC,+BAA6B,EAAA;QAC7BC,6CACER,eAAe,OAAOS;QAExBC,8CACEV,eAAeS,YAAY;QAE7BE,UAAU;QACVC,MAAM;QACNC,SAASX;QACTY,OAAO;;YAENf;YACAE,WAAY,MAAGA,SAASG,IAAI,GAAC,MAAGH,SAASI,MAAM,GAAK;0MACrD,OAAA,EAACU,OAAAA;gBACCC,OAAM;gBACNC,SAAQ;gBACRC,MAAK;gBACLC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;kNAEf,MAAA,EAACC,QAAAA;wBAAKC,GAAE;;kNACR,MAAA,EAACC,YAAAA;wBAASC,QAAO;;kCACjB,sLAAA,EAACtB,QAAAA;wBAAKuB,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;;;;;;AAIzC", "ignoreList": [0]}}, {"offset": {"line": 3631, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3637, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Terminal/Terminal.tsx"], "sourcesContent": ["import Anser from 'next/dist/compiled/anser'\nimport * as React from 'react'\nimport { HotlinkedText } from '../hot-linked-text'\nimport { EditorLink } from './EditorLink'\n\nexport type TerminalProps = { content: string }\n\nfunction getFile(lines: string[]) {\n  const contentFileName = lines.shift()\n  if (!contentFileName) return null\n  const [fileName, line, column] = contentFileName.split(':', 3)\n\n  const parsedLine = Number(line)\n  const parsedColumn = Number(column)\n  const hasLocation = !Number.isNaN(parsedLine) && !Number.isNaN(parsedColumn)\n\n  return {\n    fileName: hasLocation ? fileName : contentFileName,\n    location: hasLocation\n      ? {\n          line: parsedLine,\n          column: parsedColumn,\n        }\n      : undefined,\n  }\n}\n\nfunction getImportTraceFiles(lines: string[]) {\n  if (\n    lines.some((line) => /ReactServerComponentsError:/.test(line)) ||\n    lines.some((line) => /Import trace for requested module:/.test(line))\n  ) {\n    // Grab the lines at the end containing the files\n    const files = []\n    while (\n      /.+\\..+/.test(lines[lines.length - 1]) &&\n      !lines[lines.length - 1].includes(':')\n    ) {\n      const file = lines.pop()!.trim()\n      files.unshift(file)\n    }\n\n    return files\n  }\n\n  return []\n}\n\nfunction getEditorLinks(content: string) {\n  const lines = content.split('\\n')\n  const file = getFile(lines)\n  const importTraceFiles = getImportTraceFiles(lines)\n\n  return { file, source: lines.join('\\n'), importTraceFiles }\n}\n\nexport const Terminal: React.FC<TerminalProps> = function Terminal({\n  content,\n}) {\n  const { file, source, importTraceFiles } = React.useMemo(\n    () => getEditorLinks(content),\n    [content]\n  )\n\n  const decoded = React.useMemo(() => {\n    return Anser.ansiToJson(source, {\n      json: true,\n      use_classes: true,\n      remove_empty: true,\n    })\n  }, [source])\n\n  return (\n    <div data-nextjs-terminal>\n      {file && (\n        <EditorLink\n          isSourceFile\n          key={file.fileName}\n          file={file.fileName}\n          location={file.location}\n        />\n      )}\n      <pre>\n        {decoded.map((entry, index) => (\n          <span\n            key={`terminal-entry-${index}`}\n            style={{\n              color: entry.fg ? `var(--color-${entry.fg})` : undefined,\n              ...(entry.decoration === 'bold'\n                ? { fontWeight: 800 }\n                : entry.decoration === 'italic'\n                  ? { fontStyle: 'italic' }\n                  : undefined),\n            }}\n          >\n            <HotlinkedText text={entry.content} />\n          </span>\n        ))}\n        {importTraceFiles.map((importTraceFile) => (\n          <EditorLink\n            isSourceFile={false}\n            key={importTraceFile}\n            file={importTraceFile}\n          />\n        ))}\n      </pre>\n    </div>\n  )\n}\n"], "names": ["<PERSON><PERSON>", "React", "HotlinkedText", "EditorLink", "getFile", "lines", "contentFileName", "shift", "fileName", "line", "column", "split", "parsedLine", "Number", "parsedColumn", "hasLocation", "isNaN", "location", "undefined", "getImportTraceFiles", "some", "test", "files", "length", "includes", "file", "pop", "trim", "unshift", "getEditorLinks", "content", "importTraceFiles", "source", "join", "Terminal", "useMemo", "decoded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "div", "data-nextjs-terminal", "isSourceFile", "pre", "map", "entry", "index", "span", "style", "color", "fg", "decoration", "fontWeight", "fontStyle", "text", "importTraceFile"], "mappings": ";;;;AAAA,OAAOA,WAAW,2BAA0B;AAC5C,YAAYC,WAAW,QAAO;AAC9B,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,UAAU,QAAQ,eAAc;;;;;;AAIzC,SAASC,QAAQC,KAAe;IAC9B,MAAMC,kBAAkBD,MAAME,KAAK;IACnC,IAAI,CAACD,iBAAiB,OAAO;IAC7B,MAAM,CAACE,UAAUC,MAAMC,OAAO,GAAGJ,gBAAgBK,KAAK,CAAC,KAAK;IAE5D,MAAMC,aAAaC,OAAOJ;IAC1B,MAAMK,eAAeD,OAAOH;IAC5B,MAAMK,cAAc,CAACF,OAAOG,KAAK,CAACJ,eAAe,CAACC,OAAOG,KAAK,CAACF;IAE/D,OAAO;QACLN,UAAUO,cAAcP,WAAWF;QACnCW,UAAUF,cACN;YACEN,MAAMG;YACNF,QAAQI;QACV,IACAI;IACN;AACF;AAEA,SAASC,oBAAoBd,KAAe;IAC1C,IACEA,MAAMe,IAAI,CAAC,CAACX,OAAS,8BAA8BY,IAAI,CAACZ,UACxDJ,MAAMe,IAAI,CAAC,CAACX,OAAS,qCAAqCY,IAAI,CAACZ,QAC/D;QACA,iDAAiD;QACjD,MAAMa,QAAQ,EAAE;QAChB,MACE,SAASD,IAAI,CAAChB,KAAK,CAACA,MAAMkB,MAAM,GAAG,EAAE,KACrC,CAAClB,KAAK,CAACA,MAAMkB,MAAM,GAAG,EAAE,CAACC,QAAQ,CAAC,KAClC;YACA,MAAMC,OAAOpB,MAAMqB,GAAG,GAAIC,IAAI;YAC9BL,MAAMM,OAAO,CAACH;QAChB;QAEA,OAAOH;IACT;IAEA,OAAO,EAAE;AACX;AAEA,SAASO,eAAeC,OAAe;IACrC,MAAMzB,QAAQyB,QAAQnB,KAAK,CAAC;IAC5B,MAAMc,OAAOrB,QAAQC;IACrB,MAAM0B,mBAAmBZ,oBAAoBd;IAE7C,OAAO;QAAEoB;QAAMO,QAAQ3B,MAAM4B,IAAI,CAAC;QAAOF;IAAiB;AAC5D;AAEO,MAAMG,WAAoC,SAASA,SAAS,KAElE;IAFkE,IAAA,EACjEJ,OAAO,EACR,GAFkE;IAGjE,MAAM,EAAEL,IAAI,EAAEO,MAAM,EAAED,gBAAgB,EAAE,GAAG9B,mKAAMkC,OAAO,CACtD,IAAMN,eAAeC,UACrB;QAACA;KAAQ;IAGX,MAAMM,UAAUnC,mKAAMkC,OAAO,CAAC;QAC5B,0KAAOnC,UAAAA,CAAMqC,UAAU,CAACL,QAAQ;YAC9BM,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAACR;KAAO;IAEX,OAAA,WAAA,mLACE,OAAA,EAACS,OAAAA;QAAIC,sBAAoB,EAAA;;YACtBjB,QAAAA,WAAAA,OACC,kLAAA,qPAACtB,aAAAA,EAAAA;gBACCwC,YAAY,EAAA;gBAEZlB,MAAMA,KAAKjB,QAAQ;gBACnBS,UAAUQ,KAAKR,QAAQ;eAFlBQ,KAAKjB,QAAQ;0MAKtB,OAAA,EAACoC,OAAAA;;oBACER,QAAQS,GAAG,CAAC,CAACC,OAAOC,QAAAA,WAAAA,mLACnB,MAAA,EAACC,QAAAA;4BAECC,OAAO;gCACLC,OAAOJ,MAAMK,EAAE,GAAI,iBAAcL,MAAMK,EAAE,GAAC,MAAKjC;gCAC/C,GAAI4B,MAAMM,UAAU,KAAK,SACrB;oCAAEC,YAAY;gCAAI,IAClBP,MAAMM,UAAU,KAAK,WACnB;oCAAEE,WAAW;gCAAS,IACtBpC,SAAS;4BACjB;sCAEA,WAAA,mLAAA,MAAA,6PAAChB,gBAAAA,EAAAA;gCAAcqD,MAAMT,MAAMhB,OAAO;;2BAV5B,oBAAiBiB;oBAa1BhB,iBAAiBc,GAAG,CAAC,CAACW,kBAAAA,WAAAA,mLACrB,MAAA,qPAACrD,aAAAA,EAAAA;4BACCwC,cAAc;4BAEdlB,MAAM+B;2BADDA;;;;;AAOjB,EAAC", "ignoreList": [0]}}, {"offset": {"line": 3733, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3739, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Terminal/index.tsx"], "sourcesContent": ["export { Terminal } from './Terminal'\n"], "names": ["Terminal"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 3742, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3757, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/styles.ts"], "sourcesContent": ["import { noop as css } from '../../helpers/noop-template'\n\nconst styles = css`\n  .nextjs-container-build-error-version-status {\n    flex: 1;\n    text-align: right;\n    font-size: var(--size-font-small);\n  }\n  .nextjs-container-build-error-version-status span {\n    display: inline-block;\n    width: 10px;\n    height: 10px;\n    border-radius: 5px;\n    background: var(--color-ansi-bright-black);\n  }\n  .nextjs-container-build-error-version-status span.fresh {\n    background: var(--color-ansi-green);\n  }\n  .nextjs-container-build-error-version-status span.stale {\n    background: var(--color-ansi-yellow);\n  }\n  .nextjs-container-build-error-version-status span.outdated {\n    background: var(--color-ansi-red);\n  }\n`\n\nexport { styles }\n"], "names": ["noop", "css", "styles"], "mappings": ";;;AAAA,SAASA,QAAQC,GAAG,QAAQ,8BAA6B;;;;;;;;;;;;;AAEzD,MAAMC,uPAASD,OAAAA,EAAAA", "ignoreList": [0]}}, {"offset": {"line": 3776, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3782, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/VersionStalenessInfo.tsx"], "sourcesContent": ["import type { VersionInfo } from '../../../../../../server/dev/parse-version-info'\n\nexport function VersionStalenessInfo({\n  versionInfo,\n}: {\n  versionInfo: VersionInfo | undefined\n}) {\n  if (!versionInfo) return null\n  const { staleness } = versionInfo\n  let { text, indicatorClass, title } = getStaleness(versionInfo)\n\n  if (!text) return null\n\n  return (\n    <span className=\"nextjs-container-build-error-version-status\">\n      <span className={indicatorClass} />\n      <small data-nextjs-version-checker title={title}>\n        {text}\n      </small>{' '}\n      {staleness === 'fresh' ||\n      staleness === 'newer-than-npm' ||\n      staleness === 'unknown' ? null : (\n        <a\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          href=\"https://nextjs.org/docs/messages/version-staleness\"\n        >\n          (learn more)\n        </a>\n      )}\n      {process.env.TURBOPACK ? ' (Turbopack)' : ''}\n    </span>\n  )\n}\n\nexport function getStaleness({ installed, staleness, expected }: VersionInfo) {\n  let text = ''\n  let title = ''\n  let indicatorClass = ''\n  const versionLabel = `Next.js (${installed})`\n  switch (staleness) {\n    case 'newer-than-npm':\n    case 'fresh':\n      text = versionLabel\n      title = `Latest available version is detected (${installed}).`\n      indicatorClass = 'fresh'\n      break\n    case 'stale-patch':\n    case 'stale-minor':\n      text = `${versionLabel} out of date`\n      title = `There is a newer version (${expected}) available, upgrade recommended! `\n      indicatorClass = 'stale'\n      break\n    case 'stale-major': {\n      text = `${versionLabel} is outdated`\n      title = `An outdated version detected (latest is ${expected}), upgrade is highly recommended!`\n      indicatorClass = 'outdated'\n      break\n    }\n    case 'stale-prerelease': {\n      text = `${versionLabel} is outdated`\n      title = `There is a newer canary version (${expected}) available, please upgrade! `\n      indicatorClass = 'stale'\n      break\n    }\n    case 'unknown':\n      break\n    default:\n      break\n  }\n  return { text, indicatorClass, title }\n}\n"], "names": ["VersionStalenessInfo", "versionInfo", "staleness", "text", "indicatorClass", "title", "getStaleness", "span", "className", "small", "data-nextjs-version-checker", "a", "target", "rel", "href", "process", "env", "TURBOPACK", "installed", "expected", "versionLabel"], "mappings": ";;;;;;AAEO,SAASA,qBAAqB,KAIpC;IAJoC,IAAA,EACnCC,WAAW,EAGZ,GAJoC;IAKnC,IAAI,CAACA,aAAa,OAAO;IACzB,MAAM,EAAEC,SAAS,EAAE,GAAGD;IACtB,IAAI,EAAEE,IAAI,EAAEC,cAAc,EAAEC,KAAK,EAAE,GAAGC,aAAaL;IAEnD,IAAI,CAACE,MAAM,OAAO;IAElB,OAAA,WAAA,mLACE,OAAA,EAACI,QAAAA;QAAKC,WAAU;;0MACd,MAAA,EAACD,QAAAA;gBAAKC,WAAWJ;;0MACjB,MAAA,EAACK,SAAAA;gBAAMC,6BAA2B,EAAA;gBAACL,OAAOA;0BACvCF;;YACM;YACRD,cAAc,WACfA,cAAc,oBACdA,cAAc,YAAY,OAAA,WAAA,mLACxB,MAAA,EAACS,KAAAA;gBACCC,QAAO;gBACPC,KAAI;gBACJC,MAAK;0BACN;;YAIFC,QAAQC,GAAG,CAACC,SAAS,kBAAG,iBAAiB;;;AAGhD;AAEO,SAASX,aAAa,KAA+C;IAA/C,IAAA,EAAEY,SAAS,EAAEhB,SAAS,EAAEiB,QAAQ,EAAe,GAA/C;IAC3B,IAAIhB,OAAO;IACX,IAAIE,QAAQ;IACZ,IAAID,iBAAiB;IACrB,MAAMgB,eAAgB,cAAWF,YAAU;IAC3C,OAAQhB;QACN,KAAK;QACL,KAAK;YACHC,OAAOiB;YACPf,QAAS,2CAAwCa,YAAU;YAC3Dd,iBAAiB;YACjB;QACF,KAAK;QACL,KAAK;YACHD,OAAQ,KAAEiB,eAAa;YACvBf,QAAS,+BAA4Bc,WAAS;YAC9Cf,iBAAiB;YACjB;QACF,KAAK;YAAe;gBAClBD,OAAQ,KAAEiB,eAAa;gBACvBf,QAAS,6CAA0Cc,WAAS;gBAC5Df,iBAAiB;gBACjB;YACF;QACA,KAAK;YAAoB;gBACvBD,OAAQ,KAAEiB,eAAa;gBACvBf,QAAS,sCAAmCc,WAAS;gBACrDf,iBAAiB;gBACjB;YACF;QACA,KAAK;YACH;QACF;YACE;IACJ;IACA,OAAO;QAAED;QAAMC;QAAgBC;IAAM;AACvC", "ignoreList": [0]}}, {"offset": {"line": 3860, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3866, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/index.tsx"], "sourcesContent": ["export { styles } from './styles'\nexport { VersionStalenessInfo } from './VersionStalenessInfo'\n"], "names": ["styles", "VersionStalenessInfo"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 3870, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3886, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/container/BuildError.tsx"], "sourcesContent": ["import * as React from 'react'\nimport type { VersionInfo } from '../../../../../server/dev/parse-version-info'\nimport {\n  Dialog,\n  DialogBody,\n  DialogContent,\n  DialogHeader,\n} from '../components/Dialog'\nimport { Overlay } from '../components/Overlay'\nimport { Terminal } from '../components/Terminal'\nimport { VersionStalenessInfo } from '../components/VersionStalenessInfo'\nimport { noop as css } from '../helpers/noop-template'\n\nexport type BuildErrorProps = { message: string; versionInfo?: VersionInfo }\n\nexport const BuildError: React.FC<BuildErrorProps> = function BuildError({\n  message,\n  versionInfo,\n}) {\n  const noop = React.useCallback(() => {}, [])\n  return (\n    <Overlay fixed>\n      <Dialog\n        type=\"error\"\n        aria-labelledby=\"nextjs__container_error_label\"\n        aria-describedby=\"nextjs__container_error_desc\"\n        onClose={noop}\n      >\n        <DialogContent>\n          <DialogHeader className=\"nextjs-container-errors-header\">\n            <h1\n              id=\"nextjs__container_errors_label\"\n              className=\"nextjs__container_errors_label\"\n            >\n              {'Build Error'}\n            </h1>\n            <VersionStalenessInfo versionInfo={versionInfo} />\n            <p\n              id=\"nextjs__container_errors_desc\"\n              className=\"nextjs__container_errors_desc\"\n            >\n              Failed to compile\n            </p>\n          </DialogHeader>\n          <DialogBody className=\"nextjs-container-errors-body\">\n            <Terminal content={message} />\n            <footer>\n              <p id=\"nextjs__container_build_error_desc\">\n                <small>\n                  This error occurred during the build process and can only be\n                  dismissed by fixing the error.\n                </small>\n              </p>\n            </footer>\n          </DialogBody>\n        </DialogContent>\n      </Dialog>\n    </Overlay>\n  )\n}\n\nexport const styles = css`\n  h1.nextjs__container_errors_label {\n    font-size: var(--size-font-big);\n    line-height: var(--size-font-bigger);\n    font-weight: bold;\n    margin: var(--size-gap-double) 0;\n  }\n  .nextjs-container-errors-header p {\n    font-size: var(--size-font-small);\n    line-height: var(--size-font-big);\n    white-space: pre-wrap;\n  }\n  .nextjs-container-errors-body footer {\n    margin-top: var(--size-gap);\n  }\n  .nextjs-container-errors-body footer p {\n    margin: 0;\n  }\n\n  .nextjs-container-errors-body small {\n    color: var(--color-font);\n  }\n`\n"], "names": ["React", "Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "Overlay", "Terminal", "VersionStalenessInfo", "noop", "css", "BuildError", "message", "versionInfo", "useCallback", "fixed", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "className", "h1", "id", "p", "content", "footer", "small", "styles"], "mappings": ";;;;;AAAA,YAAYA,WAAW,QAAO;AAE9B,SACEC,MAAM,EACNC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,uBAAsB;;;AAG7B,SAASG,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;AAHtD,SAASJ,OAAO,QAAQ,wBAAuB;;;;;;AAC/C,SAASC,QAAQ,QAAQ,yBAAwB;;;;;;;;;;;;;;;;;;;AAM1C,MAAMI,aAAwC,SAASA,WAAW,KAGxE;IAHwE,IAAA,EACvEC,OAAO,EACPC,WAAW,EACZ,GAHwE;IAIvE,MAAMJ,OAAOR,mKAAMa,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,OAAA,WAAA,GACE,sLAAA,iPAACR,UAAAA,EAAAA;QAAQS,KAAK,EAAA;kBACZ,WAAA,mLAAA,MAAA,EAACb,sPAAAA,EAAAA;YACCc,MAAK;YACLC,mBAAgB;YAChBC,oBAAiB;YACjBC,SAASV;sBAET,WAAA,GAAA,uLAAA,sPAACL,gBAAAA,EAAAA;;kNACC,OAAA,EAACC,kQAAAA,EAAAA;wBAAae,WAAU;;0NACtB,MAAA,EAACC,MAAAA;gCACCC,IAAG;gCACHF,WAAU;0CAET;;0CAEH,sLAAA,2QAACZ,uBAAAA,EAAAA;gCAAqBK,aAAaA;;0CACnC,sLAAA,EAACU,KAAAA;gCACCD,IAAG;gCACHF,WAAU;0CACX;;;;mCAIH,sLAAA,mPAACjB,aAAAA,EAAAA;wBAAWiB,WAAU;;2CACpB,qLAAA,mPAACb,WAAAA,EAAAA;gCAASiB,SAASZ;;0CACnB,sLAAA,EAACa,UAAAA;0CACC,WAAA,mLAAA,MAAA,EAACF,KAAAA;oCAAED,IAAG;8CACJ,WAAA,mLAAA,MAAA,EAACI,SAAAA;kDAAM;;;;;;;;;;AAWvB,EAAC;AAEM,MAAMC,uPAASjB,OAAAA,EAAAA,mBAsBrB", "ignoreList": [0]}}, {"offset": {"line": 3974, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3980, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/icons/CloseIcon.tsx"], "sourcesContent": ["import * as React from 'react'\n\nconst CloseIcon = () => {\n  return (\n    <svg\n      width=\"24\"\n      height=\"24\"\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        d=\"M18 6L6 18\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M6 6L18 18\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  )\n}\n\nexport { CloseIcon }\n"], "names": ["React", "CloseIcon", "svg", "width", "height", "viewBox", "fill", "xmlns", "path", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;;;AAE9B,MAAMC,YAAY;IAChB,OAAA,WAAA,mLACE,OAAA,EAACC,OAAAA;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;;0MA<PERSON>,MAAA,EAACC,QAAAA;gBACCC,GAAE;gBACFC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;0BAEjB,sLAAA,EAACL,QAAAA;gBACCC,GAAE;gBACFC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;;;AAIvB", "ignoreList": [0]}}, {"offset": {"line": 4014, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4020, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { CloseIcon } from '../../icons/CloseIcon'\n\nexport type LeftRightDialogHeaderProps = {\n  children?: React.ReactNode\n  className?: string\n  previous: (() => void) | null\n  next: (() => void) | null\n  close?: () => void\n}\n\nconst LeftRightDialogHeader: React.FC<LeftRightDialogHeaderProps> =\n  function LeftRightDialogHeader({\n    children,\n    className,\n    previous,\n    next,\n    close,\n  }) {\n    const buttonLeft = React.useRef<HTMLButtonElement | null>(null)\n    const buttonRight = React.useRef<HTMLButtonElement | null>(null)\n    const buttonClose = React.useRef<HTMLButtonElement | null>(null)\n\n    const [nav, setNav] = React.useState<HTMLElement | null>(null)\n    const onNav = React.useCallback((el: HTMLElement) => {\n      setNav(el)\n    }, [])\n\n    React.useEffect(() => {\n      if (nav == null) {\n        return\n      }\n\n      const root = nav.getRootNode()\n      const d = self.document\n\n      function handler(e: KeyboardEvent) {\n        if (e.key === 'ArrowLeft') {\n          e.preventDefault()\n          e.stopPropagation()\n          if (buttonLeft.current) {\n            buttonLeft.current.focus()\n          }\n          previous && previous()\n        } else if (e.key === 'ArrowRight') {\n          e.preventDefault()\n          e.stopPropagation()\n          if (buttonRight.current) {\n            buttonRight.current.focus()\n          }\n          next && next()\n        } else if (e.key === 'Escape') {\n          e.preventDefault()\n          e.stopPropagation()\n          if (root instanceof ShadowRoot) {\n            const a = root.activeElement\n            if (a && a !== buttonClose.current && a instanceof HTMLElement) {\n              a.blur()\n              return\n            }\n          }\n\n          close?.()\n        }\n      }\n\n      root.addEventListener('keydown', handler as EventListener)\n      if (root !== d) {\n        d.addEventListener('keydown', handler)\n      }\n      return function () {\n        root.removeEventListener('keydown', handler as EventListener)\n        if (root !== d) {\n          d.removeEventListener('keydown', handler)\n        }\n      }\n    }, [close, nav, next, previous])\n\n    // Unlock focus for browsers like Firefox, that break all user focus if the\n    // currently focused item becomes disabled.\n    React.useEffect(() => {\n      if (nav == null) {\n        return\n      }\n\n      const root = nav.getRootNode()\n      // Always true, but we do this for TypeScript:\n      if (root instanceof ShadowRoot) {\n        const a = root.activeElement\n\n        if (previous == null) {\n          if (buttonLeft.current && a === buttonLeft.current) {\n            buttonLeft.current.blur()\n          }\n        } else if (next == null) {\n          if (buttonRight.current && a === buttonRight.current) {\n            buttonRight.current.blur()\n          }\n        }\n      }\n    }, [nav, next, previous])\n\n    return (\n      <div data-nextjs-dialog-left-right className={className}>\n        <nav ref={onNav}>\n          <button\n            ref={buttonLeft}\n            type=\"button\"\n            disabled={previous == null ? true : undefined}\n            aria-disabled={previous == null ? true : undefined}\n            onClick={previous ?? undefined}\n          >\n            <svg\n              viewBox=\"0 0 14 14\"\n              fill=\"none\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              <title>previous</title>\n              <path\n                d=\"M6.99996 1.16666L1.16663 6.99999L6.99996 12.8333M12.8333 6.99999H1.99996H12.8333Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n              />\n            </svg>\n          </button>\n          <button\n            ref={buttonRight}\n            type=\"button\"\n            disabled={next == null ? true : undefined}\n            aria-disabled={next == null ? true : undefined}\n            onClick={next ?? undefined}\n          >\n            <svg\n              viewBox=\"0 0 14 14\"\n              fill=\"none\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              <title>next</title>\n              <path\n                d=\"M6.99996 1.16666L12.8333 6.99999L6.99996 12.8333M1.16663 6.99999H12H1.16663Z\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n              />\n            </svg>\n          </button>\n          {children}\n        </nav>\n        {close ? (\n          <button\n            data-nextjs-errors-dialog-left-right-close-button\n            ref={buttonClose}\n            type=\"button\"\n            onClick={close}\n            aria-label=\"Close\"\n          >\n            <span aria-hidden=\"true\">\n              <CloseIcon />\n            </span>\n          </button>\n        ) : null}\n      </div>\n    )\n  }\n\nexport { LeftRightDialogHeader }\n"], "names": ["React", "CloseIcon", "LeftRightDialogHeader", "children", "className", "previous", "next", "close", "buttonLeft", "useRef", "buttonRight", "buttonClose", "nav", "set<PERSON><PERSON>", "useState", "onNav", "useCallback", "el", "useEffect", "root", "getRootNode", "d", "self", "document", "handler", "e", "key", "preventDefault", "stopPropagation", "current", "focus", "ShadowRoot", "a", "activeElement", "HTMLElement", "blur", "addEventListener", "removeEventListener", "div", "data-nextjs-dialog-left-right", "ref", "button", "type", "disabled", "undefined", "aria-disabled", "onClick", "svg", "viewBox", "fill", "xmlns", "title", "path", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "data-nextjs-errors-dialog-left-right-close-button", "aria-label", "span", "aria-hidden"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,SAAS,QAAQ,wBAAuB;;;;AAUjD,MAAMC,wBACJ,SAASA,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACN,GAN8B;IAO7B,MAAMC,aAAaR,mKAAMS,MAAM,CAA2B;IAC1D,MAAMC,cAAcV,mKAAMS,MAAM,CAA2B;IAC3D,MAAME,cAAcX,mKAAMS,MAAM,CAA2B;IAE3D,MAAM,CAACG,KAAKC,OAAO,GAAGb,mKAAMc,QAAQ,CAAqB;IACzD,MAAMC,QAAQf,mKAAMgB,WAAW,CAAC,CAACC;QAC/BJ,OAAOI;IACT,GAAG,EAAE;IAELjB,mKAAMkB,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,MAAMC,IAAIC,KAAKC,QAAQ;QAEvB,SAASC,QAAQC,CAAgB;YAC/B,IAAIA,EAAEC,GAAG,KAAK,aAAa;gBACzBD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIpB,WAAWqB,OAAO,EAAE;oBACtBrB,WAAWqB,OAAO,CAACC,KAAK;gBAC1B;gBACAzB,YAAYA;YACd,OAAO,IAAIoB,EAAEC,GAAG,KAAK,cAAc;gBACjCD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIlB,YAAYmB,OAAO,EAAE;oBACvBnB,YAAYmB,OAAO,CAACC,KAAK;gBAC3B;gBACAxB,QAAQA;YACV,OAAO,IAAImB,EAAEC,GAAG,KAAK,UAAU;gBAC7BD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIT,gBAAgBY,YAAY;oBAC9B,MAAMC,IAAIb,KAAKc,aAAa;oBAC5B,IAAID,KAAKA,MAAMrB,YAAYkB,OAAO,IAAIG,aAAaE,aAAa;wBAC9DF,EAAEG,IAAI;wBACN;oBACF;gBACF;gBAEA5B,SAAAA,OAAAA,KAAAA,IAAAA;YACF;QACF;QAEAY,KAAKiB,gBAAgB,CAAC,WAAWZ;QACjC,IAAIL,SAASE,GAAG;YACdA,EAAEe,gBAAgB,CAAC,WAAWZ;QAChC;QACA,OAAO;YACLL,KAAKkB,mBAAmB,CAAC,WAAWb;YACpC,IAAIL,SAASE,GAAG;gBACdA,EAAEgB,mBAAmB,CAAC,WAAWb;YACnC;QACF;IACF,GAAG;QAACjB;QAAOK;QAAKN;QAAMD;KAAS;IAE/B,2EAA2E;IAC3E,2CAA2C;IAC3CL,mKAAMkB,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,8CAA8C;QAC9C,IAAID,gBAAgBY,YAAY;YAC9B,MAAMC,IAAIb,KAAKc,aAAa;YAE5B,IAAI5B,YAAY,MAAM;gBACpB,IAAIG,WAAWqB,OAAO,IAAIG,MAAMxB,WAAWqB,OAAO,EAAE;oBAClDrB,WAAWqB,OAAO,CAACM,IAAI;gBACzB;YACF,OAAO,IAAI7B,QAAQ,MAAM;gBACvB,IAAII,YAAYmB,OAAO,IAAIG,MAAMtB,YAAYmB,OAAO,EAAE;oBACpDnB,YAAYmB,OAAO,CAACM,IAAI;gBAC1B;YACF;QACF;IACF,GAAG;QAACvB;QAAKN;QAAMD;KAAS;IAExB,OAAA,WAAA,mLACE,OAAA,EAACiC,OAAAA;QAAIC,+BAA6B,EAAA;QAACnC,WAAWA;;2BAC5C,sLAAA,EAACQ,OAAAA;gBAAI4B,KAAKzB;;kNACR,MAAA,EAAC0B,UAAAA;wBACCD,KAAKhC;wBACLkC,MAAK;wBACLC,UAAUtC,YAAY,OAAO,OAAOuC;wBACpCC,iBAAexC,YAAY,OAAO,OAAOuC;wBACzCE,SAASzC,YAAAA,OAAAA,WAAYuC;kCAErB,WAAA,mLAAA,OAAA,EAACG,OAAAA;4BACCC,SAAQ;4BACRC,MAAK;4BACLC,OAAM;;8NAEN,MAAA,EAACC,SAAAA;8CAAM;;8NACP,MAAA,EAACC,QAAAA;oCACC/B,GAAE;oCACFgC,QAAO;oCACPC,aAAY;oCACZC,eAAc;oCACdC,gBAAe;;;;;mCAIrB,qLAAA,EAACf,UAAAA;wBACCD,KAAK9B;wBACLgC,MAAK;wBACLC,UAAUrC,QAAQ,OAAO,OAAOsC;wBAChCC,iBAAevC,QAAQ,OAAO,OAAOsC;wBACrCE,SAASxC,QAAAA,OAAAA,OAAQsC;kCAEjB,WAAA,IAAA,sLAAA,EAACG,OAAAA;4BACCC,SAAQ;4BACRC,MAAK;4BACLC,OAAM;;8NAEN,MAAA,EAACC,SAAAA;8CAAM;;8NACP,MAAA,EAACC,QAAAA;oCACC/B,GAAE;oCACFgC,QAAO;oCACPC,aAAY;oCACZC,eAAc;oCACdC,gBAAe;;;;;oBAIpBrD;;;YAEFI,QAAAA,WAAAA,GACC,sLAAA,EAACkC,UAAAA;gBACCgB,mDAAiD,EAAA;gBACjDjB,KAAK7B;gBACL+B,MAAK;gBACLI,SAASvC;gBACTmD,cAAW;0BAEX,WAAA,mLAAA,MAAA,EAACC,QAAAA;oBAAKC,eAAY;8BAChB,WAAA,mLAAA,MAAA,mOAAC3D,YAAAA,EAAAA,CAAAA;;iBAGH;;;AAGV", "ignoreList": [0]}}, {"offset": {"line": 4187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4193, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/styles.ts"], "sourcesContent": ["import { noop as css } from '../../helpers/noop-template'\n\nconst styles = css`\n  [data-nextjs-dialog-left-right] {\n    display: flex;\n    flex-direction: row;\n    align-content: center;\n    align-items: center;\n    justify-content: space-between;\n  }\n  [data-nextjs-dialog-left-right] > nav {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    margin-right: var(--size-gap);\n  }\n  [data-nextjs-dialog-left-right] > nav > button {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n\n    width: calc(var(--size-gap-double) + var(--size-gap));\n    height: calc(var(--size-gap-double) + var(--size-gap));\n    font-size: 0;\n    border: none;\n    background-color: rgba(255, 85, 85, 0.1);\n    color: var(--color-ansi-red);\n    cursor: pointer;\n    transition: background-color 0.25s ease;\n  }\n  [data-nextjs-dialog-left-right] > nav > button > svg {\n    width: auto;\n    height: calc(var(--size-gap) + var(--size-gap-half));\n  }\n  [data-nextjs-dialog-left-right] > nav > button:hover {\n    background-color: rgba(255, 85, 85, 0.2);\n  }\n  [data-nextjs-dialog-left-right] > nav > button:disabled {\n    background-color: rgba(255, 85, 85, 0.1);\n    color: rgba(255, 85, 85, 0.4);\n    cursor: not-allowed;\n  }\n\n  [data-nextjs-dialog-left-right] > nav > button:first-of-type {\n    border-radius: var(--size-gap-half) 0 0 var(--size-gap-half);\n    margin-right: 1px;\n  }\n  [data-nextjs-dialog-left-right] > nav > button:last-of-type {\n    border-radius: 0 var(--size-gap-half) var(--size-gap-half) 0;\n  }\n\n  [data-nextjs-dialog-left-right] > button:last-of-type {\n    border: 0;\n    padding: 0;\n\n    background-color: transparent;\n    appearance: none;\n\n    opacity: 0.4;\n    transition: opacity 0.25s ease;\n\n    color: var(--color-font);\n  }\n  [data-nextjs-dialog-left-right] > button:last-of-type:hover {\n    opacity: 0.7;\n  }\n`\n\nexport { styles }\n"], "names": ["noop", "css", "styles"], "mappings": ";;;AAAA,SAASA,QAAQC,GAAG,QAAQ,8BAA6B;;;;;;;;;;;;;AAEzD,MAAMC,uPAASD,OAAAA,EAAAA", "ignoreList": [0]}}, {"offset": {"line": 4212, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4218, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/index.ts"], "sourcesContent": ["export { LeftRightDialogHeader } from './LeftRightDialogHeader'\nexport { styles } from './styles'\n"], "names": ["LeftRightDialogHeader", "styles"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4222, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4238, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Toast/styles.ts"], "sourcesContent": ["import { noop as css } from '../../helpers/noop-template'\n\nconst styles = css`\n  .nextjs-toast {\n    position: fixed;\n    bottom: var(--size-gap-double);\n    left: var(--size-gap-double);\n    max-width: 420px;\n    z-index: 9000;\n    box-shadow: 0px var(--size-gap-double) var(--size-gap-quad)\n      rgba(0, 0, 0, 0.25);\n  }\n\n  @media (max-width: 440px) {\n    .nextjs-toast {\n      max-width: 90vw;\n      left: 5vw;\n    }\n  }\n\n  .nextjs-toast-errors-parent {\n    padding: 16px;\n    border-radius: var(--size-gap-quad);\n    font-weight: 500;\n    color: var(--color-ansi-bright-white);\n    background-color: var(--color-ansi-red);\n  }\n\n  .nextjs-static-indicator-toast-wrapper {\n    width: 30px;\n    height: 30px;\n    overflow: hidden;\n    border: 0;\n    border-radius: var(--size-gap-triple);\n    background: var(--color-background);\n    color: var(--color-font);\n    transition: all 0.3s ease-in-out;\n    box-shadow:\n      inset 0 0 0 1px var(--color-border-shadow),\n      0 11px 40px 0 rgba(0, 0, 0, 0.25),\n      0 2px 10px 0 rgba(0, 0, 0, 0.12);\n  }\n\n  .nextjs-static-indicator-toast-wrapper:hover {\n    width: 140px;\n  }\n\n  .nextjs-static-indicator-toast-icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 30px;\n    height: 30px;\n  }\n\n  .nextjs-static-indicator-toast-text {\n    font-size: 14px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    opacity: 0;\n    white-space: nowrap;\n    transition: opacity 0.3s ease-in-out;\n    line-height: 30px;\n    position: absolute;\n    left: 30px;\n    top: 0;\n  }\n\n  .nextjs-static-indicator-toast-wrapper:hover\n    .nextjs-static-indicator-toast-text {\n    opacity: 1;\n  }\n\n  .nextjs-static-indicator-toast-wrapper button {\n    color: var(--color-font);\n    opacity: 0.8;\n    background: none;\n    border: none;\n    margin-left: 6px;\n    margin-top: -2px;\n    outline: 0;\n  }\n\n  .nextjs-static-indicator-toast-wrapper button:focus {\n    opacity: 1;\n  }\n\n  .nextjs-static-indicator-toast-wrapper button > svg {\n    width: 16px;\n    height: 16px;\n  }\n`\n\nexport { styles }\n"], "names": ["noop", "css", "styles"], "mappings": ";;;AAAA,SAASA,QAAQC,GAAG,QAAQ,8BAA6B;;;;;;;;;;;;;AAEzD,MAAMC,uPAASD,OAAAA,EAAAA", "ignoreList": [0]}}, {"offset": {"line": 4257, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4263, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Toast/Toast.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport type ToastProps = React.HTMLProps<HTMLDivElement> & {\n  children?: React.ReactNode\n  onClick?: () => void\n  className?: string\n}\n\nexport const Toast: React.FC<ToastProps> = function Toast({\n  onClick,\n  children,\n  className,\n  ...props\n}) {\n  return (\n    <div\n      {...props}\n      onClick={(e) => {\n        e.preventDefault()\n        return onClick?.()\n      }}\n      className={`nextjs-toast${className ? ' ' + className : ''}`}\n    >\n      <div data-nextjs-toast-wrapper>{children}</div>\n    </div>\n  )\n}\n"], "names": ["React", "Toast", "onClick", "children", "className", "props", "div", "e", "preventDefault", "data-nextjs-toast-wrapper"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;;;AAQvB,MAAMC,QAA8B,SAASA,MAAM,KAKzD;IALyD,IAAA,EACxDC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACT,GAAGC,OACJ,GALyD;IAMxD,OAAA,WAAA,mLACE,MAAA,EAACC,OAAAA;QACE,GAAGD,KAAK;QACTH,SAAS,CAACK;YACRA,EAAEC,cAAc;YAChB,OAAON,WAAAA,OAAAA,KAAAA,IAAAA;QACT;QACAE,WAAY,iBAAcA,CAAAA,YAAY,MAAMA,YAAY,EAAC;kBAEzD,WAAA,mLAAA,MAAA,EAACE,OAAAA;YAAIG,2BAAyB,EAAA;sBAAEN;;;AAGtC,EAAC", "ignoreList": [0]}}, {"offset": {"line": 4285, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4291, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Toast/index.tsx"], "sourcesContent": ["export { styles } from './styles'\nexport { Toast } from './Toast'\n"], "names": ["styles", "Toast"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4311, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/webpack-module-path.ts"], "sourcesContent": ["const replacementRegExes = [\n  /^(rsc:\\/\\/React\\/[^/]+\\/)/,\n  /^webpack-internal:\\/\\/\\/(\\([\\w-]+\\)\\/)?/,\n  /^(webpack:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)(\\([\\w-]+\\)\\/)?/,\n  /\\?\\w+(\\?\\d+)?$/, // React replay error query param, .e.g. ?c69d?0, ?c69d\n  /\\?\\d+$/, // React's fakeFunctionIdx query param\n]\n\nexport function isWebpackInternalResource(file: string) {\n  for (const regex of replacementRegExes) {\n    if (regex.test(file)) return true\n\n    file = file.replace(regex, '')\n  }\n\n  return false\n}\n\n/**\n * Format the webpack internal id to original file path\n *\n * webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n * rsc://React/Server/webpack-internal:///(rsc)/./src/hello.tsx?42 => ./src/hello.tsx\n * rsc://React/Server/webpack:///app/indirection.tsx?14cb?0 => app/indirection.tsx\n * webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n * webpack://./src/hello.tsx => ./src/hello.tsx\n * webpack:///./src/hello.tsx => ./src/hello.tsx\n */\nexport function formatFrameSourceFile(file: string) {\n  for (const regex of replacementRegExes) {\n    file = file.replace(regex, '')\n  }\n\n  return file\n}\n"], "names": ["replacementRegExes", "isWebpackInternalResource", "file", "regex", "test", "replace", "formatFrameSourceFile"], "mappings": ";;;;AAAA,MAAMA,qBAAqB;IACzB;IACA;IACA;IACA;IACA;CACD;AAEM,SAASC,0BAA0BC,IAAY;IACpD,KAAK,MAAMC,SAASH,mBAAoB;QACtC,IAAIG,MAAMC,IAAI,CAACF,OAAO,OAAO;QAE7BA,OAAOA,KAAKG,OAAO,CAACF,OAAO;IAC7B;IAEA,OAAO;AACT;AAYO,SAASG,sBAAsBJ,IAAY;IAChD,KAAK,MAAMC,SAASH,mBAAoB;QACtCE,OAAOA,KAAKG,OAAO,CAACF,OAAO;IAC7B;IAEA,OAAOD;AACT", "ignoreList": [0]}}, {"offset": {"line": 4335, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4341, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/stack-frame.ts"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport type { OriginalStackFrameResponse } from '../../server/shared'\nimport {\n  isWebpackInternalResource,\n  formatFrameSourceFile,\n} from './webpack-module-path'\nexport interface OriginalStackFrame extends OriginalStackFrameResponse {\n  error: boolean\n  reason: string | null\n  external: boolean\n  ignored: boolean\n  sourceStackFrame: StackFrame\n}\n\nfunction getOriginalStackFrame(\n  source: StackFrame,\n  type: 'server' | 'edge-server' | null,\n  isAppDir: boolean,\n  errorMessage: string\n): Promise<OriginalStackFrame> {\n  async function _getOriginalStackFrame(): Promise<OriginalStackFrame> {\n    const params = new URLSearchParams()\n    params.append('isServer', String(type === 'server'))\n    params.append('isEdgeServer', String(type === 'edge-server'))\n    params.append('isAppDirectory', String(isAppDir))\n    params.append('errorMessage', errorMessage)\n    for (const key in source) {\n      params.append(key, ((source as any)[key] ?? '').toString())\n    }\n\n    const controller = new AbortController()\n    const tm = setTimeout(() => controller.abort(), 3000)\n    const res = await self\n      .fetch(\n        `${\n          process.env.__NEXT_ROUTER_BASEPATH || ''\n        }/__nextjs_original-stack-frame?${params.toString()}`,\n        { signal: controller.signal }\n      )\n      .finally(() => {\n        clearTimeout(tm)\n      })\n    if (!res.ok || res.status === 204) {\n      return Promise.reject(new Error(await res.text()))\n    }\n\n    const body: OriginalStackFrameResponse = await res.json()\n    return {\n      error: false,\n      reason: null,\n      external: false,\n      sourceStackFrame: source,\n      originalStackFrame: body.originalStackFrame,\n      originalCodeFrame: body.originalCodeFrame || null,\n      sourcePackage: body.sourcePackage,\n      ignored: body.originalStackFrame?.ignored || false,\n    }\n  }\n\n  // TODO: merge this section into ignoredList handling\n  if (\n    source.file === 'file://' ||\n    source.file?.match(/^node:/) ||\n    source.file?.match(/https?:\\/\\//)\n  ) {\n    return Promise.resolve({\n      error: false,\n      reason: null,\n      external: true,\n      sourceStackFrame: source,\n      originalStackFrame: null,\n      originalCodeFrame: null,\n      sourcePackage: null,\n      ignored: true,\n    })\n  }\n\n  return _getOriginalStackFrame().catch((err: Error) => ({\n    error: true,\n    reason: err?.message ?? err?.toString() ?? 'Unknown Error',\n    external: false,\n    sourceStackFrame: source,\n    originalStackFrame: null,\n    originalCodeFrame: null,\n    sourcePackage: null,\n    ignored: false,\n  }))\n}\n\nexport function getOriginalStackFrames(\n  frames: StackFrame[],\n  type: 'server' | 'edge-server' | null,\n  isAppDir: boolean,\n  errorMessage: string\n) {\n  return Promise.all(\n    frames.map((frame) =>\n      getOriginalStackFrame(frame, type, isAppDir, errorMessage)\n    )\n  )\n}\n\nexport function getFrameSource(frame: StackFrame): string {\n  if (!frame.file) return ''\n\n  const isWebpackFrame = isWebpackInternalResource(frame.file)\n\n  let str = ''\n  // Skip URL parsing for webpack internal file paths.\n  if (isWebpackFrame) {\n    str = formatFrameSourceFile(frame.file)\n  } else {\n    try {\n      const u = new URL(frame.file)\n\n      let parsedPath = ''\n      // Strip the origin for same-origin scripts.\n      if (globalThis.location?.origin !== u.origin) {\n        // URLs can be valid without an `origin`, so long as they have a\n        // `protocol`. However, `origin` is preferred.\n        if (u.origin === 'null') {\n          parsedPath += u.protocol\n        } else {\n          parsedPath += u.origin\n        }\n      }\n\n      // Strip query string information as it's typically too verbose to be\n      // meaningful.\n      parsedPath += u.pathname\n      str = formatFrameSourceFile(parsedPath)\n    } catch {\n      str = formatFrameSourceFile(frame.file)\n    }\n  }\n\n  if (!isWebpackInternalResource(frame.file) && frame.lineNumber != null) {\n    if (str) {\n      if (frame.column != null) {\n        str += ` (${frame.lineNumber}:${frame.column})`\n      } else {\n        str += ` (${frame.lineNumber})`\n      }\n    }\n  }\n  return str\n}\n"], "names": ["isWebpackInternalResource", "formatFrameSourceFile", "getOriginalStackFrame", "source", "type", "isAppDir", "errorMessage", "_getOriginalStackFrame", "body", "params", "URLSearchParams", "append", "String", "key", "toString", "controller", "AbortController", "tm", "setTimeout", "abort", "res", "self", "fetch", "process", "env", "__NEXT_ROUTER_BASEPATH", "signal", "finally", "clearTimeout", "ok", "status", "Promise", "reject", "Error", "text", "json", "error", "reason", "external", "sourceStackFrame", "originalStackFrame", "originalCodeFrame", "sourcePackage", "ignored", "file", "match", "resolve", "catch", "err", "message", "getOriginalStackFrames", "frames", "all", "map", "frame", "getFrameSource", "isWebpackFrame", "str", "globalThis", "u", "URL", "parsed<PERSON><PERSON>", "location", "origin", "protocol", "pathname", "lineNumber", "column"], "mappings": ";;;;AAEA,SACEA,yBAAyB,EACzBC,qBAAqB,QAChB,wBAAuB;;AAS9B,SAASC,sBACPC,MAAkB,EAClBC,IAAqC,EACrCC,QAAiB,EACjBC,YAAoB;QA4ClBH,cACAA;IA3CF,eAAeI;YAmCFC;QAlCX,MAAMC,SAAS,IAAIC;QACnBD,OAAOE,MAAM,CAAC,YAAYC,OAAOR,SAAS;QAC1CK,OAAOE,MAAM,CAAC,gBAAgBC,OAAOR,SAAS;QAC9CK,OAAOE,MAAM,CAAC,kBAAkBC,OAAOP;QACvCI,OAAOE,MAAM,CAAC,gBAAgBL;QAC9B,IAAK,MAAMO,OAAOV,OAAQ;gBACJ;YAApBM,OAAOE,MAAM,CAACE,KAAM,CAAA,CAAA,cAACV,MAAc,CAACU,IAAI,KAAA,OAApB,cAAwB,EAAC,EAAGC,QAAQ;QAC1D;QAEA,MAAMC,aAAa,IAAIC;QACvB,MAAMC,KAAKC,WAAW,IAAMH,WAAWI,KAAK,IAAI;QAChD,MAAMC,MAAM,MAAMC,KACfC,KAAK,CAEFC,CAAAA,QAAQC,GAAG,CAACC,sBAAsB,MAAI,EAAC,IACxC,oCAAiChB,OAAOK,QAAQ,IACjD;YAAEY,QAAQX,WAAWW,MAAM;QAAC,GAE7BC,OAAO,CAAC;YACPC,aAAaX;QACf;QACF,IAAI,CAACG,IAAIS,EAAE,IAAIT,IAAIU,MAAM,KAAK,KAAK;YACjC,OAAOC,QAAQC,MAAM,CAAC,IAAIC,MAAM,MAAMb,IAAIc,IAAI;QAChD;QAEA,MAAM1B,OAAmC,MAAMY,IAAIe,IAAI;QACvD,OAAO;YACLC,OAAO;YACPC,QAAQ;YACRC,UAAU;YACVC,kBAAkBpC;YAClBqC,oBAAoBhC,KAAKgC,kBAAkB;YAC3CC,mBAAmBjC,KAAKiC,iBAAiB,IAAI;YAC7CC,eAAelC,KAAKkC,aAAa;YACjCC,SAASnC,CAAAA,CAAAA,2BAAAA,KAAKgC,kBAAkB,KAAA,OAAA,KAAA,IAAvBhC,yBAAyBmC,OAAO,KAAI;QAC/C;IACF;IAEA,qDAAqD;IACrD,IACExC,OAAOyC,IAAI,KAAK,aAAA,CAAA,CAChBzC,eAAAA,OAAOyC,IAAI,KAAA,OAAA,KAAA,IAAXzC,aAAa0C,KAAK,CAAC,SAAA,KAAA,CAAA,CACnB1C,gBAAAA,OAAOyC,IAAI,KAAA,OAAA,KAAA,IAAXzC,cAAa0C,KAAK,CAAC,cAAA,GACnB;QACA,OAAOd,QAAQe,OAAO,CAAC;YACrBV,OAAO;YACPC,QAAQ;YACRC,UAAU;YACVC,kBAAkBpC;YAClBqC,oBAAoB;YACpBC,mBAAmB;YACnBC,eAAe;YACfC,SAAS;QACX;IACF;IAEA,OAAOpC,yBAAyBwC,KAAK,CAAC,CAACC;YAE7BA,cAAAA;eAF6C;YACrDZ,OAAO;YACPC,QAAQW,CAAAA,OAAAA,CAAAA,eAAAA,OAAAA,OAAAA,KAAAA,IAAAA,IAAKC,OAAO,KAAA,OAAZD,eAAgBA,OAAAA,OAAAA,KAAAA,IAAAA,IAAKlC,QAAQ,EAAA,KAAA,OAA7BkC,OAAmC;YAC3CV,UAAU;YACVC,kBAAkBpC;YAClBqC,oBAAoB;YACpBC,mBAAmB;YACnBC,eAAe;YACfC,SAAS;QACX;;AACF;AAEO,SAASO,uBACdC,MAAoB,EACpB/C,IAAqC,EACrCC,QAAiB,EACjBC,YAAoB;IAEpB,OAAOyB,QAAQqB,GAAG,CAChBD,OAAOE,GAAG,CAAC,CAACC,QACVpD,sBAAsBoD,OAAOlD,MAAMC,UAAUC;AAGnD;AAEO,SAASiD,eAAeD,KAAiB;IAC9C,IAAI,CAACA,MAAMV,IAAI,EAAE,OAAO;IAExB,MAAMY,wQAAiBxD,4BAAAA,EAA0BsD,MAAMV,IAAI;IAE3D,IAAIa,MAAM;IACV,oDAAoD;IACpD,IAAID,gBAAgB;QAClBC,6PAAMxD,wBAAAA,EAAsBqD,MAAMV,IAAI;IACxC,OAAO;QACL,IAAI;gBAKEc;YAJJ,MAAMC,IAAI,IAAIC,IAAIN,MAAMV,IAAI;YAE5B,IAAIiB,aAAa;YACjB,4CAA4C;YAC5C,IAAIH,CAAAA,CAAAA,uBAAAA,WAAWI,QAAQ,KAAA,OAAA,KAAA,IAAnBJ,qBAAqBK,MAAM,MAAKJ,EAAEI,MAAM,EAAE;gBAC5C,gEAAgE;gBAChE,8CAA8C;gBAC9C,IAAIJ,EAAEI,MAAM,KAAK,QAAQ;oBACvBF,cAAcF,EAAEK,QAAQ;gBAC1B,OAAO;oBACLH,cAAcF,EAAEI,MAAM;gBACxB;YACF;YAEA,qEAAqE;YACrE,cAAc;YACdF,cAAcF,EAAEM,QAAQ;YACxBR,OAAMxD,8QAAAA,EAAsB4D;QAC9B,EAAE,OAAA,GAAM;YACNJ,6PAAMxD,wBAAAA,EAAsBqD,MAAMV,IAAI;QACxC;IACF;IAEA,IAAI,EAAC5C,kRAAAA,EAA0BsD,MAAMV,IAAI,KAAKU,MAAMY,UAAU,IAAI,MAAM;QACtE,IAAIT,KAAK;YACP,IAAIH,MAAMa,MAAM,IAAI,MAAM;gBACxBV,OAAQ,OAAIH,MAAMY,UAAU,GAAC,MAAGZ,MAAMa,MAAM,GAAC;YAC/C,OAAO;gBACLV,OAAQ,OAAIH,MAAMY,UAAU,GAAC;YAC/B;QACF;IACF;IACA,OAAOT;AACT", "ignoreList": [0]}}, {"offset": {"line": 4453, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4459, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/get-error-by-type.ts"], "sourcesContent": ["import {\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n} from '../../shared'\nimport type { SupportedErrorEvent } from '../container/Errors'\nimport { getOriginalStackFrames } from './stack-frame'\nimport type { OriginalStackFrame } from './stack-frame'\nimport type { ComponentStackFrame } from './parse-component-stack'\nimport { getErrorSource } from '../../../../../shared/lib/error-source'\n\nexport type ReadyRuntimeError = {\n  id: number\n  runtime: true\n  error: Error\n  frames: OriginalStackFrame[]\n  componentStackFrames?: ComponentStackFrame[]\n}\n\nexport async function getErrorByType(\n  ev: SupportedErrorEvent,\n  isAppDir: boolean\n): Promise<ReadyRuntimeError> {\n  const { id, event } = ev\n  switch (event.type) {\n    case ACTION_UNHANDLED_ERROR:\n    case ACTION_UNHANDLED_REJECTION: {\n      const readyRuntimeError: ReadyRuntimeError = {\n        id,\n        runtime: true,\n        error: event.reason,\n        frames: await getOriginalStackFrames(\n          event.frames,\n          getErrorSource(event.reason),\n          isAppDir,\n          event.reason.toString()\n        ),\n      }\n      if (event.type === ACTION_UNHANDLED_ERROR) {\n        readyRuntimeError.componentStackFrames = event.componentStackFrames\n      }\n      return readyRuntimeError\n    }\n    default: {\n      break\n    }\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const _: never = event\n  throw new Error('type system invariant violation')\n}\n"], "names": ["ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "getOriginalStackFrames", "getErrorSource", "getErrorByType", "ev", "isAppDir", "id", "event", "type", "readyRuntimeError", "runtime", "error", "reason", "frames", "toString", "componentStackFrames", "_", "Error"], "mappings": ";;;AAAA,SACEA,sBAAsB,EACtBC,0BAA0B,QACrB,eAAc;AAErB,SAASC,sBAAsB,QAAQ,gBAAe;AAGtD,SAASC,cAAc,QAAQ,yCAAwC;;;;AAUhE,eAAeC,eACpBC,EAAuB,EACvBC,QAAiB;IAEjB,MAAM,EAAEC,EAAE,EAAEC,KAAK,EAAE,GAAGH;IACtB,OAAQG,MAAMC,IAAI;QAChB,8MAAKT,yBAAAA;QACL,8MAAKC,6BAAAA;YAA4B;gBAC/B,MAAMS,oBAAuC;oBAC3CH;oBACAI,SAAS;oBACTC,OAAOJ,MAAMK,MAAM;oBACnBC,QAAQ,kPAAMZ,yBAAAA,EACZM,MAAMM,MAAM,GACZX,oMAAAA,EAAeK,MAAMK,MAAM,GAC3BP,UACAE,MAAMK,MAAM,CAACE,QAAQ;gBAEzB;gBACA,IAAIP,MAAMC,IAAI,8MAAKT,yBAAAA,EAAwB;oBACzCU,kBAAkBM,oBAAoB,GAAGR,MAAMQ,oBAAoB;gBACrE;gBACA,OAAON;YACT;QACA;YAAS;gBACP;YACF;IACF;IACA,6DAA6D;IAC7D,MAAMO,IAAWT;IACjB,MAAM,IAAIU,MAAM;AAClB", "ignoreList": [0]}}, {"offset": {"line": 4494, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4500, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.tsx"], "sourcesContent": ["import Anser from 'next/dist/compiled/anser'\nimport * as React from 'react'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { getFrameSource } from '../../helpers/stack-frame'\nimport { useOpenInEditor } from '../../helpers/use-open-in-editor'\nimport { HotlinkedText } from '../hot-linked-text'\n\nexport type CodeFrameProps = { stackFrame: StackFrame; codeFrame: string }\n\nexport const CodeFrame: React.FC<CodeFrameProps> = function CodeFrame({\n  stackFrame,\n  codeFrame,\n}) {\n  // Strip leading spaces out of the code frame:\n  const formattedFrame = React.useMemo<string>(() => {\n    const lines = codeFrame.split(/\\r?\\n/g)\n\n    // Find the minimum length of leading spaces after `|` in the code frame\n    const miniLeadingSpacesLength = lines\n      .map((line) =>\n        /^>? +\\d+ +\\| [ ]+/.exec(stripAnsi(line)) === null\n          ? null\n          : /^>? +\\d+ +\\| ( *)/.exec(stripAnsi(line))\n      )\n      .filter(Boolean)\n      .map((v) => v!.pop()!)\n      .reduce((c, n) => (isNaN(c) ? n.length : Math.min(c, n.length)), NaN)\n\n    // When the minimum length of leading spaces is greater than 1, remove them\n    // from the code frame to help the indentation looks better when there's a lot leading spaces.\n    if (miniLeadingSpacesLength > 1) {\n      return lines\n        .map((line, a) =>\n          ~(a = line.indexOf('|'))\n            ? line.substring(0, a) +\n              line.substring(a).replace(`^\\\\ {${miniLeadingSpacesLength}}`, '')\n            : line\n        )\n        .join('\\n')\n    }\n    return lines.join('\\n')\n  }, [codeFrame])\n\n  const decoded = React.useMemo(() => {\n    return Anser.ansiToJson(formattedFrame, {\n      json: true,\n      use_classes: true,\n      remove_empty: true,\n    })\n  }, [formattedFrame])\n\n  const open = useOpenInEditor({\n    file: stackFrame.file,\n    lineNumber: stackFrame.lineNumber,\n    column: stackFrame.column,\n  })\n\n  // TODO: make the caret absolute\n  return (\n    <div data-nextjs-codeframe>\n      <div>\n        <p\n          role=\"link\"\n          onClick={open}\n          tabIndex={1}\n          title=\"Click to open in your editor\"\n        >\n          <span>\n            {getFrameSource(stackFrame)} @{' '}\n            <HotlinkedText text={stackFrame.methodName} />\n          </span>\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n          >\n            <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n            <polyline points=\"15 3 21 3 21 9\"></polyline>\n            <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n          </svg>\n        </p>\n      </div>\n      <pre>\n        {decoded.map((entry, index) => (\n          <span\n            key={`frame-${index}`}\n            style={{\n              color: entry.fg ? `var(--color-${entry.fg})` : undefined,\n              ...(entry.decoration === 'bold'\n                ? { fontWeight: 800 }\n                : entry.decoration === 'italic'\n                  ? { fontStyle: 'italic' }\n                  : undefined),\n            }}\n          >\n            {entry.content}\n          </span>\n        ))}\n      </pre>\n    </div>\n  )\n}\n"], "names": ["<PERSON><PERSON>", "React", "stripAnsi", "getFrameSource", "useOpenInEditor", "HotlinkedText", "CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "useMemo", "lines", "split", "miniLeadingSpacesLength", "map", "line", "exec", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "a", "indexOf", "substring", "replace", "join", "decoded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "file", "lineNumber", "column", "div", "data-nextjs-codeframe", "p", "role", "onClick", "tabIndex", "title", "span", "text", "methodName", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2", "pre", "entry", "index", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content"], "mappings": ";;;;AAAA,OAAOA,WAAW,2BAA0B;AAC5C,YAAYC,WAAW,QAAO;AAE9B,OAAOC,eAAe,gCAA+B;AACrD,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,aAAa,QAAQ,qBAAoB;;;;;;;;AAI3C,MAAMC,YAAsC,SAASA,UAAU,KAGrE;IAHqE,IAAA,EACpEC,UAAU,EACVC,SAAS,EACV,GAHqE;IAIpE,8CAA8C;IAC9C,MAAMC,iBAAiBR,mKAAMS,OAAO,CAAS;QAC3C,MAAMC,QAAQH,UAAUI,KAAK,CAAC;QAE9B,wEAAwE;QACxE,MAAMC,0BAA0BF,MAC7BG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,gLAACd,UAAAA,EAAUa,WAAW,OAC1C,OACA,oBAAoBC,IAAI,gLAACd,UAAAA,EAAUa,QAExCE,MAAM,CAACC,SACPJ,GAAG,CAAC,CAACK,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;QAEnE,2EAA2E;QAC3E,8FAA8F;QAC9F,IAAIf,0BAA0B,GAAG;YAC/B,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMc,IACV,CAAEA,CAAAA,IAAId,KAAKe,OAAO,CAAC,IAAG,IAClBf,KAAKgB,SAAS,CAAC,GAAGF,KAClBd,KAAKgB,SAAS,CAACF,GAAGG,OAAO,CAAE,UAAOnB,0BAAwB,KAAI,MAC9DE,MAELkB,IAAI,CAAC;QACV;QACA,OAAOtB,MAAMsB,IAAI,CAAC;IACpB,GAAG;QAACzB;KAAU;IAEd,MAAM0B,UAAUjC,mKAAMS,OAAO,CAAC;QAC5B,0KAAOV,UAAAA,CAAMmC,UAAU,CAAC1B,gBAAgB;YACtC2B,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAAC7B;KAAe;IAEnB,MAAM8B,gQAAOnC,kBAAAA,EAAgB;QAC3BoC,MAAMjC,WAAWiC,IAAI;QACrBC,YAAYlC,WAAWkC,UAAU;QACjCC,QAAQnC,WAAWmC,MAAM;IAC3B;IAEA,gCAAgC;IAChC,OAAA,WAAA,mLACE,OAAA,EAACC,OAAAA;QAAIC,uBAAqB,EAAA;;2BACxB,qLAAA,EAACD,OAAAA;0BACC,WAAA,mLAAA,OAAA,EAACE,KAAAA;oBACCC,MAAK;oBACLC,SAASR;oBACTS,UAAU;oBACVC,OAAM;;sNAEN,OAAA,EAACC,QAAAA;;4QACE/C,iBAAAA,EAAeI;gCAAY;gCAAG;8CAC/B,sLAAA,6PAACF,gBAAAA,EAAAA;oCAAc8C,MAAM5C,WAAW6C,UAAU;;;;sNAE5C,OAAA,EAACC,OAAAA;4BACCC,OAAM;4BACNC,SAAQ;4BACRC,MAAK;4BACLC,QAAO;4BACPC,aAAY;4BACZC,eAAc;4BACdC,gBAAe;;8NAEf,MAAA,EAACC,QAAAA;oCAAKC,GAAE;;8NACR,MAAA,EAACC,YAAAA;oCAASC,QAAO;;8NACjB,MAAA,EAACjD,QAAAA;oCAAKkD,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;;;;;;;0MAIvC,MAAA,EAACC,OAAAA;0BACEnC,QAAQpB,GAAG,CAAC,CAACwD,OAAOC,QAAAA,WAAAA,mLACnB,MAAA,EAACrB,QAAAA;wBAECsB,OAAO;4BACLC,OAAOH,MAAMI,EAAE,GAAI,iBAAcJ,MAAMI,EAAE,GAAC,MAAKC;4BAC/C,GAAIL,MAAMM,UAAU,KAAK,SACrB;gCAAEC,YAAY;4BAAI,IAClBP,MAAMM,UAAU,KAAK,WACnB;gCAAEE,WAAW;4BAAS,IACtBH,SAAS;wBACjB;kCAECL,MAAMS,OAAO;uBAVR,WAAQR;;;;AAgB1B,EAAC", "ignoreList": [0]}}, {"offset": {"line": 4610, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4616, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/CodeFrame/index.tsx"], "sourcesContent": ["export { CodeFrame } from './CodeFrame'\n"], "names": ["CodeFrame"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4619, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4634, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.tsx"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  getFrameSource,\n  type OriginalStackFrame,\n} from '../../helpers/stack-frame'\nimport { useOpenInEditor } from '../../helpers/use-open-in-editor'\nimport { HotlinkedText } from '../../components/hot-linked-text'\n\nexport const CallStackFrame: React.FC<{\n  frame: OriginalStackFrame\n}> = function CallStackFrame({ frame }) {\n  // TODO: ability to expand resolved frames\n  // TODO: render error or external indicator\n\n  const f: StackFrame = frame.originalStackFrame ?? frame.sourceStackFrame\n  const hasSource = Boolean(frame.originalCodeFrame)\n  const open = useOpenInEditor(\n    hasSource\n      ? {\n          file: f.file,\n          lineNumber: f.lineNumber,\n          column: f.column,\n        }\n      : undefined\n  )\n\n  // Format method to strip out the webpack layer prefix.\n  // e.g. (app-pages-browser)/./app/page.tsx -> ./app/page.tsx\n  const formattedMethod = f.methodName.replace(/^\\([\\w-]+\\)\\//, '')\n\n  // Formatted file source could be empty. e.g. <anonymous> will be formatted to empty string,\n  // we'll skip rendering the frame in this case.\n  const fileSource = getFrameSource(f)\n  if (!fileSource) {\n    return null\n  }\n\n  return (\n    <div data-nextjs-call-stack-frame>\n      <h3 data-nextjs-frame-expanded={!frame.ignored}>\n        <HotlinkedText text={formattedMethod} />\n      </h3>\n      <div\n        data-has-source={hasSource ? 'true' : undefined}\n        data-no-source={hasSource ? undefined : 'true'}\n        tabIndex={hasSource ? 10 : undefined}\n        role={hasSource ? 'link' : undefined}\n        onClick={open}\n        title={hasSource ? 'Click to open in your editor' : undefined}\n      >\n        <span>{fileSource}</span>\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        >\n          <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n          <polyline points=\"15 3 21 3 21 9\"></polyline>\n          <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n        </svg>\n      </div>\n    </div>\n  )\n}\n"], "names": ["getFrameSource", "useOpenInEditor", "HotlinkedText", "CallStackFrame", "frame", "f", "originalStackFrame", "sourceStackFrame", "hasSource", "Boolean", "originalCodeFrame", "open", "file", "lineNumber", "column", "undefined", "formattedMethod", "methodName", "replace", "fileSource", "div", "data-nextjs-call-stack-frame", "h3", "data-nextjs-frame-expanded", "ignored", "text", "data-has-source", "data-no-source", "tabIndex", "role", "onClick", "title", "span", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2"], "mappings": ";;;;AACA,SACEA,cAAc,QAET,4BAA2B;AAClC,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,aAAa,QAAQ,mCAAkC;;;;;AAEzD,MAAMC,iBAER,SAASA,eAAe,KAAS;IAAT,IAAA,EAAEC,KAAK,EAAE,GAAT;QAILA;IAHtB,0CAA0C;IAC1C,2CAA2C;IAE3C,MAAMC,IAAgBD,CAAAA,4BAAAA,MAAME,kBAAkB,KAAA,OAAxBF,4BAA4BA,MAAMG,gBAAgB;IACxE,MAAMC,YAAYC,QAAQL,MAAMM,iBAAiB;IACjD,MAAMC,WAAOV,uQAAAA,EACXO,YACI;QACEI,MAAMP,EAAEO,IAAI;QACZC,YAAYR,EAAEQ,UAAU;QACxBC,QAAQT,EAAES,MAAM;IAClB,IACAC;IAGN,uDAAuD;IACvD,4DAA4D;IAC5D,MAAMC,kBAAkBX,EAAEY,UAAU,CAACC,OAAO,CAAC,iBAAiB;IAE9D,4FAA4F;IAC5F,+CAA+C;IAC/C,MAAMC,aAAanB,6PAAAA,EAAeK;IAClC,IAAI,CAACc,YAAY;QACf,OAAO;IACT;IAEA,OAAA,WAAA,OACE,mLAAA,EAACC,OAAAA;QAAIC,8BAA4B,EAAA;;0MAC/B,MAAA,EAACC,MAAAA;gBAAGC,8BAA4B,CAACnB,MAAMoB,OAAO;0BAC5C,WAAA,mLAAA,MAAA,6PAACtB,gBAAAA,EAAAA;oBAAcuB,MAAMT;;;2BAEvB,sLAAA,EAACI,OAAAA;gBACCM,mBAAiBlB,YAAY,SAASO;gBACtCY,kBAAgBnB,YAAYO,YAAY;gBACxCa,UAAUpB,YAAY,KAAKO;gBAC3Bc,MAAMrB,YAAY,SAASO;gBAC3Be,SAASnB;gBACToB,OAAOvB,YAAY,iCAAiCO;;kNAEpD,MAAA,EAACiB,QAAAA;kCAAMb;;kNACP,OAAA,EAACc,OAAAA;wBACCC,OAAM;wBACNC,SAAQ;wBACRC,MAAK;wBACLC,QAAO;wBACPC,aAAY;wBACZC,eAAc;wBACdC,gBAAe;;0NAEf,MAAA,EAACC,QAAAA;gCAAKC,GAAE;;0NACR,MAAA,EAACC,YAAAA;gCAASC,QAAO;;0NACjB,MAAA,EAACC,QAAAA;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;;;;;;;;AAK3C,EAAC", "ignoreList": [0]}}, {"offset": {"line": 4714, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4720, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/container/RuntimeError/index.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { CodeFrame } from '../../components/CodeFrame'\nimport type { ReadyRuntimeError } from '../../helpers/get-error-by-type'\nimport { noop as css } from '../../helpers/noop-template'\nimport { CallStackFrame } from './CallStackFrame'\n\nexport type RuntimeErrorProps = { error: ReadyRuntimeError }\n\nexport function RuntimeError({ error }: RuntimeErrorProps) {\n  const [isIgnoredExpanded, setIsIgnoredExpanded] = React.useState(false)\n  const {\n    firstFrame,\n    allLeadingFrames,\n    trailingCallStackFrames,\n    displayedFramesCount,\n  } = React.useMemo(() => {\n    const filteredFrames = error.frames.filter((frame) =>\n      isIgnoredExpanded ? true : !frame.ignored\n    )\n\n    const firstFirstPartyFrameIndex = filteredFrames.findIndex(\n      (entry) =>\n        !entry.ignored &&\n        Boolean(entry.originalCodeFrame) &&\n        Boolean(entry.originalStackFrame)\n    )\n\n    return {\n      displayedFramesCount: filteredFrames.length,\n      firstFrame: filteredFrames[firstFirstPartyFrameIndex] ?? null,\n      allLeadingFrames:\n        firstFirstPartyFrameIndex < 0\n          ? []\n          : filteredFrames.slice(0, firstFirstPartyFrameIndex),\n      trailingCallStackFrames: filteredFrames.slice(\n        firstFirstPartyFrameIndex + 1\n      ),\n    }\n  }, [error.frames, isIgnoredExpanded])\n\n  return (\n    <React.Fragment>\n      {firstFrame ? (\n        <>\n          <h2>Source</h2>\n          {allLeadingFrames.map((frame, frameIndex) => (\n            <CallStackFrame\n              key={`call-stack-leading-${frameIndex}`}\n              frame={frame}\n            />\n          ))}\n          <CodeFrame\n            stackFrame={firstFrame.originalStackFrame!}\n            codeFrame={firstFrame.originalCodeFrame!}\n          />\n        </>\n      ) : undefined}\n\n      {trailingCallStackFrames.map((frame, frameIndex) => (\n        <CallStackFrame\n          key={`call-stack-leading-${frameIndex}`}\n          frame={frame}\n        />\n      ))}\n      {\n        // if the default displayed ignored frames count is equal equal to the total frames count, hide the button\n        displayedFramesCount === error.frames.length &&\n        !isIgnoredExpanded ? null : (\n          <button\n            data-expand-ignore-button={isIgnoredExpanded}\n            onClick={() => setIsIgnoredExpanded(!isIgnoredExpanded)}\n          >\n            {`${isIgnoredExpanded ? 'Hide' : 'Show'} ignored frames`}\n          </button>\n        )\n      }\n    </React.Fragment>\n  )\n}\n\nexport const styles = css`\n  [data-nextjs-call-stack-frame]:not(:last-child),\n  [data-nextjs-component-stack-frame]:not(:last-child) {\n    margin-bottom: var(--size-gap-double);\n  }\n\n  [data-expand-ignore-button]:focus:not(:focus-visible),\n  [data-expand-ignore-button] {\n    background: none;\n    border: none;\n    color: var(--color-font);\n    cursor: pointer;\n    font-size: var(--size-font);\n    margin: var(--size-gap) 0;\n    padding: 0;\n    text-decoration: underline;\n    outline: none;\n  }\n\n  [data-nextjs-data-runtime-error-copy-button],\n  [data-nextjs-data-runtime-error-copy-button]:focus:not(:focus-visible) {\n    position: relative;\n    margin-left: var(--size-gap);\n    padding: 0;\n    border: none;\n    background: none;\n    outline: none;\n  }\n  [data-nextjs-data-runtime-error-copy-button] > svg {\n    vertical-align: middle;\n  }\n  .nextjs-data-runtime-error-copy-button {\n    color: inherit;\n  }\n  .nextjs-data-runtime-error-copy-button--initial:hover {\n    cursor: pointer;\n  }\n  .nextjs-data-runtime-error-copy-button[aria-disabled='true'] {\n    opacity: 0.3;\n    cursor: not-allowed;\n  }\n  .nextjs-data-runtime-error-copy-button--error,\n  .nextjs-data-runtime-error-copy-button--error:hover {\n    color: var(--color-ansi-red);\n  }\n  .nextjs-data-runtime-error-copy-button--success {\n    color: var(--color-ansi-green);\n  }\n\n  [data-nextjs-call-stack-frame] > h3,\n  [data-nextjs-component-stack-frame] > h3 {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-family: var(--font-stack-monospace);\n    font-size: var(--size-font);\n  }\n  [data-nextjs-call-stack-frame] > h3[data-nextjs-frame-expanded='false'] {\n    color: #666;\n    display: inline-block;\n  }\n  [data-nextjs-call-stack-frame] > div,\n  [data-nextjs-component-stack-frame] > div {\n    display: flex;\n    align-items: center;\n    padding-left: calc(var(--size-gap) + var(--size-gap-half));\n    font-size: var(--size-font-small);\n    color: #999;\n  }\n  [data-nextjs-call-stack-frame] > div > svg,\n  [data-nextjs-component-stack-frame] > [role='link'] > svg {\n    width: auto;\n    height: var(--size-font-small);\n    margin-left: var(--size-gap);\n    flex-shrink: 0;\n    display: none;\n  }\n\n  [data-nextjs-call-stack-frame] > div[data-has-source],\n  [data-nextjs-component-stack-frame] > [role='link'] {\n    cursor: pointer;\n  }\n  [data-nextjs-call-stack-frame] > div[data-has-source]:hover,\n  [data-nextjs-component-stack-frame] > [role='link']:hover {\n    text-decoration: underline dotted;\n  }\n  [data-nextjs-call-stack-frame] > div[data-has-source] > svg,\n  [data-nextjs-component-stack-frame] > [role='link'] > svg {\n    display: unset;\n  }\n\n  [data-nextjs-call-stack-framework-icon] {\n    margin-right: var(--size-gap);\n  }\n  [data-nextjs-call-stack-framework-icon='next'] > mask {\n    mask-type: alpha;\n  }\n  [data-nextjs-call-stack-framework-icon='react'] {\n    color: rgb(20, 158, 202);\n  }\n  [data-nextjs-collapsed-call-stack-details][open]\n    [data-nextjs-call-stack-chevron-icon] {\n    transform: rotate(90deg);\n  }\n  [data-nextjs-collapsed-call-stack-details] summary {\n    display: flex;\n    align-items: center;\n    margin-bottom: var(--size-gap);\n    list-style: none;\n  }\n  [data-nextjs-collapsed-call-stack-details] summary::-webkit-details-marker {\n    display: none;\n  }\n\n  [data-nextjs-collapsed-call-stack-details] h3 {\n    color: #666;\n  }\n  [data-nextjs-collapsed-call-stack-details] [data-nextjs-call-stack-frame] {\n    margin-bottom: var(--size-gap-double);\n  }\n\n  [data-nextjs-container-errors-pseudo-html] {\n    position: relative;\n  }\n  [data-nextjs-container-errors-pseudo-html-collapse] {\n    position: absolute;\n    left: 10px;\n    top: 10px;\n    color: inherit;\n    background: none;\n    border: none;\n    padding: 0;\n  }\n  [data-nextjs-container-errors-pseudo-html--diff='add'] {\n    color: var(--color-ansi-green);\n  }\n  [data-nextjs-container-errors-pseudo-html--diff='remove'] {\n    color: var(--color-ansi-red);\n  }\n  [data-nextjs-container-errors-pseudo-html--tag-error] {\n    color: var(--color-ansi-red);\n    font-weight: bold;\n  }\n  /* hide but text are still accessible in DOM */\n  [data-nextjs-container-errors-pseudo-html--hint] {\n    display: inline-block;\n    font-size: 0;\n  }\n  [data-nextjs-container-errors-pseudo-html--tag-adjacent='false'] {\n    color: var(--color-accents-1);\n  }\n`\n"], "names": ["React", "CodeFrame", "noop", "css", "CallStackFrame", "RuntimeError", "error", "isIgnoredExpanded", "setIsIgnoredExpanded", "useState", "firstFrame", "allLeadingFrames", "trailingCallStackFrames", "displayedFramesCount", "useMemo", "filteredFrames", "frames", "filter", "frame", "ignored", "firstFirstPartyFrameIndex", "findIndex", "entry", "Boolean", "originalCodeFrame", "originalStackFrame", "length", "slice", "Fragment", "h2", "map", "frameIndex", "stackFrame", "codeFrame", "undefined", "button", "data-expand-ignore-button", "onClick", "styles"], "mappings": ";;;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,SAAS,QAAQ,6BAA4B;AAEtD,SAASC,QAAQC,GAAG,QAAQ,8BAA6B;AACzD,SAASC,cAAc,QAAQ,mBAAkB;;;;;;;;;;;;;;;;;;AAI1C,SAASC,aAAa,KAA4B;IAA5B,IAAA,EAAEC,KAAK,EAAqB,GAA5B;IAC3B,MAAM,CAACC,mBAAmBC,qBAAqB,GAAGR,mKAAMS,QAAQ,CAAC;IACjE,MAAM,EACJC,UAAU,EACVC,gBAAgB,EAChBC,uBAAuB,EACvBC,oBAAoB,EACrB,GAAGb,mKAAMc,OAAO,CAAC;QAChB,MAAMC,iBAAiBT,MAAMU,MAAM,CAACC,MAAM,CAAC,CAACC,QAC1CX,oBAAoB,OAAO,CAACW,MAAMC,OAAO;QAG3C,MAAMC,4BAA4BL,eAAeM,SAAS,CACxD,CAACC,QACC,CAACA,MAAMH,OAAO,IACdI,QAAQD,MAAME,iBAAiB,KAC/BD,QAAQD,MAAMG,kBAAkB;YAKtBV;QAFd,OAAO;YACLF,sBAAsBE,eAAeW,MAAM;YAC3ChB,YAAYK,CAAAA,4CAAAA,cAAc,CAACK,0BAA0B,KAAA,OAAzCL,4CAA6C;YACzDJ,kBACES,4BAA4B,IACxB,EAAE,GACFL,eAAeY,KAAK,CAAC,GAAGP;YAC9BR,yBAAyBG,eAAeY,KAAK,CAC3CP,4BAA4B;QAEhC;IACF,GAAG;QAACd,MAAMU,MAAM;QAAET;KAAkB;IAEpC,OAAA,WAAA,mLACE,OAAA,EAACP,mKAAM4B,QAAQ,EAAA;;YACZlB,aAAAA,WAAAA,mLACC,OAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;kCACE,sLAAA,EAACmB,MAAAA;kCAAG;;oBACHlB,iBAAiBmB,GAAG,CAAC,CAACZ,OAAOa,aAAAA,WAAAA,OAC5B,kLAAA,4PAAC3B,iBAAAA,EAAAA;4BAECc,OAAOA;2BADD,wBAAqBa;mCAI/B,qLAAA,qPAAC9B,YAAAA,EAAAA;wBACC+B,YAAYtB,WAAWe,kBAAkB;wBACzCQ,WAAWvB,WAAWc,iBAAiB;;;iBAGzCU;YAEHtB,wBAAwBkB,GAAG,CAAC,CAACZ,OAAOa,aAAAA,WAAAA,IACnC,qLAAA,4PAAC3B,iBAAAA,EAAAA;oBAECc,OAAOA;mBADD,wBAAqBa;YAK7B,0GAA0G;YAC1GlB,yBAAyBP,MAAMU,MAAM,CAACU,MAAM,IAC5C,CAACnB,oBAAoB,OAAA,WAAA,mLACnB,MAAA,EAAC4B,UAAAA;gBACCC,6BAA2B7B;gBAC3B8B,SAAS,IAAM7B,qBAAqB,CAACD;0BAEnC,KAAEA,CAAAA,oBAAoB,SAAS,MAAK,IAAE;;;;AAMpD;AAEO,MAAM+B,uPAASnC,OAAAA,EAAAA,mBAsJrB", "ignoreList": [0]}}, {"offset": {"line": 4792, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4798, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/icons/CollapseIcon.tsx"], "sourcesContent": ["export function CollapseIcon({ collapsed }: { collapsed?: boolean } = {}) {\n  return (\n    <svg\n      data-nextjs-call-stack-chevron-icon\n      data-collapsed={collapsed}\n      fill=\"none\"\n      height=\"20\"\n      width=\"20\"\n      shapeRendering=\"geometricPrecision\"\n      stroke=\"currentColor\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"2\"\n      viewBox=\"0 0 24 24\"\n      // rotate 90 degrees if not collapsed.\n      // If collapsed isn't present, the rotation is applied via the `data-nextjs-collapsed-call-stack-details` element's `open` attribute\n      {...(typeof collapsed === 'boolean'\n        ? { style: { transform: collapsed ? undefined : 'rotate(90deg)' } }\n        : {})}\n    >\n      <path d=\"M9 18l6-6-6-6\" />\n    </svg>\n  )\n}\n"], "names": ["CollapseIcon", "collapsed", "svg", "data-nextjs-call-stack-chevron-icon", "data-collapsed", "fill", "height", "width", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "viewBox", "style", "transform", "undefined", "path", "d"], "mappings": ";;;;;AAAO,SAASA,aAAa,KAAA;IAAA,IAAA,EAAEC,SAAS,EAA2B,GAAtC,UAAA,KAAA,IAAyC,CAAC,IAA1C;IAC3B,OAAA,WAAA,IACE,qLAAA,EAACC,OAAAA;QACCC,qCAAmC,EAAA;QACnCC,kBAAgBH;QAChBI,MAAK;QACLC,QAAO;QACPC,OAAM;QACNC,gBAAe;QACfC,QAAO;QACPC,eAAc;QACdC,gBAAe;QACfC,aAAY;QACZC,SAAQ;QAGP,GAAI,OAAOZ,cAAc,YACtB;YAAEa,OAAO;gBAAEC,WAAWd,YAAYe,YAAY;YAAgB;QAAE,IAChE,CAAC,CAAC;kBAEN,WAAA,mLAAA,MAAA,EAACC,QAAAA;YAAKC,GAAE;;;AAGd", "ignoreList": [0]}}, {"offset": {"line": 4827, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4833, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/container/RuntimeError/component-stack-pseudo-html.tsx"], "sourcesContent": ["import { useMemo, Fragment, useState } from 'react'\nimport type { ComponentStackFrame } from '../../helpers/parse-component-stack'\nimport { CollapseIcon } from '../../icons/CollapseIcon'\n\nfunction getAdjacentProps(isAdj: boolean) {\n  return { 'data-nextjs-container-errors-pseudo-html--tag-adjacent': isAdj }\n}\n\n/**\n *\n * Format component stack into pseudo HTML\n * component stack is an array of strings, e.g.: ['p', 'p', 'Page', ...]\n *\n * For html tags mismatch, it will render it for the code block\n *\n * ```\n * <pre>\n *  <code>{`\n *    <Page>\n *       <p red>\n *         <p red>\n *  `}</code>\n * </pre>\n * ```\n *\n * For text mismatch, it will render it for the code block\n *\n * ```\n * <pre>\n * <code>{`\n *   <Page>\n *     <p>\n *       \"Server Text\" (green)\n *       \"Client Text\" (red)\n *     </p>\n *   </Page>\n * `}</code>\n * ```\n *\n * For bad text under a tag it will render it for the code block,\n * e.g. \"Mismatched Text\" under <p>\n *\n * ```\n * <pre>\n * <code>{`\n *   <Page>\n *     <div>\n *       <p>\n *         \"Mismatched Text\" (red)\n *      </p>\n *     </div>\n *   </Page>\n * `}</code>\n * ```\n *\n */\nexport function PseudoHtmlDiff({\n  componentStackFrames,\n  firstContent,\n  secondContent,\n  hydrationMismatchType,\n  reactOutputComponentDiff,\n  ...props\n}: {\n  componentStackFrames: ComponentStackFrame[]\n  firstContent: string\n  secondContent: string\n  reactOutputComponentDiff: string | undefined\n  hydrationMismatchType: 'tag' | 'text' | 'text-in-tag'\n} & React.HTMLAttributes<HTMLPreElement>) {\n  const isHtmlTagsWarning = hydrationMismatchType === 'tag'\n  const isReactHydrationDiff = !!reactOutputComponentDiff\n\n  // For text mismatch, mismatched text will take 2 rows, so we display 4 rows of component stack\n  const MAX_NON_COLLAPSED_FRAMES = isHtmlTagsWarning ? 6 : 4\n  const [isHtmlCollapsed, toggleCollapseHtml] = useState(true)\n\n  const htmlComponents = useMemo(() => {\n    const componentStacks: React.ReactNode[] = []\n    // React 19 unified mismatch\n    if (isReactHydrationDiff) {\n      let currentComponentIndex = componentStackFrames.length - 1\n      const reactComponentDiffLines = reactOutputComponentDiff.split('\\n')\n      const diffHtmlStack: React.ReactNode[] = []\n      reactComponentDiffLines.forEach((line, index) => {\n        let trimmedLine = line.trim()\n        const isDiffLine = trimmedLine[0] === '+' || trimmedLine[0] === '-'\n        const spaces = ' '.repeat(Math.max(componentStacks.length * 2, 1))\n\n        if (isDiffLine) {\n          const sign = trimmedLine[0]\n          trimmedLine = trimmedLine.slice(1).trim() // trim spaces after sign\n          diffHtmlStack.push(\n            <span\n              key={'comp-diff' + index}\n              data-nextjs-container-errors-pseudo-html--diff={\n                sign === '+' ? 'add' : 'remove'\n              }\n            >\n              {sign}\n              {spaces}\n              {trimmedLine}\n              {'\\n'}\n            </span>\n          )\n        } else if (currentComponentIndex >= 0) {\n          const isUserLandComponent = trimmedLine.startsWith(\n            '<' + componentStackFrames[currentComponentIndex].component\n          )\n          // If it's matched userland component or it's ... we will keep the component stack in diff\n          if (isUserLandComponent || trimmedLine === '...') {\n            currentComponentIndex--\n            componentStacks.push(\n              <span key={'comp-diff' + index}>\n                {spaces}\n                {trimmedLine}\n                {'\\n'}\n              </span>\n            )\n          } else if (!isHtmlCollapsed) {\n            componentStacks.push(\n              <span key={'comp-diff' + index}>\n                {spaces}\n                {trimmedLine}\n                {'\\n'}\n              </span>\n            )\n          }\n        } else if (!isHtmlCollapsed) {\n          // In general, if it's not collapsed, show the whole diff\n          componentStacks.push(\n            <span key={'comp-diff' + index}>\n              {spaces}\n              {trimmedLine}\n              {'\\n'}\n            </span>\n          )\n        }\n      })\n      return componentStacks.concat(diffHtmlStack)\n    }\n\n    const nestedHtmlStack: React.ReactNode[] = []\n    const tagNames = isHtmlTagsWarning\n      ? // tags could have < or > in the name, so we always remove them to match\n        [firstContent.replace(/<|>/g, ''), secondContent.replace(/<|>/g, '')]\n      : []\n\n    let lastText = ''\n\n    const componentStack = componentStackFrames\n      .map((frame) => frame.component)\n      .reverse()\n\n    // [child index, parent index]\n    const matchedIndex = [-1, -1]\n    if (isHtmlTagsWarning) {\n      // Reverse search for the child tag\n      for (let i = componentStack.length - 1; i >= 0; i--) {\n        if (componentStack[i] === tagNames[0]) {\n          matchedIndex[0] = i\n          break\n        }\n      }\n      // Start searching parent tag from child tag above\n      for (let i = matchedIndex[0] - 1; i >= 0; i--) {\n        if (componentStack[i] === tagNames[1]) {\n          matchedIndex[1] = i\n          break\n        }\n      }\n    }\n\n    componentStack.forEach((component, index, componentList) => {\n      const spaces = ' '.repeat(nestedHtmlStack.length * 2)\n\n      // When component is the server or client tag name, highlight it\n      const isHighlightedTag = isHtmlTagsWarning\n        ? index === matchedIndex[0] || index === matchedIndex[1]\n        : tagNames.includes(component)\n      const isAdjacentTag =\n        isHighlightedTag ||\n        Math.abs(index - matchedIndex[0]) <= 1 ||\n        Math.abs(index - matchedIndex[1]) <= 1\n\n      const isLastFewFrames =\n        !isHtmlTagsWarning && index >= componentList.length - 6\n\n      const adjProps = getAdjacentProps(isAdjacentTag)\n\n      if ((isHtmlTagsWarning && isAdjacentTag) || isLastFewFrames) {\n        const codeLine = (\n          <span>\n            {spaces}\n            <span\n              {...adjProps}\n              {...{\n                ...(isHighlightedTag\n                  ? {\n                      'data-nextjs-container-errors-pseudo-html--tag-error':\n                        true,\n                    }\n                  : undefined),\n              }}\n            >\n              {`<${component}>\\n`}\n            </span>\n          </span>\n        )\n        lastText = component\n\n        const wrappedCodeLine = (\n          <Fragment key={nestedHtmlStack.length}>\n            {codeLine}\n            {/* Add ^^^^ to the target tags used for snapshots but not displayed for users */}\n            {isHighlightedTag && (\n              <span data-nextjs-container-errors-pseudo-html--hint>\n                {spaces + '^'.repeat(component.length + 2) + '\\n'}\n              </span>\n            )}\n          </Fragment>\n        )\n        nestedHtmlStack.push(wrappedCodeLine)\n      } else {\n        if (\n          nestedHtmlStack.length >= MAX_NON_COLLAPSED_FRAMES &&\n          isHtmlCollapsed\n        ) {\n          return\n        }\n\n        if (!isHtmlCollapsed || isLastFewFrames) {\n          nestedHtmlStack.push(\n            <span {...adjProps} key={nestedHtmlStack.length}>\n              {spaces}\n              {'<' + component + '>\\n'}\n            </span>\n          )\n        } else if (isHtmlCollapsed && lastText !== '...') {\n          lastText = '...'\n          nestedHtmlStack.push(\n            <span {...adjProps} key={nestedHtmlStack.length}>\n              {spaces}\n              {'...\\n'}\n            </span>\n          )\n        }\n      }\n    })\n    // Hydration mismatch: text or text-tag\n    if (!isHtmlTagsWarning) {\n      const spaces = ' '.repeat(nestedHtmlStack.length * 2)\n      let wrappedCodeLine\n      if (hydrationMismatchType === 'text') {\n        // hydration type is \"text\", represent [server content, client content]\n        wrappedCodeLine = (\n          <Fragment key={nestedHtmlStack.length}>\n            <span data-nextjs-container-errors-pseudo-html--diff=\"remove\">\n              {spaces + `\"${firstContent}\"\\n`}\n            </span>\n            <span data-nextjs-container-errors-pseudo-html--diff=\"add\">\n              {spaces + `\"${secondContent}\"\\n`}\n            </span>\n          </Fragment>\n        )\n      } else if (hydrationMismatchType === 'text-in-tag') {\n        // hydration type is \"text-in-tag\", represent [parent tag, mismatch content]\n        wrappedCodeLine = (\n          <Fragment key={nestedHtmlStack.length}>\n            <span data-nextjs-container-errors-pseudo-html--tag-adjacent>\n              {spaces + `<${secondContent}>\\n`}\n            </span>\n            <span data-nextjs-container-errors-pseudo-html--diff=\"remove\">\n              {spaces + `  \"${firstContent}\"\\n`}\n            </span>\n          </Fragment>\n        )\n      }\n      nestedHtmlStack.push(wrappedCodeLine)\n    }\n\n    return nestedHtmlStack\n  }, [\n    componentStackFrames,\n    isHtmlCollapsed,\n    firstContent,\n    secondContent,\n    isHtmlTagsWarning,\n    hydrationMismatchType,\n    MAX_NON_COLLAPSED_FRAMES,\n    isReactHydrationDiff,\n    reactOutputComponentDiff,\n  ])\n\n  return (\n    <div data-nextjs-container-errors-pseudo-html>\n      <button\n        tabIndex={10} // match CallStackFrame\n        data-nextjs-container-errors-pseudo-html-collapse\n        onClick={() => toggleCollapseHtml(!isHtmlCollapsed)}\n      >\n        <CollapseIcon collapsed={isHtmlCollapsed} />\n      </button>\n      <pre {...props}>\n        <code>{htmlComponents}</code>\n      </pre>\n    </div>\n  )\n}\n"], "names": ["useMemo", "Fragment", "useState", "CollapseIcon", "getAdjacentProps", "isAdj", "PseudoHtmlDiff", "componentStackFrames", "firstContent", "second<PERSON><PERSON>nt", "hydrationMismatchType", "reactOutputComponentDiff", "props", "isHtmlTagsWarning", "isReactHydrationDiff", "MAX_NON_COLLAPSED_FRAMES", "isHtmlCollapsed", "toggleCollapseHtml", "htmlComponents", "componentStacks", "currentComponentIndex", "length", "reactComponentDiffLines", "split", "diffHtmlStack", "for<PERSON>ach", "line", "index", "trimmedLine", "trim", "isDiffLine", "spaces", "repeat", "Math", "max", "sign", "slice", "push", "span", "data-nextjs-container-errors-pseudo-html--diff", "isUserLandComponent", "startsWith", "component", "concat", "nestedHtmlStack", "tagNames", "replace", "lastText", "componentStack", "map", "frame", "reverse", "matchedIndex", "i", "componentList", "isHighlightedTag", "includes", "isAdjacentTag", "abs", "isLastFewFrames", "adjProps", "codeLine", "undefined", "wrappedCodeLine", "data-nextjs-container-errors-pseudo-html--hint", "key", "data-nextjs-container-errors-pseudo-html--tag-adjacent", "div", "data-nextjs-container-errors-pseudo-html", "button", "tabIndex", "data-nextjs-container-errors-pseudo-html-collapse", "onClick", "collapsed", "pre", "code"], "mappings": ";;;;;AAEA,SAASG,YAAY,QAAQ,2BAA0B;;;;;AAEvD,SAASC,iBAAiBC,KAAc;IACtC,OAAO;QAAE,0DAA0DA;IAAM;AAC3E;AAkDO,SAASC,eAAe,KAaS;IAbT,IAAA,EAC7BC,oBAAoB,EACpBC,YAAY,EACZC,aAAa,EACbC,qBAAqB,EACrBC,wBAAwB,EACxB,GAAGC,OAOmC,GAbT;IAc7B,MAAMC,oBAAoBH,0BAA0B;IACpD,MAAMI,uBAAuB,CAAC,CAACH;IAE/B,+FAA+F;IAC/F,MAAMI,2BAA2BF,oBAAoB,IAAI;IACzD,MAAM,CAACG,iBAAiBC,mBAAmB,OAAGf,8KAAAA,EAAS;IAEvD,MAAMgB,wLAAiBlB,UAAAA,EAAQ;QAC7B,MAAMmB,kBAAqC,EAAE;QAC7C,4BAA4B;QAC5B,IAAIL,sBAAsB;YACxB,IAAIM,wBAAwBb,qBAAqBc,MAAM,GAAG;YAC1D,MAAMC,0BAA0BX,yBAAyBY,KAAK,CAAC;YAC/D,MAAMC,gBAAmC,EAAE;YAC3CF,wBAAwBG,OAAO,CAAC,CAACC,MAAMC;gBACrC,IAAIC,cAAcF,KAAKG,IAAI;gBAC3B,MAAMC,aAAaF,WAAW,CAAC,EAAE,KAAK,OAAOA,WAAW,CAAC,EAAE,KAAK;gBAChE,MAAMG,SAAS,IAAIC,MAAM,CAACC,KAAKC,GAAG,CAACf,gBAAgBE,MAAM,GAAG,GAAG;gBAE/D,IAAIS,YAAY;oBACd,MAAMK,OAAOP,WAAW,CAAC,EAAE;oBAC3BA,cAAcA,YAAYQ,KAAK,CAAC,GAAGP,IAAI,GAAG,yBAAyB;;oBACnEL,cAAca,IAAI,CAAA,WAAA,mLAChB,OAAA,EAACC,QAAAA;wBAECC,kDACEJ,SAAS,MAAM,QAAQ;;4BAGxBA;4BACAJ;4BACAH;4BACA;;uBARI,cAAcD;gBAWzB,OAAO,IAAIP,yBAAyB,GAAG;oBACrC,MAAMoB,sBAAsBZ,YAAYa,UAAU,CAChD,MAAMlC,oBAAoB,CAACa,sBAAsB,CAACsB,SAAS;oBAE7D,0FAA0F;oBAC1F,IAAIF,uBAAuBZ,gBAAgB,OAAO;wBAChDR;wBACAD,gBAAgBkB,IAAI,CAAA,WAAA,mLAClB,OAAA,EAACC,QAAAA;;gCACEP;gCACAH;gCACA;;2BAHQ,cAAcD;oBAM7B,OAAO,IAAI,CAACX,iBAAiB;wBAC3BG,gBAAgBkB,IAAI,CAAA,WAAA,OAClB,mLAAA,EAACC,QAAAA;;gCACEP;gCACAH;gCACA;;2BAHQ,cAAcD;oBAM7B;gBACF,OAAO,IAAI,CAACX,iBAAiB;oBAC3B,yDAAyD;oBACzDG,gBAAgBkB,IAAI,CAAA,WAAA,OAClB,mLAAA,EAACC,QAAAA;;4BACEP;4BACAH;4BACA;;uBAHQ,cAAcD;gBAM7B;YACF;YACA,OAAOR,gBAAgBwB,MAAM,CAACnB;QAChC;QAEA,MAAMoB,kBAAqC,EAAE;QAC7C,MAAMC,WAAWhC,oBAEb;YAACL,aAAasC,OAAO,CAAC,QAAQ;YAAKrC,cAAcqC,OAAO,CAAC,QAAQ;SAAI,GACrE,EAAE;QAEN,IAAIC,WAAW;QAEf,MAAMC,iBAAiBzC,qBACpB0C,GAAG,CAAC,CAACC,QAAUA,MAAMR,SAAS,EAC9BS,OAAO;QAEV,8BAA8B;QAC9B,MAAMC,eAAe;YAAC,CAAC;YAAG,CAAC;SAAE;QAC7B,IAAIvC,mBAAmB;YACrB,mCAAmC;YACnC,IAAK,IAAIwC,IAAIL,eAAe3B,MAAM,GAAG,GAAGgC,KAAK,GAAGA,IAAK;gBACnD,IAAIL,cAAc,CAACK,EAAE,KAAKR,QAAQ,CAAC,EAAE,EAAE;oBACrCO,YAAY,CAAC,EAAE,GAAGC;oBAClB;gBACF;YACF;YACA,kDAAkD;YAClD,IAAK,IAAIA,IAAID,YAAY,CAAC,EAAE,GAAG,GAAGC,KAAK,GAAGA,IAAK;gBAC7C,IAAIL,cAAc,CAACK,EAAE,KAAKR,QAAQ,CAAC,EAAE,EAAE;oBACrCO,YAAY,CAAC,EAAE,GAAGC;oBAClB;gBACF;YACF;QACF;QAEAL,eAAevB,OAAO,CAAC,CAACiB,WAAWf,OAAO2B;YACxC,MAAMvB,SAAS,IAAIC,MAAM,CAACY,gBAAgBvB,MAAM,GAAG;YAEnD,gEAAgE;YAChE,MAAMkC,mBAAmB1C,oBACrBc,UAAUyB,YAAY,CAAC,EAAE,IAAIzB,UAAUyB,YAAY,CAAC,EAAE,GACtDP,SAASW,QAAQ,CAACd;YACtB,MAAMe,gBACJF,oBACAtB,KAAKyB,GAAG,CAAC/B,QAAQyB,YAAY,CAAC,EAAE,KAAK,KACrCnB,KAAKyB,GAAG,CAAC/B,QAAQyB,YAAY,CAAC,EAAE,KAAK;YAEvC,MAAMO,kBACJ,CAAC9C,qBAAqBc,SAAS2B,cAAcjC,MAAM,GAAG;YAExD,MAAMuC,WAAWxD,iBAAiBqD;YAElC,IAAK5C,qBAAqB4C,iBAAkBE,iBAAiB;gBAC3D,MAAME,WAAAA,WAAAA,mLACJ,OAAA,EAACvB,QAAAA;;wBACEP;sCACD,sLAAA,EAACO,QAAAA;4BACE,GAAGsB,QAAQ;4BAEV,GAAIL,mBACA;gCACE,uDACE;4BACJ,IACAO,SAAS;sCAGb,MAAGpB,YAAU;;;;gBAIrBK,WAAWL;gBAEX,MAAMqB,kBAAAA,WAAAA,mLACJ,OAAA,qKAAC9D,WAAAA,EAAAA;;wBACE4D;wBAEAN,oBAAAA,WAAAA,mLACC,MAAA,EAACjB,QAAAA;4BAAK0B,gDAA8C,EAAA;sCACjDjC,SAAS,IAAIC,MAAM,CAACU,UAAUrB,MAAM,GAAG,KAAK;;;mBALpCuB,gBAAgBvB,MAAM;gBAUvCuB,gBAAgBP,IAAI,CAAC0B;YACvB,OAAO;gBACL,IACEnB,gBAAgBvB,MAAM,IAAIN,4BAC1BC,iBACA;oBACA;gBACF;gBAEA,IAAI,CAACA,mBAAmB2C,iBAAiB;oBACvCf,gBAAgBP,IAAI,CAAA,WAAA,0KAClB,gBAAA,EAACC,QAAAA;wBAAM,GAAGsB,QAAQ;wBAAEK,KAAKrB,gBAAgBvB,MAAM;;4BAC5CU;4BACA,MAAMW,YAAY;;;gBAGzB,OAAO,IAAI1B,mBAAmB+B,aAAa,OAAO;oBAChDA,WAAW;oBACXH,gBAAgBP,IAAI,CAAA,WAAA,0KAClB,gBAAA,EAACC,QAAAA;wBAAM,GAAGsB,QAAQ;wBAAEK,KAAKrB,gBAAgBvB,MAAM;;4BAC5CU;4BACA;;;gBAGP;YACF;QACF;QACA,uCAAuC;QACvC,IAAI,CAAClB,mBAAmB;YACtB,MAAMkB,SAAS,IAAIC,MAAM,CAACY,gBAAgBvB,MAAM,GAAG;YACnD,IAAI0C;YACJ,IAAIrD,0BAA0B,QAAQ;gBACpC,uEAAuE;gBACvEqD,kBAAAA,WAAAA,mLACE,OAAA,qKAAC9D,WAAAA,EAAAA;;sNACC,MAAA,EAACqC,QAAAA;4BAAKC,kDAA+C;sCAClDR,SAAS,CAAC,MAAGvB,eAAa,KAAG;;uCAEhC,qLAAA,EAAC8B,QAAAA;4BAAKC,kDAA+C;sCAClDR,SAAS,CAAC,MAAGtB,gBAAc,KAAG;;;mBALpBmC,gBAAgBvB,MAAM;YASzC,OAAO,IAAIX,0BAA0B,eAAe;gBAClD,4EAA4E;gBAC5EqD,kBAAAA,WAAAA,mLACE,OAAA,qKAAC9D,WAAAA,EAAAA;;sCACC,sLAAA,EAACqC,QAAAA;4BAAK4B,wDAAsD,EAAA;sCACzDnC,SAAS,CAAC,MAAGtB,gBAAc,KAAG;;uCAEjC,qLAAA,EAAC6B,QAAAA;4BAAKC,kDAA+C;sCAClDR,SAAS,CAAC,QAAKvB,eAAa,KAAG;;;mBALrBoC,gBAAgBvB,MAAM;YASzC;YACAuB,gBAAgBP,IAAI,CAAC0B;QACvB;QAEA,OAAOnB;IACT,GAAG;QACDrC;QACAS;QACAR;QACAC;QACAI;QACAH;QACAK;QACAD;QACAH;KACD;IAED,OAAA,WAAA,IACE,sLAAA,EAACwD,OAAAA;QAAIC,0CAAwC,EAAA;;0MAC3C,MAAA,EAACC,UAAAA;gBACCC,UAAU;gBACVC,mDAAiD,EAAA;gBACjDC,SAAS,IAAMvD,mBAAmB,CAACD;0BAEnC,WAAA,mLAAA,MAAA,qOAACb,gBAAAA,EAAAA;oBAAasE,WAAWzD;;;0MAE3B,MAAA,EAAC0D,OAAAA;gBAAK,GAAG9D,KAAK;0BACZ,WAAA,mLAAA,MAAA,EAAC+D,QAAAA;8BAAMzD;;;;;AAIf", "ignoreList": [0]}}, {"offset": {"line": 5065, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5071, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/hydration-error-info.ts"], "sourcesContent": ["import { getHydrationErrorStackInfo } from '../../../is-hydration-error'\n\nexport type HydrationErrorState = {\n  // Hydration warning template format: <message> <serverContent> <clientContent>\n  warning?: [string, string, string]\n  componentStack?: string\n  serverContent?: string\n  clientContent?: string\n  // React 19 hydration diff format: <notes> <link> <component diff?>\n  notes?: string\n  reactOutputComponentDiff?: string\n}\n\ntype NullableText = string | null | undefined\n\nexport const hydrationErrorState: HydrationErrorState = {}\n\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n  'Warning: In HTML, %s cannot be a child of <%s>.%s\\nThis will cause a hydration error.%s',\n  'Warning: In HTML, %s cannot be a descendant of <%s>.\\nThis will cause a hydration error.%s',\n  'Warning: In HTML, text nodes cannot be a child of <%s>.\\nThis will cause a hydration error.',\n  \"Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\\nThis will cause a hydration error.\",\n  'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n  'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s',\n])\nconst textAndTagsMismatchWarnings = new Set([\n  'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n  'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s',\n])\nconst textMismatchWarning =\n  'Warning: Text content did not match. Server: \"%s\" Client: \"%s\"%s'\n\nexport const getHydrationWarningType = (\n  message: NullableText\n): 'tag' | 'text' | 'text-in-tag' => {\n  if (typeof message !== 'string') {\n    // TODO: Doesn't make sense to treat no message as a hydration error message.\n    // We should bail out somewhere earlier.\n    return 'text'\n  }\n\n  const normalizedMessage = message.startsWith('Warning: ')\n    ? message\n    : `Warning: ${message}`\n\n  if (isHtmlTagsWarning(normalizedMessage)) return 'tag'\n  if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag'\n\n  return 'text'\n}\n\nconst isHtmlTagsWarning = (message: string) => htmlTagsWarnings.has(message)\n\nconst isTextMismatchWarning = (message: string) =>\n  textMismatchWarning === message\nconst isTextInTagsMismatchWarning = (msg: string) =>\n  textAndTagsMismatchWarnings.has(msg)\n\nconst isKnownHydrationWarning = (message: NullableText) => {\n  if (typeof message !== 'string') {\n    return false\n  }\n  // React 18 has the `Warning: ` prefix.\n  // React 19 does not.\n  const normalizedMessage = message.startsWith('Warning: ')\n    ? message\n    : `Warning: ${message}`\n\n  return (\n    isHtmlTagsWarning(normalizedMessage) ||\n    isTextInTagsMismatchWarning(normalizedMessage) ||\n    isTextMismatchWarning(normalizedMessage)\n  )\n}\n\nexport const getReactHydrationDiffSegments = (msg: NullableText) => {\n  if (msg) {\n    const { message, diff } = getHydrationErrorStackInfo(msg)\n    if (message) return [message, diff]\n  }\n  return undefined\n}\n\n/**\n * Patch console.error to capture hydration errors.\n * If any of the knownHydrationWarnings are logged, store the message and component stack.\n * When the hydration runtime error is thrown, the message and component stack are added to the error.\n * This results in a more helpful error message in the error overlay.\n */\n\nexport function storeHydrationErrorStateFromConsoleArgs(...args: any[]) {\n  const [msg, serverContent, clientContent, componentStack] = args\n  if (isKnownHydrationWarning(msg)) {\n    hydrationErrorState.warning = [\n      // remove the last %s from the message\n      msg,\n      serverContent,\n      clientContent,\n    ]\n    hydrationErrorState.componentStack = componentStack\n    hydrationErrorState.serverContent = serverContent\n    hydrationErrorState.clientContent = clientContent\n  }\n}\n"], "names": ["getHydrationErrorStackInfo", "hydrationErrorState", "htmlTagsWarnings", "Set", "textAndTagsMismatchWarnings", "textMismatchWarning", "getHydrationWarningType", "message", "normalizedMessage", "startsWith", "isHtmlTagsWarning", "isTextInTagsMismatchWarning", "has", "isTextMismatchWarning", "msg", "isKnownHydrationWarning", "getReactHydrationDiffSegments", "diff", "undefined", "storeHydrationErrorStateFromConsoleArgs", "args", "serverContent", "clientContent", "componentStack", "warning"], "mappings": ";;;;;;AAAA,SAASA,0BAA0B,QAAQ,8BAA6B;;AAejE,MAAMC,sBAA2C,CAAC,EAAC;AAE1D,iIAAiI;AACjI,MAAMC,mBAAmB,IAAIC,IAAI;IAC/B;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,8BAA8B,IAAID,IAAI;IAC1C;IACA;CACD;AACD,MAAME,sBACJ;AAEK,MAAMC,0BAA0B,CACrCC;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,6EAA6E;QAC7E,wCAAwC;QACxC,OAAO;IACT;IAEA,MAAMC,oBAAoBD,QAAQE,UAAU,CAAC,eACzCF,UACC,cAAWA;IAEhB,IAAIG,kBAAkBF,oBAAoB,OAAO;IACjD,IAAIG,4BAA4BH,oBAAoB,OAAO;IAE3D,OAAO;AACT,EAAC;AAED,MAAME,oBAAoB,CAACH,UAAoBL,iBAAiBU,GAAG,CAACL;AAEpE,MAAMM,wBAAwB,CAACN,UAC7BF,wBAAwBE;AAC1B,MAAMI,8BAA8B,CAACG,MACnCV,4BAA4BQ,GAAG,CAACE;AAElC,MAAMC,0BAA0B,CAACR;IAC/B,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,uCAAuC;IACvC,qBAAqB;IACrB,MAAMC,oBAAoBD,QAAQE,UAAU,CAAC,eACzCF,UACC,cAAWA;IAEhB,OACEG,kBAAkBF,sBAClBG,4BAA4BH,sBAC5BK,sBAAsBL;AAE1B;AAEO,MAAMQ,gCAAgC,CAACF;IAC5C,IAAIA,KAAK;QACP,MAAM,EAAEP,OAAO,EAAEU,IAAI,EAAE,uMAAGjB,6BAAAA,EAA2Bc;QACrD,IAAIP,SAAS,OAAO;YAACA;YAASU;SAAK;IACrC;IACA,OAAOC;AACT,EAAC;AASM,SAASC;IAAwC,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,OAAH,IAAA,MAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,IAAAA,CAAH,KAAA,GAAA,SAAA,CAAA,KAAc;;IACpE,MAAM,CAACN,KAAKO,eAAeC,eAAeC,eAAe,GAAGH;IAC5D,IAAIL,wBAAwBD,MAAM;QAChCb,oBAAoBuB,OAAO,GAAG;YAC5B,sCAAsC;YACtCV;YACAO;YACAC;SACD;QACDrB,oBAAoBsB,cAAc,GAAGA;QACrCtB,oBAAoBoB,aAAa,GAAGA;QACpCpB,oBAAoBqB,aAAa,GAAGA;IACtC;AACF", "ignoreList": [0]}}, {"offset": {"line": 5144, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5150, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/copy-button/index.tsx"], "sourcesContent": ["import * as React from 'react'\n\nfunction useCopyLegacy(content: string) {\n  type CopyState =\n    | {\n        state: 'initial'\n      }\n    | {\n        state: 'error'\n        error: unknown\n      }\n    | { state: 'success' }\n    | { state: 'pending' }\n\n  // This would be simpler with useActionState but we need to support React 18 here.\n  // React 18 also doesn't have async transitions.\n  const [copyState, dispatch] = React.useReducer(\n    (\n      state: CopyState,\n      action:\n        | { type: 'reset' | 'copied' | 'copying' }\n        | { type: 'error'; error: unknown }\n    ): CopyState => {\n      if (action.type === 'reset') {\n        return { state: 'initial' }\n      }\n      if (action.type === 'copied') {\n        return { state: 'success' }\n      }\n      if (action.type === 'copying') {\n        return { state: 'pending' }\n      }\n      if (action.type === 'error') {\n        return { state: 'error', error: action.error }\n      }\n      return state\n    },\n    {\n      state: 'initial',\n    }\n  )\n  function copy() {\n    if (isPending) {\n      return\n    }\n\n    if (!navigator.clipboard) {\n      dispatch({\n        type: 'error',\n        error: new Error('Copy to clipboard is not supported in this browser'),\n      })\n    } else {\n      dispatch({ type: 'copying' })\n      navigator.clipboard.writeText(content).then(\n        () => {\n          dispatch({ type: 'copied' })\n        },\n        (error) => {\n          dispatch({ type: 'error', error })\n        }\n      )\n    }\n  }\n  const reset = React.useCallback(() => {\n    dispatch({ type: 'reset' })\n  }, [])\n\n  const isPending = copyState.state === 'pending'\n\n  return [copyState, copy, reset, isPending] as const\n}\n\nfunction useCopyModern(content: string) {\n  type CopyState =\n    | {\n        state: 'initial'\n      }\n    | {\n        state: 'error'\n        error: unknown\n      }\n    | { state: 'success' }\n\n  const [copyState, dispatch, isPending] = React.useActionState(\n    (\n      state: CopyState,\n      action: 'reset' | 'copy'\n    ): CopyState | Promise<CopyState> => {\n      if (action === 'reset') {\n        return { state: 'initial' }\n      }\n      if (action === 'copy') {\n        if (!navigator.clipboard) {\n          return {\n            state: 'error',\n            error: new Error(\n              'Copy to clipboard is not supported in this browser'\n            ),\n          }\n        }\n        return navigator.clipboard.writeText(content).then(\n          () => {\n            return { state: 'success' }\n          },\n          (error) => {\n            return { state: 'error', error }\n          }\n        )\n      }\n      return state\n    },\n    {\n      state: 'initial',\n    }\n  )\n\n  function copy() {\n    React.startTransition(() => {\n      dispatch('copy')\n    })\n  }\n\n  const reset = React.useCallback(() => {\n    dispatch('reset')\n  }, [\n    // TODO: `dispatch` from `useActionState` is not reactive.\n    // Remove from dependencies once https://github.com/facebook/react/pull/29665 is released.\n    dispatch,\n  ])\n\n  return [copyState, copy, reset, isPending] as const\n}\n\nconst useCopy =\n  typeof React.useActionState === 'function' ? useCopyModern : useCopyLegacy\n\nexport function CopyButton({\n  actionLabel,\n  successLabel,\n  content,\n  icon,\n  disabled,\n  ...props\n}: React.HTMLProps<HTMLButtonElement> & {\n  actionLabel: string\n  successLabel: string\n  content: string\n  icon?: React.ReactNode\n}) {\n  const [copyState, copy, reset, isPending] = useCopy(content)\n\n  const error = copyState.state === 'error' ? copyState.error : null\n  React.useEffect(() => {\n    if (error !== null) {\n      // Additional console.error to get the stack.\n      console.error(error)\n    }\n  }, [error])\n  React.useEffect(() => {\n    if (copyState.state === 'success') {\n      const timeoutId = setTimeout(() => {\n        reset()\n      }, 2000)\n\n      return () => {\n        clearTimeout(timeoutId)\n      }\n    }\n  }, [isPending, copyState.state, reset])\n  const isDisabled = isPending || disabled\n  const label = copyState.state === 'success' ? successLabel : actionLabel\n\n  // Assign default icon\n  const renderedIcon =\n    copyState.state === 'success' ? <CopySuccessIcon /> : icon || <CopyIcon />\n\n  return (\n    <button\n      {...props}\n      type=\"button\"\n      title={label}\n      aria-label={label}\n      aria-disabled={isDisabled}\n      data-nextjs-data-runtime-error-copy-button\n      className={`nextjs-data-runtime-error-copy-button nextjs-data-runtime-error-copy-button--${copyState.state}`}\n      onClick={() => {\n        if (!isDisabled) {\n          copy()\n        }\n      }}\n    >\n      {renderedIcon}\n      {copyState.state === 'error' ? ` ${copyState.error}` : null}\n    </button>\n  )\n}\n\nfunction CopyIcon() {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 24 24\"\n      fill=\"transparent\"\n      stroke=\"currentColor\"\n      strokeWidth=\"1.5\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    >\n      <rect width=\"14\" height=\"14\" x=\"8\" y=\"8\" rx=\"2\" ry=\"2\" />\n      <path d=\"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\" />\n    </svg>\n  )\n}\n\nfunction CopySuccessIcon() {\n  return (\n    <svg\n      height=\"16\"\n      xlinkTitle=\"copied\"\n      viewBox=\"0 0 16 16\"\n      width=\"16\"\n      stroke=\"currentColor\"\n      fill=\"currentColor\"\n    >\n      <path d=\"M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z\" />\n    </svg>\n  )\n}\n"], "names": ["React", "useCopyLegacy", "content", "copyState", "dispatch", "useReducer", "state", "action", "type", "error", "copy", "isPending", "navigator", "clipboard", "Error", "writeText", "then", "reset", "useCallback", "useCopyModern", "useActionState", "startTransition", "useCopy", "Copy<PERSON><PERSON><PERSON>", "actionLabel", "successLabel", "icon", "disabled", "props", "useEffect", "console", "timeoutId", "setTimeout", "clearTimeout", "isDisabled", "label", "renderedIcon", "CopySuccessIcon", "CopyIcon", "button", "title", "aria-label", "aria-disabled", "data-nextjs-data-runtime-error-copy-button", "className", "onClick", "svg", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "rect", "x", "y", "rx", "ry", "path", "d", "xlinkTitle"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;;;AAE9B,SAASC,cAAcC,OAAe;IAYpC,kFAAkF;IAClF,gDAAgD;IAChD,MAAM,CAACC,WAAWC,SAAS,GAAGJ,mKAAMK,UAAU,CAC5C,CACEC,OACAC;QAIA,IAAIA,OAAOC,IAAI,KAAK,SAAS;YAC3B,OAAO;gBAAEF,OAAO;YAAU;QAC5B;QACA,IAAIC,OAAOC,IAAI,KAAK,UAAU;YAC5B,OAAO;gBAAEF,OAAO;YAAU;QAC5B;QACA,IAAIC,OAAOC,IAAI,KAAK,WAAW;YAC7B,OAAO;gBAAEF,OAAO;YAAU;QAC5B;QACA,IAAIC,OAAOC,IAAI,KAAK,SAAS;YAC3B,OAAO;gBAAEF,OAAO;gBAASG,OAAOF,OAAOE,KAAK;YAAC;QAC/C;QACA,OAAOH;IACT,GACA;QACEA,OAAO;IACT;IAEF,SAASI;QACP,IAAIC,WAAW;YACb;QACF;QAEA,IAAI,CAACC,UAAUC,SAAS,EAAE;YACxBT,SAAS;gBACPI,MAAM;gBACNC,OAAO,IAAIK,MAAM;YACnB;QACF,OAAO;YACLV,SAAS;gBAAEI,MAAM;YAAU;YAC3BI,UAAUC,SAAS,CAACE,SAAS,CAACb,SAASc,IAAI,CACzC;gBACEZ,SAAS;oBAAEI,MAAM;gBAAS;YAC5B,GACA,CAACC;gBACCL,SAAS;oBAAEI,MAAM;oBAASC;gBAAM;YAClC;QAEJ;IACF;IACA,MAAMQ,QAAQjB,mKAAMkB,WAAW,CAAC;QAC9Bd,SAAS;YAAEI,MAAM;QAAQ;IAC3B,GAAG,EAAE;IAEL,MAAMG,YAAYR,UAAUG,KAAK,KAAK;IAEtC,OAAO;QAACH;QAAWO;QAAMO;QAAON;KAAU;AAC5C;AAEA,SAASQ,cAAcjB,OAAe;IAWpC,MAAM,CAACC,WAAWC,UAAUO,UAAU,GAAGX,mKAAMoB,cAAc,CAC3D,CACEd,OACAC;QAEA,IAAIA,WAAW,SAAS;YACtB,OAAO;gBAAED,OAAO;YAAU;QAC5B;QACA,IAAIC,WAAW,QAAQ;YACrB,IAAI,CAACK,UAAUC,SAAS,EAAE;gBACxB,OAAO;oBACLP,OAAO;oBACPG,OAAO,IAAIK,MACT;gBAEJ;YACF;YACA,OAAOF,UAAUC,SAAS,CAACE,SAAS,CAACb,SAASc,IAAI,CAChD;gBACE,OAAO;oBAAEV,OAAO;gBAAU;YAC5B,GACA,CAACG;gBACC,OAAO;oBAAEH,OAAO;oBAASG;gBAAM;YACjC;QAEJ;QACA,OAAOH;IACT,GACA;QACEA,OAAO;IACT;IAGF,SAASI;QACPV,mKAAMqB,eAAe,CAAC;YACpBjB,SAAS;QACX;IACF;IAEA,MAAMa,QAAQjB,mKAAMkB,WAAW,CAAC;QAC9Bd,SAAS;IACX,GAAG;QACD,0DAA0D;QAC1D,0FAA0F;QAC1FA;KACD;IAED,OAAO;QAACD;QAAWO;QAAMO;QAAON;KAAU;AAC5C;AAEA,MAAMW,UACJ,OAAOtB,mKAAMoB,cAAc,KAAK,aAAaD,gBAAgBlB;AAExD,SAASsB,WAAW,KAY1B;IAZ0B,IAAA,EACzBC,WAAW,EACXC,YAAY,EACZvB,OAAO,EACPwB,IAAI,EACJC,QAAQ,EACR,GAAGC,OAMJ,GAZ0B;IAazB,MAAM,CAACzB,WAAWO,MAAMO,OAAON,UAAU,GAAGW,QAAQpB;IAEpD,MAAMO,QAAQN,UAAUG,KAAK,KAAK,UAAUH,UAAUM,KAAK,GAAG;IAC9DT,mKAAM6B,SAAS,CAAC;QACd,IAAIpB,UAAU,MAAM;YAClB,6CAA6C;YAC7CqB,QAAQrB,KAAK,CAACA;QAChB;IACF,GAAG;QAACA;KAAM;IACVT,mKAAM6B,SAAS,CAAC;QACd,IAAI1B,UAAUG,KAAK,KAAK,WAAW;YACjC,MAAMyB,YAAYC,WAAW;gBAC3Bf;YACF,GAAG;YAEH,OAAO;gBACLgB,aAAaF;YACf;QACF;IACF,GAAG;QAACpB;QAAWR,UAAUG,KAAK;QAAEW;KAAM;IACtC,MAAMiB,aAAavB,aAAagB;IAChC,MAAMQ,QAAQhC,UAAUG,KAAK,KAAK,YAAYmB,eAAeD;IAE7D,sBAAsB;IACtB,MAAMY,eACJjC,UAAUG,KAAK,KAAK,YAAA,WAAA,GAAY,sLAAA,EAAC+B,iBAAAA,CAAAA,KAAqBX,QAAAA,WAAAA,mLAAQ,MAAA,EAACY,UAAAA,CAAAA;IAEjE,OAAA,WAAA,mLACE,OAAA,EAACC,UAAAA;QACE,GAAGX,KAAK;QACTpB,MAAK;QACLgC,OAAOL;QACPM,cAAYN;QACZO,iBAAeR;QACfS,4CAA0C,EAAA;QAC1CC,WAAY,kFAA+EzC,UAAUG,KAAK;QAC1GuC,SAAS;YACP,IAAI,CAACX,YAAY;gBACfxB;YACF;QACF;;YAEC0B;YACAjC,UAAUG,KAAK,KAAK,UAAW,MAAGH,UAAUM,KAAK,GAAK;;;AAG7D;AAEA,SAAS6B;IACP,OAAA,WAAA,GACE,uLAAA,EAACQ,OAAAA;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;;2BAEf,qLAAA,EAACC,QAAAA;gBAAKR,OAAM;gBAAKC,QAAO;gBAAKQ,GAAE;gBAAIC,GAAE;gBAAIC,IAAG;gBAAIC,IAAG;;0MACnD,MAAA,EAACC,QAAAA;gBAAKC,GAAE;;;;AAGd;AAEA,SAASxB;IACP,OAAA,WAAA,mLACE,MAAA,EAACS,OAAAA;QACCE,QAAO;QACPc,YAAW;QACXb,SAAQ;QACRF,OAAM;QACNI,QAAO;QACPD,MAAK;kBAEL,WAAA,mLAAA,MAAA,EAACU,QAAAA;YAAKC,GAAE;;;AAGd", "ignoreList": [0]}}, {"offset": {"line": 5360, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5366, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/nodejs-inspector.tsx"], "sourcesContent": ["import { CopyButton } from './copy-button'\n\n// Inline this helper to avoid widely used across the codebase,\n// as for this feature the Chrome detector doesn't need to be super accurate.\nfunction isChrome() {\n  if (typeof window === 'undefined') return false\n  const isChromium = 'chrome' in window && window.chrome\n  const vendorName = window.navigator.vendor\n\n  return (\n    isChromium !== null &&\n    isChromium !== undefined &&\n    vendorName === 'Google Inc.'\n  )\n}\n\nconst isChromeBrowser = isChrome()\n\nfunction NodeJsIcon(props: any) {\n  return (\n    <svg\n      width=\"44\"\n      height=\"44\"\n      viewBox=\"0 0 44 44\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <g clipPath=\"url(#clip0_1546_2)\">\n        <path\n          d=\"M22 0L41.0526 11V33L22 44L2.94744 33V11L22 0Z\"\n          fill=\"#71BD55\"\n        />\n        <path\n          d=\"M41.0493 11.0001L41.0493 33L22 1.5583e-07L41.0493 11.0001Z\"\n          fill=\"#A1DF83\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_1546_2\">\n          <rect width=\"44\" height=\"44\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  )\n}\n\nfunction NodeJsDisabledIcon(props: any) {\n  return (\n    <svg\n      width=\"44\"\n      height=\"44\"\n      viewBox=\"0 0 44 44\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        d=\"M4.44744 11.866L22 1.73205L39.5526 11.866V32.134L22 42.2679L4.44744 32.134V11.866Z\"\n        stroke=\"currentColor\"\n        fill=\"transparent\"\n        strokeWidth=\"3\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M22 2L39 32\"\n        stroke=\"currentColor\"\n        strokeWidth=\"3\"\n        strokeLinecap=\"round\"\n      />\n    </svg>\n  )\n}\n\nconst label =\n  'Learn more about enabling Node.js inspector for server code with Chrome DevTools'\n\nexport function NodejsInspectorCopyButton({\n  devtoolsFrontendUrl,\n}: {\n  devtoolsFrontendUrl: string | undefined\n}) {\n  const content = devtoolsFrontendUrl || ''\n  const disabled = !content || !isChromeBrowser\n  if (disabled) {\n    return (\n      <a\n        title={label}\n        aria-label={label}\n        className=\"nextjs-data-runtime-error-inspect-link\"\n        href={`https://nextjs.org/docs/app/building-your-application/configuring/debugging#server-side-code`}\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n      >\n        <NodeJsDisabledIcon width={16} height={16} />\n      </a>\n    )\n  }\n  return (\n    <CopyButton\n      data-nextjs-data-runtime-error-copy-devtools-url\n      actionLabel={'Copy Chrome DevTools URL'}\n      successLabel=\"Copied\"\n      content={content}\n      icon={<NodeJsIcon width={16} height={16} />}\n    />\n  )\n}\n"], "names": ["Copy<PERSON><PERSON><PERSON>", "isChrome", "window", "isChromium", "chrome", "vendorName", "navigator", "vendor", "undefined", "isChrome<PERSON><PERSON><PERSON>", "NodeJsIcon", "props", "svg", "width", "height", "viewBox", "fill", "xmlns", "g", "clipPath", "path", "d", "defs", "id", "rect", "NodeJsDisabledIcon", "stroke", "strokeWidth", "strokeLinejoin", "strokeLinecap", "label", "NodejsInspectorCopyButton", "devtoolsFrontendUrl", "content", "disabled", "a", "title", "aria-label", "className", "href", "target", "rel", "data-nextjs-data-runtime-error-copy-devtools-url", "actionLabel", "successLabel", "icon"], "mappings": ";;;;AAAA,SAASA,UAAU,QAAQ,gBAAe;;;AAE1C,+DAA+D;AAC/D,6EAA6E;AAC7E,SAASC;IACP,IAAI,OAAOC,WAAW,aAAa,OAAO;IAC1C,MAAMC,aAAa,YAAYD,UAAUA,OAAOE,MAAM;IACtD,MAAMC,aAAaH,OAAOI,SAAS,CAACC,MAAM;IAE1C,OACEJ,eAAe,QACfA,eAAeK,aACfH,eAAe;AAEnB;AAEA,MAAMI,kBAAkBR;AAExB,SAASS,WAAWC,KAAU;IAC5B,OAAA,WAAA,mLACE,OAAA,EAACC,OAAAA;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACL,GAAGN,KAAK;;0MAET,OAAA,EAACO,KAAAA;gBAAEC,UAAS;;kCACV,sLAAA,EAACC,QAAAA;wBACCC,GAAE;wBACFL,MAAK;;kNAEP,MAAA,EAACI,QAAAA;wBACCC,GAAE;wBACFL,MAAK;;;;0MAGT,MAAA,EAACM,QAAAA;0BACC,WAAA,GAAA,sLAAA,EAACH,YAAAA;oBAASI,IAAG;8BACX,WAAA,mLAAA,MAAA,EAACC,QAAAA;wBAAKX,OAAM;wBAAKC,QAAO;wBAAKE,MAAK;;;;;;AAK5C;AAEA,SAASS,mBAAmBd,KAAU;IACpC,OAAA,WAAA,GACE,uLAAA,EAACC,OAAAA;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACL,GAAGN,KAAK;;0MAET,MAAA,EAACS,QAAAA;gBACCC,GAAE;gBACFK,QAAO;gBACPV,MAAK;gBACLW,aAAY;gBACZC,gBAAe;;8BAEjB,kLAAA,EAACR,QAAAA;gBACCC,GAAE;gBACFK,QAAO;gBACPC,aAAY;gBACZE,eAAc;;;;AAItB;AAEA,MAAMC,QACJ;AAEK,SAASC,0BAA0B,KAIzC;IAJyC,IAAA,EACxCC,mBAAmB,EAGpB,GAJyC;IAKxC,MAAMC,UAAUD,uBAAuB;IACvC,MAAME,WAAW,CAACD,WAAW,CAACxB;IAC9B,IAAIyB,UAAU;QACZ,OAAA,WAAA,mLACE,MAAA,EAACC,KAAAA;YACCC,OAAON;YACPO,cAAYP;YACZQ,WAAU;YACVC,MAAO;YACPC,QAAO;YACPC,KAAI;sBAEJ,WAAA,mLAAA,MAAA,EAAChB,oBAAAA;gBAAmBZ,OAAO;gBAAIC,QAAQ;;;IAG7C;IACA,OAAA,WAAA,mLACE,MAAA,sPAACd,aAAAA,EAAAA;QACC0C,kDAAgD,EAAA;QAChDC,aAAa;QACbC,cAAa;QACbX,SAASA;QACTY,MAAAA,WAAAA,mLAAM,MAAA,EAACnC,YAAAA;YAAWG,OAAO;YAAIC,QAAQ;;;AAG3C", "ignoreList": [0]}}, {"offset": {"line": 5472, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5478, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/console-error.ts"], "sourcesContent": ["// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\nconst digestSym = Symbol.for('next.console.error.digest')\nconst consoleTypeSym = Symbol.for('next.console.error.type')\n\n// Represent non Error shape unhandled promise rejections or console.error errors.\n// Those errors will be captured and displayed in Error Overlay.\ntype UnhandledError = Error & {\n  [digestSym]: 'NEXT_UNHANDLED_ERROR'\n  [consoleTypeSym]: 'string' | 'error'\n}\n\nexport function createUnhandledError(message: string | Error): UnhandledError {\n  const error = (\n    typeof message === 'string' ? new Error(message) : message\n  ) as UnhandledError\n  error[digestSym] = 'NEXT_UNHANDLED_ERROR'\n  error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error'\n  return error\n}\n\nexport const isUnhandledConsoleOrRejection = (\n  error: any\n): error is UnhandledError => {\n  return error && error[digestSym] === 'NEXT_UNHANDLED_ERROR'\n}\n\nexport const getUnhandledErrorType = (error: UnhandledError) => {\n  return error[consoleTypeSym]\n}\n"], "names": ["digestSym", "Symbol", "for", "consoleTypeSym", "createUnhandledError", "message", "error", "Error", "isUnhandledConsoleOrRejection", "getUnhandledErrorType"], "mappings": "AAAA,yJAAyJ;;;;;;AACzJ,MAAMA,YAAYC,OAAOC,GAAG,CAAC;AAC7B,MAAMC,iBAAiBF,OAAOC,GAAG,CAAC;AAS3B,SAASE,qBAAqBC,OAAuB;IAC1D,MAAMC,QACJ,OAAOD,YAAY,WAAW,IAAIE,MAAMF,WAAWA;IAErDC,KAAK,CAACN,UAAU,GAAG;IACnBM,KAAK,CAACH,eAAe,GAAG,OAAOE,YAAY,WAAW,WAAW;IACjE,OAAOC;AACT;AAEO,MAAME,gCAAgC,CAC3CF;IAEA,OAAOA,SAASA,KAAK,CAACN,UAAU,KAAK;AACvC,EAAC;AAEM,MAAMS,wBAAwB,CAACH;IACpC,OAAOA,KAAK,CAACH,eAAe;AAC9B,EAAC", "ignoreList": [0]}}, {"offset": {"line": 5498, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5504, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/container/Errors.tsx"], "sourcesContent": ["import { useState, useEffect, useMemo, useCallback } from 'react'\nimport {\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  type UnhandledErrorAction,\n  type UnhandledRejectionAction,\n} from '../../shared'\nimport type { DebugInfo } from '../../types'\nimport {\n  Dialog,\n  DialogBody,\n  DialogContent,\n  DialogHeader,\n} from '../components/Dialog'\nimport { LeftRightDialogHeader } from '../components/LeftRightDialogHeader'\nimport { Overlay } from '../components/Overlay'\nimport { Toast } from '../components/Toast'\nimport { getErrorByType } from '../helpers/get-error-by-type'\nimport type { ReadyRuntimeError } from '../helpers/get-error-by-type'\nimport { noop as css } from '../helpers/noop-template'\nimport { CloseIcon } from '../icons/CloseIcon'\nimport { RuntimeError } from './RuntimeError'\nimport { VersionStalenessInfo } from '../components/VersionStalenessInfo'\nimport type { VersionInfo } from '../../../../../server/dev/parse-version-info'\nimport { getErrorSource } from '../../../../../shared/lib/error-source'\nimport { HotlinkedText } from '../components/hot-linked-text'\nimport { PseudoHtmlDiff } from './RuntimeError/component-stack-pseudo-html'\nimport {\n  type HydrationErrorState,\n  getHydrationWarningType,\n} from '../helpers/hydration-error-info'\nimport { NodejsInspectorCopyButton } from '../components/nodejs-inspector'\nimport { CopyButton } from '../components/copy-button'\nimport {\n  getUnhandledErrorType,\n  isUnhandledConsoleOrRejection,\n} from '../helpers/console-error'\n\nexport type SupportedErrorEvent = {\n  id: number\n  event: UnhandledErrorAction | UnhandledRejectionAction\n}\nexport type ErrorsProps = {\n  isAppDir: boolean\n  errors: SupportedErrorEvent[]\n  initialDisplayState: DisplayState\n  versionInfo?: VersionInfo\n  hasStaticIndicator?: boolean\n  debugInfo?: DebugInfo\n}\n\ntype ReadyErrorEvent = ReadyRuntimeError\n\ntype DisplayState = 'minimized' | 'fullscreen' | 'hidden'\n\nfunction isNextjsLink(text: string): boolean {\n  return text.startsWith('https://nextjs.org')\n}\n\nfunction ErrorDescription({\n  error,\n  hydrationWarning,\n}: {\n  error: Error\n  hydrationWarning: string | null\n}) {\n  const isUnhandledOrReplayError = isUnhandledConsoleOrRejection(error)\n  const unhandledErrorType = isUnhandledOrReplayError\n    ? getUnhandledErrorType(error)\n    : null\n  const isConsoleErrorStringMessage = unhandledErrorType === 'string'\n  // If the error is:\n  // - hydration warning\n  // - captured console error or unhandled rejection\n  // skip displaying the error name\n  const title =\n    (isUnhandledOrReplayError && isConsoleErrorStringMessage) ||\n    hydrationWarning\n      ? ''\n      : error.name + ': '\n\n  // If it's replayed error, display the environment name\n  const environmentName =\n    'environmentName' in error ? error['environmentName'] : ''\n  const envPrefix = environmentName ? `[ ${environmentName} ] ` : ''\n  return (\n    <>\n      {envPrefix}\n      {title}\n      <HotlinkedText\n        text={hydrationWarning || error.message}\n        matcher={isNextjsLink}\n      />\n    </>\n  )\n}\n\nfunction getErrorSignature(ev: SupportedErrorEvent): string {\n  const { event } = ev\n  switch (event.type) {\n    case ACTION_UNHANDLED_ERROR:\n    case ACTION_UNHANDLED_REJECTION: {\n      return `${event.reason.name}::${event.reason.message}::${event.reason.stack}`\n    }\n    default: {\n    }\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const _: never = event\n  return ''\n}\n\nexport function Errors({\n  isAppDir,\n  errors,\n  initialDisplayState,\n  versionInfo,\n  hasStaticIndicator,\n  debugInfo,\n}: ErrorsProps) {\n  const [lookups, setLookups] = useState(\n    {} as { [eventId: string]: ReadyErrorEvent }\n  )\n\n  const [readyErrors, nextError] = useMemo<\n    [ReadyErrorEvent[], SupportedErrorEvent | null]\n  >(() => {\n    let ready: ReadyErrorEvent[] = []\n    let next: SupportedErrorEvent | null = null\n\n    // Ensure errors are displayed in the order they occurred in:\n    for (let idx = 0; idx < errors.length; ++idx) {\n      const e = errors[idx]\n      const { id } = e\n      if (id in lookups) {\n        ready.push(lookups[id])\n        continue\n      }\n\n      // Check for duplicate errors\n      if (idx > 0) {\n        const prev = errors[idx - 1]\n        if (getErrorSignature(prev) === getErrorSignature(e)) {\n          continue\n        }\n      }\n\n      next = e\n      break\n    }\n\n    return [ready, next]\n  }, [errors, lookups])\n\n  const isLoading = useMemo<boolean>(() => {\n    return readyErrors.length < 1 && Boolean(errors.length)\n  }, [errors.length, readyErrors.length])\n\n  useEffect(() => {\n    if (nextError == null) {\n      return\n    }\n    let mounted = true\n\n    getErrorByType(nextError, isAppDir).then(\n      (resolved) => {\n        // We don't care if the desired error changed while we were resolving,\n        // thus we're not tracking it using a ref. Once the work has been done,\n        // we'll store it.\n        if (mounted) {\n          setLookups((m) => ({ ...m, [resolved.id]: resolved }))\n        }\n      },\n      () => {\n        // TODO: handle this, though an edge case\n      }\n    )\n\n    return () => {\n      mounted = false\n    }\n  }, [nextError, isAppDir])\n\n  const [displayState, setDisplayState] =\n    useState<DisplayState>(initialDisplayState)\n  const [activeIdx, setActiveIndex] = useState<number>(0)\n  const previous = useCallback(\n    () => setActiveIndex((v) => Math.max(0, v - 1)),\n    []\n  )\n  const next = useCallback(\n    () =>\n      setActiveIndex((v) =>\n        Math.max(0, Math.min(readyErrors.length - 1, v + 1))\n      ),\n    [readyErrors.length]\n  )\n\n  const activeError = useMemo<ReadyErrorEvent | null>(\n    () => readyErrors[activeIdx] ?? null,\n    [activeIdx, readyErrors]\n  )\n\n  // Reset component state when there are no errors to be displayed.\n  // This should never happen, but lets handle it.\n  useEffect(() => {\n    if (errors.length < 1) {\n      setLookups({})\n      setDisplayState('hidden')\n      setActiveIndex(0)\n    }\n  }, [errors.length])\n\n  const minimize = useCallback(() => setDisplayState('minimized'), [])\n  const hide = useCallback(() => setDisplayState('hidden'), [])\n  const fullscreen = useCallback(() => setDisplayState('fullscreen'), [])\n\n  // This component shouldn't be rendered with no errors, but if it is, let's\n  // handle it gracefully by rendering nothing.\n  if (errors.length < 1 || activeError == null) {\n    return null\n  }\n\n  if (isLoading) {\n    // TODO: better loading state\n    return <Overlay />\n  }\n\n  if (displayState === 'hidden') {\n    return null\n  }\n\n  if (displayState === 'minimized') {\n    return (\n      <Toast\n        data-nextjs-toast\n        className={`nextjs-toast-errors-parent${hasStaticIndicator ? ' nextjs-error-with-static' : ''}`}\n        onClick={fullscreen}\n      >\n        <div className=\"nextjs-toast-errors\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n          >\n            <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n            <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n            <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n          </svg>\n          <span>\n            {readyErrors.length} error{readyErrors.length > 1 ? 's' : ''}\n          </span>\n          <button\n            data-nextjs-toast-errors-hide-button\n            className=\"nextjs-toast-hide-button\"\n            type=\"button\"\n            onClick={(e) => {\n              e.stopPropagation()\n              hide()\n            }}\n            aria-label=\"Hide Errors\"\n          >\n            <CloseIcon />\n          </button>\n        </div>\n      </Toast>\n    )\n  }\n\n  const error = activeError.error\n  const isServerError = ['server', 'edge-server'].includes(\n    getErrorSource(error) || ''\n  )\n  const isUnhandledError = isUnhandledConsoleOrRejection(error)\n  const errorDetails: HydrationErrorState = (error as any).details || {}\n  const notes = errorDetails.notes || ''\n  const [warningTemplate, serverContent, clientContent] =\n    errorDetails.warning || [null, '', '']\n\n  const hydrationErrorType = getHydrationWarningType(warningTemplate)\n  const hydrationWarning = warningTemplate\n    ? warningTemplate\n        .replace('%s', serverContent)\n        .replace('%s', clientContent)\n        .replace('%s', '') // remove the %s for stack\n        .replace(/%s$/, '') // If there's still a %s at the end, remove it\n        .replace(/^Warning: /, '')\n        .replace(/^Error: /, '')\n    : null\n\n  return (\n    <Overlay>\n      <Dialog\n        type=\"error\"\n        aria-labelledby=\"nextjs__container_errors_label\"\n        aria-describedby=\"nextjs__container_errors_desc\"\n        onClose={isServerError ? undefined : minimize}\n      >\n        <DialogContent>\n          <DialogHeader className=\"nextjs-container-errors-header\">\n            <LeftRightDialogHeader\n              previous={activeIdx > 0 ? previous : null}\n              next={activeIdx < readyErrors.length - 1 ? next : null}\n              close={isServerError ? undefined : minimize}\n            >\n              <small>\n                <span>{activeIdx + 1}</span> of{' '}\n                <span data-nextjs-dialog-header-total-count>\n                  {readyErrors.length}\n                </span>\n                {' error'}\n                {readyErrors.length < 2 ? '' : 's'}\n              </small>\n              <VersionStalenessInfo versionInfo={versionInfo} />\n            </LeftRightDialogHeader>\n\n            <div className=\"nextjs__container_errors__error_title\">\n              <h1\n                id=\"nextjs__container_errors_label\"\n                className=\"nextjs__container_errors_label\"\n              >\n                {isServerError\n                  ? 'Server Error'\n                  : isUnhandledError\n                    ? 'Console Error'\n                    : 'Unhandled Runtime Error'}\n              </h1>\n              <span>\n                <CopyButton\n                  data-nextjs-data-runtime-error-copy-stack\n                  actionLabel=\"Copy error stack\"\n                  successLabel=\"Copied\"\n                  content={error.stack || ''}\n                  disabled={!error.stack}\n                />\n\n                <NodejsInspectorCopyButton\n                  devtoolsFrontendUrl={debugInfo?.devtoolsFrontendUrl}\n                />\n              </span>\n            </div>\n            <p\n              id=\"nextjs__container_errors_desc\"\n              className=\"nextjs__container_errors_desc\"\n            >\n              <ErrorDescription\n                error={error}\n                hydrationWarning={hydrationWarning}\n              />\n            </p>\n            {notes ? (\n              <>\n                <p\n                  id=\"nextjs__container_errors__notes\"\n                  className=\"nextjs__container_errors__notes\"\n                >\n                  {notes}\n                </p>\n              </>\n            ) : null}\n            {hydrationWarning ? (\n              <p\n                id=\"nextjs__container_errors__link\"\n                className=\"nextjs__container_errors__link\"\n              >\n                <HotlinkedText text=\"See more info here: https://nextjs.org/docs/messages/react-hydration-error\" />\n              </p>\n            ) : null}\n\n            {hydrationWarning &&\n            (activeError.componentStackFrames?.length ||\n              !!errorDetails.reactOutputComponentDiff) ? (\n              <PseudoHtmlDiff\n                className=\"nextjs__container_errors__component-stack\"\n                hydrationMismatchType={hydrationErrorType}\n                componentStackFrames={activeError.componentStackFrames || []}\n                firstContent={serverContent}\n                secondContent={clientContent}\n                reactOutputComponentDiff={errorDetails.reactOutputComponentDiff}\n              />\n            ) : null}\n            {isServerError ? (\n              <div>\n                <small>\n                  This error happened while generating the page. Any console\n                  logs will be displayed in the terminal window.\n                </small>\n              </div>\n            ) : undefined}\n          </DialogHeader>\n          <DialogBody className=\"nextjs-container-errors-body\">\n            <RuntimeError key={activeError.id.toString()} error={activeError} />\n          </DialogBody>\n        </DialogContent>\n      </Dialog>\n    </Overlay>\n  )\n}\n\nexport const styles = css`\n  .nextjs-error-with-static {\n    bottom: calc(var(--size-gap-double) * 4.5);\n  }\n  .nextjs-container-errors-header {\n    position: relative;\n  }\n  .nextjs-container-errors-header > h1 {\n    font-size: var(--size-font-big);\n    line-height: var(--size-font-bigger);\n    font-weight: bold;\n    margin: calc(var(--size-gap-double) * 1.5) 0;\n    color: var(--color-title-h1);\n  }\n  .nextjs-container-errors-header small {\n    font-size: var(--size-font-small);\n    color: var(--color-accents-1);\n    margin-left: var(--size-gap-double);\n  }\n  .nextjs-container-errors-header small > span {\n    font-family: var(--font-stack-monospace);\n  }\n  .nextjs-container-errors-header p {\n    font-size: var(--size-font-small);\n    line-height: var(--size-font-big);\n    white-space: pre-wrap;\n  }\n  .nextjs__container_errors_desc {\n    font-family: var(--font-stack-monospace);\n    padding: var(--size-gap) var(--size-gap-double);\n    border-left: 2px solid var(--color-text-color-red-1);\n    margin-top: var(--size-gap);\n    font-weight: bold;\n    color: var(--color-text-color-red-1);\n    background-color: var(--color-text-background-red-1);\n  }\n  p.nextjs__container_errors__link {\n    margin: var(--size-gap-double) auto;\n    color: var(--color-text-color-red-1);\n    font-weight: 600;\n    font-size: 15px;\n  }\n  p.nextjs__container_errors__notes {\n    margin: var(--size-gap-double) auto;\n    color: var(--color-stack-notes);\n    font-weight: 600;\n    font-size: 15px;\n  }\n  .nextjs-container-errors-header > div > small {\n    margin: 0;\n    margin-top: var(--size-gap-half);\n  }\n  .nextjs-container-errors-header > p > a {\n    color: inherit;\n    font-weight: bold;\n  }\n  .nextjs-container-errors-body > h2:not(:first-child) {\n    margin-top: calc(var(--size-gap-double) + var(--size-gap));\n  }\n  .nextjs-container-errors-body > h2 {\n    color: var(--color-title-color);\n    margin-bottom: var(--size-gap);\n    font-size: var(--size-font-big);\n  }\n  .nextjs__container_errors__component-stack {\n    padding: 12px 32px;\n    color: var(--color-ansi-fg);\n    background: var(--color-ansi-bg);\n  }\n  .nextjs-toast-errors-parent {\n    cursor: pointer;\n    transition: transform 0.2s ease;\n  }\n  .nextjs-toast-errors-parent:hover {\n    transform: scale(1.1);\n  }\n  .nextjs-toast-errors {\n    display: flex;\n    align-items: center;\n    justify-content: flex-start;\n  }\n  .nextjs-toast-errors > svg {\n    margin-right: var(--size-gap);\n  }\n  .nextjs-toast-hide-button {\n    margin-left: var(--size-gap-triple);\n    border: none;\n    background: none;\n    color: var(--color-ansi-bright-white);\n    padding: 0;\n    transition: opacity 0.25s ease;\n    opacity: 0.7;\n  }\n  .nextjs-toast-hide-button:hover {\n    opacity: 1;\n  }\n  .nextjs-container-errors-header\n    > .nextjs-container-build-error-version-status {\n    position: absolute;\n    top: 0;\n    right: 0;\n  }\n  .nextjs__container_errors_inspect_copy_button {\n    cursor: pointer;\n    background: none;\n    border: none;\n    color: var(--color-ansi-bright-white);\n    font-size: 1.5rem;\n    padding: 0;\n    margin: 0;\n    margin-left: var(--size-gap);\n    transition: opacity 0.25s ease;\n  }\n  .nextjs__container_errors__error_title {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  }\n  .nextjs-data-runtime-error-inspect-link,\n  .nextjs-data-runtime-error-inspect-link:hover {\n    margin: 0 8px;\n    color: inherit;\n  }\n`\n"], "names": ["useState", "useEffect", "useMemo", "useCallback", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "LeftRightDialogHeader", "Overlay", "Toast", "getErrorByType", "noop", "css", "CloseIcon", "RuntimeError", "VersionStalenessInfo", "getErrorSource", "HotlinkedText", "PseudoHtmlDiff", "getHydrationWarningType", "NodejsInspectorCopyButton", "Copy<PERSON><PERSON><PERSON>", "getUnhandledErrorType", "isUnhandledConsoleOrRejection", "isNextjsLink", "text", "startsWith", "ErrorDescription", "error", "hydrationWarning", "isUnhandledOrReplayError", "unhandledErrorType", "isConsoleErrorStringMessage", "title", "name", "environmentName", "envPrefix", "message", "matcher", "getErrorSignature", "ev", "event", "type", "reason", "stack", "_", "Errors", "isAppDir", "errors", "initialDisplayState", "versionInfo", "hasStaticIndicator", "debugInfo", "activeError", "lookups", "setLookups", "readyErrors", "nextError", "ready", "next", "idx", "length", "e", "id", "push", "prev", "isLoading", "Boolean", "mounted", "then", "resolved", "m", "displayState", "setDisplayState", "activeIdx", "setActiveIndex", "previous", "v", "Math", "max", "min", "minimize", "hide", "fullscreen", "data-nextjs-toast", "className", "onClick", "div", "svg", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "circle", "cx", "cy", "r", "line", "x1", "y1", "x2", "y2", "span", "button", "data-nextjs-toast-errors-hide-button", "stopPropagation", "aria-label", "isServerError", "includes", "isUnhandledError", "errorDetails", "details", "notes", "warningTemplate", "serverContent", "clientContent", "warning", "hydrationErrorType", "replace", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "undefined", "close", "small", "data-nextjs-dialog-header-total-count", "h1", "data-nextjs-data-runtime-error-copy-stack", "actionLabel", "successLabel", "content", "disabled", "devtoolsFrontendUrl", "p", "componentStackFrames", "reactOutputComponentDiff", "hydrationMismatchType", "firstContent", "second<PERSON><PERSON>nt", "toString", "styles"], "mappings": ";;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,QAAO;AACjE,SACEC,sBAAsB,EACtBC,0BAA0B,QAGrB,eAAc;AAErB,SACEC,MAAM,EACNC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,uBAAsB;AAC7B,SAASC,qBAAqB,QAAQ,sCAAqC;;AAE3E,SAASE,KAAK,QAAQ,sBAAqB;AAC3C,SAASC,cAAc,QAAQ,+BAA8B;AAE7D,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;AACtD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,qCAAoC;AAEzE,SAASC,cAAc,QAAQ,yCAAwC;AACvE,SAASC,aAAa,QAAQ,gCAA+B;AAC7D,SAASC,cAAc,QAAQ,6CAA4C;AAC3E,SAEEC,uBAAuB,QAClB,kCAAiC;AACxC,SAASC,yBAAyB,QAAQ,iCAAgC;AAC1E,SAASC,UAAU,QAAQ,4BAA2B;AACtD,SACEC,qBAAqB,EACrBC,6BAA6B,QACxB,2BAA0B;;AArBjC,SAASf,OAAO,QAAQ,wBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwC/C,SAASgB,aAAaC,IAAY;IAChC,OAAOA,KAAKC,UAAU,CAAC;AACzB;AAEA,SAASC,iBAAiB,KAMzB;IANyB,IAAA,EACxBC,KAAK,EACLC,gBAAgB,EAIjB,GANyB;IAOxB,MAAMC,yQAA2BP,gCAAAA,EAA8BK;IAC/D,MAAMG,qBAAqBD,2BACvBR,sQAAAA,EAAsBM,SACtB;IACJ,MAAMI,8BAA8BD,uBAAuB;IAC3D,mBAAmB;IACnB,sBAAsB;IACtB,kDAAkD;IAClD,iCAAiC;IACjC,MAAME,QACHH,4BAA4BE,+BAC7BH,mBACI,KACAD,MAAMM,IAAI,GAAG;IAEnB,uDAAuD;IACvD,MAAMC,kBACJ,qBAAqBP,QAAQA,KAAK,CAAC,kBAAkB,GAAG;IAC1D,MAAMQ,YAAYD,kBAAmB,OAAIA,kBAAgB,QAAO;IAChE,OAAA,WAAA,mLACE,OAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;YACGC;YACAH;yMACD,OAAA,6PAAChB,gBAAAA,EAAAA;gBACCQ,MAAMI,oBAAoBD,MAAMS,OAAO;gBACvCC,SAASd;;;;AAIjB;AAEA,SAASe,kBAAkBC,EAAuB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,OAAQC,MAAMC,IAAI;QAChB,8MAAKzC,yBAAAA;QACL,8MAAKC,6BAAAA;YAA4B;gBAC/B,OAAUuC,MAAME,MAAM,CAACT,IAAI,GAAC,OAAIO,MAAME,MAAM,CAACN,OAAO,GAAC,OAAII,MAAME,MAAM,CAACC,KAAK;YAC7E;QACA;YAAS,CACT;IACF;IAEA,6DAA6D;IAC7D,MAAMC,IAAWJ;IACjB,OAAO;AACT;AAEO,SAASK,OAAO,KAOT;IAPS,IAAA,EACrBC,QAAQ,EACRC,MAAM,EACNC,mBAAmB,EACnBC,WAAW,EACXC,kBAAkB,EAClBC,SAAS,EACG,GAPS;QAwQVC;IAhQX,MAAM,CAACC,SAASC,WAAW,0KAAG1D,WAAAA,EAC5B,CAAC;IAGH,MAAM,CAAC2D,aAAaC,UAAU,IAAG1D,gLAAAA,EAE/B;QACA,IAAI2D,QAA2B,EAAE;QACjC,IAAIC,OAAmC;QAEvC,6DAA6D;QAC7D,IAAK,IAAIC,MAAM,GAAGA,MAAMZ,OAAOa,MAAM,EAAE,EAAED,IAAK;YAC5C,MAAME,IAAId,MAAM,CAACY,IAAI;YACrB,MAAM,EAAEG,EAAE,EAAE,GAAGD;YACf,IAAIC,MAAMT,SAAS;gBACjBI,MAAMM,IAAI,CAACV,OAAO,CAACS,GAAG;gBACtB;YACF;YAEA,6BAA6B;YAC7B,IAAIH,MAAM,GAAG;gBACX,MAAMK,OAAOjB,MAAM,CAACY,MAAM,EAAE;gBAC5B,IAAIrB,kBAAkB0B,UAAU1B,kBAAkBuB,IAAI;oBACpD;gBACF;YACF;YAEAH,OAAOG;YACP;QACF;QAEA,OAAO;YAACJ;YAAOC;SAAK;IACtB,GAAG;QAACX;QAAQM;KAAQ;IAEpB,MAAMY,kLAAYnE,WAAAA,EAAiB;QACjC,OAAOyD,YAAYK,MAAM,GAAG,KAAKM,QAAQnB,OAAOa,MAAM;IACxD,GAAG;QAACb,OAAOa,MAAM;QAAEL,YAAYK,MAAM;KAAC;2KAEtC/D,YAAAA,EAAU;QACR,IAAI2D,aAAa,MAAM;YACrB;QACF;QACA,IAAIW,UAAU;gQAEd1D,iBAAAA,EAAe+C,WAAWV,UAAUsB,IAAI,CACtC,CAACC;YACC,sEAAsE;YACtE,uEAAuE;YACvE,kBAAkB;YAClB,IAAIF,SAAS;gBACXb,WAAW,CAACgB,IAAO,CAAA;wBAAE,GAAGA,CAAC;wBAAE,CAACD,SAASP,EAAE,CAAC,EAAEO;oBAAS,CAAA;YACrD;QACF,GACA;QACE,yCAAyC;QAC3C;QAGF,OAAO;YACLF,UAAU;QACZ;IACF,GAAG;QAACX;QAAWV;KAAS;IAExB,MAAM,CAACyB,cAAcC,gBAAgB,OACnC5E,8KAAAA,EAAuBoD;IACzB,MAAM,CAACyB,WAAWC,eAAe,0KAAG9E,WAAAA,EAAiB;IACrD,MAAM+E,WAAW5E,qLAAAA,EACf,IAAM2E,eAAe,CAACE,IAAMC,KAAKC,GAAG,CAAC,GAAGF,IAAI,KAC5C,EAAE;IAEJ,MAAMlB,8KAAO3D,cAAAA,EACX,IACE2E,eAAe,CAACE,IACdC,KAAKC,GAAG,CAAC,GAAGD,KAAKE,GAAG,CAACxB,YAAYK,MAAM,GAAG,GAAGgB,IAAI,MAErD;QAACrB,YAAYK,MAAM;KAAC;IAGtB,MAAMR,qLAActD,UAAAA,EAClB;YAAMyD;eAAAA,CAAAA,yBAAAA,WAAW,CAACkB,UAAU,KAAA,OAAtBlB,yBAA0B;OAChC;QAACkB;QAAWlB;KAAY;IAG1B,kEAAkE;IAClE,gDAAgD;2KAChD1D,YAAAA,EAAU;QACR,IAAIkD,OAAOa,MAAM,GAAG,GAAG;YACrBN,WAAW,CAAC;YACZkB,gBAAgB;YAChBE,eAAe;QACjB;IACF,GAAG;QAAC3B,OAAOa,MAAM;KAAC;IAElB,MAAMoB,kLAAWjF,cAAAA,EAAY,IAAMyE,gBAAgB,cAAc,EAAE;IACnE,MAAMS,OAAOlF,qLAAAA,EAAY,IAAMyE,gBAAgB,WAAW,EAAE;IAC5D,MAAMU,oLAAanF,cAAAA,EAAY,IAAMyE,gBAAgB,eAAe,EAAE;IAEtE,2EAA2E;IAC3E,6CAA6C;IAC7C,IAAIzB,OAAOa,MAAM,GAAG,KAAKR,eAAe,MAAM;QAC5C,OAAO;IACT;IAEA,IAAIa,WAAW;QACb,6BAA6B;QAC7B,OAAA,WAAA,OAAO,kLAAA,iPAAC1D,UAAAA,EAAAA,CAAAA;IACV;IAEA,IAAIgE,iBAAiB,UAAU;QAC7B,OAAO;IACT;IAEA,IAAIA,iBAAiB,aAAa;QAChC,OAAA,WAAA,mLACE,MAAA,6OAAC/D,QAAAA,EAAAA;YACC2E,mBAAiB,EAAA;YACjBC,WAAY,+BAA4BlC,CAAAA,qBAAqB,8BAA8B,EAAC;YAC5FmC,SAASH;sBAET,WAAA,mLAAA,OAAA,EAACI,OAAAA;gBAAIF,WAAU;;kCACb,uLAAA,EAACG,OAAAA;wBACCC,OAAM;wBACNC,OAAM;wBACNC,QAAO;wBACPC,SAAQ;wBACRC,MAAK;wBACLC,QAAO;wBACPC,aAAY;wBACZC,eAAc;wBACdC,gBAAe;;0NAEf,MAAA,EAACC,UAAAA;gCAAOC,IAAG;gCAAKC,IAAG;gCAAKC,GAAE;;0NAC1B,MAAA,EAACC,QAAAA;gCAAKC,IAAG;gCAAKC,IAAG;gCAAIC,IAAG;gCAAKC,IAAG;;0NAChC,MAAA,EAACJ,QAAAA;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;gCAAQC,IAAG;;;;kNAEtC,OAAA,EAACC,QAAAA;;4BACEnD,YAAYK,MAAM;4BAAC;4BAAOL,YAAYK,MAAM,GAAG,IAAI,MAAM;;;kNAE5D,MAAA,EAAC+C,UAAAA;wBACCC,sCAAoC,EAAA;wBACpCxB,WAAU;wBACV3C,MAAK;wBACL4C,SAAS,CAACxB;4BACRA,EAAEgD,eAAe;4BACjB5B;wBACF;wBACA6B,cAAW;kCAEX,WAAA,GAAA,sLAAA,mOAAClG,YAAAA,EAAAA,CAAAA;;;;;IAKX;IAEA,MAAMe,QAAQyB,YAAYzB,KAAK;IAC/B,MAAMoF,gBAAgB;QAAC;QAAU;KAAc,CAACC,QAAQ,qLACtDjG,iBAAAA,EAAeY,UAAU;IAE3B,MAAMsF,iQAAmB3F,gCAAAA,EAA8BK;IACvD,MAAMuF,eAAqCvF,MAAcwF,OAAO,IAAI,CAAC;IACrE,MAAMC,QAAQF,aAAaE,KAAK,IAAI;IACpC,MAAM,CAACC,iBAAiBC,eAAeC,cAAc,GACnDL,aAAaM,OAAO,IAAI;QAAC;QAAM;QAAI;KAAG;IAExC,MAAMC,6QAAqBvG,0BAAAA,EAAwBmG;IACnD,MAAMzF,mBAAmByF,kBACrBA,gBACGK,OAAO,CAAC,MAAMJ,eACdI,OAAO,CAAC,MAAMH,eACdG,OAAO,CAAC,MAAM,IAAI,0BAA0B;KAC5CA,OAAO,CAAC,OAAO,IAAI,8CAA8C;KACjEA,OAAO,CAAC,cAAc,IACtBA,OAAO,CAAC,YAAY,MACvB;IAEJ,OAAA,WAAA,mLACE,MAAA,iPAACnH,UAAAA,EAAAA;kBACC,WAAA,GAAA,sLAAA,+OAACL,SAAAA,EAAAA;YACCuC,MAAK;YACLkF,mBAAgB;YAChBC,oBAAiB;YACjBC,SAASd,gBAAgBe,YAAY9C;sBAErC,WAAA,OAAA,mLAAA,sPAAC5E,gBAAAA,EAAAA;;kNACC,OAAA,qPAACC,eAAAA,EAAAA;wBAAa+E,WAAU;;0NACtB,OAAA,4QAAC9E,yBAAAA,EAAAA;gCACCqE,UAAUF,YAAY,IAAIE,WAAW;gCACrCjB,MAAMe,YAAYlB,YAAYK,MAAM,GAAG,IAAIF,OAAO;gCAClDqE,OAAOhB,gBAAgBe,YAAY9C;;iOAEnC,QAAA,EAACgD,SAAAA;;0OACC,MAAA,EAACtB,QAAAA;0DAAMjC,YAAY;;4CAAS;4CAAI;0OAChC,MAAA,EAACiC,QAAAA;gDAAKuB,uCAAqC,EAAA;0DACxC1E,YAAYK,MAAM;;4CAEpB;4CACAL,YAAYK,MAAM,GAAG,IAAI,KAAK;;;kOAEjC,MAAA,0QAAC9C,wBAAAA,EAAAA;wCAAqBmC,aAAaA;;;;0NAGrC,OAAA,EAACqC,OAAAA;gCAAIF,WAAU;;kOACb,MAAA,EAAC8C,MAAAA;wCACCpE,IAAG;wCACHsB,WAAU;kDAET2B,gBACG,iBACAE,mBACE,kBACA;;kOAER,OAAA,EAACP,QAAAA;;0OACC,MAAA,sPAACtF,aAAAA,EAAAA;gDACC+G,2CAAyC,EAAA;gDACzCC,aAAY;gDACZC,cAAa;gDACbC,SAAS3G,MAAMgB,KAAK,IAAI;gDACxB4F,UAAU,CAAC5G,MAAMgB,KAAK;;0OAGxB,MAAA,kPAACxB,4BAAAA,EAAAA;gDACCqH,mBAAmB,EAAErF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWqF,mBAAmB;;;;;;8CAIzD,kLAAA,EAACC,KAAAA;gCACC3E,IAAG;gCACHsB,WAAU;0CAEV,WAAA,mLAAA,MAAA,EAAC1D,kBAAAA;oCACCC,OAAOA;oCACPC,kBAAkBA;;;4BAGrBwF,QAAAA,WAAAA,mLACC,MAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;0CACE,WAAA,mLAAA,MAAA,EAACqB,KAAAA;oCACC3E,IAAG;oCACHsB,WAAU;8CAETgC;;iCAGH;4BACHxF,mBAAAA,WAAAA,mLACC,MAAA,EAAC6G,KAAAA;gCACC3E,IAAG;gCACHsB,WAAU;0CAEV,WAAA,mLAAA,MAAA,6PAACpE,gBAAAA,EAAAA;oCAAcQ,MAAK;;iCAEpB;4BAEHI,oBACAwB,CAAAA,CAAAA,CAAAA,oCAAAA,YAAYsF,oBAAoB,KAAA,OAAA,KAAA,IAAhCtF,kCAAkCQ,MAAM,KACvC,CAAC,CAACsD,aAAayB,wBAAuB,IAAA,WAAA,mLACtC,MAAA,kRAAC1H,iBAAAA,EAAAA;gCACCmE,WAAU;gCACVwD,uBAAuBnB;gCACvBiB,sBAAsBtF,YAAYsF,oBAAoB,IAAI,EAAE;gCAC5DG,cAAcvB;gCACdwB,eAAevB;gCACfoB,0BAA0BzB,aAAayB,wBAAwB;iCAE/D;4BACH5B,gBAAAA,WAAAA,mLACC,MAAA,EAACzB,OAAAA;0CACC,WAAA,mLAAA,MAAA,EAAC0C,SAAAA;8CAAM;;iCAKPF;;;kNAEN,MAAA,mPAAC3H,aAAAA,EAAAA;wBAAWiF,WAAU;kCACpB,WAAA,mLAAA,MAAA,mPAACvE,eAAAA,EAAAA;4BAA6Cc,OAAOyB;2BAAlCA,YAAYU,EAAE,CAACiF,QAAQ;;;;;;AAMtD;AAEO,MAAMC,uPAASrI,OAAAA,EAAAA,mBA2HrB", "ignoreList": [0]}}, {"offset": {"line": 5896, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5902, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/icons/LightningBolt.tsx"], "sourcesContent": ["export const LightningBolt = (props: React.SVGProps<any>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width={16}\n    height={16}\n    fill=\"none\"\n    {...props}\n  >\n    <path\n      style={{ fill: 'var(--color-font)' }}\n      fillRule=\"evenodd\"\n      d=\"M7.157 0 2.333 9.408l-.56 1.092H7a.25.25 0 0 1 .25.25V16h1.593l4.824-9.408.56-1.092H9a.25.25 0 0 1-.25-.25V0H7.157ZM7 9H4.227L7.25 3.106V5.25C7.25 6.216 8.034 7 9 7h2.773L8.75 12.894V10.75A1.75 1.75 0 0 0 7 9Z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n)\n"], "names": ["LightningBolt", "props", "svg", "xmlns", "width", "height", "fill", "path", "style", "fillRule", "d", "clipRule"], "mappings": ";;;;;AAAO,MAAMA,gBAAgB,CAACC,QAAAA,WAAAA,mLAC5B,MAAA,EAACC,OAAAA;QACCC,OAAM;QACNC,OAAO;QACPC,QAAQ;QACRC,MAAK;QACJ,GAAGL,KAAK;kBAET,WAAA,mLAAA,MAAA,EAACM,QAAAA;YACCC,OAAO;gBAAEF,MAAM;YAAoB;YACnCG,UAAS;YACTC,GAAE;YACFC,UAAS;;OAGd", "ignoreList": [0]}}, {"offset": {"line": 5922, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5928, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/container/StaticIndicator.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { Toast } from '../components/Toast'\nimport { <PERSON>Bolt } from '../icons/LightningBolt'\nimport { CloseIcon } from '../icons/CloseIcon'\nimport type { Dispatcher } from '../../app/hot-reloader-client'\n\nexport function StaticIndicator({ dispatcher }: { dispatcher?: Dispatcher }) {\n  return (\n    <Toast role=\"status\" className={`nextjs-static-indicator-toast-wrapper`}>\n      <div className=\"nextjs-static-indicator-toast-icon\">\n        <LightningBolt />\n      </div>\n      <div className=\"nextjs-static-indicator-toast-text\">\n        Static route\n        <button\n          onClick={() => {\n            // When dismissed, we hide the indicator for 1 hour. Store the\n            // timestamp for when to show it again.\n            const oneHourAway = Date.now() + 1 * 60 * 60 * 1000\n\n            localStorage?.setItem(\n              '__NEXT_DISMISS_PRERENDER_INDICATOR',\n              oneHourAway.toString()\n            )\n\n            dispatcher?.onStaticIndicator(false)\n          }}\n          className=\"nextjs-toast-hide-button\"\n          aria-label=\"Hide static indicator\"\n        >\n          <CloseIcon />\n        </button>\n      </div>\n    </Toast>\n  )\n}\n"], "names": ["React", "Toast", "LightningBolt", "CloseIcon", "StaticIndicator", "dispatcher", "role", "className", "div", "button", "onClick", "localStorage", "oneHourAway", "Date", "now", "setItem", "toString", "onStaticIndicator", "aria-label"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,KAAK,QAAQ,sBAAqB;AAC3C,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,SAAS,QAAQ,qBAAoB;;;;;;;AAGvC,SAASC,gBAAgB,KAA2C;IAA3C,IAAA,EAAEC,UAAU,EAA+B,GAA3C;IAC9B,OAAA,WAAA,OACE,mLAAA,6OAACJ,QAAAA,EAAAA;QAAMK,MAAK;QAASC,WAAY;;0MAC/B,MAAA,EAACC,OAAAA;gBAAID,WAAU;0BACb,WAAA,mLAAA,MAAA,uOAACL,gBAAAA,EAAAA,CAAAA;;0MAEH,OAAA,EAACM,OAAAA;gBAAID,WAAU;;oBAAqC;mCAElD,qLAAA,EAACE,UAAAA;wBACCC,SAAS;gCAKPC;4BAJA,8DAA8D;4BAC9D,uCAAuC;4BACvC,MAAMC,cAAcC,KAAKC,GAAG,KAAK,IAAI,KAAK,KAAK;6BAE/CH,gBAAAA,YAAAA,KAAAA,OAAAA,KAAAA,IAAAA,cAAcI,OAAO,CACnB,sCACAH,YAAYI,QAAQ;4BAGtBX,cAAAA,OAAAA,KAAAA,IAAAA,WAAYY,iBAAiB,CAAC;wBAChC;wBACAV,WAAU;wBACVW,cAAW;kCAEX,WAAA,mLAAA,MAAA,mOAACf,YAAAA,EAAAA,CAAAA;;;;;;AAKX", "ignoreList": [0]}}, {"offset": {"line": 5974, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5980, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/styles/Base.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { noop as css } from '../helpers/noop-template'\n\nexport function Base() {\n  return (\n    <style>\n      {css`\n        :host {\n          --size-gap-half: 4px;\n          --size-gap: 8px;\n          --size-gap-double: 16px;\n          --size-gap-triple: 24px;\n          --size-gap-quad: 32px;\n\n          --size-font-small: 14px;\n          --size-font: 16px;\n          --size-font-big: 20px;\n          --size-font-bigger: 24px;\n\n          --color-background: white;\n          --color-font: #757575;\n          --color-backdrop: rgba(17, 17, 17, 0.2);\n          --color-border-shadow: rgba(0, 0, 0, 0.145);\n\n          --color-title-color: #1f1f1f;\n          --color-stack-h6: #222;\n          --color-stack-headline: #666;\n          --color-stack-subline: #999;\n          --color-stack-notes: #777;\n\n          --color-accents-1: #808080;\n          --color-accents-2: #222222;\n          --color-accents-3: #404040;\n\n          --color-text-color-red-1: #ff5555;\n          --color-text-background-red-1: #fff9f9;\n\n          --font-stack-monospace: 'SFMono-Regular', Consol<PERSON>, 'Liberation Mono',\n            Menlo, Courier, monospace;\n          --font-stack-sans: -apple-system, 'Source Sans Pro', sans-serif;\n\n          --color-ansi-selection: rgba(95, 126, 151, 0.48);\n          --color-ansi-bg: #111111;\n          --color-ansi-fg: #cccccc;\n\n          --color-ansi-white: #777777;\n          --color-ansi-black: #141414;\n          --color-ansi-blue: #00aaff;\n          --color-ansi-cyan: #88ddff;\n          --color-ansi-green: #98ec65;\n          --color-ansi-magenta: #aa88ff;\n          --color-ansi-red: #ff5555;\n          --color-ansi-yellow: #ffcc33;\n          --color-ansi-bright-white: #ffffff;\n          --color-ansi-bright-black: #777777;\n          --color-ansi-bright-blue: #33bbff;\n          --color-ansi-bright-cyan: #bbecff;\n          --color-ansi-bright-green: #b6f292;\n          --color-ansi-bright-magenta: #cebbff;\n          --color-ansi-bright-red: #ff8888;\n          --color-ansi-bright-yellow: #ffd966;\n        }\n\n        @media (prefers-color-scheme: dark) {\n          :host {\n            --color-background: rgb(28, 28, 30);\n            --color-font: white;\n            --color-backdrop: rgb(44, 44, 46);\n            --color-border-shadow: rgba(255, 255, 255, 0.145);\n\n            --color-title-color: #fafafa;\n            --color-stack-h6: rgb(200, 200, 204);\n            --color-stack-headline: rgb(99, 99, 102);\n            --color-stack-notes: #a9a9a9;\n            --color-stack-subline: rgb(121, 121, 121);\n\n            --color-accents-3: rgb(118, 118, 118);\n\n            --color-text-background-red-1: #2a1e1e;\n          }\n        }\n\n        .mono {\n          font-family: var(--font-stack-monospace);\n        }\n\n        h1,\n        h2,\n        h3,\n        h4,\n        h5,\n        h6 {\n          margin-bottom: var(--size-gap);\n          font-weight: 500;\n          line-height: 1.5;\n        }\n      `}\n    </style>\n  )\n}\n"], "names": ["React", "noop", "css", "Base", "style"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;;;;;;;;;;;;;;;AAE/C,SAASC;IACd,OAAA,WAAA,mLACE,MAAA,EAACC,SAAAA;gQACEF,OAAAA,EAAAA;;AA6FP", "ignoreList": [0]}}, {"offset": {"line": 6005, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6011, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/CodeFrame/styles.tsx"], "sourcesContent": ["import { noop as css } from '../../helpers/noop-template'\n\nconst styles = css`\n  [data-nextjs-codeframe] {\n    overflow: auto;\n    border-radius: var(--size-gap-half);\n    background-color: var(--color-ansi-bg);\n    color: var(--color-ansi-fg);\n    margin-bottom: var(--size-gap-double);\n  }\n  [data-nextjs-codeframe]::selection,\n  [data-nextjs-codeframe] *::selection {\n    background-color: var(--color-ansi-selection);\n  }\n  [data-nextjs-codeframe] * {\n    color: inherit;\n    background-color: transparent;\n    font-family: var(--font-stack-monospace);\n  }\n\n  [data-nextjs-codeframe] > * {\n    margin: 0;\n    padding: calc(var(--size-gap) + var(--size-gap-half))\n      calc(var(--size-gap-double) + var(--size-gap-half));\n  }\n  [data-nextjs-codeframe] > div {\n    display: inline-block;\n    width: auto;\n    min-width: 100%;\n    border-bottom: 1px solid var(--color-ansi-bright-black);\n  }\n  [data-nextjs-codeframe] > div > p {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    cursor: pointer;\n    margin: 0;\n  }\n  [data-nextjs-codeframe] > div > p:hover {\n    text-decoration: underline dotted;\n  }\n  [data-nextjs-codeframe] div > p > svg {\n    width: auto;\n    height: 1em;\n    margin-left: 8px;\n  }\n  [data-nextjs-codeframe] div > pre {\n    overflow: hidden;\n    display: inline-block;\n  }\n`\n\nexport { styles }\n"], "names": ["noop", "css", "styles"], "mappings": ";;;AAAA,SAASA,QAAQC,GAAG,QAAQ,8BAA6B;;;;;;;;;;;;;AAEzD,MAAMC,uPAASD,OAAAA,EAAAA", "ignoreList": [0]}}, {"offset": {"line": 6030, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6036, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Overlay/styles.tsx"], "sourcesContent": ["import { noop as css } from '../../helpers/noop-template'\n\nconst styles = css`\n  [data-nextjs-dialog-overlay] {\n    position: fixed;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    overflow: auto;\n    z-index: 9000;\n\n    display: flex;\n    align-content: center;\n    align-items: center;\n    flex-direction: column;\n    padding: 10vh 15px 0;\n  }\n\n  @media (max-height: 812px) {\n    [data-nextjs-dialog-overlay] {\n      padding: 15px 15px 0;\n    }\n  }\n\n  [data-nextjs-dialog-backdrop] {\n    position: fixed;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    background-color: var(--color-backdrop);\n    pointer-events: all;\n    z-index: -1;\n  }\n\n  [data-nextjs-dialog-backdrop-fixed] {\n    cursor: not-allowed;\n    -webkit-backdrop-filter: blur(8px);\n    backdrop-filter: blur(8px);\n  }\n`\n\nexport { styles }\n"], "names": ["noop", "css", "styles"], "mappings": ";;;AAAA,SAASA,QAAQC,GAAG,QAAQ,8BAA6B;;;;;;;;;;;;;AAEzD,MAAMC,uPAASD,OAAAA,EAAAA", "ignoreList": [0]}}, {"offset": {"line": 6055, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6061, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/components/Terminal/styles.tsx"], "sourcesContent": ["import { noop as css } from '../../helpers/noop-template'\n\nconst styles = css`\n  [data-nextjs-terminal] {\n    border-radius: var(--size-gap-half);\n    background-color: var(--color-ansi-bg);\n    color: var(--color-ansi-fg);\n  }\n  [data-nextjs-terminal]::selection,\n  [data-nextjs-terminal] *::selection {\n    background-color: var(--color-ansi-selection);\n  }\n  [data-nextjs-terminal] * {\n    color: inherit;\n    background-color: transparent;\n    font-family: var(--font-stack-monospace);\n  }\n  [data-nextjs-terminal] > * {\n    margin: 0;\n    padding: calc(var(--size-gap) + var(--size-gap-half))\n      calc(var(--size-gap-double) + var(--size-gap-half));\n  }\n\n  [data-nextjs-terminal] pre {\n    white-space: pre-wrap;\n    word-break: break-word;\n  }\n\n  [data-with-open-in-editor-link] svg {\n    width: auto;\n    height: var(--size-font-small);\n    margin-left: var(--size-gap);\n  }\n  [data-with-open-in-editor-link] {\n    cursor: pointer;\n  }\n  [data-with-open-in-editor-link]:hover {\n    text-decoration: underline dotted;\n  }\n  [data-with-open-in-editor-link-source-file] {\n    border-bottom: 1px solid var(--color-ansi-bright-black);\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    line-break: anywhere;\n  }\n  [data-with-open-in-editor-link-import-trace] {\n    margin-left: var(--size-gap-double);\n  }\n  [data-nextjs-terminal] a {\n    color: inherit;\n  }\n`\n\nexport { styles }\n"], "names": ["noop", "css", "styles"], "mappings": ";;;AAAA,SAASA,QAAQC,GAAG,QAAQ,8BAA6B;;;;;;;;;;;;;AAEzD,MAAMC,uPAASD,OAAAA,EAAAA", "ignoreList": [0]}}, {"offset": {"line": 6080, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6086, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/styles/ComponentStyles.tsx"], "sourcesContent": ["import { styles as codeFrame } from '../components/CodeFrame/styles'\nimport { styles as dialog } from '../components/Dialog'\nimport { styles as leftRightDialogHeader } from '../components/LeftRightDialogHeader/styles'\nimport { styles as overlay } from '../components/Overlay/styles'\nimport { styles as terminal } from '../components/Terminal/styles'\nimport { styles as toast } from '../components/Toast'\nimport { styles as versionStaleness } from '../components/VersionStalenessInfo'\nimport { styles as buildErrorStyles } from '../container/BuildError'\nimport { styles as containerErrorStyles } from '../container/Errors'\nimport { styles as containerRuntimeErrorStyles } from '../container/RuntimeError'\nimport { noop as css } from '../helpers/noop-template'\n\nexport function ComponentStyles() {\n  return (\n    <style>\n      {css`\n        ${overlay}\n        ${toast}\n        ${dialog}\n        ${leftRightDialogHeader}\n        ${codeFrame}\n        ${terminal}\n        ${buildErrorStyles}\n        ${containerErrorStyles}\n        ${containerRuntimeErrorStyles}\n        ${versionStaleness}\n      `}\n    </style>\n  )\n}\n"], "names": ["styles", "codeFrame", "dialog", "leftRightDialogHeader", "overlay", "terminal", "toast", "versionStaleness", "buildErrorStyles", "containerErrorStyles", "containerRuntimeErrorStyles", "noop", "css", "ComponentStyles", "style"], "mappings": ";;;;AAAA,SAASA,UAAUC,SAAS,QAAQ,iCAAgC;;AAEpE,SAASD,UAAUG,qBAAqB,QAAQ,6CAA4C;AAC5F,SAASH,UAAUI,OAAO,QAAQ,+BAA8B;AAChE,SAASJ,UAAUK,QAAQ,QAAQ,gCAA+B;AAClE,SAASL,UAAUM,KAAK,QAAQ,sBAAqB;AACrD,SAASN,UAAUO,gBAAgB,QAAQ,qCAAoC;AAC/E,SAASP,UAAUQ,gBAAgB,QAAQ,0BAAyB;AACpE,SAASR,UAAUS,oBAAoB,QAAQ,sBAAqB;AACpE,SAAST,UAAUU,2BAA2B,QAAQ,4BAA2B;AACjF,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;;;AATtD,SAASZ,UAAUE,MAAM,QAAQ,uBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhD,SAASW;IACd,OAAA,WAAA,mLACE,MAAA,EAACC,SAAAA;gQACEF,OAAAA,EAAAA,iQACGR,SAAAA,8OACAE,SAAAA,+OACAJ,SAAAA,EACAC,qQAAAA,kPACAF,SAAAA,iPACAI,SAAAA,wOACAG,SAAAA,oOACAC,SAAAA,mPACAC,SAAAA,6PACAH,SAAAA;;AAIV", "ignoreList": [0]}}, {"offset": {"line": 6142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6148, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/styles/CssReset.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { noop as css } from '../helpers/noop-template'\n\nexport function CssReset() {\n  return (\n    <style>\n      {css`\n        :host {\n          all: initial;\n\n          /* the direction property is not reset by 'all' */\n          direction: ltr;\n        }\n\n        /*!\n         * Bootstrap Reboot v4.4.1 (https://getbootstrap.com/)\n         * Copyright 2011-2019 The Bootstrap Authors\n         * Copyright 2011-2019 Twitter, Inc.\n         * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n         * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)\n         */\n        *,\n        *::before,\n        *::after {\n          box-sizing: border-box;\n        }\n\n        :host {\n          font-family: sans-serif;\n          line-height: 1.15;\n          -webkit-text-size-adjust: 100%;\n          -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n        }\n\n        article,\n        aside,\n        figcaption,\n        figure,\n        footer,\n        header,\n        hgroup,\n        main,\n        nav,\n        section {\n          display: block;\n        }\n\n        :host {\n          margin: 0;\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n            'Helvetica Neue', Arial, 'Noto Sans', sans-serif,\n            'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\n            'Noto Color Emoji';\n          font-size: 16px;\n          font-weight: 400;\n          line-height: 1.5;\n          color: var(--color-font);\n          text-align: left;\n          background-color: #fff;\n        }\n\n        [tabindex='-1']:focus:not(:focus-visible) {\n          outline: 0 !important;\n        }\n\n        hr {\n          box-sizing: content-box;\n          height: 0;\n          overflow: visible;\n        }\n\n        h1,\n        h2,\n        h3,\n        h4,\n        h5,\n        h6 {\n          margin-top: 0;\n          margin-bottom: 8px;\n        }\n\n        p {\n          margin-top: 0;\n          margin-bottom: 16px;\n        }\n\n        abbr[title],\n        abbr[data-original-title] {\n          text-decoration: underline;\n          -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n          cursor: help;\n          border-bottom: 0;\n          -webkit-text-decoration-skip-ink: none;\n          text-decoration-skip-ink: none;\n        }\n\n        address {\n          margin-bottom: 16px;\n          font-style: normal;\n          line-height: inherit;\n        }\n\n        ol,\n        ul,\n        dl {\n          margin-top: 0;\n          margin-bottom: 16px;\n        }\n\n        ol ol,\n        ul ul,\n        ol ul,\n        ul ol {\n          margin-bottom: 0;\n        }\n\n        dt {\n          font-weight: 700;\n        }\n\n        dd {\n          margin-bottom: 8px;\n          margin-left: 0;\n        }\n\n        blockquote {\n          margin: 0 0 16px;\n        }\n\n        b,\n        strong {\n          font-weight: bolder;\n        }\n\n        small {\n          font-size: 80%;\n        }\n\n        sub,\n        sup {\n          position: relative;\n          font-size: 75%;\n          line-height: 0;\n          vertical-align: baseline;\n        }\n\n        sub {\n          bottom: -0.25em;\n        }\n\n        sup {\n          top: -0.5em;\n        }\n\n        a {\n          color: #007bff;\n          text-decoration: none;\n          background-color: transparent;\n        }\n\n        a:hover {\n          color: #0056b3;\n          text-decoration: underline;\n        }\n\n        a:not([href]) {\n          color: inherit;\n          text-decoration: none;\n        }\n\n        a:not([href]):hover {\n          color: inherit;\n          text-decoration: none;\n        }\n\n        pre,\n        code,\n        kbd,\n        samp {\n          font-family: SFMono-Regular, Menlo, Monaco, Consolas,\n            'Liberation Mono', 'Courier New', monospace;\n          font-size: 1em;\n        }\n\n        pre {\n          margin-top: 0;\n          margin-bottom: 16px;\n          overflow: auto;\n        }\n\n        figure {\n          margin: 0 0 16px;\n        }\n\n        img {\n          vertical-align: middle;\n          border-style: none;\n        }\n\n        svg {\n          overflow: hidden;\n          vertical-align: middle;\n        }\n\n        table {\n          border-collapse: collapse;\n        }\n\n        caption {\n          padding-top: 12px;\n          padding-bottom: 12px;\n          color: #6c757d;\n          text-align: left;\n          caption-side: bottom;\n        }\n\n        th {\n          text-align: inherit;\n        }\n\n        label {\n          display: inline-block;\n          margin-bottom: 8px;\n        }\n\n        button {\n          border-radius: 0;\n        }\n\n        button:focus {\n          outline: 1px dotted;\n          outline: 5px auto -webkit-focus-ring-color;\n        }\n\n        input,\n        button,\n        select,\n        optgroup,\n        textarea {\n          margin: 0;\n          font-family: inherit;\n          font-size: inherit;\n          line-height: inherit;\n        }\n\n        button,\n        input {\n          overflow: visible;\n        }\n\n        button,\n        select {\n          text-transform: none;\n        }\n\n        select {\n          word-wrap: normal;\n        }\n\n        button,\n        [type='button'],\n        [type='reset'],\n        [type='submit'] {\n          -webkit-appearance: button;\n        }\n\n        button:not(:disabled),\n        [type='button']:not(:disabled),\n        [type='reset']:not(:disabled),\n        [type='submit']:not(:disabled) {\n          cursor: pointer;\n        }\n\n        button::-moz-focus-inner,\n        [type='button']::-moz-focus-inner,\n        [type='reset']::-moz-focus-inner,\n        [type='submit']::-moz-focus-inner {\n          padding: 0;\n          border-style: none;\n        }\n\n        input[type='radio'],\n        input[type='checkbox'] {\n          box-sizing: border-box;\n          padding: 0;\n        }\n\n        input[type='date'],\n        input[type='time'],\n        input[type='datetime-local'],\n        input[type='month'] {\n          -webkit-appearance: listbox;\n        }\n\n        textarea {\n          overflow: auto;\n          resize: vertical;\n        }\n\n        fieldset {\n          min-width: 0;\n          padding: 0;\n          margin: 0;\n          border: 0;\n        }\n\n        legend {\n          display: block;\n          width: 100%;\n          max-width: 100%;\n          padding: 0;\n          margin-bottom: 8px;\n          font-size: 24px;\n          line-height: inherit;\n          color: inherit;\n          white-space: normal;\n        }\n\n        progress {\n          vertical-align: baseline;\n        }\n\n        [type='number']::-webkit-inner-spin-button,\n        [type='number']::-webkit-outer-spin-button {\n          height: auto;\n        }\n\n        [type='search'] {\n          outline-offset: -2px;\n          -webkit-appearance: none;\n        }\n\n        [type='search']::-webkit-search-decoration {\n          -webkit-appearance: none;\n        }\n\n        ::-webkit-file-upload-button {\n          font: inherit;\n          -webkit-appearance: button;\n        }\n\n        output {\n          display: inline-block;\n        }\n\n        summary {\n          display: list-item;\n          cursor: pointer;\n        }\n\n        template {\n          display: none;\n        }\n\n        [hidden] {\n          display: none !important;\n        }\n      `}\n    </style>\n  )\n}\n"], "names": ["React", "noop", "css", "CssReset", "style"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;;;;;;;;;;;;;;;AAE/C,SAASC;IACd,OAAA,WAAA,mLACE,MAAA,EAACC,SAAAA;gQACEF,OAAAA,EAAAA;;AAmWP", "ignoreList": [0]}}, {"offset": {"line": 6173, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6179, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/container/root-layout-missing-tags-error.tsx"], "sourcesContent": ["import * as React from 'react'\nimport type { VersionInfo } from '../../../../../server/dev/parse-version-info'\nimport { Dialog, DialogContent, DialogHeader } from '../components/Dialog'\nimport { Overlay } from '../components/Overlay'\nimport { VersionStalenessInfo } from '../components/VersionStalenessInfo'\nimport { HotlinkedText } from '../components/hot-linked-text'\n\ntype RootLayoutMissingTagsErrorProps = {\n  missingTags: string[]\n  versionInfo?: VersionInfo\n}\n\nexport const RootLayoutMissingTagsError: React.FC<RootLayoutMissingTagsErrorProps> =\n  function RootLayoutMissingTagsError({ missingTags, versionInfo }) {\n    const noop = React.useCallback(() => {}, [])\n    return (\n      <Overlay>\n        <Dialog\n          type=\"error\"\n          aria-labelledby=\"nextjs__container_errors_label\"\n          aria-describedby=\"nextjs__container_errors_desc\"\n          onClose={noop}\n        >\n          <DialogContent>\n            <DialogHeader className=\"nextjs-container-errors-header\">\n              <VersionStalenessInfo versionInfo={versionInfo} />\n              <h1\n                id=\"nextjs__container_errors_label\"\n                className=\"nextjs__container_errors_label\"\n              >\n                Missing required html tags\n              </h1>\n              <p\n                id=\"nextjs__container_errors_desc\"\n                className=\"nextjs__container_errors_desc\"\n              >\n                <HotlinkedText\n                  text={`The following tags are missing in the Root Layout: ${missingTags\n                    .map((tagName) => `<${tagName}>`)\n                    .join(\n                      ', '\n                    )}.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags`}\n                />\n              </p>\n            </DialogHeader>\n          </DialogContent>\n        </Dialog>\n      </Overlay>\n    )\n  }\n"], "names": ["React", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "Overlay", "VersionStalenessInfo", "HotlinkedText", "RootLayoutMissingTagsError", "missingTags", "versionInfo", "noop", "useCallback", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "className", "h1", "id", "p", "text", "map", "tagName", "join"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;AAE9B,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,uBAAsB;;;AAG1E,SAASG,aAAa,QAAQ,gCAA+B;AAF7D,SAASF,OAAO,QAAQ,wBAAuB;;;;AAC/C,SAASC,oBAAoB,QAAQ,qCAAoC;;;;;;;AAQlE,MAAME,6BACX,SAASA,2BAA2B,KAA4B;IAA5B,IAAA,EAAEC,WAAW,EAAEC,WAAW,EAAE,GAA5B;IAClC,MAAMC,OAAOV,mKAAMW,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,OAAA,WAAA,mLACE,MAAA,iPAACP,UAAAA,EAAAA;kBACC,WAAA,mLAAA,MAAA,EAACH,sPAAAA,EAAAA;YACCW,MAAK;YACLC,mBAAgB;YAChBC,oBAAiB;YACjBC,SAASL;sBAET,WAAA,mLAAA,MAAA,qPAACR,iBAAAA,EAAAA;0BACC,WAAA,mLAAA,OAAA,qPAACC,eAAAA,EAAAA;oBAAaa,WAAU;;uCACtB,qLAAA,2QAACX,uBAAAA,EAAAA;4BAAqBI,aAAaA;;sNACnC,MAAA,EAACQ,MAAAA;4BACCC,IAAG;4BACHF,WAAU;sCACX;;sNAGD,MAAA,EAACG,KAAAA;4BACCD,IAAG;4BACHF,WAAU;sCAEV,WAAA,mLAAA,MAAA,6PAACV,gBAAAA,EAAAA;gCACCc,MAAO,wDAAqDZ,YACzDa,GAAG,CAAC,CAACC,UAAa,MAAGA,UAAQ,KAC7BC,IAAI,CACH,QACA;;;;;;;;AAQpB,EAAC", "ignoreList": [0]}}, {"offset": {"line": 6233, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6239, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/runtime-error-handler.ts"], "sourcesContent": ["export const RuntimeErrorHandler = {\n  hadRuntimeError: false,\n}\n"], "names": ["RuntimeError<PERSON>andler", "hadRuntimeError"], "mappings": ";;;AAAO,MAAMA,sBAAsB;IACjCC,iBAAiB;AACnB,EAAC", "ignoreList": [0]}}, {"offset": {"line": 6245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6251, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/app/ReactDevOverlay.tsx"], "sourcesContent": ["import React from 'react'\nimport type { OverlayState } from '../shared'\nimport { ShadowPortal } from '../internal/components/ShadowPortal'\nimport { BuildError } from '../internal/container/BuildError'\nimport { Errors } from '../internal/container/Errors'\nimport { StaticIndicator } from '../internal/container/StaticIndicator'\nimport { Base } from '../internal/styles/Base'\nimport { ComponentStyles } from '../internal/styles/ComponentStyles'\nimport { CssReset } from '../internal/styles/CssReset'\nimport { RootLayoutMissingTagsError } from '../internal/container/root-layout-missing-tags-error'\nimport type { Dispatcher } from './hot-reloader-client'\nimport { RuntimeErrorHandler } from '../internal/helpers/runtime-error-handler'\n\ninterface ReactDevOverlayState {\n  isReactError: boolean\n}\nexport default class ReactDevOverlay extends React.PureComponent<\n  {\n    state: OverlayState\n    dispatcher?: Dispatcher\n    children: React.ReactNode\n  },\n  ReactDevOverlayState\n> {\n  state = { isReactError: false }\n\n  static getDerivedStateFromError(error: Error): ReactDevOverlayState {\n    if (!error.stack) return { isReactError: false }\n\n    RuntimeErrorHandler.hadRuntimeError = true\n    return {\n      isReactError: true,\n    }\n  }\n\n  render() {\n    const { state, children, dispatcher } = this.props\n    const { isReactError } = this.state\n\n    const hasBuildError = state.buildError != null\n    const hasRuntimeErrors = Boolean(state.errors.length)\n    const hasStaticIndicator = state.staticIndicator\n    const debugInfo = state.debugInfo\n\n    return (\n      <>\n        {isReactError ? (\n          <html>\n            <head></head>\n            <body></body>\n          </html>\n        ) : (\n          children\n        )}\n        <ShadowPortal>\n          <CssReset />\n          <Base />\n          <ComponentStyles />\n          {state.rootLayoutMissingTags?.length ? (\n            <RootLayoutMissingTagsError\n              missingTags={state.rootLayoutMissingTags}\n            />\n          ) : hasBuildError ? (\n            <BuildError\n              message={state.buildError!}\n              versionInfo={state.versionInfo}\n            />\n          ) : (\n            <>\n              {hasRuntimeErrors ? (\n                <Errors\n                  isAppDir={true}\n                  initialDisplayState={\n                    isReactError ? 'fullscreen' : 'minimized'\n                  }\n                  errors={state.errors}\n                  versionInfo={state.versionInfo}\n                  hasStaticIndicator={hasStaticIndicator}\n                  debugInfo={debugInfo}\n                />\n              ) : null}\n\n              {hasStaticIndicator && (\n                <StaticIndicator dispatcher={dispatcher} />\n              )}\n            </>\n          )}\n        </ShadowPortal>\n      </>\n    )\n  }\n}\n"], "names": ["React", "ShadowPort<PERSON>", "BuildError", "Errors", "StaticIndicator", "Base", "ComponentStyles", "CssReset", "RootLayoutMissingTagsError", "RuntimeError<PERSON>andler", "ReactDevOverlay", "PureComponent", "getDerivedStateFromError", "error", "stack", "isReactError", "hadRuntimeError", "render", "state", "children", "dispatcher", "props", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "hasStaticIndicator", "staticIndicator", "debugInfo", "html", "head", "body", "rootLayoutMissingTags", "missingTags", "message", "versionInfo", "isAppDir", "initialDisplayState"], "mappings": ";;;;AAAA,OAAOA,WAAW,QAAO;AAEzB,SAASC,YAAY,QAAQ,sCAAqC;AAClE,SAASC,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,MAAM,QAAQ,+BAA8B;AACrD,SAASC,eAAe,QAAQ,wCAAuC;AACvE,SAASC,IAAI,QAAQ,0BAAyB;AAC9C,SAASC,eAAe,QAAQ,qCAAoC;AACpE,SAASC,QAAQ,QAAQ,8BAA6B;AACtD,SAASC,0BAA0B,QAAQ,uDAAsD;AAEjG,SAASC,mBAAmB,QAAQ,4CAA2C;;;;;;;;;;;;AAKhE,MAAMC,2LAAwBV,UAAAA,CAAMW,aAAa;IAU9D,OAAOC,yBAAyBC,KAAY,EAAwB;QAClE,IAAI,CAACA,MAAMC,KAAK,EAAE,OAAO;YAAEC,cAAc;QAAM;QAE/CN,2QAAAA,CAAoBO,eAAe,GAAG;QACtC,OAAO;YACLD,cAAc;QAChB;IACF;IAEAE,SAAS;YAuBAC;QAtBP,MAAM,EAAEA,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAE,GAAG,IAAI,CAACC,KAAK;QAClD,MAAM,EAAEN,YAAY,EAAE,GAAG,IAAI,CAACG,KAAK;QAEnC,MAAMI,gBAAgBJ,MAAMK,UAAU,IAAI;QAC1C,MAAMC,mBAAmBC,QAAQP,MAAMQ,MAAM,CAACC,MAAM;QACpD,MAAMC,qBAAqBV,MAAMW,eAAe;QAChD,MAAMC,YAAYZ,MAAMY,SAAS;QAEjC,OAAA,WAAA,mLACE,OAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;gBACGf,eAAAA,WAAAA,mLACC,OAAA,EAACgB,QAAAA;;sNACC,MAAA,EAACC,QAAAA,CAAAA;sNACD,MAAA,EAACC,QAAAA,CAAAA;;qBAGHd;+BAEF,sLAAA,2OAAClB,eAAAA,EAAAA;;sNACC,MAAA,mOAACM,WAAAA,EAAAA,CAAAA;sNACD,MAAA,+NAACF,OAAAA,EAAAA,CAAAA;sCACD,sLAAA,0OAACC,kBAAAA,EAAAA,CAAAA;wBACAY,CAAAA,CAAAA,+BAAAA,MAAMgB,qBAAqB,KAAA,OAAA,KAAA,IAA3BhB,6BAA6BS,MAAM,IAAA,WAAA,mLAClC,MAAA,wQAACnB,6BAAAA,EAAAA;4BACC2B,aAAajB,MAAMgB,qBAAqB;6BAExCZ,gBAAAA,WAAAA,IACF,qLAAA,wOAACpB,aAAAA,EAAAA;4BACCkC,SAASlB,MAAMK,UAAU;4BACzBc,aAAanB,MAAMmB,WAAW;2NAGhC,OAAA,EAAA,2KAAA,CAAA,WAAA,EAAA;;gCACGb,mBAAAA,WAAAA,GACC,sLAAA,oOAACrB,SAAAA,EAAAA;oCACCmC,UAAU;oCACVC,qBACExB,eAAe,eAAe;oCAEhCW,QAAQR,MAAMQ,MAAM;oCACpBW,aAAanB,MAAMmB,WAAW;oCAC9BT,oBAAoBA;oCACpBE,WAAWA;qCAEX;gCAEHF,sBAAAA,WAAAA,mLACC,MAAA,EAACxB,6PAAAA,EAAAA;oCAAgBgB,YAAYA;;;;;;;;IAO3C;;QA1Ea,KAAA,IAAA,OAAA,IAAA,CAQbF,KAAAA,GAAQ;YAAEH,cAAc;QAAM;;AAmEhC", "ignoreList": [0]}}, {"offset": {"line": 6340, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6346, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/attach-hydration-error-state.ts"], "sourcesContent": ["import {\n  isHydrationError,\n  getDefaultHydrationErrorMessage,\n} from '../../../is-hydration-error'\nimport {\n  hydrationErrorState,\n  getReactHydrationDiffSegments,\n} from './hydration-error-info'\n\nexport function attachHydrationErrorState(error: Error) {\n  if (\n    isHydrationError(error) &&\n    !error.message.includes(\n      'https://nextjs.org/docs/messages/react-hydration-error'\n    )\n  ) {\n    const reactHydrationDiffSegments = getReactHydrationDiffSegments(\n      error.message\n    )\n    let parsedHydrationErrorState: typeof hydrationErrorState = {}\n    if (reactHydrationDiffSegments) {\n      parsedHydrationErrorState = {\n        ...(error as any).details,\n        ...hydrationErrorState,\n        warning: hydrationErrorState.warning || [\n          getDefaultHydrationErrorMessage(),\n        ],\n        notes: reactHydrationDiffSegments[0],\n        reactOutputComponentDiff: reactHydrationDiffSegments[1],\n      }\n    } else {\n      // If there's any extra information in the error message to display,\n      // append it to the error message details property\n      if (hydrationErrorState.warning) {\n        // The patched console.error found hydration errors logged by React\n        // Append the logged warning to the error message\n        parsedHydrationErrorState = {\n          ...(error as any).details,\n          // It contains the warning, component stack, server and client tag names\n          ...hydrationErrorState,\n        }\n      }\n    }\n    ;(error as any).details = parsedHydrationErrorState\n  }\n}\n"], "names": ["isHydrationError", "getDefaultHydrationErrorMessage", "hydrationErrorState", "getReactHydrationDiffSegments", "attachHydrationErrorState", "error", "message", "includes", "reactHydrationDiffSegments", "parsedHydrationErrorState", "details", "warning", "notes", "reactOutputComponentDiff"], "mappings": ";;;AAAA,SACEA,gBAAgB,EAChBC,+BAA+B,QAC1B,8BAA6B;AACpC,SACEC,mBAAmB,EACnBC,6BAA6B,QACxB,yBAAwB;;;AAExB,SAASC,0BAA0BC,KAAY;IACpD,wMACEL,mBAAAA,EAAiBK,UACjB,CAACA,MAAMC,OAAO,CAACC,QAAQ,CACrB,2DAEF;QACA,MAAMC,8BAA6BL,uRAAAA,EACjCE,MAAMC,OAAO;QAEf,IAAIG,4BAAwD,CAAC;QAC7D,IAAID,4BAA4B;YAC9BC,4BAA4B;gBAC1B,GAAIJ,MAAcK,OAAO;gBACzB,uPAAGR,sBAAmB;gBACtBS,6PAAST,sBAAAA,CAAoBS,OAAO,IAAI;qBACtCV,qOAAAA;iBACD;gBACDW,OAAOJ,0BAA0B,CAAC,EAAE;gBACpCK,0BAA0BL,0BAA0B,CAAC,EAAE;YACzD;QACF,OAAO;YACL,oEAAoE;YACpE,kDAAkD;YAClD,wPAAIN,sBAAAA,CAAoBS,OAAO,EAAE;gBAC/B,mEAAmE;gBACnE,iDAAiD;gBACjDF,4BAA4B;oBAC1B,GAAIJ,MAAcK,OAAO;oBACzB,wEAAwE;oBACxE,uPAAGR,sBAAmB;gBACxB;YACF;QACF;;QACEG,MAAcK,OAAO,GAAGD;IAC5B;AACF", "ignoreList": [0]}}, {"offset": {"line": 6384, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6390, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/enqueue-client-error.ts"], "sourcesContent": ["import { isHydrationError } from '../../../is-hydration-error'\n\n// Dedupe the two consecutive errors: If the previous one is same as current one, ignore the current one.\nexport function enqueueConsecutiveDedupedError(\n  queue: Array<Error>,\n  error: Error\n) {\n  const isFront = isHydrationError(error)\n  const previousError = isFront ? queue[0] : queue[queue.length - 1]\n  // Compare the error stack to dedupe the consecutive errors\n  if (previousError && previousError.stack === error.stack) {\n    return\n  }\n  // TODO: change all to push error into errorQueue,\n  // currently there's a async api error is always erroring while hydration error showing up.\n  // Move hydration error to the front of the queue to unblock.\n  if (isFront) {\n    queue.unshift(error)\n  } else {\n    queue.push(error)\n  }\n}\n"], "names": ["isHydrationError", "enqueueConsecutiveDedupedError", "queue", "error", "isFront", "previousError", "length", "stack", "unshift", "push"], "mappings": ";;;AAAA,SAASA,gBAAgB,QAAQ,8BAA6B;;AAGvD,SAASC,+BACdC,KAAmB,EACnBC,KAAY;IAEZ,MAAMC,8MAAUJ,mBAAAA,EAAiBG;IACjC,MAAME,gBAAgBD,UAAUF,KAAK,CAAC,EAAE,GAAGA,KAAK,CAACA,MAAMI,MAAM,GAAG,EAAE;IAClE,2DAA2D;IAC3D,IAAID,iBAAiBA,cAAcE,KAAK,KAAKJ,MAAMI,KAAK,EAAE;QACxD;IACF;IACA,kDAAkD;IAClD,2FAA2F;IAC3F,6DAA6D;IAC7D,IAAIH,SAAS;QACXF,MAAMM,OAAO,CAACL;IAChB,OAAO;QACLD,MAAMO,IAAI,CAACN;IACb;AACF", "ignoreList": [0]}}, {"offset": {"line": 6411, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6417, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/stitched-error.ts"], "sourcesContent": ["import React from 'react'\nimport isError from '../../../../../lib/is-error'\n\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame'\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\n  `(at ${REACT_ERROR_STACK_BOTTOM_FRAME} )|(${REACT_ERROR_STACK_BOTTOM_FRAME}\\\\@)`\n)\n\nconst captureOwnerStack = (React as any).captureOwnerStack\n  ? (React as any).captureOwnerStack\n  : () => ''\n\nexport function getReactStitchedError<T = unknown>(err: T): Error | T {\n  if (typeof (React as any).captureOwnerStack !== 'function') {\n    return err\n  }\n  const isErrorInstance = isError(err)\n  const originStack = isErrorInstance ? err.stack || '' : ''\n  const originMessage = isErrorInstance ? err.message : ''\n  const stackLines = originStack.split('\\n')\n  const indexOfSplit = stackLines.findIndex((line) =>\n    REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line)\n  )\n  const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n  let newStack = isOriginalReactError\n    ? stackLines.slice(0, indexOfSplit).join('\\n')\n    : originStack\n\n  const newError = new Error(originMessage)\n  // Copy all enumerable properties, e.g. digest\n  Object.assign(newError, err)\n  newError.stack = newStack\n\n  // Avoid duplicate overriding stack frames\n  appendOwnerStack(newError)\n\n  return newError\n}\n\nfunction appendOwnerStack(error: Error) {\n  let stack = error.stack || ''\n  // Avoid duplicate overriding stack frames\n  const ownerStack = captureOwnerStack()\n  if (ownerStack && stack.endsWith(ownerStack) === false) {\n    stack += ownerStack\n    // Override stack\n    error.stack = stack\n  }\n}\n"], "names": ["React", "isError", "REACT_ERROR_STACK_BOTTOM_FRAME", "REACT_ERROR_STACK_BOTTOM_FRAME_REGEX", "RegExp", "captureOwnerStack", "getReactStitchedError", "err", "isErrorInstance", "originStack", "stack", "originMessage", "message", "stackLines", "split", "indexOfSplit", "findIndex", "line", "test", "isOriginalReactError", "newStack", "slice", "join", "newError", "Error", "Object", "assign", "appendOwnerStack", "error", "ownerStack", "endsWith"], "mappings": ";;;AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,aAAa,8BAA6B;;;AAEjD,MAAMC,iCAAiC;AACvC,MAAMC,uCAAuC,IAAIC,OAC9C,SAAMF,iCAA+B,SAAMA,iCAA+B;AAG7E,MAAMG,uLAAqBL,UAAAA,CAAcK,iBAAiB,sKACrDL,UAAAA,CAAcK,iBAAiB,GAChC,IAAM;AAEH,SAASC,sBAAmCC,GAAM;IACvD,IAAI,0KAAQP,UAAAA,CAAcK,iBAAiB,KAAK,YAAY;QAC1D,OAAOE;IACT;IACA,MAAMC,mBAAkBP,+KAAAA,EAAQM;IAChC,MAAME,cAAcD,kBAAkBD,IAAIG,KAAK,IAAI,KAAK;IACxD,MAAMC,gBAAgBH,kBAAkBD,IAAIK,OAAO,GAAG;IACtD,MAAMC,aAAaJ,YAAYK,KAAK,CAAC;IACrC,MAAMC,eAAeF,WAAWG,SAAS,CAAC,CAACC,OACzCd,qCAAqCe,IAAI,CAACD;IAE5C,MAAME,uBAAuBJ,gBAAgB,EAAE,mCAAmC;;IAClF,IAAIK,WAAWD,uBACXN,WAAWQ,KAAK,CAAC,GAAGN,cAAcO,IAAI,CAAC,QACvCb;IAEJ,MAAMc,WAAW,IAAIC,MAAMb;IAC3B,8CAA8C;IAC9Cc,OAAOC,MAAM,CAACH,UAAUhB;IACxBgB,SAASb,KAAK,GAAGU;IAEjB,0CAA0C;IAC1CO,iBAAiBJ;IAEjB,OAAOA;AACT;AAEA,SAASI,iBAAiBC,KAAY;IACpC,IAAIlB,QAAQkB,MAAMlB,KAAK,IAAI;IAC3B,0CAA0C;IAC1C,MAAMmB,aAAaxB;IACnB,IAAIwB,cAAcnB,MAAMoB,QAAQ,CAACD,gBAAgB,OAAO;QACtDnB,SAASmB;QACT,iBAAiB;QACjBD,MAAMlB,KAAK,GAAGA;IAChB;AACF", "ignoreList": [0]}}, {"offset": {"line": 6457, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6463, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { attachHydrationErrorState } from './attach-hydration-error-state'\nimport { isNextRouterError } from '../../../is-next-router-error'\nimport { storeHydrationErrorStateFromConsoleArgs } from './hydration-error-info'\nimport { formatConsoleArgs } from '../../../../lib/console'\nimport isError from '../../../../../lib/is-error'\nimport { createUnhandledError } from './console-error'\nimport { enqueueConsecutiveDedupedError } from './enqueue-client-error'\nimport { getReactStitchedError } from './stitched-error'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\nexport type ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleClientError(\n  originError: unknown,\n  consoleErrorArgs: any[],\n  capturedFromConsole: boolean = false\n) {\n  let error: Error\n  if (!originError || !isError(originError)) {\n    // If it's not an error, format the args into an error\n    const formattedErrorMessage = formatConsoleArgs(consoleErrorArgs)\n    error = createUnhandledError(formattedErrorMessage)\n  } else {\n    error = capturedFromConsole\n      ? createUnhandledError(originError)\n      : originError\n  }\n  error = getReactStitchedError(error)\n\n  storeHydrationErrorStateFromConsoleArgs(...consoleErrorArgs)\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  if (isNextRouterError(event.error)) {\n    event.preventDefault()\n    return false\n  }\n  handleClientError(event.error, [])\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  let error = reason\n  if (error && !isError(error)) {\n    error = createUnhandledError(error + '')\n  }\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["useEffect", "attachHydrationErrorState", "isNextRouterError", "storeHydrationErrorStateFromConsoleArgs", "formatConsoleArgs", "isError", "createUnhandledError", "enqueueConsecutiveDedupedError", "getReactStitchedError", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "handleClientError", "originError", "consoleErrorArgs", "capturedFromConsole", "error", "formattedErrorMessage", "handler", "useErrorHandler", "handleOnUnhandledError", "handleOnUnhandledRejection", "for<PERSON>ach", "push", "splice", "indexOf", "onUnhandledError", "event", "preventDefault", "onUnhandledRejection", "ev", "reason", "handleGlobalErrors", "window", "Error", "stackTraceLimit", "addEventListener"], "mappings": ";;;;;AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,yBAAyB,QAAQ,iCAAgC;AAC1E,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,uCAAuC,QAAQ,yBAAwB;AAChF,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,OAAOC,aAAa,8BAA6B;AACjD,SAASC,oBAAoB,QAAQ,kBAAiB;AACtD,SAASC,8BAA8B,QAAQ,yBAAwB;AACvE,SAASC,qBAAqB,QAAQ,mBAAkB;;;;;;;;;;AAExD,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAE1C,SAASC,kBACdC,WAAoB,EACpBC,gBAAuB,EACvBC,mBAAoC;IAApCA,IAAAA,wBAAAA,KAAAA,GAAAA,sBAA+B;IAE/B,IAAIC;IACJ,IAAI,CAACH,eAAe,CAAChB,gLAAAA,EAAQgB,cAAc;QACzC,sDAAsD;QACtD,MAAMI,oMAAwBrB,oBAAAA,EAAkBkB;QAChDE,SAAQlB,oQAAAA,EAAqBmB;IAC/B,OAAO;QACLD,QAAQD,oQACJjB,uBAAAA,EAAqBe,eACrBA;IACN;IACAG,SAAQhB,sQAAAA,EAAsBgB;4PAE9BrB,0CAAAA,KAA2CmB;uQAC3CrB,4BAAAA,EAA0BuB;KAE1BjB,wRAAAA,EAA+BS,YAAYQ;IAC3C,KAAK,MAAME,WAAWT,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbiB,QAAQF;QACV;IACF;AACF;AAEO,SAASG,gBACdC,sBAAoC,EACpCC,0BAAwC;2KAExC7B,YAAAA,EAAU;QACR,wBAAwB;QACxBgB,WAAWc,OAAO,CAACF;QACnBV,eAAeY,OAAO,CAACD;QAEvB,wBAAwB;QACxBZ,cAAcc,IAAI,CAACH;QACnBT,kBAAkBY,IAAI,CAACF;QAEvB,OAAO;YACL,oBAAoB;YACpBZ,cAAce,MAAM,CAACf,cAAcgB,OAAO,CAACL,yBAAyB;YACpET,kBAAkBa,MAAM,CACtBb,kBAAkBc,OAAO,CAACJ,6BAC1B;QAEJ;IACF,GAAG;QAACD;QAAwBC;KAA2B;AACzD;AAEA,SAASK,iBAAiBC,KAA8B;IACtD,6MAAIjC,oBAAAA,EAAkBiC,MAAMX,KAAK,GAAG;QAClCW,MAAMC,cAAc;QACpB,OAAO;IACT;IACAhB,kBAAkBe,MAAMX,KAAK,EAAE,EAAE;AACnC;AAEA,SAASa,qBAAqBC,EAAwC;IACpE,MAAMC,SAASD,MAAAA,OAAAA,KAAAA,IAAAA,GAAIC,MAAM;IACzB,6MAAIrC,oBAAAA,EAAkBqC,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,IAAIZ,QAAQe;IACZ,IAAIf,SAAS,CAACnB,gLAAAA,EAAQmB,QAAQ;QAC5BA,sPAAQlB,uBAAAA,EAAqBkB,QAAQ;IACvC;IAEAN,eAAea,IAAI,CAACP;IACpB,KAAK,MAAME,WAAWP,kBAAmB;QACvCO,QAAQF;IACV;AACF;AAEO,SAASgB;IACd,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,OAAA,GAAM,CAAC;QAETF,OAAOG,gBAAgB,CAAC,SAASV;QACjCO,OAAOG,gBAAgB,CAAC,sBAAsBP;IAChD;AACF", "ignoreList": [0]}}, {"offset": {"line": 6563, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6569, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/get-socket-url.ts"], "sourcesContent": ["import { normalizedAssetPrefix } from '../../../../../shared/lib/normalized-asset-prefix'\n\nfunction getSocketProtocol(assetPrefix: string): string {\n  let protocol = window.location.protocol\n\n  try {\n    // assetPrefix is a url\n    protocol = new URL(assetPrefix).protocol\n  } catch {}\n\n  return protocol === 'http:' ? 'ws:' : 'wss:'\n}\n\nexport function getSocketUrl(assetPrefix: string | undefined): string {\n  const prefix = normalizedAssetPrefix(assetPrefix)\n  const protocol = getSocketProtocol(assetPrefix || '')\n\n  if (URL.canParse(prefix)) {\n    // since normalized asset prefix is ensured to be a URL format,\n    // we can safely replace the protocol\n    return prefix.replace(/^http/, 'ws')\n  }\n\n  const { hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? `:${port}` : ''}${prefix}`\n}\n"], "names": ["normalizedAssetPrefix", "getSocketProtocol", "assetPrefix", "protocol", "window", "location", "URL", "getSocketUrl", "prefix", "canParse", "replace", "hostname", "port"], "mappings": ";;;AAAA,SAASA,qBAAqB,QAAQ,oDAAmD;;AAEzF,SAASC,kBAAkBC,WAAmB;IAC5C,IAAIC,WAAWC,OAAOC,QAAQ,CAACF,QAAQ;IAEvC,IAAI;QACF,uBAAuB;QACvBA,WAAW,IAAIG,IAAIJ,aAAaC,QAAQ;IAC1C,EAAE,OAAA,GAAM,CAAC;IAET,OAAOA,aAAa,UAAU,QAAQ;AACxC;AAEO,SAASI,aAAaL,WAA+B;IAC1D,MAAMM,2MAASR,wBAAAA,EAAsBE;IACrC,MAAMC,WAAWF,kBAAkBC,eAAe;IAElD,IAAII,IAAIG,QAAQ,CAACD,SAAS;QACxB,+DAA+D;QAC/D,qCAAqC;QACrC,OAAOA,OAAOE,OAAO,CAAC,SAAS;IACjC;IAEA,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGR,OAAOC,QAAQ;IAC1C,OAAUF,WAAS,OAAIQ,WAAWC,CAAAA,OAAQ,MAAGA,OAAS,EAAC,IAAIJ;AAC7D", "ignoreList": [0]}}, {"offset": {"line": 6593, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6599, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/use-websocket.ts"], "sourcesContent": ["import { useCallback, useContext, useEffect, useRef } from 'react'\nimport { GlobalLayoutRouterContext } from '../../../../../shared/lib/app-router-context.shared-runtime'\nimport { getSocketUrl } from './get-socket-url'\nimport type { TurbopackMsgToBrowser } from '../../../../../server/dev/hot-reloader-types'\n\nexport function useWebsocket(assetPrefix: string) {\n  const webSocketRef = useRef<WebSocket>(undefined)\n\n  useEffect(() => {\n    if (webSocketRef.current) {\n      return\n    }\n\n    const url = getSocketUrl(assetPrefix)\n\n    webSocketRef.current = new window.WebSocket(`${url}/_next/webpack-hmr`)\n  }, [assetPrefix])\n\n  return webSocketRef\n}\n\nexport function useSendMessage(webSocketRef: ReturnType<typeof useWebsocket>) {\n  const sendMessage = useCallback(\n    (data: string) => {\n      const socket = webSocketRef.current\n      if (!socket || socket.readyState !== socket.OPEN) {\n        return\n      }\n      return socket.send(data)\n    },\n    [webSocketRef]\n  )\n  return sendMessage\n}\n\nexport function useTurbopack(\n  sendMessage: ReturnType<typeof useSendMessage>,\n  onUpdateError: (err: unknown) => void\n) {\n  const turbopackState = useRef<{\n    init: boolean\n    queue: Array<TurbopackMsgToBrowser> | undefined\n    callback: ((msg: TurbopackMsgToBrowser) => void) | undefined\n  }>({\n    init: false,\n    // Until the dynamic import resolves, queue any turbopack messages which will be replayed.\n    queue: [],\n    callback: undefined,\n  })\n\n  const processTurbopackMessage = useCallback((msg: TurbopackMsgToBrowser) => {\n    const { callback, queue } = turbopackState.current\n    if (callback) {\n      callback(msg)\n    } else {\n      queue!.push(msg)\n    }\n  }, [])\n\n  useEffect(() => {\n    const { current: initCurrent } = turbopackState\n    // TODO(WEB-1589): only install if `process.turbopack` set.\n    if (initCurrent.init) {\n      return\n    }\n    initCurrent.init = true\n\n    import(\n      // @ts-expect-error requires \"moduleResolution\": \"node16\" in tsconfig.json and not .ts extension\n      '@vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts'\n    ).then(({ connect }) => {\n      const { current } = turbopackState\n      connect({\n        addMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n          current.callback = cb\n\n          // Replay all Turbopack messages before we were able to establish the HMR client.\n          for (const msg of current.queue!) {\n            cb(msg)\n          }\n          current.queue = undefined\n        },\n        sendMessage,\n        onUpdateError,\n      })\n    })\n  }, [sendMessage, onUpdateError])\n\n  return processTurbopackMessage\n}\n\nexport function useWebsocketPing(\n  websocketRef: ReturnType<typeof useWebsocket>\n) {\n  const sendMessage = useSendMessage(websocketRef)\n  const { tree } = useContext(GlobalLayoutRouterContext)\n\n  useEffect(() => {\n    // Taken from on-demand-entries-client.js\n    const interval = setInterval(() => {\n      sendMessage(\n        JSON.stringify({\n          event: 'ping',\n          tree,\n          appDirRoute: true,\n        })\n      )\n    }, 2500)\n    return () => clearInterval(interval)\n  }, [tree, sendMessage])\n}\n"], "names": ["useCallback", "useContext", "useEffect", "useRef", "GlobalLayoutRouterContext", "getSocketUrl", "useWebsocket", "assetPrefix", "webSocketRef", "undefined", "current", "url", "window", "WebSocket", "useSendMessage", "sendMessage", "data", "socket", "readyState", "OPEN", "send", "useTurbopack", "onUpdateError", "turbopackState", "init", "queue", "callback", "processTurbopackMessage", "msg", "push", "initCurrent", "then", "connect", "addMessageListener", "cb", "useWebsocketPing", "websocketRef", "tree", "interval", "setInterval", "JSON", "stringify", "event", "appDirRoute", "clearInterval"], "mappings": ";;;;;;AAAA,SAASA,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,QAAO;AAClE,SAASC,yBAAyB,QAAQ,8DAA6D;AACvG,SAASC,YAAY,QAAQ,mBAAkB;;;;AAGxC,SAASC,aAAaC,WAAmB;IAC9C,MAAMC,sLAAeL,SAAAA,EAAkBM;2KAEvCP,YAAAA,EAAU;QACR,IAAIM,aAAaE,OAAO,EAAE;YACxB;QACF;QAEA,MAAMC,wPAAMN,eAAAA,EAAaE;QAEzBC,aAAaE,OAAO,GAAG,IAAIE,OAAOC,SAAS,CAAE,KAAEF,MAAI;IACrD,GAAG;QAACJ;KAAY;IAEhB,OAAOC;AACT;AAEO,SAASM,eAAeN,YAA6C;IAC1E,MAAMO,kBAAcf,iLAAAA,EAClB,CAACgB;QACC,MAAMC,SAAST,aAAaE,OAAO;QACnC,IAAI,CAACO,UAAUA,OAAOC,UAAU,KAAKD,OAAOE,IAAI,EAAE;YAChD;QACF;QACA,OAAOF,OAAOG,IAAI,CAACJ;IACrB,GACA;QAACR;KAAa;IAEhB,OAAOO;AACT;AAEO,SAASM,aACdN,WAA8C,EAC9CO,aAAqC;IAErC,MAAMC,qBAAiBpB,4KAAAA,EAIpB;QACDqB,MAAM;QACN,0FAA0F;QAC1FC,OAAO,EAAE;QACTC,UAAUjB;IACZ;IAEA,MAAMkB,2BAA0B3B,oLAAAA,EAAY,CAAC4B;QAC3C,MAAM,EAAEF,QAAQ,EAAED,KAAK,EAAE,GAAGF,eAAeb,OAAO;QAClD,IAAIgB,UAAU;YACZA,SAASE;QACX,OAAO;YACLH,MAAOI,IAAI,CAACD;QACd;IACF,GAAG,EAAE;KAEL1B,kLAAAA,EAAU;QACR,MAAM,EAAEQ,SAASoB,WAAW,EAAE,GAAGP;QACjC,2DAA2D;QAC3D,IAAIO,YAAYN,IAAI,EAAE;YACpB;QACF;QACAM,YAAYN,IAAI,GAAG;QAEnB,MAAM,CACJ,gGAAgG,0BAEhGO,IAAI,CAAC,CAAA;gBAAC,EAAEC,OAAO,EAAE,GAAA;YACjB,MAAM,EAAEtB,OAAO,EAAE,GAAGa;YACpBS,QAAQ;gBACNC,oBAAmBC,EAAwC;oBACzDxB,QAAQgB,QAAQ,GAAGQ;oBAEnB,iFAAiF;oBACjF,KAAK,MAAMN,OAAOlB,QAAQe,KAAK,CAAG;wBAChCS,GAAGN;oBACL;oBACAlB,QAAQe,KAAK,GAAGhB;gBAClB;gBACAM;gBACAO;YACF;QACF;IACF,GAAG;QAACP;QAAaO;KAAc;IAE/B,OAAOK;AACT;AAEO,SAASQ,iBACdC,YAA6C;IAE7C,MAAMrB,cAAcD,eAAesB;IACnC,MAAM,EAAEC,IAAI,EAAE,IAAGpC,mLAAAA,yMAAWG,4BAAAA;2KAE5BF,YAAAA,EAAU;QACR,yCAAyC;QACzC,MAAMoC,WAAWC,YAAY;YAC3BxB,YACEyB,KAAKC,SAAS,CAAC;gBACbC,OAAO;gBACPL;gBACAM,aAAa;YACf;QAEJ,GAAG;QACH,OAAO,IAAMC,cAAcN;IAC7B,GAAG;QAACD;QAAMtB;KAAY;AACxB", "ignoreList": [0]}}, {"offset": {"line": 6698, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6704, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/internal/helpers/parse-component-stack.ts"], "sourcesContent": ["export type ComponentStackFrame = {\n  canOpenInEditor: boolean\n  component: string\n  file?: string\n  lineNumber?: number\n  column?: number\n}\n\nenum LocationType {\n  FILE = 'file',\n  WEBPACK_INTERNAL = 'webpack-internal',\n  HTTP = 'http',\n  PROTOCOL_RELATIVE = 'protocol-relative',\n  UNKNOWN = 'unknown',\n}\n\n/**\n * Get the type of frame line based on the location\n */\nfunction getLocationType(location: string): LocationType {\n  if (location.startsWith('file://')) {\n    return LocationType.FILE\n  }\n  if (location.includes('webpack-internal://')) {\n    return LocationType.WEBPACK_INTERNAL\n  }\n  if (location.startsWith('http://') || location.startsWith('https://')) {\n    return LocationType.HTTP\n  }\n  if (location.startsWith('//')) {\n    return LocationType.PROTOCOL_RELATIVE\n  }\n  return LocationType.UNKNOWN\n}\n\nfunction parseStackFrameLocation(\n  location: string\n): Omit<ComponentStackFrame, 'component'> {\n  const locationType = getLocationType(location)\n\n  const modulePath = location?.replace(\n    /^(webpack-internal:\\/\\/\\/|file:\\/\\/)(\\(.*\\)\\/)?/,\n    ''\n  )\n  const [, file, lineNumber, column] =\n    modulePath?.match(/^(.+):(\\d+):(\\d+)/) ?? []\n\n  switch (locationType) {\n    case LocationType.FILE:\n    case LocationType.WEBPACK_INTERNAL:\n      return {\n        canOpenInEditor: true,\n        file,\n        lineNumber: lineNumber ? Number(lineNumber) : undefined,\n        column: column ? Number(column) : undefined,\n      }\n    // When the location is a URL we only show the file\n    // TODO: Resolve http(s) URLs through sourcemaps\n    case LocationType.HTTP:\n    case LocationType.PROTOCOL_RELATIVE:\n    case LocationType.UNKNOWN:\n    default: {\n      return {\n        canOpenInEditor: false,\n      }\n    }\n  }\n}\n\nexport function parseComponentStack(\n  componentStack: string\n): ComponentStackFrame[] {\n  const componentStackFrames: ComponentStackFrame[] = []\n  for (const line of componentStack.trim().split('\\n')) {\n    // TODO: support safari stack trace\n    // Get component and file from the component stack line\n    const match = /at ([^ ]+)( \\((.*)\\))?/.exec(line)\n    if (match?.[1]) {\n      const component = match[1]\n      const location = match[3]\n\n      if (!location) {\n        componentStackFrames.push({\n          canOpenInEditor: false,\n          component,\n        })\n        continue\n      }\n\n      // Stop parsing the component stack if we reach a Next.js component\n      if (location?.includes('next/dist')) {\n        break\n      }\n\n      const frameLocation = parseStackFrameLocation(location)\n      componentStackFrames.push({\n        component,\n        ...frameLocation,\n      })\n    }\n  }\n\n  return componentStackFrames\n}\n"], "names": ["LocationType", "getLocationType", "location", "startsWith", "includes", "parseStackFrameLocation", "locationType", "modulePath", "replace", "file", "lineNumber", "column", "match", "canOpenInEditor", "Number", "undefined", "parseComponentStack", "componentStack", "componentStackFrames", "line", "trim", "split", "exec", "component", "push", "frameLocation"], "mappings": ";;;AAQA,IAAKA,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;;;WAAAA;EAAAA,gBAAAA,CAAAA;AAQL;;CAEC,GACD,SAASC,gBAAgBC,QAAgB;IACvC,IAAIA,SAASC,UAAU,CAAC,YAAY;QAClC,OAAA;IACF;IACA,IAAID,SAASE,QAAQ,CAAC,wBAAwB;QAC5C,OAAA;IACF;IACA,IAAIF,SAASC,UAAU,CAAC,cAAcD,SAASC,UAAU,CAAC,aAAa;QACrE,OAAA;IACF;IACA,IAAID,SAASC,UAAU,CAAC,OAAO;QAC7B,OAAA;IACF;IACA,OAAA;AACF;AAEA,SAASE,wBACPH,QAAgB;IAEhB,MAAMI,eAAeL,gBAAgBC;IAErC,MAAMK,aAAaL,YAAAA,OAAAA,KAAAA,IAAAA,SAAUM,OAAO,CAClC,mDACA;QAGAD;IADF,MAAM,GAAGE,MAAMC,YAAYC,OAAO,GAChCJ,CAAAA,oBAAAA,cAAAA,OAAAA,KAAAA,IAAAA,WAAYK,KAAK,CAAC,oBAAA,KAAA,OAAlBL,oBAA0C,EAAE;IAE9C,OAAQD;QACN,KAAA;QACA,KAAA;YACE,OAAO;gBACLO,iBAAiB;gBACjBJ;gBACAC,YAAYA,aAAaI,OAAOJ,cAAcK;gBAC9CJ,QAAQA,SAASG,OAAOH,UAAUI;YACpC;QACF,mDAAmD;QACnD,gDAAgD;QAChD,KAAA;QACA,KAAA;QACA,KAAA;QACA;YAAS;gBACP,OAAO;oBACLF,iBAAiB;gBACnB;YACF;IACF;AACF;AAEO,SAASG,oBACdC,cAAsB;IAEtB,MAAMC,uBAA8C,EAAE;IACtD,KAAK,MAAMC,QAAQF,eAAeG,IAAI,GAAGC,KAAK,CAAC,MAAO;QACpD,mCAAmC;QACnC,uDAAuD;QACvD,MAAMT,QAAQ,yBAAyBU,IAAI,CAACH;QAC5C,IAAIP,SAAAA,OAAAA,KAAAA,IAAAA,KAAO,CAAC,EAAE,EAAE;YACd,MAAMW,YAAYX,KAAK,CAAC,EAAE;YAC1B,MAAMV,WAAWU,KAAK,CAAC,EAAE;YAEzB,IAAI,CAACV,UAAU;gBACbgB,qBAAqBM,IAAI,CAAC;oBACxBX,iBAAiB;oBACjBU;gBACF;gBACA;YACF;YAEA,mEAAmE;YACnE,IAAIrB,YAAAA,OAAAA,KAAAA,IAAAA,SAAUE,QAAQ,CAAC,cAAc;gBACnC;YACF;YAEA,MAAMqB,gBAAgBpB,wBAAwBH;YAC9CgB,qBAAqBM,IAAI,CAAC;gBACxBD;gBACA,GAAGE,aAAa;YAClB;QACF;IACF;IAEA,OAAOP;AACT", "ignoreList": [0]}}, {"offset": {"line": 6788, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6794, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/react-dev-overlay/app/hot-reloader-client.tsx"], "sourcesContent": ["import type { ReactNode } from 'react'\nimport { useCallback, useEffect, startTransition, useMemo, useRef } from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport formatWebpackMessages from '../internal/helpers/format-webpack-messages'\nimport { useRouter } from '../../navigation'\nimport {\n  ACTION_BEFORE_REFRESH,\n  ACTION_BUILD_ERROR,\n  ACTION_BUILD_OK,\n  ACTION_DEBUG_INFO,\n  ACTION_REFRESH,\n  ACTION_STATIC_INDICATOR,\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  ACTION_VERSION_INFO,\n  useErrorOverlayReducer,\n} from '../shared'\nimport { parseStack } from '../internal/helpers/parse-stack'\nimport ReactDevOverlay from './ReactDevOverlay'\nimport { useErrorHandler } from '../internal/helpers/use-error-handler'\nimport { RuntimeErrorHandler } from '../internal/helpers/runtime-error-handler'\nimport {\n  useSendMessage,\n  useTurbopack,\n  useWebsocket,\n  useWebsocketPing,\n} from '../internal/helpers/use-websocket'\nimport { parseComponentStack } from '../internal/helpers/parse-component-stack'\nimport type { VersionInfo } from '../../../../server/dev/parse-version-info'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport { extractModulesFromTurbopackMessage } from '../../../../server/dev/extract-modules-from-turbopack-message'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from '../shared'\nimport type { HydrationErrorState } from '../internal/helpers/hydration-error-info'\nimport type { DebugInfo } from '../types'\nimport { useUntrackedPathname } from '../../navigation-untracked'\nimport { getReactStitchedError } from '../internal/helpers/stitched-error'\n\nexport interface Dispatcher {\n  onBuildOk(): void\n  onBuildError(message: string): void\n  onVersionInfo(versionInfo: VersionInfo): void\n  onDebugInfo(debugInfo: DebugInfo): void\n  onBeforeRefresh(): void\n  onRefresh(): void\n  onStaticIndicator(status: boolean): void\n}\n\nlet mostRecentCompilationHash: any = null\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now())\nlet reloading = false\nlet startLatency: number | null = null\n\nlet pendingHotUpdateWebpack = Promise.resolve()\nlet resolvePendingHotUpdateWebpack: () => void = () => {}\nfunction setPendingHotUpdateWebpack() {\n  pendingHotUpdateWebpack = new Promise((resolve) => {\n    resolvePendingHotUpdateWebpack = () => {\n      resolve()\n    }\n  })\n}\n\nexport function waitForWebpackRuntimeHotUpdate() {\n  return pendingHotUpdateWebpack\n}\n\nfunction handleBeforeHotUpdateWebpack(\n  dispatcher: Dispatcher,\n  hasUpdates: boolean\n) {\n  if (hasUpdates) {\n    dispatcher.onBeforeRefresh()\n  }\n}\n\nfunction handleSuccessfulHotUpdateWebpack(\n  dispatcher: Dispatcher,\n  sendMessage: (message: string) => void,\n  updatedModules: ReadonlyArray<string>\n) {\n  resolvePendingHotUpdateWebpack()\n  dispatcher.onBuildOk()\n  reportHmrLatency(sendMessage, updatedModules)\n\n  dispatcher.onRefresh()\n}\n\nfunction reportHmrLatency(\n  sendMessage: (message: string) => void,\n  updatedModules: ReadonlyArray<string>\n) {\n  if (!startLatency) return\n  let endLatency = Date.now()\n  const latency = endLatency - startLatency\n  console.log(`[Fast Refresh] done in ${latency}ms`)\n  sendMessage(\n    JSON.stringify({\n      event: 'client-hmr-latency',\n      id: window.__nextDevClientId,\n      startTime: startLatency,\n      endTime: endLatency,\n      page: window.location.pathname,\n      updatedModules,\n      // Whether the page (tab) was hidden at the time the event occurred.\n      // This can impact the accuracy of the event's timing.\n      isPageHidden: document.visibilityState === 'hidden',\n    })\n  )\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */\nfunction isUpdateAvailable() {\n  if (process.env.TURBOPACK) {\n    return true\n  }\n\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  // @ts-expect-error module.hot exists\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: any) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: any) {\n      if (status === 'idle') {\n        // @ts-expect-error module.hot exists\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    // @ts-expect-error module.hot exists\n    module.hot.addStatusHandler(handler)\n  }\n}\n\nfunction performFullReload(err: any, sendMessage: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  if (reloading) return\n  reloading = true\n  window.location.reload()\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(\n  onBeforeUpdate: (hasUpdates: boolean) => void,\n  onHotUpdateSuccess: (updatedModules: string[]) => void,\n  sendMessage: any,\n  dispatcher: Dispatcher\n) {\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    resolvePendingHotUpdateWebpack()\n    dispatcher.onBuildOk()\n    reportHmrLatency(sendMessage, [])\n    return\n  }\n\n  function handleApplyUpdates(err: any, updatedModules: string[] | null) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n      if (err) {\n        console.warn(\n          '[Fast Refresh] performing full reload\\n\\n' +\n            \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n            'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n            'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n            'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n            'Fast Refresh requires at least one parent function component in your React tree.'\n        )\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err, sendMessage)\n      return\n    }\n\n    const hasUpdates = Boolean(updatedModules.length)\n    if (typeof onHotUpdateSuccess === 'function') {\n      // Maybe we want to do something.\n      onHotUpdateSuccess(updatedModules)\n    }\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdates(\n        hasUpdates ? () => {} : onBeforeUpdate,\n        hasUpdates ? () => dispatcher.onBuildOk() : onHotUpdateSuccess,\n        sendMessage,\n        dispatcher\n      )\n    } else {\n      dispatcher.onBuildOk()\n      if (process.env.__NEXT_TEST_MODE) {\n        afterApplyUpdates(() => {\n          if (self.__NEXT_HMR_CB) {\n            self.__NEXT_HMR_CB()\n            self.__NEXT_HMR_CB = null\n          }\n        })\n      }\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  // @ts-expect-error module.hot exists\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: any[] | null) => {\n      if (!updatedModules) {\n        return null\n      }\n\n      if (typeof onBeforeUpdate === 'function') {\n        const hasUpdates = Boolean(updatedModules.length)\n        onBeforeUpdate(hasUpdates)\n      }\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      // @ts-expect-error module.hot exists\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: any[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\n/** Handles messages from the sevrer for the App Router. */\nfunction processMessage(\n  obj: HMR_ACTION_TYPES,\n  sendMessage: (message: string) => void,\n  processTurbopackMessage: (msg: TurbopackMsgToBrowser) => void,\n  router: ReturnType<typeof useRouter>,\n  dispatcher: Dispatcher,\n  appIsrManifestRef: ReturnType<typeof useRef>,\n  pathnameRef: ReturnType<typeof useRef>\n) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  function handleErrors(errors: ReadonlyArray<unknown>) {\n    // \"Massage\" webpack messages.\n    const formatted = formatWebpackMessages({\n      errors: errors,\n      warnings: [],\n    })\n\n    // Only show the first error.\n    dispatcher.onBuildError(formatted.errors[0])\n\n    // Also log them to the console.\n    for (let i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (process.env.__NEXT_TEST_MODE) {\n      if (self.__NEXT_HMR_CB) {\n        self.__NEXT_HMR_CB(formatted.errors[0])\n        self.__NEXT_HMR_CB = null\n      }\n    }\n  }\n\n  function handleHotUpdate() {\n    if (process.env.TURBOPACK) {\n      dispatcher.onBuildOk()\n    } else {\n      tryApplyUpdates(\n        function onBeforeHotUpdate(hasUpdates: boolean) {\n          handleBeforeHotUpdateWebpack(dispatcher, hasUpdates)\n        },\n        function onSuccessfulHotUpdate(webpackUpdatedModules: string[]) {\n          // Only dismiss it when we're sure it's a hot update.\n          // Otherwise it would flicker right before the reload.\n          handleSuccessfulHotUpdateWebpack(\n            dispatcher,\n            sendMessage,\n            webpackUpdatedModules\n          )\n        },\n        sendMessage,\n        dispatcher\n      )\n    }\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.APP_ISR_MANIFEST: {\n      if (process.env.__NEXT_APP_ISR_INDICATOR) {\n        if (appIsrManifestRef) {\n          appIsrManifestRef.current = obj.data\n\n          // handle initial status on receiving manifest\n          // navigation is handled in useEffect for pathname changes\n          // as we'll receive the updated manifest before usePathname\n          // triggers for new value\n          if ((pathnameRef.current as string) in obj.data) {\n            // the indicator can be hidden for an hour.\n            // check if it's still hidden\n            const indicatorHiddenAt = Number(\n              localStorage?.getItem('__NEXT_DISMISS_PRERENDER_INDICATOR')\n            )\n\n            const isHidden =\n              indicatorHiddenAt &&\n              !isNaN(indicatorHiddenAt) &&\n              Date.now() < indicatorHiddenAt\n\n            if (!isHidden) {\n              dispatcher.onStaticIndicator(true)\n            }\n          } else {\n            dispatcher.onStaticIndicator(false)\n          }\n        }\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      startLatency = Date.now()\n      if (!process.env.TURBOPACK) {\n        setPendingHotUpdateWebpack()\n      }\n      console.log('[Fast Refresh] rebuilding')\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      if (obj.hash) {\n        handleAvailableHash(obj.hash)\n      }\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug)\n\n      const hasErrors = Boolean(errors && errors.length)\n      // Compilation with errors (e.g. syntax error or missing modules).\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        handleErrors(errors)\n        return\n      }\n\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        // Print warnings to the console.\n        const formattedMessages = formatWebpackMessages({\n          warnings: warnings,\n          errors: [],\n        })\n\n        for (let i = 0; i < formattedMessages.warnings.length; i++) {\n          if (i === 5) {\n            console.warn(\n              'There were more warnings in other files.\\n' +\n                'You can find a complete log in the terminal.'\n            )\n            break\n          }\n          console.warn(stripAnsi(formattedMessages.warnings[i]))\n        }\n\n        // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: __nextDevClientId,\n        })\n      )\n\n      if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n        // Handle hot updates\n        handleHotUpdate()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n        data: {\n          sessionId: obj.data.sessionId,\n        },\n      })\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      const updatedModules = extractModulesFromTurbopackMessage(obj.data)\n      dispatcher.onBeforeRefresh()\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n        data: obj.data,\n      })\n      dispatcher.onRefresh()\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null, sendMessage)\n      }\n      reportHmrLatency(sendMessage, updatedModules)\n      break\n    }\n    // TODO-APP: make server component change more granular\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      sendMessage(\n        JSON.stringify({\n          event: 'server-component-reload-page',\n          clientId: __nextDevClientId,\n        })\n      )\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        if (reloading) return\n        reloading = true\n        return window.location.reload()\n      }\n      startTransition(() => {\n        router.hmrRefresh()\n        dispatcher.onRefresh()\n      })\n\n      if (process.env.__NEXT_TEST_MODE) {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      }\n\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n      sendMessage(\n        JSON.stringify({\n          event: 'client-reload-page',\n          clientId: __nextDevClientId,\n        })\n      )\n      if (reloading) return\n      reloading = true\n      return window.location.reload()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE: {\n      // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n      return router.hmrRefresh()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n      return\n    }\n    default: {\n    }\n  }\n}\n\nexport default function HotReload({\n  assetPrefix,\n  children,\n}: {\n  assetPrefix: string\n  children?: ReactNode\n}) {\n  const [state, dispatch] = useErrorOverlayReducer()\n\n  const dispatcher = useMemo<Dispatcher>(() => {\n    return {\n      onBuildOk() {\n        dispatch({ type: ACTION_BUILD_OK })\n      },\n      onBuildError(message) {\n        dispatch({ type: ACTION_BUILD_ERROR, message })\n      },\n      onBeforeRefresh() {\n        dispatch({ type: ACTION_BEFORE_REFRESH })\n      },\n      onRefresh() {\n        dispatch({ type: ACTION_REFRESH })\n      },\n      onVersionInfo(versionInfo) {\n        dispatch({ type: ACTION_VERSION_INFO, versionInfo })\n      },\n      onStaticIndicator(status: boolean) {\n        dispatch({ type: ACTION_STATIC_INDICATOR, staticIndicator: status })\n      },\n      onDebugInfo(debugInfo) {\n        dispatch({ type: ACTION_DEBUG_INFO, debugInfo })\n      },\n    }\n  }, [dispatch])\n\n  const handleOnUnhandledError = useCallback(\n    (error: Error): void => {\n      const errorDetails = (error as any).details as\n        | HydrationErrorState\n        | undefined\n      // Component stack is added to the error in use-error-handler in case there was a hydration error\n      const componentStackTrace =\n        (error as any)._componentStack || errorDetails?.componentStack\n      const warning = errorDetails?.warning\n      const stitchedError = getReactStitchedError(error)\n\n      dispatch({\n        type: ACTION_UNHANDLED_ERROR,\n        reason: stitchedError,\n        frames: parseStack(stitchedError.stack || ''),\n        componentStackFrames:\n          typeof componentStackTrace === 'string'\n            ? parseComponentStack(componentStackTrace)\n            : undefined,\n        warning,\n      })\n    },\n    [dispatch]\n  )\n\n  const handleOnUnhandledRejection = useCallback(\n    (reason: Error): void => {\n      const stitchedError = getReactStitchedError(reason)\n      dispatch({\n        type: ACTION_UNHANDLED_REJECTION,\n        reason: stitchedError,\n        frames: parseStack(stitchedError.stack || ''),\n      })\n    },\n    [dispatch]\n  )\n  useErrorHandler(handleOnUnhandledError, handleOnUnhandledRejection)\n\n  const webSocketRef = useWebsocket(assetPrefix)\n  useWebsocketPing(webSocketRef)\n  const sendMessage = useSendMessage(webSocketRef)\n  const processTurbopackMessage = useTurbopack(sendMessage, (err) =>\n    performFullReload(err, sendMessage)\n  )\n\n  const router = useRouter()\n\n  // We don't want access of the pathname for the dev tools to trigger a dynamic\n  // access (as the dev overlay will never be present in production).\n  const pathname = useUntrackedPathname()\n  const appIsrManifestRef = useRef<Record<string, false | number>>({})\n  const pathnameRef = useRef(pathname)\n\n  if (process.env.__NEXT_APP_ISR_INDICATOR) {\n    // this conditional is only for dead-code elimination which\n    // isn't a runtime conditional only build-time so ignore hooks rule\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      pathnameRef.current = pathname\n\n      const appIsrManifest = appIsrManifestRef.current\n\n      if (appIsrManifest) {\n        if (pathname && pathname in appIsrManifest) {\n          try {\n            const indicatorHiddenAt = Number(\n              localStorage?.getItem('__NEXT_DISMISS_PRERENDER_INDICATOR')\n            )\n\n            const isHidden =\n              indicatorHiddenAt &&\n              !isNaN(indicatorHiddenAt) &&\n              Date.now() < indicatorHiddenAt\n\n            if (!isHidden) {\n              dispatcher.onStaticIndicator(true)\n            }\n          } catch (reason) {\n            let message = ''\n\n            if (reason instanceof DOMException) {\n              // Most likely a SecurityError, because of an unavailable localStorage\n              message = reason.stack ?? reason.message\n            } else if (reason instanceof Error) {\n              message = 'Error: ' + reason.message + '\\n' + (reason.stack ?? '')\n            } else {\n              message = 'Unexpected Exception: ' + reason\n            }\n\n            console.warn('[HMR] ' + message)\n          }\n        } else {\n          dispatcher.onStaticIndicator(false)\n        }\n      }\n    }, [pathname, dispatcher])\n  }\n\n  useEffect(() => {\n    const websocket = webSocketRef.current\n    if (!websocket) return\n\n    const handler = (event: MessageEvent<any>) => {\n      try {\n        const obj = JSON.parse(event.data)\n        processMessage(\n          obj,\n          sendMessage,\n          processTurbopackMessage,\n          router,\n          dispatcher,\n          appIsrManifestRef,\n          pathnameRef\n        )\n      } catch (err: any) {\n        console.warn(\n          '[HMR] Invalid message: ' +\n            JSON.stringify(event.data) +\n            '\\n' +\n            (err?.stack ?? '')\n        )\n      }\n    }\n\n    websocket.addEventListener('message', handler)\n    return () => websocket.removeEventListener('message', handler)\n  }, [\n    sendMessage,\n    router,\n    webSocketRef,\n    dispatcher,\n    processTurbopackMessage,\n    appIsrManifestRef,\n  ])\n\n  return (\n    <ReactDevOverlay state={state} dispatcher={dispatcher}>\n      {children}\n    </ReactDevOverlay>\n  )\n}\n"], "names": ["useCallback", "useEffect", "startTransition", "useMemo", "useRef", "stripAnsi", "formatWebpackMessages", "useRouter", "ACTION_BEFORE_REFRESH", "ACTION_BUILD_ERROR", "ACTION_BUILD_OK", "ACTION_DEBUG_INFO", "ACTION_REFRESH", "ACTION_STATIC_INDICATOR", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_VERSION_INFO", "useErrorOverlayReducer", "parseStack", "ReactDevOverlay", "useErrorHandler", "RuntimeError<PERSON>andler", "useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "parseComponentStack", "HMR_ACTIONS_SENT_TO_BROWSER", "extractModulesFromTurbopackMessage", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "useUntrackedPathname", "getReactStitchedError", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "startLatency", "pendingHotUpdateWebpack", "Promise", "resolve", "resolvePendingHotUpdateWebpack", "setPendingHotUpdateWebpack", "waitForWebpackRuntimeHotUpdate", "handleBeforeHotUpdateWebpack", "dispatcher", "hasUpdates", "onBeforeRefresh", "handleSuccessfulHotUpdateWebpack", "sendMessage", "updatedModules", "onBuildOk", "reportHmrLatency", "onRefresh", "endLatency", "latency", "console", "log", "JSON", "stringify", "event", "id", "window", "startTime", "endTime", "page", "location", "pathname", "isPageHidden", "document", "visibilityState", "handleAvailableHash", "hash", "isUpdateAvailable", "process", "env", "TURBOPACK", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "stackTrace", "stack", "split", "slice", "join", "message", "hadRuntimeError", "dependency<PERSON><PERSON>n", "undefined", "reload", "tryApplyUpdates", "onBeforeUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "warn", "Boolean", "length", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "appIsrManifestRef", "pathnameRef", "handleErrors", "errors", "formatted", "warnings", "onBuildError", "i", "error", "handleHotUpdate", "onBeforeHotUpdate", "onSuccessfulHotUpdate", "webpackUpdatedModules", "action", "APP_ISR_MANIFEST", "__NEXT_APP_ISR_INDICATOR", "current", "data", "localStorage", "indicatorHiddenAt", "Number", "getItem", "isHidden", "isNaN", "onStaticIndicator", "BUILDING", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "debug", "onDebugInfo", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "sessionId", "TURBOPACK_MESSAGE", "SERVER_COMPONENT_CHANGES", "hmrRefresh", "RELOAD_PAGE", "ADDED_PAGE", "REMOVED_PAGE", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "HotReload", "assetPrefix", "children", "state", "dispatch", "staticIndicator", "debugInfo", "handleOnUnhandledError", "errorDetails", "details", "componentStackTrace", "_componentStack", "componentStack", "warning", "stitchedError", "reason", "frames", "componentStackFrames", "handleOnUnhandledRejection", "webSocketRef", "appIsrManifest", "DOMException", "websocket", "addEventListener", "removeEventListener"], "mappings": ";;;;;AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,eAAe,EAAEC,OAAO,EAAEC,MAAM,QAAQ,QAAO;AAChF,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,2BAA2B,8CAA6C;AAC/E,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SACEC,qBAAqB,EACrBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,uBAAuB,EACvBC,sBAAsB,EACtBC,0BAA0B,EAC1BC,mBAAmB,EACnBC,sBAAsB,QACjB,YAAW;AAClB,SAASC,UAAU,QAAQ,kCAAiC;AAC5D,OAAOC,qBAAqB,oBAAmB;AAC/C,SAASC,eAAe,QAAQ,wCAAuC;AACvE,SAASC,mBAAmB,QAAQ,4CAA2C;AAC/E,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,gBAAgB,QACX,oCAAmC;AAC1C,SAASC,mBAAmB,QAAQ,4CAA2C;AAE/E,SAASC,2BAA2B,QAAQ,4CAA2C;AAKvF,SAASC,kCAAkC,QAAQ,gEAA+D;AAIlH,SAASE,oBAAoB,QAAQ,6BAA4B;AACjE,SAASC,qBAAqB,QAAQ,qCAAoC;;;;;;;;;;;;;;;;;;;AAY1E,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,eAA8B;AAElC,IAAIC,0BAA0BC,QAAQC,OAAO;AAC7C,IAAIC,iCAA6C,KAAO;AACxD,SAASC;IACPJ,0BAA0B,IAAIC,QAAQ,CAACC;QACrCC,iCAAiC;YAC/BD;QACF;IACF;AACF;AAEO,SAASG;IACd,OAAOL;AACT;AAEA,SAASM,6BACPC,UAAsB,EACtBC,UAAmB;IAEnB,IAAIA,YAAY;QACdD,WAAWE,eAAe;IAC5B;AACF;AAEA,SAASC,iCACPH,UAAsB,EACtBI,WAAsC,EACtCC,cAAqC;IAErCT;IACAI,WAAWM,SAAS;IACpBC,iBAAiBH,aAAaC;IAE9BL,WAAWQ,SAAS;AACtB;AAEA,SAASD,iBACPH,WAAsC,EACtCC,cAAqC;IAErC,IAAI,CAACb,cAAc;IACnB,IAAIiB,aAAapB,KAAKC,GAAG;IACzB,MAAMoB,UAAUD,aAAajB;IAC7BmB,QAAQC,GAAG,CAAE,4BAAyBF,UAAQ;IAC9CN,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,IAAIC,OAAOhC,iBAAiB;QAC5BiC,WAAW1B;QACX2B,SAASV;QACTW,MAAMH,OAAOI,QAAQ,CAACC,QAAQ;QAC9BjB;QACA,oEAAoE;QACpE,sDAAsD;QACtDkB,cAAcC,SAASC,eAAe,KAAK;IAC7C;AAEJ;AAEA,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtC3C,4BAA4B2C;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIC,QAAQC,GAAG,CAACC,SAAS,eAAE;QACzB,OAAO;IACT;;AAMF;AAEA,6CAA6C;AAC7C,SAASE;IACP,qCAAqC;IACrC,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrB,qCAAqC;gBACrCF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACA,qCAAqC;QACrCJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEvC,WAAgB;IACnD,MAAMwC,aACJD,OACC,CAACA,IAAIE,KAAK,IAAIF,IAAIE,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDL,IAAIM,OAAO,IACXN,MAAM,EAAC;IAEXvC,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACP6B;QACAM,iBAAiB,CAAC,sPAAC7E,sBAAAA,CAAoB6E,eAAe;QACtDC,iBAAiBR,MAAMA,IAAIQ,eAAe,GAAGC;IAC/C;IAGF,IAAI7D,WAAW;IACfA,YAAY;IACZ0B,OAAOI,QAAQ,CAACgC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,gBACPC,cAA6C,EAC7CC,kBAAsD,EACtDpD,WAAgB,EAChBJ,UAAsB;IAEtB,IAAI,CAAC4B,uBAAuB,CAACK,mBAAmB;QAC9CrC;QACAI,WAAWM,SAAS;QACpBC,iBAAiBH,aAAa,EAAE;QAChC;IACF;IAEA,SAASqD,mBAAmBd,GAAQ,EAAEtC,cAA+B;QACnE,IAAIsC,4PAAOtE,sBAAAA,CAAoB6E,eAAe,IAAI,CAAC7C,gBAAgB;YACjE,IAAIsC,KAAK;gBACPhC,QAAQ+C,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,yPAAIrF,sBAAAA,CAAoB6E,eAAe,EAAE;gBAC9CvC,QAAQ+C,IAAI,0MAAC7E,uCAAAA;YACf;YACA6D,kBAAkBC,KAAKvC;YACvB;QACF;QAEA,MAAMH,aAAa0D,QAAQtD,eAAeuD,MAAM;QAChD,IAAI,OAAOJ,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBnD;QACrB;QAEA,IAAIuB,qBAAqB;YACvB,+DAA+D;YAC/D0B,gBACErD,aAAa,KAAO,IAAIsD,gBACxBtD,aAAa,IAAMD,WAAWM,SAAS,KAAKkD,oBAC5CpD,aACAJ;QAEJ,OAAO;YACLA,WAAWM,SAAS;YACpB,IAAIuB,QAAQC,GAAG,CAAC+B,gBAAgB,OAAE;;YAOlC;QACF;IACF;IAEA,2DAA2D;IAC3D,qCAAqC;IACrC3B,OAAOC,GAAG,CACP6B,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAC5D;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOkD,mBAAmB,YAAY;YACxC,MAAMtD,aAAa0D,QAAQtD,eAAeuD,MAAM;YAChDL,eAAetD;QACjB;QACA,2DAA2D;QAC3D,qCAAqC;QACrC,OAAOiC,OAAOC,GAAG,CAAC+B,KAAK;IACzB,GACCD,IAAI,CACH,CAAC5D;QACCoD,mBAAmB,MAAMpD;IAC3B,GACA,CAACsC;QACCc,mBAAmBd,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAASwB,eACPC,GAAqB,EACrBhE,WAAsC,EACtCiE,uBAA6D,EAC7DC,MAAoC,EACpCtE,UAAsB,EACtBuE,iBAA4C,EAC5CC,WAAsC;IAEtC,IAAI,CAAE,CAAA,YAAYJ,GAAE,GAAI;QACtB;IACF;IAEA,SAASK,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYrH,qQAAAA,EAAsB;YACtCoH,QAAQA;YACRE,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B5E,WAAW6E,YAAY,CAACF,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAII,IAAI,GAAGA,IAAIH,UAAUD,MAAM,CAACd,MAAM,EAAEkB,IAAK;YAChDnE,QAAQoE,KAAK,gLAAC1H,UAAAA,EAAUsH,UAAUD,MAAM,CAACI,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIjD,QAAQC,GAAG,CAAC+B,gBAAgB,OAAE;;QAKlC;IACF;IAEA,SAASmB;QACP,IAAInD,QAAQC,GAAG,CAACC,SAAS,eAAE;YACzB/B,WAAWM,SAAS;QACtB,OAAO;;QAiBP;IACF;IAEA,OAAQ8D,IAAIgB,MAAM;QAChB,8LAAKzG,8BAAAA,CAA4B0G,gBAAgB;YAAE;gBACjD,IAAIxD,QAAQC,GAAG,CAACwD,wBAAwB,AAAE;oBACxC,IAAIf,mBAAmB;wBACrBA,kBAAkBgB,OAAO,GAAGnB,IAAIoB,IAAI;wBAEpC,8CAA8C;wBAC9C,0DAA0D;wBAC1D,2DAA2D;wBAC3D,yBAAyB;wBACzB,IAAKhB,YAAYe,OAAO,IAAenB,IAAIoB,IAAI,EAAE;gCAI7CC;4BAHF,2CAA2C;4BAC3C,6BAA6B;4BAC7B,MAAMC,oBAAoBC,OAAAA,CACxBF,gBAAAA,YAAAA,KAAAA,OAAAA,KAAAA,IAAAA,cAAcG,OAAO,CAAC;4BAGxB,MAAMC,WACJH,qBACA,CAACI,MAAMJ,sBACPrG,KAAKC,GAAG,KAAKoG;4BAEf,IAAI,CAACG,UAAU;gCACb7F,WAAW+F,iBAAiB,CAAC;4BAC/B;wBACF,OAAO;4BACL/F,WAAW+F,iBAAiB,CAAC;wBAC/B;oBACF;gBACF;gBACA;YACF;QACA,8LAAKpH,8BAAAA,CAA4BqH,QAAQ;YAAE;gBACzCxG,eAAeH,KAAKC,GAAG;gBACvB,IAAI,CAACuC,QAAQC,GAAG,CAACC,SAAS,aAAE;;gBAE5B;gBACApB,QAAQC,GAAG,CAAC;gBACZ;YACF;QACA,KAAKjC,uNAAAA,CAA4BsH,KAAK;QACtC,8LAAKtH,8BAAAA,CAA4BuH,IAAI;YAAE;gBACrC,IAAI9B,IAAIzC,IAAI,EAAE;oBACZD,oBAAoB0C,IAAIzC,IAAI;gBAC9B;gBAEA,MAAM,EAAE+C,MAAM,EAAEE,QAAQ,EAAE,GAAGR;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKpE,WAAWmG,aAAa,CAAC/B,IAAIgC,WAAW;gBAClE,IAAI,WAAWhC,OAAOA,IAAIiC,KAAK,EAAErG,WAAWsG,WAAW,CAAClC,IAAIiC,KAAK;gBAEjE,MAAME,YAAY5C,QAAQe,UAAUA,OAAOd,MAAM;gBACjD,kEAAkE;gBAClE,IAAI2C,WAAW;oBACbnG,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPyF,YAAY9B,OAAOd,MAAM;wBACzB6C,UAAUxH;oBACZ;oBAGFwF,aAAaC;oBACb;gBACF;gBAEA,MAAMgC,cAAc/C,QAAQiB,YAAYA,SAAShB,MAAM;gBACvD,IAAI8C,aAAa;oBACftG,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP4F,cAAc/B,SAAShB,MAAM;wBAC7B6C,UAAUxH;oBACZ;oBAGF,iCAAiC;oBACjC,MAAM2H,oBAAoBtJ,qQAAAA,EAAsB;wBAC9CsH,UAAUA;wBACVF,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAII,IAAI,GAAGA,IAAI8B,kBAAkBhC,QAAQ,CAAChB,MAAM,EAAEkB,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXnE,QAAQ+C,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACA/C,QAAQ+C,IAAI,gLAACrG,UAAAA,EAAUuJ,kBAAkBhC,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEA1E,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP0F,UAAUxH;gBACZ;gBAGF,IAAImF,IAAIgB,MAAM,8LAAKzG,8BAAAA,CAA4BsH,KAAK,EAAE;oBACpD,qBAAqB;oBACrBjB;gBACF;gBACA;YACF;QACA,KAAKrG,uNAAAA,CAA4BkI,mBAAmB;YAAE;gBACpDxC,wBAAwB;oBACtByC,+LAAMnI,8BAAAA,CAA4BkI,mBAAmB;oBACrDrB,MAAM;wBACJuB,WAAW3C,IAAIoB,IAAI,CAACuB,SAAS;oBAC/B;gBACF;gBACA;YACF;QACA,8LAAKpI,8BAAAA,CAA4BqI,iBAAiB;YAAE;gBAClD,MAAM3G,wOAAiBzB,qCAAAA,EAAmCwF,IAAIoB,IAAI;gBAClExF,WAAWE,eAAe;gBAC1BmE,wBAAwB;oBACtByC,+LAAMnI,8BAAAA,CAA4BqI,iBAAiB;oBACnDxB,MAAMpB,IAAIoB,IAAI;gBAChB;gBACAxF,WAAWQ,SAAS;gBACpB,IAAInC,2QAAAA,CAAoB6E,eAAe,EAAE;oBACvCvC,QAAQ+C,IAAI,0MAAC7E,uCAAAA;oBACb6D,kBAAkB,MAAMtC;gBAC1B;gBACAG,iBAAiBH,aAAaC;gBAC9B;YACF;QACA,uDAAuD;QACvD,8LAAK1B,8BAAAA,CAA4BsI,wBAAwB;YAAE;gBACzD7G,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP0F,UAAUxH;gBACZ;gBAEF,IAAIZ,2QAAAA,CAAoB6E,eAAe,EAAE;oBACvC,IAAI3D,WAAW;oBACfA,YAAY;oBACZ,OAAO0B,OAAOI,QAAQ,CAACgC,MAAM;gBAC/B;uLACAnG,kBAAAA,EAAgB;oBACdoH,OAAO4C,UAAU;oBACjBlH,WAAWQ,SAAS;gBACtB;gBAEA,IAAIqB,QAAQC,GAAG,CAAC+B,gBAAgB,OAAE;;gBAKlC;gBAEA;YACF;QACA,8LAAKlF,8BAAAA,CAA4BwI,WAAW;YAAE;gBAC5C/G,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP0F,UAAUxH;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAO0B,OAAOI,QAAQ,CAACgC,MAAM;YAC/B;QACA,KAAK1E,uNAAAA,CAA4ByI,UAAU;QAC3C,8LAAKzI,8BAAAA,CAA4B0I,YAAY;YAAE;gBAC7C,qFAAqF;gBACrF,OAAO/C,OAAO4C,UAAU;YAC1B;QACA,8LAAKvI,8BAAAA,CAA4B2I,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGnD;gBACtB,IAAImD,WAAW;oBACb,MAAM,EAAEtE,OAAO,EAAEJ,KAAK,EAAE,GAAGhC,KAAK2G,KAAK,CAACD;oBACtC,MAAMxC,QAAQ,IAAI0C,MAAMxE;oBACxB8B,MAAMlC,KAAK,GAAGA;oBACd4B,aAAa;wBAACM;qBAAM;gBACtB;gBACA;YACF;QACA,8LAAKpG,8BAAAA,CAA4B+I,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS,CACT;IACF;AACF;AAEe,SAASC,UAAU,KAMjC;IANiC,IAAA,EAChCC,WAAW,EACXC,QAAQ,EAIT,GANiC;IAOhC,MAAM,CAACC,OAAOC,SAAS,gNAAG9J,yBAAAA;IAE1B,MAAM+B,oLAAa7C,UAAAA,EAAoB;QACrC,OAAO;YACLmD;gBACEyH,SAAS;oBAAEjB,+MAAMpJ,kBAAAA;gBAAgB;YACnC;YACAmH,cAAa5B,OAAO;gBAClB8E,SAAS;oBAAEjB,+MAAMrJ,qBAAAA;oBAAoBwF;gBAAQ;YAC/C;YACA/C;gBACE6H,SAAS;oBAAEjB,+MAAMtJ,wBAAAA;gBAAsB;YACzC;YACAgD;gBACEuH,SAAS;oBAAEjB,+MAAMlJ,iBAAAA;gBAAe;YAClC;YACAuI,eAAcC,WAAW;gBACvB2B,SAAS;oBAAEjB,+MAAM9I,sBAAAA;oBAAqBoI;gBAAY;YACpD;YACAL,mBAAkB3D,MAAe;gBAC/B2F,SAAS;oBAAEjB,+MAAMjJ,0BAAAA;oBAAyBmK,iBAAiB5F;gBAAO;YACpE;YACAkE,aAAY2B,SAAS;gBACnBF,SAAS;oBAAEjB,+MAAMnJ,oBAAAA;oBAAmBsK;gBAAU;YAChD;QACF;IACF,GAAG;QAACF;KAAS;IAEb,MAAMG,gMAAyBlL,cAAAA,EAC7B,CAAC+H;QACC,MAAMoD,eAAgBpD,MAAcqD,OAAO;QAG3C,iGAAiG;QACjG,MAAMC,sBACHtD,MAAcuD,eAAe,IAAA,CAAIH,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcI,cAAc;QAChE,MAAMC,UAAUL,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcK,OAAO;QACrC,MAAMC,oBAAgB1J,mQAAAA,EAAsBgG;QAE5CgD,SAAS;YACPjB,+MAAMhJ,yBAAAA;YACN4K,QAAQD;YACRE,oPAAQzK,aAAAA,EAAWuK,cAAc5F,KAAK,IAAI;YAC1C+F,sBACE,OAAOP,wBAAwB,oQAC3B3J,sBAAAA,EAAoB2J,uBACpBjF;YACNoF;QACF;IACF,GACA;QAACT;KAAS;IAGZ,MAAMc,iCAA6B7L,iLAAAA,EACjC,CAAC0L;QACC,MAAMD,+PAAgB1J,wBAAAA,EAAsB2J;QAC5CX,SAAS;YACPjB,MAAM/I,sOAAAA;YACN2K,QAAQD;YACRE,oPAAQzK,aAAAA,EAAWuK,cAAc5F,KAAK,IAAI;QAC5C;IACF,GACA;QAACkF;KAAS;IAEZ3J,uQAAAA,EAAgB8J,wBAAwBW;IAExC,MAAMC,6PAAetK,eAAAA,EAAaoJ;KAClCnJ,gQAAAA,EAAiBqK;IACjB,MAAM1I,4PAAc9B,iBAAAA,EAAewK;IACnC,MAAMzE,wQAA0B9F,eAAAA,EAAa6B,aAAa,CAACuC,MACzDD,kBAAkBC,KAAKvC;IAGzB,MAAMkE,+MAAS/G,YAAAA;IAEf,8EAA8E;IAC9E,mEAAmE;IACnE,MAAM+D,8MAAWxC,uBAAAA;IACjB,MAAMyF,oBAAoBnH,gLAAAA,EAAuC,CAAC;IAClE,MAAMoH,qLAAcpH,SAAAA,EAAOkE;IAE3B,IAAIO,QAAQC,GAAG,CAACwD,wBAAwB,AAAE;QACxC,2DAA2D;QAC3D,mEAAmE;QACnE,sDAAsD;+KACtDrI,YAAAA,EAAU;YACRuH,YAAYe,OAAO,GAAGjE;YAEtB,MAAMyH,iBAAiBxE,kBAAkBgB,OAAO;YAEhD,IAAIwD,gBAAgB;gBAClB,IAAIzH,YAAYA,YAAYyH,gBAAgB;oBAC1C,IAAI;4BAEAtD;wBADF,MAAMC,oBAAoBC,OAAAA,CACxBF,gBAAAA,YAAAA,KAAAA,OAAAA,KAAAA,IAAAA,cAAcG,OAAO,CAAC;wBAGxB,MAAMC,WACJH,qBACA,CAACI,MAAMJ,sBACPrG,KAAKC,GAAG,KAAKoG;wBAEf,IAAI,CAACG,UAAU;4BACb7F,WAAW+F,iBAAiB,CAAC;wBAC/B;oBACF,EAAE,OAAO2C,QAAQ;wBACf,IAAIzF,UAAU;wBAEd,IAAIyF,kBAAkBM,cAAc;gCAExBN;4BADV,sEAAsE;4BACtEzF,UAAUyF,CAAAA,gBAAAA,OAAO7F,KAAK,KAAA,OAAZ6F,gBAAgBA,OAAOzF,OAAO;wBAC1C,OAAO,IAAIyF,kBAAkBjB,OAAO;gCACaiB;4BAA/CzF,UAAU,YAAYyF,OAAOzF,OAAO,GAAG,OAAQyF,CAAAA,CAAAA,iBAAAA,OAAO7F,KAAK,KAAA,OAAZ6F,iBAAgB,EAAC;wBAClE,OAAO;4BACLzF,UAAU,2BAA2ByF;wBACvC;wBAEA/H,QAAQ+C,IAAI,CAAC,WAAWT;oBAC1B;gBACF,OAAO;oBACLjD,WAAW+F,iBAAiB,CAAC;gBAC/B;YACF;QACF,GAAG;YAACzE;YAAUtB;SAAW;IAC3B;2KAEA/C,YAAAA,EAAU;QACR,MAAMgM,YAAYH,aAAavD,OAAO;QACtC,IAAI,CAAC0D,WAAW;QAEhB,MAAM1G,UAAU,CAACxB;YACf,IAAI;gBACF,MAAMqD,MAAMvD,KAAK2G,KAAK,CAACzG,MAAMyE,IAAI;gBACjCrB,eACEC,KACAhE,aACAiE,yBACAC,QACAtE,YACAuE,mBACAC;YAEJ,EAAE,OAAO7B,KAAU;oBAKZA;gBAJLhC,QAAQ+C,IAAI,CACV,4BACE7C,KAAKC,SAAS,CAACC,MAAMyE,IAAI,IACzB,OACC7C,CAAAA,CAAAA,aAAAA,OAAAA,OAAAA,KAAAA,IAAAA,IAAKE,KAAK,KAAA,OAAVF,aAAc,EAAC;YAEtB;QACF;QAEAsG,UAAUC,gBAAgB,CAAC,WAAW3G;QACtC,OAAO,IAAM0G,UAAUE,mBAAmB,CAAC,WAAW5G;IACxD,GAAG;QACDnC;QACAkE;QACAwE;QACA9I;QACAqE;QACAE;KACD;IAED,OAAA,WAAA,mLACE,MAAA,2NAACpG,UAAAA,EAAAA;QAAgB2J,OAAOA;QAAO9H,YAAYA;kBACxC6H;;AAGP", "ignoreList": [0]}}, {"offset": {"line": 7327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
# app/schemas/counseling_session.py
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime
from typing import Optional, List
from .payment import  PaymentResponse

class SessionCreate(BaseModel):
    event_name: str
    date: datetime
    start_time: datetime
    end_time: datetime
    meeting_link: Optional[str] = None

class SessionUpdate(BaseModel):
    event_name: Optional[str] = None
    date: Optional[datetime] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    meeting_link: Optional[str] = None

class CounselorInfo(BaseModel):
    id: int
    name: str
    email: Optional[str] = None
    profile_image: Optional[str] = None

    class Config:
        from_attributes = True

class SessionResponse(BaseModel):
    id: int
    event_name: str
    date: datetime
    start_time: datetime
    end_time: datetime
    student_id: Optional[int] = None  # Can be None for deleted students
    counselor_id: int
    student_user_id: Optional[int] = None  # Student's user ID
    student_name: Optional[str] = None  # Student's full name
    meeting_link: Optional[str]
    status: str = "upcoming"
    payment: Optional[PaymentResponse] = None
    payment_status: Optional[str] = "unpaid"  # Payment status field
    payout_status: Optional[str] = "pending"  # Counselor payout status
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SessionListResponse(BaseModel):
    total: int
    items: List[SessionResponse]


class SessionNotesCreate(BaseModel):
    notes: str = Field(..., min_length=1)

class SessionNotesResponse(BaseModel):
    id: int
    session_id: int
    notes: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True



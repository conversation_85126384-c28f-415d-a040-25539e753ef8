"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@components/ui/dialog";
import { Button } from "@components/ui/button";
import { Textarea } from "@components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@components/ui/avatar";
import { useWebSocketChat } from "@/app/hooks/useWebSocketChat";
import { useProfile } from "@/app/hooks/student/useProfile";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { generatePlaceholder } from "@/app/utils/image";

const messageSchema = z.object({
  message: z
    .string()
    .min(1, "Message is required")
    .max(500, "Message cannot exceed 500 characters"),
});

type MessageFormData = z.infer<typeof messageSchema>;

interface CounselorInfo {
  user_id: number | null;
  first_name: string;
  last_name: string;
  profile_picture_url: string | null;
}

interface SendMessageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  counselorInfo: CounselorInfo;
}

export function SendMessageDialog({
  open,
  onOpenChange,
  counselorInfo,
}: SendMessageDialogProps) {
  const [isSending, setIsSending] = useState(false);
  const router = useRouter();
  const { userInfo } = useProfile();
  const { sendInitialMessage } = useWebSocketChat();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
  });

  const onSubmit = async (data: MessageFormData) => {
    if (!userInfo || !counselorInfo?.user_id) return;

    setIsSending(true);
    try {
      await sendInitialMessage({
        message: data.message,
        recipient_id: counselorInfo.user_id,
      });

      toast.success("Message sent successfully!");
      reset();
      onOpenChange(false);
      router.push("/student/dashboard/messages");
    } catch (error: any) {
      if (
        error?.response?.data?.detail?.includes(
          "between a counselor and a student"
        )
      ) {
        toast.error("You cannot send any messages to this person.");
      } else {
        toast.error("Failed to send message. Please try again.");
      }
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            Send message to:
          </DialogTitle>
        </DialogHeader>
        <div className="flex items-center space-x-4 mt-4">
          <Avatar className="w-16 h-16">
            <AvatarImage
              src={
                counselorInfo?.profile_picture_url ||
                generatePlaceholder(
                  counselorInfo?.first_name,
                  counselorInfo?.last_name
                )
              }
              alt={counselorInfo?.first_name || ""}
            />
            <AvatarFallback>{counselorInfo?.first_name || ""}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <h3 className="text-xl font-semibold">
              {counselorInfo
                ? `${counselorInfo.first_name} ${counselorInfo.last_name}`
                : "loading..."}
            </h3>
          </div>
        </div>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mt-4">
          <div>
            <Textarea
              {...register("message")}
              placeholder="Type your message here..."
              className="min-h-[100px]"
            />
            {errors.message && (
              <p className="text-red-500 text-sm mt-1">
                {errors.message.message}
              </p>
            )}
          </div>
          <Button
            type="submit"
            className="w-full bg-blue-800 hover:bg-blue-800/90"
            disabled={isSending}
          >
            {isSending ? "Sending..." : "Send Message"}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}

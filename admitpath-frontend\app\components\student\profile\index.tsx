"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useProfile } from "@/app/hooks/student/useProfile";
import { useAuth } from "@/app/hooks/useAuth";
import { ProfileHeader } from "./profile-header";
import { GoalsBlock } from "./goals";
import { ProfileInfoGrid } from "./profile-info";
import { EducationBlock } from "./education";
import { ProfileEditor } from "./edit-mode";
import { DeleteAccountDialog } from "@/app/components/common/delete-account-dialog";
import { toast } from "react-toastify";
import apiClient from "@/lib/apiClient";

type EditModeState = {
  isEditing: boolean;
  initialTab: "personal" | "educational";
};

export default function Profile() {
  const router = useRouter();
  const { logout } = useAuth();
  const {
    loading,
    userInfo,
    personalInfo,
    educationInfo,
    fetchUserInfo,
    fetchPersonalInfo,
    fetchEducationalBackground,
  } = useProfile();

  const [editMode, setEditMode] = useState<EditModeState>({
    isEditing: false,
    initialTab: "personal",
  });

  const [deleteDialog, setDeleteDialog] = useState({
    isOpen: false,
    isLoading: false,
    errorMessage: "",
    cannotDeleteReason: null as any,
  });

  useEffect(() => {
    fetchUserInfo();
    fetchPersonalInfo();
    fetchEducationalBackground();
  }, [fetchUserInfo, fetchPersonalInfo, fetchEducationalBackground]);

  const handleEditClick = (section: "personal" | "educational") => {
    setEditMode({
      isEditing: true,
      initialTab: section,
    });
  };

  const handleDeleteAccount = () => {
    setDeleteDialog({
      isOpen: true,
      isLoading: false,
      errorMessage: "",
      cannotDeleteReason: null,
    });
  };

  const handleDeleteConfirm = async () => {
    setDeleteDialog((prev) => ({ ...prev, isLoading: true, errorMessage: "" }));

    try {
      await apiClient.delete("/user/delete-account");

      // Success - logout and redirect
      toast.success("Account deleted successfully");
      logout();
      router.push("/auth/signup");
    } catch (error: any) {
      console.error("Delete account error:", error);

      if (error.response?.status === 400) {
        // Cannot delete account - show reason
        const errorDetail = error.response.data.detail;
        setDeleteDialog((prev) => ({
          ...prev,
          isLoading: false,
          cannotDeleteReason: errorDetail,
        }));
      } else {
        // Other error
        setDeleteDialog((prev) => ({
          ...prev,
          isLoading: false,
          errorMessage:
            error.response?.data?.detail ||
            "Failed to delete account. Please try again.",
        }));
      }
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialog({
      isOpen: false,
      isLoading: false,
      errorMessage: "",
      cannotDeleteReason: null,
    });
  };

  return (
    <div className="w-full">
      <div className="max-w-7xl mx-auto bg-white rounded-xl p-4 sm:p-6 lg:p-8 flex flex-col gap-y-4 lg:gap-y-6">
        {editMode.isEditing ? (
          <ProfileEditor
            initialTab={editMode.initialTab}
            onClose={() =>
              setEditMode({ isEditing: false, initialTab: "personal" })
            }
          />
        ) : (
          <>
            <ProfileHeader
              userInfo={userInfo}
              educationInfo={educationInfo}
              onEditClick={() => handleEditClick("personal")}
              onDeleteAccount={handleDeleteAccount}
            />
            <ProfileInfoGrid
              userInfo={userInfo}
              personalInfo={personalInfo}
              loading={loading}
            />
            <GoalsBlock
              loading={loading}
              personalInfo={personalInfo}
              onEditClick={() => handleEditClick("personal")}
            />
            <EducationBlock
              educationInfo={educationInfo}
              onEditClick={() => handleEditClick("educational")}
            />
          </>
        )}
      </div>

      <DeleteAccountDialog
        isOpen={deleteDialog.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        isLoading={deleteDialog.isLoading}
        userType="student"
        errorMessage={deleteDialog.errorMessage}
        cannotDeleteReason={deleteDialog.cannotDeleteReason}
      />
    </div>
  );
}

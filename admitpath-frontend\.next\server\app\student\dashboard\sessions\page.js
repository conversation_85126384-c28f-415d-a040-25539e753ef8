const CHUNK_PUBLIC_PATH = "server/app/student/dashboard/sessions/page.js";
const runtime = require("../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_97f32f._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__592060._.js");
runtime.loadChunk("server/chunks/ssr/app_db13a2._.js");
runtime.loadChunk("server/chunks/ssr/app_layout_tsx_ab3da7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ce97a5._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_b4e556.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_d758e6.js");
runtime.loadChunk("server/chunks/ssr/app_student_layout_tsx_260ce4._.js");
runtime.loadChunk("server/chunks/ssr/app_student_dashboard_layout_tsx_082d41._.js");
runtime.loadChunk("server/chunks/ssr/_7b874e._.js");
runtime.loadChunk("server/chunks/ssr/_e1c9d8._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/student/dashboard/sessions/page/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/student/dashboard/sessions/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico [app-rsc] (static)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/student/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/student/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/student/dashboard/sessions/loading.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/app/student/dashboard/sessions/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;

export interface Session {
  id: number;
  event_name: string;
  date: string;
  start_time: string;
  end_time: string;
  student_id: number | null;
  student_user_id: number | null;
  student_name: string | null;
  counselor_id: number;
  meeting_link: string;
  status: "upcoming" | "completed" | "cancelled";
  payment_status: "paid" | "unpaid" | "failed";
  payment: {
    id: number;
    amount: number;
    status: "pending" | "completed" | "failed";
    created_at: string;
    updated_at: string;
  };
  created_at: string;
  updated_at: string;
}

export interface CreateSession {
  session: {
    event_name: string;
    date: string;
    start_time: string;
    end_time: string;
    // meeting_link: string;
  };
  payment: {
    amount: number;
  };
}

export interface UpdateSession {
  event_name?: string; // Made optional
  date: string;
  start_time: string;
  end_time: string;
}

export interface SessionsResponse {
  total: number;
  items: Session[];
}

// export interface SessionNote {
//   id: number;
//   session_id: number;
//   student_id: number;
//   student_notes: string;
//   counselor_notes: string;
//   created_at: string;
//   updated_at: string;
// }

export interface SessionNotesData {
  id: number;
  session_id: number;
  notes: string;
  created_at: string;
  updated_at: string;
}

export interface NotesPayload {
  notes: string;
}

export interface Feedback {
  average_rating: number;
  total_reviews: number;
  reviews: Review[];
}

interface Review {
  id: number;
  session_id: number;
  student_id: number;
  student_profile_picture: string | null;
  student_name: string;
  rating: number;
  received_expected_service: boolean;
  comment: string;
  additional_feedback: string;
  created_at: string;
}

export interface CreateSessionResponse {
  success: boolean;
  data?: Session;
  error?: string;
}

export interface SessionsState {
  loading: boolean;
  error: string | null;
  sessions: SessionsResponse | null;
  currentSession: Session | null;
  currentNotes: SessionNotesData | null;
  studentsFeedback: Feedback | null;
  fetchSessions: (
    timezone: string,
    params?: {
      status?: string;
      from_date?: string;
      to_date?: string;
      payment_status?: "all" | "paid" | "unpaid";
    }
  ) => Promise<void>;
  createSession: (data: CreateSession) => Promise<CreateSessionResponse>;
  getSingleSession: (sessionId: number, timezone: string) => Promise<void>;
  updateSession: (
    sessionId: number,
    data: UpdateSession,
    timezone: string
  ) => Promise<void>;
  deleteSession: (sessionId: number) => Promise<void>;
  fetchSessionNotes: (sessionId: number) => Promise<void>;
  createSessionNotes: (sessionId: number, data: NotesPayload) => Promise<void>;
  updateSessionNotes: (sessionId: number, data: NotesPayload) => Promise<void>;
  deleteSessionNotes: (sessionId: number) => Promise<void>;
  getStudentsFeedback: (counselorId: number) => Promise<void>;
  clearError: () => void;
}

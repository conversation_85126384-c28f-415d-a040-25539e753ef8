from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from .payment import PaymentCreate, PaymentResponse
from enum import Enum
from fastapi.encoders import jsonable_encoder

# Enum for session types
class SessionType(str, Enum):
    REGULAR = "regular"
    FREE = "free"


# Response schemas
class CounselorServiceInfo(BaseModel):
    id: int
    service_type: str
    description: Optional[str]
    price: float

    class Config:
        from_attributes = True

class StudentSessionResponse(BaseModel):
    id: int
    counselor_id: Optional[int] = None  # Can be None for deleted counselors
    counselor_user_id: Optional[int] = None
    student_id: int
    event_name: str
    date: datetime
    start_time: datetime
    end_time: datetime
    status: str
    description: Optional[str] = None
    payment: Optional[PaymentResponse] = None
    created_at: datetime
    updated_at: datetime
    meeting_link: Optional[str] = None
    counselor_name: Optional[str] = None
    counselor_profile_picture: Optional[str] = None

    class Config:
        from_attributes = True

class StudentSessionListResponse(BaseModel):
    total: int
    items: list[StudentSessionResponse]

# In your student_session.py (schemas)
from enum import Enum

class ServiceType(str, Enum):
    UNIVERSITY_SHORTLISTING = "University Shortlisting"
    ESSAY_REVIEW = "Essay Review"
    INTERVIEW_PREP = "Interview Preparation"
    CAREER_GUIDANCE = "Career Guidance"
    SUBJECT_SELECTION = "Subject Selection"
    APPLICATION_REVIEW = "Application Review"
    GENERAL_COUNSELING = "General Counseling"
    FREE_INTRO_CALL = "Free 15 Minutes Intro Call"
    # Don't use the OTHER enum value directly - instead validate separately to support custom types

class StudentSessionCreate(BaseModel):
    service_type: str
    description: Optional[str] = None
    date: datetime
    start_time: datetime
    end_time: datetime

class StudentSessionWithPaymentCreate(BaseModel):
    session: StudentSessionCreate
    payment: Optional[PaymentCreate] = None




# Specific response for session creation that excludes meeting_link
class CreateSessionResponse(BaseModel):
    id: int
    counselor_id: int
    student_id: int
    event_name: str
    date: datetime
    start_time: datetime
    end_time: datetime
    status: str
    description: Optional[str] = None
    payment: Optional[PaymentResponse] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

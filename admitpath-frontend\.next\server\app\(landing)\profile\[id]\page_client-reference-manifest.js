globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/(landing)/profile/[id]/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/app/components/ui/StarBackground.tsx":{"id":"[project]/app/components/ui/StarBackground.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/app-dir/link.js":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/app/layout.tsx <module evaluation>":{"id":"[project]/app/layout.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/image-component.js":{"id":"[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/app/components/public/Navbar.tsx":{"id":"[project]/app/components/public/Navbar.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/app-dir/link.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/app/(landing)/profile/[id]/page.tsx":{"id":"[project]/app/(landing)/profile/[id]/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js","static/chunks/_add942._.js","static/chunks/node_modules_e49a7d._.js","static/chunks/app_(landing)_profile_[id]_page_tsx_26f04e._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/app-router.js":{"id":"[project]/node_modules/next/dist/client/components/app-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js":{"id":"[project]/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/app-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/app-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js <module evaluation>":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/app/(landing)/profile/[id]/page.tsx <module evaluation>":{"id":"[project]/app/(landing)/profile/[id]/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js","static/chunks/_add942._.js","static/chunks/node_modules_e49a7d._.js","static/chunks/app_(landing)_profile_[id]_page_tsx_26f04e._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false},"[project]/app/components/public/Navbar.tsx <module evaluation>":{"id":"[project]/app/components/public/Navbar.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"async":false},"[project]/app/components/ui/StarBackground.tsx <module evaluation>":{"id":"[project]/app/components/ui/StarBackground.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"async":false},"[project]/app/layout.tsx":{"id":"[project]/app/layout.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/image-component.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"async":false},"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"async":false}},"ssrModuleMapping":{},"edgeSSRModuleMapping":{"[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/image-component.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/app/components/public/Navbar.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/public/Navbar.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/app/layout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/layout.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/error-boundary.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/app/components/ui/StarBackground.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/ui/StarBackground.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/app/(landing)/profile/[id]/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/(landing)/profile/[id]/page.tsx [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/app-dir/link.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/app-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/app-router.js [app-edge-ssr] (ecmascript)","name":"*","chunks":[],"async":false}}},"rscModuleMapping":{},"edgeRscModuleMapping":{"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/app/(landing)/profile/[id]/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/(landing)/profile/[id]/page.tsx (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/error-boundary.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/app-dir/link.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/dev-root-http-access-fallback-boundary.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/app-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/app-router.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/app/components/ui/StarBackground.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/ui/StarBackground.tsx (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/app/components/public/Navbar.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/public/Navbar.tsx (client proxy)","name":"*","chunks":[],"async":false}},"[project]/app/layout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/layout.tsx (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/image-component.js (client proxy)","name":"*","chunks":[],"async":false}},"[project]/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js (client proxy)","name":"*","chunks":[],"async":false}}},"entryCSSFiles":{"[project]/app/(landing)/profile/[id]/page":[{"path":"static/chunks/[root of the server]__f9c7b3._.css","inlined":false}],"[project]/app/(landing)/layout":[{"path":"static/chunks/[root of the server]__f9c7b3._.css","inlined":false}],"[project]/app/layout":[{"path":"static/chunks/[root of the server]__f9c7b3._.css","inlined":false}],"[project]/app/favicon.ico":[]},"entryJSFiles":{"[project]/app/(landing)/profile/[id]/page":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js","static/chunks/_add942._.js","static/chunks/node_modules_e49a7d._.js","static/chunks/app_(landing)_profile_[id]_page_tsx_26f04e._.js"],"[project]/app/favicon.ico":["static/chunks/_468cce._.js","static/chunks/app_favicon_ico_mjs_5426c6._.js"],"[project]/app/(landing)/layout":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js","static/chunks/_f2e300._.js","static/chunks/node_modules_23c2ad._.js","static/chunks/app_(landing)_layout_tsx_71e84a._.js"],"[project]/app/layout":["static/chunks/[root of the server]__8bc6ed._.js","static/chunks/app_layout_tsx_70f0b1._.js"]}}

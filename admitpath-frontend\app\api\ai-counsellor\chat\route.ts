import { NextRequest } from "next/server";
import Groq from "groq-sdk";

export const runtime = "edge";

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { message, conversationHistory = [] } = body;

    if (!process.env.GROQ_API_KEY) {
      throw new Error("GROQ_API_KEY is not configured");
    }

    if (!message?.trim()) {
      throw new Error("Message cannot be empty");
    }

    // Create a comprehensive prompt for the AI counsellor chat
    const systemPrompt = `You are an experienced academic counsellor and educational consultant specializing in university admissions, career guidance, and academic planning. You are having a real-time text conversation with a student.

Your role is to:
- Provide helpful, encouraging, and practical advice
- Ask clarifying questions when needed to better understand the student's situation
- Offer specific, actionable guidance for university admissions, career planning, and academic success
- Be supportive and understanding of student concerns and anxieties
- Share relevant insights about different universities, programs, and career paths
- Help students identify their strengths and areas for improvement
- Provide guidance on application strategies, essays, interviews, and standardized tests

Communication style:
- Be conversational and approachable, like a friendly mentor
- Keep responses concise but informative (aim for 2-4 paragraphs)
- Use encouraging language and acknowledge student concerns
- Ask follow-up questions to continue the conversation naturally
- Provide specific examples when helpful

Remember: You're having a chat conversation, so be more conversational and interactive than in formal counselling sessions.`;

    // Build conversation messages
    const messages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      ...conversationHistory.map((msg: any) => ({
        role: msg.role as "user" | "assistant",
        content: msg.content,
      })),
      {
        role: "user" as const,
        content: message,
      },
    ];

    // Create streaming completion
    const stream = await groq.chat.completions.create({
      messages,
      model: "meta-llama/llama-4-maverick-17b-128e-instruct",
      temperature: 0.7,
      max_tokens: 1000,
      top_p: 0.9,
      stream: true,
    });

    // Create a readable stream for the response
    const encoder = new TextEncoder();
    const readable = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || "";
            if (content) {
              const data = `data: ${JSON.stringify({ content })}\n\n`;
              controller.enqueue(encoder.encode(data));
            }
          }

          // Send end signal
          const endData = `data: ${JSON.stringify({ done: true })}\n\n`;
          controller.enqueue(encoder.encode(endData));
          controller.close();
        } catch (error) {
          console.error("Streaming error:", error);
          const errorData = `data: ${JSON.stringify({
            error: "Stream error occurred",
          })}\n\n`;
          controller.enqueue(encoder.encode(errorData));
          controller.close();
        }
      },
    });

    return new Response(readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error: any) {
    console.error("Error in AI chat endpoint:", error);

    const encoder = new TextEncoder();
    const errorStream = new ReadableStream({
      start(controller) {
        const errorData = `data: ${JSON.stringify({
          error: error.message || "Failed to generate AI response",
        })}\n\n`;
        controller.enqueue(encoder.encode(errorData));
        controller.close();
      },
    });

    return new Response(errorStream, {
      status: 500,
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  }
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/skeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nexport const ProfileHeaderSkeleton = () => {\r\n  return (\r\n    <div className=\"flex flex-col sm:flex-row items-start justify-between gap-4 sm:gap-6\">\r\n      <div className=\"flex flex-row items-center gap-4 sm:gap-6\">\r\n        <div className=\"w-32 h-32 sm:w-[140px] sm:h-[140px] bg-gray-200 rounded-full\" />\r\n        <div className=\"space-y-2 sm:space-y-3 text-left\">\r\n          <div className=\"h-8 w-48 bg-gray-200 rounded-lg\" />\r\n          <div className=\"h-4 w-32 bg-gray-200 rounded-lg\" />\r\n        </div>\r\n      </div>\r\n      <div className=\"h-10 w-32 bg-gray-200 rounded-xl\" />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const ProfileInfoGridSkeleton = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 border rounded-xl p-3\">\r\n      {[...Array(4)].map((_, index) => (\r\n        <div\r\n          key={index}\r\n          className=\"flex items-center gap-3 text-gray-600 p-4 rounded-lg\"\r\n        >\r\n          <div className=\"rounded-lg border border-gray-200 bg-gray-200 p-2 my-1 w-10 h-10\" />\r\n          <div className=\"flex flex-col justify-between min-w-0 space-y-2\">\r\n            <div className=\"h-5 w-24 bg-gray-200 rounded-lg\" />\r\n            <div className=\"h-4 w-20 bg-gray-200 rounded-lg\" />\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const GoalsSkeleton = () => {\r\n  return (\r\n    <div className=\"space-y-3 rounded-xl border p-2.5 sm:p-4 lg:p-6\">\r\n      <div className=\"h-6 w-20 bg-gray-200 rounded-lg mb-4\" />\r\n      <div className=\"space-y-2\">\r\n        <div className=\"h-4 w-full bg-gray-200 rounded-lg\" />\r\n        <div className=\"h-4 w-3/4 bg-gray-200 rounded-lg\" />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const EducationSkeleton = () => {\r\n  return (\r\n    <div className=\"space-y-3 rounded-xl border p-2.5 sm:p-4 lg:p-6\">\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <div className=\"h-6 w-24 bg-gray-200 rounded-lg\" />\r\n        <div className=\"h-10 w-10 bg-gray-200 rounded-lg\" />\r\n      </div>\r\n      {[...Array(2)].map((_, index) => (\r\n        <div key={index} className=\"flex gap-4 pb-6 border-b last:border-0\">\r\n          <div className=\"w-16 h-16 bg-gray-200 rounded-lg\" />\r\n          <div className=\"flex-1 space-y-2\">\r\n            <div className=\"h-5 w-48 bg-gray-200 rounded-lg\" />\r\n            <div className=\"h-4 w-32 bg-gray-200 rounded-lg\" />\r\n            <div className=\"h-4 w-40 bg-gray-200 rounded-lg\" />\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,wBAAwB;IACnC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAGnB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;AAEO,MAAM,0BAA0B;IACrC,qBACE,8OAAC;QAAI,WAAU;kBACZ;eAAI,MAAM;SAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;gBAEC,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;eANZ;;;;;;;;;;AAYf;AAEO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;AAEO,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;YAEhB;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;oBAAgB,WAAU;;sCACzB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;mBALT;;;;;;;;;;;AAWlB"}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/profile-menu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faEllipsisV, faTrash } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\ninterface ProfileMenuProps {\r\n  onDeleteAccount: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport const ProfileMenu: React.FC<ProfileMenuProps> = ({\r\n  onDeleteAccount,\r\n  isLoading = false,\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  const toggleMenu = () => setIsOpen((prev) => !prev);\r\n\r\n  const handleClickOutside = (event: MouseEvent) => {\r\n    if (\r\n      dropdownRef.current &&\r\n      !dropdownRef.current.contains(event.target as Node)\r\n    ) {\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"relative\" ref={dropdownRef}>\r\n      <button\r\n        onClick={toggleMenu}\r\n        className={`p-2 rounded-lg hover:bg-gray-100 transition-colors ${\r\n          isLoading ? \"opacity-50 cursor-wait\" : \"\"\r\n        }`}\r\n        disabled={isLoading}\r\n        aria-label=\"Profile menu\"\r\n      >\r\n        <FontAwesomeIcon\r\n          icon={faEllipsisV}\r\n          className={`w-4 h-4 text-gray-600 ${\r\n            isLoading ? \"animate-pulse\" : \"\"\r\n          }`}\r\n        />\r\n      </button>\r\n\r\n      {isOpen && (\r\n        <div className=\"absolute top-full right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-2 w-48 z-50\">\r\n          <button\r\n            className=\"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors\"\r\n            onClick={() => {\r\n              onDeleteAccount();\r\n              setIsOpen(false);\r\n            }}\r\n          >\r\n            <FontAwesomeIcon\r\n              icon={faTrash}\r\n              className=\"w-4 h-4 mr-3 text-red-500\"\r\n            />\r\n            Delete Account\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,MAAM,cAA0C,CAAC,EACtD,eAAe,EACf,YAAY,KAAK,EAClB;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,aAAa,IAAM,UAAU,CAAC,OAAS,CAAC;IAE9C,MAAM,qBAAqB,CAAC;QAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;YACA,UAAU;QACZ;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,8OAAC;gBACC,SAAS;gBACT,WAAW,CAAC,mDAAmD,EAC7D,YAAY,2BAA2B,IACvC;gBACF,UAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,oKAAA,CAAA,kBAAe;oBACd,MAAM,wKAAA,CAAA,cAAW;oBACjB,WAAW,CAAC,sBAAsB,EAChC,YAAY,kBAAkB,IAC9B;;;;;;;;;;;YAIL,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;wBACP;wBACA,UAAU;oBACZ;;sCAEA,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,UAAO;4BACb,WAAU;;;;;;wBACV;;;;;;;;;;;;;;;;;;AAOd"}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/profile-header.tsx"], "sourcesContent": ["import {\r\n  EducationInfo,\r\n  humanizeValue,\r\n  UserInfo,\r\n} from \"@/app/types/student/profile\";\r\nimport Image from \"next/image\";\r\nimport { ProfileHeaderSkeleton } from \"./skeleton\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { ProfileMenu } from \"@/app/components/common/profile-menu\";\r\n\r\nexport const ProfileHeader = ({\r\n  userInfo,\r\n  educationInfo,\r\n  onEditClick,\r\n  onDeleteAccount,\r\n}: {\r\n  userInfo: UserInfo | null;\r\n  educationInfo: EducationInfo[] | null;\r\n  onEditClick: () => void;\r\n  onDeleteAccount: () => void;\r\n}) => {\r\n  return !userInfo || !educationInfo ? (\r\n    <ProfileHeaderSkeleton />\r\n  ) : (\r\n    <div className=\"flex flex-col sm:flex-row items-start justify-between gap-4 sm:gap-6\">\r\n      <div className=\"flex flex-row items-center gap-4 sm:gap-6\">\r\n        <div className=\"relative\">\r\n          <Image\r\n            src={\r\n              userInfo.profile_picture_url ||\r\n              generatePlaceholder(userInfo.firstName, userInfo.lastName)\r\n            }\r\n            alt={`${userInfo.firstName} ${userInfo.lastName}`}\r\n            width={140}\r\n            height={140}\r\n            className=\"rounded-full border-4 w-32 h-32 sm:w-[140px] sm:h-[140px]\"\r\n          />\r\n          <div className=\"absolute bottom-2 right-2 w-6 h-6 sm:w-8 sm:h-8 bg-green-500 rounded-full border-4 border-white\" />\r\n        </div>\r\n        <div className=\"space-y-2 sm:space-y-3 text-left\">\r\n          <h1 className=\"text-xl sm:text-2xl font-semibold\">\r\n            {userInfo.firstName} {userInfo.lastName}\r\n          </h1>\r\n          <div className=\"text-gray-600 text-sm sm:text-base\">\r\n            {educationInfo[0]?.institution_name || \"No institution added\"}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex items-center gap-3\">\r\n        <button\r\n          onClick={onEditClick}\r\n          className=\"p-3 text-gray-700 text-base sm:text-lg font-semibold hover:bg-gray-200 rounded-xl bg-gray-100 transition-all whitespace-nowrap\"\r\n        >\r\n          Edit Profile\r\n        </button>\r\n        <ProfileMenu\r\n          onDeleteAccount={onDeleteAccount}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAKA;AACA;AACA;AACA;;;;;;AAEO,MAAM,gBAAgB,CAAC,EAC5B,QAAQ,EACR,aAAa,EACb,WAAW,EACX,eAAe,EAMhB;IACC,OAAO,CAAC,YAAY,CAAC,8BACnB,8OAAC,oJAAA,CAAA,wBAAqB;;;;6BAEtB,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KACE,SAAS,mBAAmB,IAC5B,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,SAAS,EAAE,SAAS,QAAQ;gCAE3D,KAAK,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;gCACjD,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCACX,SAAS,SAAS;oCAAC;oCAAE,SAAS,QAAQ;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;0CACZ,aAAa,CAAC,EAAE,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,8OAAC,+IAAA,CAAA,cAAW;wBACV,iBAAiB;;;;;;;;;;;;;;;;;;AAK3B"}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/goals.tsx"], "sourcesContent": ["import { PersonalInfo } from \"@/app/types/student/profile\";\r\nimport { GoalsSkeleton } from \"./skeleton\";\r\nimport { Pencil } from \"lucide-react\";\r\n\r\nexport const GoalsBlock = ({\r\n  loading,\r\n  personalInfo,\r\n  onEditClick,\r\n}: {\r\n  loading: boolean;\r\n  personalInfo: PersonalInfo | null;\r\n  onEditClick: () => void;\r\n}) => {\r\n  return loading ? (\r\n    <GoalsSkeleton />\r\n  ) : (\r\n    <div className=\"space-y-3 sm:space-y-4 rounded-xl border p-2.5 sm:p-4 lg:p-6 \">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h2 className=\"text-base sm:text-xl font-semibold\">Goals</h2>\r\n        <button\r\n          onClick={onEditClick}\r\n          className=\"p-3 text-gray-700 text-base sm:text-lg font-semibold hover:bg-gray-200 rounded-xl bg-gray-100 transition-all whitespace-nowrap\"\r\n        >\r\n          <Pencil className=\"w-5 h-5 sm:w-6 sm:h-6\" />\r\n        </button>\r\n      </div>\r\n      <p className=\"text-gray-600 leading-relaxed text-sm sm:text-base\">\r\n        {personalInfo?.goals || \"No Goals provided\"}\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEO,MAAM,aAAa,CAAC,EACzB,OAAO,EACP,YAAY,EACZ,WAAW,EAKZ;IACC,OAAO,wBACL,8OAAC,oJAAA,CAAA,gBAAa;;;;6BAEd,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGtB,8OAAC;gBAAE,WAAU;0BACV,cAAc,SAAS;;;;;;;;;;;;AAIhC"}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/types/student/profile.ts"], "sourcesContent": ["export interface PersonalInfo {\r\n  nationality: string;\r\n  country_of_residence: string;\r\n  gender: string;\r\n  date_of_birth: string | null;\r\n  first_name?: string;\r\n  last_name?: string;\r\n  goals?: string;\r\n}\r\n\r\nexport interface EducationInfo {\r\n  id?: number;\r\n  student_id?: number;\r\n  current_education_level:\r\n    | \"\"\r\n    | \"high_school\"\r\n    | \"bachelors\"\r\n    | \"masters\"\r\n    | \"phd\"\r\n    | \"other\";\r\n  institution_name: string;\r\n  institution_type: \"school\" | \"university\" | \"other\";\r\n  start_date: string;\r\n  end_date?: string; // Make it optional\r\n  is_current: boolean;\r\n  created_at?: string;\r\n  updated_at?: string;\r\n}\r\n\r\nexport interface NewEducationInfo\r\n  extends Omit<\r\n    EducationInfo,\r\n    \"id\" | \"student_id\" | \"created_at\" | \"updated_at\"\r\n  > {}\r\n\r\n// Response type\r\nexport interface EducationResponse extends EducationInfo {\r\n  id: number;\r\n  student_id: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface PersonalInfoResponse extends PersonalInfo {\r\n  id: number;\r\n  student_id: number;\r\n}\r\n\r\nexport interface ServiceInfo {\r\n  selected_services: string[];\r\n  additional_details: string;\r\n}\r\n\r\nexport interface UserInfo {\r\n  user_id: number;\r\n  student_id: number | null;\r\n  counselor_id: number | null;\r\n\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  userType: string;\r\n  isProfileComplete: boolean;\r\n  profile_picture_url: string | null;\r\n}\r\n\r\nexport interface ProfileState {\r\n  loading: boolean;\r\n  error: string | null;\r\n  currentStep: number | null;\r\n  userInfo: UserInfo | null;\r\n  personalInfo: PersonalInfo | null;\r\n  educationInfo: EducationInfo[] | null;\r\n  servicesInfo: ServiceInfo | null;\r\n  setCurrentStep: (step: number) => void;\r\n  fetchUserInfo: () => Promise<void>;\r\n  fetchPersonalInfo: () => Promise<void>;\r\n  fetchEducationalBackground: () => Promise<void>;\r\n  fetchServicesInfo: () => Promise<void>;\r\n  figureCurrentStep: () => Promise<void>;\r\n  submitPersonalInfo: (data: PersonalInfo) => Promise<void>;\r\n  submitEducationInfo: (data: EducationInfo[]) => any;\r\n  updateEducationInfo: (\r\n    id: number,\r\n    data: Partial<EducationInfo>\r\n  ) => Promise<void>;\r\n  deleteEducationInfo: (id: number) => Promise<void>;\r\n  submitExpectedServices: (data: ServiceInfo) => Promise<void>;\r\n  updateprofile_picture_url: (url: string | null) => void;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const educationLevelMap = {\r\n  high_school: \"High School\",\r\n  bachelors: \"Bachelor's Degree\",\r\n  masters: \"Master's Degree\",\r\n  phd: \"Ph.D.\",\r\n  other: \"Other\",\r\n} as const;\r\n\r\nexport const genderMap = {\r\n  male: \"Male\",\r\n  female: \"Female\",\r\n  other: \"Other\",\r\n} as const;\r\n\r\nexport type EducationLevel = keyof typeof educationLevelMap;\r\nexport type Gender = keyof typeof genderMap;\r\n\r\nexport const humanizeValue = (\r\n  value: string,\r\n  type: \"current_education_level\" | \"gender\"\r\n): string => {\r\n  const map =\r\n    type === \"current_education_level\" ? educationLevelMap : genderMap;\r\n  return map[value as keyof typeof map] || value;\r\n};\r\n"], "names": [], "mappings": ";;;;;AA4FO,MAAM,oBAAoB;IAC/B,aAAa;IACb,WAAW;IACX,SAAS;IACT,KAAK;IACL,OAAO;AACT;AAEO,MAAM,YAAY;IACvB,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAKO,MAAM,gBAAgB,CAC3B,OACA;IAEA,MAAM,MACJ,SAAS,4BAA4B,oBAAoB;IAC3D,OAAO,GAAG,CAAC,MAA0B,IAAI;AAC3C"}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/profile-info.tsx"], "sourcesContent": ["import {\r\n  humanizeValue,\r\n  PersonalInfo,\r\n  UserInfo,\r\n} from \"@/app/types/student/profile\";\r\nimport { format } from \"date-fns\";\r\nimport { Calendar, Mail, MapPin, UserIcon } from \"lucide-react\";\r\nimport { ProfileInfoGridSkeleton } from \"./skeleton\";\r\n\r\nexport const ProfileInfoGrid = ({\r\n  loading,\r\n  userInfo,\r\n  personalInfo,\r\n}: {\r\n  loading: boolean;\r\n  userInfo: UserInfo | null;\r\n  personalInfo: PersonalInfo | null;\r\n}) => {\r\n  const infoItems = [\r\n    {\r\n      icon: <Mail className=\"w-5 h-5\" />,\r\n      label: \"Email Address\",\r\n      value: userInfo?.email,\r\n    },\r\n    ...(personalInfo\r\n      ? [\r\n          {\r\n            icon: <MapPin className=\"w-5 h-5\" />,\r\n            label: \"Located in\",\r\n            value: personalInfo.country_of_residence,\r\n          },\r\n          {\r\n            icon: <Calendar className=\"w-5 h-5\" />,\r\n            label: \"Date of Birth\",\r\n            value: personalInfo.date_of_birth ? format(new Date(personalInfo.date_of_birth), \"PP\") : \"\",\r\n          },\r\n          {\r\n            icon: <UserIcon className=\"w-5 h-5\" />,\r\n            label: \"Gender\",\r\n            value: humanizeValue(personalInfo.gender, \"gender\"),\r\n          },\r\n        ]\r\n      : []),\r\n  ];\r\n\r\n  return loading || !userInfo ? (\r\n    <ProfileInfoGridSkeleton />\r\n  ) : (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 border rounded-xl p-3\">\r\n      {infoItems.map((item, index) => (\r\n        <div\r\n          key={index}\r\n          className=\"flex items-center gap-3 text-gray-600 p-4 rounded-lg\"\r\n        >\r\n          <button className=\"rounded-lg border border-gray-200 bg-gray-50 p-2 my-1 shrink-0\">\r\n            {item.icon}\r\n          </button>\r\n          <div className=\"flex flex-col justify-between min-w-0\">\r\n            <p className=\"text-md font-semibold text-black truncate\">\r\n              {item.value}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500\">{item.label}</p>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAOA;AADA;AAAA;AAAA;AADA;AACA;;;;;;AAGO,MAAM,kBAAkB,CAAC,EAC9B,OAAO,EACP,QAAQ,EACR,YAAY,EAKb;IACC,MAAM,YAAY;QAChB;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,OAAO,UAAU;QACnB;WACI,eACA;YACE;gBACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;gBACxB,OAAO;gBACP,OAAO,aAAa,oBAAoB;YAC1C;YACA;gBACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;gBACP,OAAO,aAAa,aAAa,GAAG,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,aAAa,GAAG,QAAQ;YAC3F;YACA;gBACE,oBAAM,8OAAC,sMAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAO;gBACP,OAAO,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,MAAM,EAAE;YAC5C;SACD,GACD,EAAE;KACP;IAED,OAAO,WAAW,CAAC,yBACjB,8OAAC,oJAAA,CAAA,0BAAuB;;;;6BAExB,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;gBAEC,WAAU;;kCAEV,8OAAC;wBAAO,WAAU;kCACf,KAAK,IAAI;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CACV,KAAK,KAAK;;;;;;0CAEb,8OAAC;gCAAE,WAAU;0CAAyB,KAAK,KAAK;;;;;;;;;;;;;eAV7C;;;;;;;;;;AAgBf"}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/education.tsx"], "sourcesContent": ["import { EducationInfo, humanizeValue } from \"@/app/types/student/profile\";\r\nimport { format } from \"date-fns\";\r\nimport { Pencil } from \"lucide-react\";\r\nimport { EducationSkeleton } from \"./skeleton\";\r\n\r\nexport const EducationBlock = ({\r\n  educationInfo,\r\n  onEditClick,\r\n}: {\r\n  educationInfo: EducationInfo[] | null;\r\n  onEditClick: () => void;\r\n}) => {\r\n  return !educationInfo ? (\r\n    <EducationSkeleton />\r\n  ) : (\r\n    <div className=\"space-y-3 sm:space-y-4 rounded-xl border p-2.5 sm:p-4 lg:p-6 \">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h2 className=\"text-base sm:text-xl font-semibold\">Education</h2>\r\n        <button \r\n          onClick={onEditClick}\r\n          className=\"p-3 text-gray-700 text-base sm:text-lg font-semibold hover:bg-gray-200 rounded-xl bg-gray-100 transition-all whitespace-nowrap\"\r\n        >\r\n          <Pencil className=\"w-5 h-5 sm:w-6 sm:h-6\" />\r\n        </button>\r\n      </div>\r\n      {educationInfo.map((education, index) => (\r\n        <div\r\n          key={index}\r\n          className=\"space-y-1 sm:space-y-2 flex-1 min-w-0 pb-4 sm:pb-6 border-b-2 last:border-b-0\"\r\n        >\r\n          <h3 className=\"font-medium text-base sm:text-lg break-words\">\r\n            {education.institution_name}\r\n          </h3>\r\n          <p className=\"text-gray-600 text-md break-words\">\r\n            {humanizeValue(\r\n              education.current_education_level,\r\n              \"current_education_level\"\r\n            )}\r\n          </p>\r\n          <p className=\"text-gray-500 text-md \">\r\n            {format(new Date(education.start_date), \"PP\")} -{\" \"}\r\n            {education.is_current\r\n              ? \"Present\"\r\n              : format(new Date(education.end_date!), \"PP\")}\r\n          </p>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AADA;AADA;;;;;;AAIO,MAAM,iBAAiB,CAAC,EAC7B,aAAa,EACb,WAAW,EAIZ;IACC,OAAO,CAAC,8BACN,8OAAC,oJAAA,CAAA,oBAAiB;;;;6BAElB,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAGrB,cAAc,GAAG,CAAC,CAAC,WAAW,sBAC7B,8OAAC;oBAEC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCACX,UAAU,gBAAgB;;;;;;sCAE7B,8OAAC;4BAAE,WAAU;sCACV,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD,EACX,UAAU,uBAAuB,EACjC;;;;;;sCAGJ,8OAAC;4BAAE,WAAU;;gCACV,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,UAAU,GAAG;gCAAM;gCAAG;gCAChD,UAAU,UAAU,GACjB,YACA,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,QAAQ,GAAI;;;;;;;;mBAhBvC;;;;;;;;;;;AAsBf"}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useProfilePicture.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useProfile } from \"./useProfile\";\r\n\r\ninterface ProfilePictureState {\r\n  loading: boolean;\r\n  error: string | null;\r\n  uploadProfilePicture: (file: File) => Promise<string>;\r\n  deleteProfilePicture: () => Promise<void>;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useProfilePicture = create<ProfilePictureState>((set) => {\r\n  // Get the updateprofile_picture_url function from useProfile\r\n  const { updateprofile_picture_url } = useProfile.getState();\r\n\r\n  return {\r\n    loading: false,\r\n    error: null,\r\n\r\n    uploadProfilePicture: async (file: File) => {\r\n      try {\r\n        set({ loading: true, error: null });\r\n        const formData = new FormData();\r\n        formData.append(\"file\", file);\r\n\r\n        const response = await apiClient.post(\r\n          \"/user/profile-picture\",\r\n          formData,\r\n          {\r\n            headers: {\r\n              \"Content-Type\": \"multipart/form-data\",\r\n            },\r\n          }\r\n        );\r\n\r\n        const profile_picture_url = response.data.profile_picture_url;\r\n        updateprofile_picture_url(profile_picture_url);\r\n\r\n        set({ loading: false });\r\n        toast.success(\"Profile picture uploaded successfully\");\r\n        return profile_picture_url;\r\n      } catch (error: any) {\r\n        const errorMessage =\r\n          error.response?.data?.detail || \"Failed to upload profile picture\";\r\n        set({ error: errorMessage, loading: false });\r\n        toast.error(errorMessage);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    deleteProfilePicture: async () => {\r\n      try {\r\n        set({ loading: true, error: null });\r\n        await apiClient.delete(\"/user/profile-picture\");\r\n        updateprofile_picture_url(null);\r\n        set({ loading: false });\r\n        toast.success(\"Profile picture deleted successfully\");\r\n      } catch (error: any) {\r\n        const errorMessage =\r\n          error.response?.data?.detail || \"Failed to delete profile picture\";\r\n        set({ error: errorMessage, loading: false });\r\n        toast.error(errorMessage);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    clearError: () => set({ error: null }),\r\n  };\r\n});\r\n"], "names": [], "mappings": ";;;AAGA;AACA;AACA;AAHA;AAFA;;;;;AAeO,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAuB,CAAC;IAC5D,6DAA6D;IAC7D,MAAM,EAAE,yBAAyB,EAAE,GAAG,qIAAA,CAAA,aAAU,CAAC,QAAQ;IAEzD,OAAO;QACL,SAAS;QACT,OAAO;QAEP,sBAAsB,OAAO;YAC3B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,yBACA,UACA;oBACE,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAGF,MAAM,sBAAsB,SAAS,IAAI,CAAC,mBAAmB;gBAC7D,0BAA0B;gBAE1B,IAAI;oBAAE,SAAS;gBAAM;gBACrB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,sBAAsB;YACpB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CAAC;gBACvB,0BAA0B;gBAC1B,IAAI;oBAAE,SAAS;gBAAM;gBACrB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC;AACF"}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,sMAAM,UAAU,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nCard.displayName = \"Card\";\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardHeader.displayName = \"CardHeader\";\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardTitle.displayName = \"CardTitle\";\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardDescription.displayName = \"CardDescription\";\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n));\r\nCardContent.displayName = \"CardContent\";\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardFooter.displayName = \"CardFooter\";\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardDescription,\r\n  CardContent,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n);\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n));\r\nLabel.displayName = LabelPrimitive.Root.displayName;\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAHA;AAHA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,sMAAM,UAAU,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAe,IAAI;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,kKAAe,IAAI,CAAC,WAAW"}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,mKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,sMAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,mKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,mKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,mKAAgB,MAAM;kBACrB,cAAA,8OAAC,mKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,mKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mKAAgB,aAAa;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,8OAAC,mKAAgB,QAAQ;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,mKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,sMAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG"}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1345, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/countries.ts"], "sourcesContent": ["export const countries = [\r\n  { name: \"Afghanistan\", code: \"AF\" },\r\n  { name: \"Åland Islands\", code: \"AX\" },\r\n  { name: \"Albania\", code: \"AL\" },\r\n  { name: \"Algeria\", code: \"DZ\" },\r\n  { name: \"American Samoa\", code: \"AS\" },\r\n  { name: \"Andorra\", code: \"AD\" },\r\n  { name: \"Angola\", code: \"AO\" },\r\n  { name: \"Ang<PERSON><PERSON>\", code: \"AI\" },\r\n  { name: \"Antarctica\", code: \"AQ\" },\r\n  { name: \"Antigua and Barbuda\", code: \"AG\" },\r\n  { name: \"Argentina\", code: \"AR\" },\r\n  { name: \"Armenia\", code: \"AM\" },\r\n  { name: \"Aruba\", code: \"AW\" },\r\n  { name: \"Australia\", code: \"AU\" },\r\n  { name: \"Austria\", code: \"AT\" },\r\n  { name: \"Azerbaijan\", code: \"AZ\" },\r\n  { name: \"Bahamas\", code: \"BS\" },\r\n  { name: \"Bahrain\", code: \"BH\" },\r\n  { name: \"Bangladesh\", code: \"BD\" },\r\n  { name: \"Barbados\", code: \"BB\" },\r\n  { name: \"Belarus\", code: \"BY\" },\r\n  { name: \"Belgium\", code: \"BE\" },\r\n  { name: \"Belize\", code: \"BZ\" },\r\n  { name: \"Benin\", code: \"BJ\" },\r\n  { name: \"Bermuda\", code: \"BM\" },\r\n  { name: \"Bhutan\", code: \"BT\" },\r\n  { name: \"Bolivia\", code: \"BO\" },\r\n  { name: \"Bosnia and Herzegovina\", code: \"BA\" },\r\n  { name: \"Botswana\", code: \"BW\" },\r\n  { name: \"Bouvet Island\", code: \"BV\" },\r\n  { name: \"Brazil\", code: \"BR\" },\r\n  { name: \"British Indian Ocean Territory\", code: \"IO\" },\r\n  { name: \"Brunei Darussalam\", code: \"BN\" },\r\n  { name: \"Bulgaria\", code: \"BG\" },\r\n  { name: \"Burkina Faso\", code: \"BF\" },\r\n  { name: \"Burundi\", code: \"BI\" },\r\n  { name: \"Cambodia\", code: \"KH\" },\r\n  { name: \"Cameroon\", code: \"CM\" },\r\n  { name: \"Canada\", code: \"CA\" },\r\n  { name: \"Cape Verde\", code: \"CV\" },\r\n  { name: \"Cayman Islands\", code: \"KY\" },\r\n  { name: \"Central African Republic\", code: \"CF\" },\r\n  { name: \"Chad\", code: \"TD\" },\r\n  { name: \"Chile\", code: \"CL\" },\r\n  { name: \"China\", code: \"CN\" },\r\n  { name: \"Christmas Island\", code: \"CX\" },\r\n  { name: \"Cocos (Keeling) Islands\", code: \"CC\" },\r\n  { name: \"Colombia\", code: \"CO\" },\r\n  { name: \"Comoros\", code: \"KM\" },\r\n  { name: \"Congo\", code: \"CG\" },\r\n  { name: \"Congo, The Democratic Republic of The\", code: \"CD\" },\r\n  { name: \"Cook Islands\", code: \"CK\" },\r\n  { name: \"Costa Rica\", code: \"CR\" },\r\n  { name: \"Cote D'ivoire\", code: \"CI\" },\r\n  { name: \"Croatia\", code: \"HR\" },\r\n  { name: \"Cuba\", code: \"CU\" },\r\n  { name: \"Cyprus\", code: \"CY\" },\r\n  { name: \"Czech Republic\", code: \"CZ\" },\r\n  { name: \"Denmark\", code: \"DK\" },\r\n  { name: \"Djibouti\", code: \"DJ\" },\r\n  { name: \"Dominica\", code: \"DM\" },\r\n  { name: \"Dominican Republic\", code: \"DO\" },\r\n  { name: \"Ecuador\", code: \"EC\" },\r\n  { name: \"Egypt\", code: \"EG\" },\r\n  { name: \"El Salvador\", code: \"SV\" },\r\n  { name: \"Equatorial Guinea\", code: \"GQ\" },\r\n  { name: \"Eritrea\", code: \"ER\" },\r\n  { name: \"Estonia\", code: \"EE\" },\r\n  { name: \"Ethiopia\", code: \"ET\" },\r\n  { name: \"Falkland Islands (Malvinas)\", code: \"FK\" },\r\n  { name: \"Faroe Islands\", code: \"FO\" },\r\n  { name: \"Fiji\", code: \"FJ\" },\r\n  { name: \"Finland\", code: \"FI\" },\r\n  { name: \"France\", code: \"FR\" },\r\n  { name: \"French Guiana\", code: \"GF\" },\r\n  { name: \"French Polynesia\", code: \"PF\" },\r\n  { name: \"French Southern Territories\", code: \"TF\" },\r\n  { name: \"Gabon\", code: \"GA\" },\r\n  { name: \"Gambia\", code: \"GM\" },\r\n  { name: \"Georgia\", code: \"GE\" },\r\n  { name: \"Germany\", code: \"DE\" },\r\n  { name: \"Ghana\", code: \"GH\" },\r\n  { name: \"Gibraltar\", code: \"GI\" },\r\n  { name: \"Greece\", code: \"GR\" },\r\n  { name: \"Greenland\", code: \"GL\" },\r\n  { name: \"Grenada\", code: \"GD\" },\r\n  { name: \"Guadeloupe\", code: \"GP\" },\r\n  { name: \"Guam\", code: \"GU\" },\r\n  { name: \"Guatemala\", code: \"GT\" },\r\n  { name: \"Guernsey\", code: \"GG\" },\r\n  { name: \"Guinea\", code: \"GN\" },\r\n  { name: \"Guinea-bissau\", code: \"GW\" },\r\n  { name: \"Guyana\", code: \"GY\" },\r\n  { name: \"Haiti\", code: \"HT\" },\r\n  { name: \"Heard Island and Mcdonald Islands\", code: \"HM\" },\r\n  { name: \"Holy See (Vatican City State)\", code: \"VA\" },\r\n  { name: \"Honduras\", code: \"HN\" },\r\n  { name: \"Hong Kong\", code: \"HK\" },\r\n  { name: \"Hungary\", code: \"HU\" },\r\n  { name: \"Iceland\", code: \"IS\" },\r\n  { name: \"India\", code: \"IN\" },\r\n  { name: \"Indonesia\", code: \"ID\" },\r\n  { name: \"Iran, Islamic Republic of\", code: \"IR\" },\r\n  { name: \"Iraq\", code: \"IQ\" },\r\n  { name: \"Ireland\", code: \"IE\" },\r\n  { name: \"Isle of Man\", code: \"IM\" },\r\n  { name: \"Israel\", code: \"IL\" },\r\n  { name: \"Italy\", code: \"IT\" },\r\n  { name: \"Jamaica\", code: \"JM\" },\r\n  { name: \"Japan\", code: \"JP\" },\r\n  { name: \"Jersey\", code: \"JE\" },\r\n  { name: \"Jordan\", code: \"JO\" },\r\n  { name: \"Kazakhstan\", code: \"KZ\" },\r\n  { name: \"Kenya\", code: \"KE\" },\r\n  { name: \"Kiribati\", code: \"KI\" },\r\n  { name: \"Korea, Democratic People's Republic of\", code: \"KP\" },\r\n  { name: \"Korea, Republic of\", code: \"KR\" },\r\n  { name: \"Kuwait\", code: \"KW\" },\r\n  { name: \"Kyrgyzstan\", code: \"KG\" },\r\n  { name: \"Lao People's Democratic Republic\", code: \"LA\" },\r\n  { name: \"Latvia\", code: \"LV\" },\r\n  { name: \"Lebanon\", code: \"LB\" },\r\n  { name: \"Lesotho\", code: \"LS\" },\r\n  { name: \"Liberia\", code: \"LR\" },\r\n  { name: \"Libyan Arab Jamahiriya\", code: \"LY\" },\r\n  { name: \"Liechtenstein\", code: \"LI\" },\r\n  { name: \"Lithuania\", code: \"LT\" },\r\n  { name: \"Luxembourg\", code: \"LU\" },\r\n  { name: \"Macao\", code: \"MO\" },\r\n  { name: \"Macedonia, The Former Yugoslav Republic of\", code: \"MK\" },\r\n  { name: \"Madagascar\", code: \"MG\" },\r\n  { name: \"Malawi\", code: \"MW\" },\r\n  { name: \"Malaysia\", code: \"MY\" },\r\n  { name: \"Maldives\", code: \"MV\" },\r\n  { name: \"Mali\", code: \"ML\" },\r\n  { name: \"Malta\", code: \"MT\" },\r\n  { name: \"Marshall Islands\", code: \"MH\" },\r\n  { name: \"Martinique\", code: \"MQ\" },\r\n  { name: \"Mauritania\", code: \"MR\" },\r\n  { name: \"Mauritius\", code: \"MU\" },\r\n  { name: \"Mayotte\", code: \"YT\" },\r\n  { name: \"Mexico\", code: \"MX\" },\r\n  { name: \"Micronesia, Federated States of\", code: \"FM\" },\r\n  { name: \"Moldova, Republic of\", code: \"MD\" },\r\n  { name: \"Monaco\", code: \"MC\" },\r\n  { name: \"Mongolia\", code: \"MN\" },\r\n  { name: \"Montenegro\", code: \"ME\" },\r\n  { name: \"Montserrat\", code: \"MS\" },\r\n  { name: \"Morocco\", code: \"MA\" },\r\n  { name: \"Mozambique\", code: \"MZ\" },\r\n  { name: \"Myanmar\", code: \"MM\" },\r\n  { name: \"Namibia\", code: \"NA\" },\r\n  { name: \"Nauru\", code: \"NR\" },\r\n  { name: \"Nepal\", code: \"NP\" },\r\n  { name: \"Netherlands\", code: \"NL\" },\r\n  { name: \"Netherlands Antilles\", code: \"AN\" },\r\n  { name: \"New Caledonia\", code: \"NC\" },\r\n  { name: \"New Zealand\", code: \"NZ\" },\r\n  { name: \"Nicaragua\", code: \"NI\" },\r\n  { name: \"Niger\", code: \"NE\" },\r\n  { name: \"Nigeria\", code: \"NG\" },\r\n  { name: \"Niue\", code: \"NU\" },\r\n  { name: \"Norfolk Island\", code: \"NF\" },\r\n  { name: \"Northern Mariana Islands\", code: \"MP\" },\r\n  { name: \"Norway\", code: \"NO\" },\r\n  { name: \"Oman\", code: \"OM\" },\r\n  { name: \"Pakistan\", code: \"PK\" },\r\n  { name: \"Palau\", code: \"PW\" },\r\n  { name: \"Palestinian Territory, Occupied\", code: \"PS\" },\r\n  { name: \"Panama\", code: \"PA\" },\r\n  { name: \"Papua New Guinea\", code: \"PG\" },\r\n  { name: \"Paraguay\", code: \"PY\" },\r\n  { name: \"Peru\", code: \"PE\" },\r\n  { name: \"Philippines\", code: \"PH\" },\r\n  { name: \"Pitcairn\", code: \"PN\" },\r\n  { name: \"Poland\", code: \"PL\" },\r\n  { name: \"Portugal\", code: \"PT\" },\r\n  { name: \"Puerto Rico\", code: \"PR\" },\r\n  { name: \"Qatar\", code: \"QA\" },\r\n  { name: \"Reunion\", code: \"RE\" },\r\n  { name: \"Romania\", code: \"RO\" },\r\n  { name: \"Russian Federation\", code: \"RU\" },\r\n  { name: \"Rwanda\", code: \"RW\" },\r\n  { name: \"Saint Helena\", code: \"SH\" },\r\n  { name: \"Saint Kitts and Nevis\", code: \"KN\" },\r\n  { name: \"Saint Lucia\", code: \"LC\" },\r\n  { name: \"Saint Pierre and Miquelon\", code: \"PM\" },\r\n  { name: \"Saint Vincent and The Grenadines\", code: \"VC\" },\r\n  { name: \"Samoa\", code: \"WS\" },\r\n  { name: \"San Marino\", code: \"SM\" },\r\n  { name: \"Sao Tome and Principe\", code: \"ST\" },\r\n  { name: \"Saudi Arabia\", code: \"SA\" },\r\n  { name: \"Senegal\", code: \"SN\" },\r\n  { name: \"Serbia\", code: \"RS\" },\r\n  { name: \"Seychelles\", code: \"SC\" },\r\n  { name: \"Sierra Leone\", code: \"SL\" },\r\n  { name: \"Singapore\", code: \"SG\" },\r\n  { name: \"Slovakia\", code: \"SK\" },\r\n  { name: \"Slovenia\", code: \"SI\" },\r\n  { name: \"Solomon Islands\", code: \"SB\" },\r\n  { name: \"Somalia\", code: \"SO\" },\r\n  { name: \"South Africa\", code: \"ZA\" },\r\n  { name: \"South Georgia and The South Sandwich Islands\", code: \"GS\" },\r\n  { name: \"Spain\", code: \"ES\" },\r\n  { name: \"Sri Lanka\", code: \"LK\" },\r\n  { name: \"Sudan\", code: \"SD\" },\r\n  { name: \"Suriname\", code: \"SR\" },\r\n  { name: \"Svalbard and Jan Mayen\", code: \"SJ\" },\r\n  { name: \"Swaziland\", code: \"SZ\" },\r\n  { name: \"Sweden\", code: \"SE\" },\r\n  { name: \"Switzerland\", code: \"CH\" },\r\n  { name: \"Syrian Arab Republic\", code: \"SY\" },\r\n  { name: \"Taiwan\", code: \"TW\" },\r\n  { name: \"Tajikistan\", code: \"TJ\" },\r\n  { name: \"Tanzania, United Republic of\", code: \"TZ\" },\r\n  { name: \"Thailand\", code: \"TH\" },\r\n  { name: \"Timor-leste\", code: \"TL\" },\r\n  { name: \"Togo\", code: \"TG\" },\r\n  { name: \"Tokelau\", code: \"TK\" },\r\n  { name: \"Tonga\", code: \"TO\" },\r\n  { name: \"Trinidad and Tobago\", code: \"TT\" },\r\n  { name: \"Tunisia\", code: \"TN\" },\r\n  { name: \"Turkey\", code: \"TR\" },\r\n  { name: \"Turkmenistan\", code: \"TM\" },\r\n  { name: \"Turks and Caicos Islands\", code: \"TC\" },\r\n  { name: \"Tuvalu\", code: \"TV\" },\r\n  { name: \"Uganda\", code: \"UG\" },\r\n  { name: \"Ukraine\", code: \"UA\" },\r\n  { name: \"United Arab Emirates\", code: \"AE\" },\r\n  { name: \"United Kingdom\", code: \"GB\" },\r\n  { name: \"United States\", code: \"US\" },\r\n  { name: \"United States Minor Outlying Islands\", code: \"UM\" },\r\n  { name: \"Uruguay\", code: \"UY\" },\r\n  { name: \"Uzbekistan\", code: \"UZ\" },\r\n  { name: \"Vanuatu\", code: \"VU\" },\r\n  { name: \"Venezuela\", code: \"VE\" },\r\n  { name: \"Viet Nam\", code: \"VN\" },\r\n  { name: \"Virgin Islands, British\", code: \"VG\" },\r\n  { name: \"Virgin Islands, U.S.\", code: \"VI\" },\r\n  { name: \"Wallis and Futuna\", code: \"WF\" },\r\n  { name: \"Western Sahara\", code: \"EH\" },\r\n  { name: \"Yemen\", code: \"YE\" },\r\n  { name: \"Zambia\", code: \"ZM\" },\r\n  { name: \"Zimbabwe\", code: \"ZW\" },\r\n];\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACvB;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAuB,MAAM;IAAK;IAC1C;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAkC,MAAM;IAAK;IACrD;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAA2B,MAAM;IAAK;IAC9C;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAyC,MAAM;IAAK;IAC5D;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA+B,MAAM;IAAK;IAClD;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAA+B,MAAM;IAAK;IAClD;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAqC,MAAM;IAAK;IACxD;QAAE,MAAM;QAAiC,MAAM;IAAK;IACpD;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAA6B,MAAM;IAAK;IAChD;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA0C,MAAM;IAAK;IAC7D;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAoC,MAAM;IAAK;IACvD;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAA8C,MAAM;IAAK;IACjE;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAmC,MAAM;IAAK;IACtD;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAmC,MAAM;IAAK;IACtD;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAyB,MAAM;IAAK;IAC5C;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAA6B,MAAM;IAAK;IAChD;QAAE,MAAM;QAAoC,MAAM;IAAK;IACvD;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAyB,MAAM;IAAK;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAmB,MAAM;IAAK;IACtC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAgD,MAAM;IAAK;IACnE;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAgC,MAAM;IAAK;IACnD;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAuB,MAAM;IAAK;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAwC,MAAM;IAAK;IAC3D;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA2B,MAAM;IAAK;IAC9C;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;CAChC"}}, {"offset": {"line": 2326, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2332, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/edit-personal-info.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { useProfilePicture } from \"@/app/hooks/student/useProfilePicture\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@components/ui/avatar\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Card, CardContent } from \"@components/ui/card\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport { Label } from \"@components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@components/ui/select\";\r\nimport { Textarea } from \"@components/ui/textarea\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { countries } from \"@/lib/countries\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\ninterface PersonalInfoFormProps {\r\n  onSuccess: () => void;\r\n}\r\n\r\nexport function PersonalInfoForm({ onSuccess }: PersonalInfoFormProps) {\r\n  const { userInfo, personalInfo, submitPersonalInfo } = useProfile();\r\n  const {\r\n    uploadProfilePicture,\r\n    deleteProfilePicture,\r\n    loading: pictureLoading,\r\n  } = useProfilePicture();\r\n  const [loading, setLoading] = useState(false);\r\n  const [formData, setFormData] = useState(\r\n    personalInfo || {\r\n      first_name: userInfo?.firstName || \"\",\r\n      last_name: userInfo?.lastName || \"\",\r\n      nationality: \"\",\r\n      country_of_residence: \"\",\r\n      gender: \"\",\r\n      date_of_birth: null,\r\n      goals: \"\",\r\n    }\r\n  );\r\n\r\n  const [dateInputs, setDateInputs] = useState({\r\n    day: \"\",\r\n    month: \"\",\r\n    year: \"\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (personalInfo) {\r\n      setFormData(personalInfo);\r\n      if (personalInfo.date_of_birth) {\r\n        const date = new Date(personalInfo.date_of_birth);\r\n        setDateInputs({\r\n          day: date.getDate().toString().padStart(2, \"0\"),\r\n          month: (date.getMonth() + 1).toString().padStart(2, \"0\"),\r\n          year: date.getFullYear().toString(),\r\n        });\r\n      }\r\n    }\r\n  }, [personalInfo]);\r\n\r\n  const handleInputChange = (\r\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\r\n  ) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const validateAndUpdateDate = (updatedDateInputs: typeof dateInputs) => {\r\n    const { day, month, year } = updatedDateInputs;\r\n    \r\n    // Only proceed if all fields have values\r\n    if (!day || !month || !year) {\r\n      setFormData((prev) => ({ ...prev, date_of_birth: null }));\r\n      return;\r\n    }\r\n\r\n    const dayNum = parseInt(day);\r\n    const monthNum = parseInt(month);\r\n    const yearNum = parseInt(year);\r\n\r\n    // Basic validation\r\n    if (dayNum < 1 || dayNum > 31 || monthNum < 1 || monthNum > 12 || yearNum < 1900 || yearNum > new Date().getFullYear()) {\r\n      setFormData((prev) => ({ ...prev, date_of_birth: null }));\r\n      return;\r\n    }\r\n\r\n    // Create date and validate it's a real date\r\n    const date = new Date(yearNum, monthNum - 1, dayNum);\r\n    \r\n    // Check if the date is valid (handles cases like Feb 30th)\r\n    if (\r\n      date.getFullYear() === yearNum &&\r\n      date.getMonth() === monthNum - 1 &&\r\n      date.getDate() === dayNum\r\n    ) {\r\n      const dateStr = `${year}-${month.padStart(2, \"0\")}-${day.padStart(2, \"0\")}`;\r\n      setFormData((prev) => ({ ...prev, date_of_birth: dateStr }));\r\n    } else {\r\n      setFormData((prev) => ({ ...prev, date_of_birth: null }));\r\n    }\r\n  };\r\n\r\n  const handleDateChange = (\r\n    e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>\r\n  ) => {\r\n    const { name, value } = e.target;\r\n    let newValue = value;\r\n\r\n    // Validation for input constraints\r\n    if (name === \"day\") {\r\n      const day = parseInt(value);\r\n      if (day < 1) newValue = \"1\";\r\n      if (day > 31) newValue = \"31\";\r\n    }\r\n    if (name === \"year\") {\r\n      const year = parseInt(value);\r\n      const currentYear = new Date().getFullYear();\r\n      if (year > currentYear) newValue = currentYear.toString();\r\n      if (year < 1900) newValue = \"1900\";\r\n    }\r\n\r\n    const updatedDateInputs = { ...dateInputs, [name]: newValue };\r\n    setDateInputs(updatedDateInputs);\r\n    \r\n    // Validate and update the form data immediately\r\n    validateAndUpdateDate(updatedDateInputs);\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    try {\r\n      // Format the date properly for the API\r\n      const formattedData = {\r\n        ...formData,\r\n        date_of_birth: formData.date_of_birth\r\n          ? new Date(formData.date_of_birth).toISOString()\r\n          : null,\r\n      };\r\n      await submitPersonalInfo(formattedData);\r\n      onSuccess();\r\n    } catch (error) {\r\n      console.error(error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <CardContent className=\"pt-6\">\r\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n          <div className=\"flex items-center gap-6\">\r\n            <Avatar className=\"h-24 w-24\">\r\n              <AvatarImage\r\n                src={\r\n                  userInfo?.profile_picture_url ||\r\n                  generatePlaceholder(userInfo?.firstName, userInfo?.lastName)\r\n                }\r\n              />\r\n              <AvatarFallback>\r\n                {userInfo?.firstName?.[0]}\r\n                {userInfo?.lastName?.[0]}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div className=\"space-y-2\">\r\n              <input\r\n                type=\"file\"\r\n                id=\"profile-picture\"\r\n                accept=\"image/*\"\r\n                className=\"hidden\"\r\n                onChange={async (e) => {\r\n                  const file = e.target.files?.[0];\r\n                  if (file) {\r\n                    try {\r\n                      await uploadProfilePicture(file);\r\n                    } catch (error) {\r\n                      // Error is handled by the hook\r\n                    }\r\n                  }\r\n                }}\r\n              />\r\n              <div className=\"flex lg:flex-row flex-col gap-2\">\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"outline\"\r\n                  onClick={() =>\r\n                    document.getElementById(\"profile-picture\")?.click()\r\n                  }\r\n                  disabled={pictureLoading}\r\n                >\r\n                  {pictureLoading ? \"Uploading...\" : \"Upload Picture\"}\r\n                </Button>\r\n                {userInfo?.profile_picture_url && (\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"destructive\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        await deleteProfilePicture();\r\n                      } catch (error) {\r\n                        // Error is handled by the hook\r\n                      }\r\n                    }}\r\n                    disabled={pictureLoading}\r\n                  >\r\n                    Remove Picture\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              {!userInfo?.profile_picture_url && (\r\n                <p className=\"text-sm text-gray-500\">\r\n                  Recommended: Square image, at least 400x400 pixels\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid sm:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"firstName\">First Name</Label>\r\n              <Input\r\n                id=\"firstName\"\r\n                name=\"first_name\"\r\n                value={formData.first_name}\r\n                onChange={handleInputChange}\r\n                className=\"bg-white\"\r\n              />\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"lastName\">Last Name</Label>\r\n              <Input\r\n                id=\"lastName\"\r\n                name=\"last_name\"\r\n                value={formData.last_name}\r\n                onChange={handleInputChange}\r\n                className=\"bg-white\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid sm:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"nationality\">Nationality</Label>\r\n              <Select\r\n                value={formData.nationality}\r\n                onValueChange={(value) =>\r\n                  setFormData((prev) => ({ ...prev, nationality: value }))\r\n                }\r\n              >\r\n                <SelectTrigger className=\"bg-white cursor-pointer hover:bg-gray-50 text-gray-900\">\r\n                  <SelectValue\r\n                    placeholder=\"Select nationality\"\r\n                    className=\"text-gray-900\"\r\n                  >\r\n                    {countries.find((c) => c.code === formData.nationality)\r\n                      ?.name || \"Select nationality\"}\r\n                  </SelectValue>\r\n                </SelectTrigger>\r\n                <SelectContent className=\"bg-white\">\r\n                  {countries.map((country) => (\r\n                    <SelectItem\r\n                      key={country.code}\r\n                      value={country.code}\r\n                      className=\"cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg\"\r\n                    >\r\n                      {country.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"residence\">Country of residence</Label>\r\n              <Select\r\n                value={formData.country_of_residence}\r\n                onValueChange={(value) =>\r\n                  setFormData((prev) => ({\r\n                    ...prev,\r\n                    country_of_residence: value,\r\n                  }))\r\n                }\r\n              >\r\n                <SelectTrigger className=\"bg-white cursor-pointer hover:bg-gray-50 text-gray-900\">\r\n                  <SelectValue\r\n                    placeholder=\"Select country\"\r\n                    className=\"text-gray-900\"\r\n                  >\r\n                    {countries.find(\r\n                      (c) => c.code === formData.country_of_residence\r\n                    )?.name || \"Select country\"}\r\n                  </SelectValue>\r\n                </SelectTrigger>\r\n                <SelectContent className=\"bg-white\">\r\n                  {countries.map((country) => (\r\n                    <SelectItem\r\n                      key={country.code}\r\n                      value={country.code}\r\n                      className=\"cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg\"\r\n                    >\r\n                      {country.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid sm:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"gender\">Gender</Label>\r\n              <Select\r\n                value={formData.gender}\r\n                onValueChange={(value) =>\r\n                  setFormData((prev) => ({ ...prev, gender: value }))\r\n                }\r\n              >\r\n                <SelectTrigger className=\"bg-white cursor-pointer hover:bg-gray-50 text-gray-900\">\r\n                  <SelectValue\r\n                    placeholder=\"Select gender\"\r\n                    className=\"text-gray-900\"\r\n                  >\r\n                    {formData.gender === \"male\"\r\n                      ? \"Male\"\r\n                      : formData.gender === \"female\"\r\n                      ? \"Female\"\r\n                      : formData.gender === \"other\"\r\n                      ? \"Other\"\r\n                      : \"Select gender\"}\r\n                  </SelectValue>\r\n                </SelectTrigger>\r\n                <SelectContent className=\"bg-white\">\r\n                  <SelectItem\r\n                    value=\"male\"\r\n                    className=\"cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg\"\r\n                  >\r\n                    Male\r\n                  </SelectItem>\r\n                  <SelectItem\r\n                    value=\"female\"\r\n                    className=\"cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg\"\r\n                  >\r\n                    Female\r\n                  </SelectItem>\r\n                  <SelectItem\r\n                    value=\"other\"\r\n                    className=\"cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg\"\r\n                  >\r\n                    Other\r\n                  </SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"date_of_birth\">\r\n                Date of Birth <span className=\"text-red-500\">*</span>\r\n              </Label>\r\n              <div className=\"flex space-x-2\">\r\n                <Input\r\n                  type=\"number\"\r\n                  name=\"day\"\r\n                  placeholder=\"DD\"\r\n                  min=\"1\"\r\n                  max=\"31\"\r\n                  value={dateInputs.day}\r\n                  onChange={handleDateChange}\r\n                  className=\"w-20 bg-white\"\r\n                />\r\n                <select\r\n                  name=\"month\"\r\n                  value={dateInputs.month}\r\n                  onChange={handleDateChange}\r\n                  className=\"flex-1 bg-white border border-input rounded-md px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\r\n                >\r\n                  <option value=\"\">Select Month</option>\r\n                  {Array.from({ length: 12 }, (_, i) => {\r\n                    const month = (i + 1).toString().padStart(2, \"0\");\r\n                    return (\r\n                      <option key={month} value={month}>\r\n                        {new Date(2000, i).toLocaleString(\"default\", {\r\n                          month: \"long\",\r\n                        })}\r\n                      </option>\r\n                    );\r\n                  })}\r\n                </select>\r\n                <Input\r\n                  type=\"number\"\r\n                  name=\"year\"\r\n                  placeholder=\"YYYY\"\r\n                  min=\"1900\"\r\n                  max={new Date().getFullYear()}\r\n                  value={dateInputs.year}\r\n                  onChange={handleDateChange}\r\n                  className=\"w-24 bg-white\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"goal\">Goal</Label>\r\n            <Textarea\r\n              value={formData.goals || \"\"}\r\n              name=\"goals\"\r\n              onChange={handleInputChange}\r\n              className=\"min-h-[100px] bg-white\"\r\n              placeholder=\"Share your academic and career goals...\"\r\n              maxLength={400}\r\n            />\r\n            <div className=\"text-xs text-right text-muted-foreground\">\r\n              {formData.goals?.length || 0}/400\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex justify-end gap-4 mt-6\">\r\n            <Button type=\"button\" variant=\"ghost\">\r\n              Cancel\r\n            </Button>\r\n            <Button type=\"submit\" disabled={loading}>\r\n              {loading ? \"Saving...\" : \"Save changes\"}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAnBA;;;;;;;;;;;;;;AAyBO,SAAS,iBAAiB,EAAE,SAAS,EAAyB;IACnE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAChE,MAAM,EACJ,oBAAoB,EACpB,oBAAoB,EACpB,SAAS,cAAc,EACxB,GAAG,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrC,gBAAgB;QACd,YAAY,UAAU,aAAa;QACnC,WAAW,UAAU,YAAY;QACjC,aAAa;QACb,sBAAsB;QACtB,QAAQ;QACR,eAAe;QACf,OAAO;IACT;IAGF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,KAAK;QACL,OAAO;QACP,MAAM;IACR;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,YAAY;YACZ,IAAI,aAAa,aAAa,EAAE;gBAC9B,MAAM,OAAO,IAAI,KAAK,aAAa,aAAa;gBAChD,cAAc;oBACZ,KAAK,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAC3C,OAAO,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBACpD,MAAM,KAAK,WAAW,GAAG,QAAQ;gBACnC;YACF;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACnD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;QAE7B,yCAAyC;QACzC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM;YAC3B,YAAY,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,eAAe;gBAAK,CAAC;YACvD;QACF;QAEA,MAAM,SAAS,SAAS;QACxB,MAAM,WAAW,SAAS;QAC1B,MAAM,UAAU,SAAS;QAEzB,mBAAmB;QACnB,IAAI,SAAS,KAAK,SAAS,MAAM,WAAW,KAAK,WAAW,MAAM,UAAU,QAAQ,UAAU,IAAI,OAAO,WAAW,IAAI;YACtH,YAAY,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,eAAe;gBAAK,CAAC;YACvD;QACF;QAEA,4CAA4C;QAC5C,MAAM,OAAO,IAAI,KAAK,SAAS,WAAW,GAAG;QAE7C,2DAA2D;QAC3D,IACE,KAAK,WAAW,OAAO,WACvB,KAAK,QAAQ,OAAO,WAAW,KAC/B,KAAK,OAAO,OAAO,QACnB;YACA,MAAM,UAAU,GAAG,KAAK,CAAC,EAAE,MAAM,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,QAAQ,CAAC,GAAG,MAAM;YAC3E,YAAY,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,eAAe;gBAAQ,CAAC;QAC5D,OAAO;YACL,YAAY,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,eAAe;gBAAK,CAAC;QACzD;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,IAAI,WAAW;QAEf,mCAAmC;QACnC,IAAI,SAAS,OAAO;YAClB,MAAM,MAAM,SAAS;YACrB,IAAI,MAAM,GAAG,WAAW;YACxB,IAAI,MAAM,IAAI,WAAW;QAC3B;QACA,IAAI,SAAS,QAAQ;YACnB,MAAM,OAAO,SAAS;YACtB,MAAM,cAAc,IAAI,OAAO,WAAW;YAC1C,IAAI,OAAO,aAAa,WAAW,YAAY,QAAQ;YACvD,IAAI,OAAO,MAAM,WAAW;QAC9B;QAEA,MAAM,oBAAoB;YAAE,GAAG,UAAU;YAAE,CAAC,KAAK,EAAE;QAAS;QAC5D,cAAc;QAEd,gDAAgD;QAChD,sBAAsB;IACxB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,IAAI;YACF,uCAAuC;YACvC,MAAM,gBAAgB;gBACpB,GAAG,QAAQ;gBACX,eAAe,SAAS,aAAa,GACjC,IAAI,KAAK,SAAS,aAAa,EAAE,WAAW,KAC5C;YACN;YACA,MAAM,mBAAmB;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;kBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,kIAAA,CAAA,cAAW;wCACV,KACE,UAAU,uBACV,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,WAAW,UAAU;;;;;;kDAGvD,8OAAC,kIAAA,CAAA,iBAAc;;4CACZ,UAAU,WAAW,CAAC,EAAE;4CACxB,UAAU,UAAU,CAAC,EAAE;;;;;;;;;;;;;0CAG5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,QAAO;wCACP,WAAU;wCACV,UAAU,OAAO;4CACf,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;4CAChC,IAAI,MAAM;gDACR,IAAI;oDACF,MAAM,qBAAqB;gDAC7B,EAAE,OAAO,OAAO;gDACd,+BAA+B;gDACjC;4CACF;wCACF;;;;;;kDAEF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IACP,SAAS,cAAc,CAAC,oBAAoB;gDAE9C,UAAU;0DAET,iBAAiB,iBAAiB;;;;;;4CAEpC,UAAU,qCACT,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;oDACP,IAAI;wDACF,MAAM;oDACR,EAAE,OAAO,OAAO;oDACd,+BAA+B;oDACjC;gDACF;gDACA,UAAU;0DACX;;;;;;;;;;;;oCAKJ,CAAC,UAAU,qCACV,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAO3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,UAAU;wCAC1B,UAAU;wCACV,WAAU;;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,SAAS;wCACzB,UAAU;wCACV,WAAU;;;;;;;;;;;;;;;;;;kCAKhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,WAAW;wCAC3B,eAAe,CAAC,QACd,YAAY,CAAC,OAAS,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa;gDAAM,CAAC;;0DAGxD,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDACV,aAAY;oDACZ,WAAU;8DAET,gHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,SAAS,WAAW,GAClD,QAAQ;;;;;;;;;;;0DAGhB,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACtB,gHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBACd,8OAAC,kIAAA,CAAA,aAAU;wDAET,OAAO,QAAQ,IAAI;wDACnB,WAAU;kEAET,QAAQ,IAAI;uDAJR,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;0CAU3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,oBAAoB;wCACpC,eAAe,CAAC,QACd,YAAY,CAAC,OAAS,CAAC;oDACrB,GAAG,IAAI;oDACP,sBAAsB;gDACxB,CAAC;;0DAGH,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDACV,aAAY;oDACZ,WAAU;8DAET,gHAAA,CAAA,YAAS,CAAC,IAAI,CACb,CAAC,IAAM,EAAE,IAAI,KAAK,SAAS,oBAAoB,GAC9C,QAAQ;;;;;;;;;;;0DAGf,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACtB,gHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBACd,8OAAC,kIAAA,CAAA,aAAU;wDAET,OAAO,QAAQ,IAAI;wDACnB,WAAU;kEAET,QAAQ,IAAI;uDAJR,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAY7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAS;;;;;;kDACxB,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,MAAM;wCACtB,eAAe,CAAC,QACd,YAAY,CAAC,OAAS,CAAC;oDAAE,GAAG,IAAI;oDAAE,QAAQ;gDAAM,CAAC;;0DAGnD,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDACV,aAAY;oDACZ,WAAU;8DAET,SAAS,MAAM,KAAK,SACjB,SACA,SAAS,MAAM,KAAK,WACpB,WACA,SAAS,MAAM,KAAK,UACpB,UACA;;;;;;;;;;;0DAGR,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACvB,8OAAC,kIAAA,CAAA,aAAU;wDACT,OAAM;wDACN,WAAU;kEACX;;;;;;kEAGD,8OAAC,kIAAA,CAAA,aAAU;wDACT,OAAM;wDACN,WAAU;kEACX;;;;;;kEAGD,8OAAC,kIAAA,CAAA,aAAU;wDACT,OAAM;wDACN,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;0CAMP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAgB;0DACf,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,aAAY;gDACZ,KAAI;gDACJ,KAAI;gDACJ,OAAO,WAAW,GAAG;gDACrB,UAAU;gDACV,WAAU;;;;;;0DAEZ,8OAAC;gDACC,MAAK;gDACL,OAAO,WAAW,KAAK;gDACvB,UAAU;gDACV,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,MAAM,IAAI,CAAC;wDAAE,QAAQ;oDAAG,GAAG,CAAC,GAAG;wDAC9B,MAAM,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;wDAC7C,qBACE,8OAAC;4DAAmB,OAAO;sEACxB,IAAI,KAAK,MAAM,GAAG,cAAc,CAAC,WAAW;gEAC3C,OAAO;4DACT;2DAHW;;;;;oDAMjB;;;;;;;0DAEF,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,aAAY;gDACZ,KAAI;gDACJ,KAAK,IAAI,OAAO,WAAW;gDAC3B,OAAO,WAAW,IAAI;gDACtB,UAAU;gDACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAO;;;;;;0CACtB,8OAAC,oIAAA,CAAA,WAAQ;gCACP,OAAO,SAAS,KAAK,IAAI;gCACzB,MAAK;gCACL,UAAU;gCACV,WAAU;gCACV,aAAY;gCACZ,WAAW;;;;;;0CAEb,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,KAAK,EAAE,UAAU;oCAAE;;;;;;;;;;;;;kCAIjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;0CAAQ;;;;;;0CAGtC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU;0CAC7B,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC"}}, {"offset": {"line": 3067, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3073, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/edit-educational-info.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRef, useEffect, useState } from \"react\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { Button } from \"@/app/components/ui/button\";\r\nimport { Input } from \"@/app/components/ui/input\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/app/components/ui/select\";\r\nimport { Plus } from \"lucide-react\";\r\nimport { EducationInfo } from \"@/app/types/student/profile\";\r\nimport { Label } from \"@/app/components/ui/label\";\r\nimport { Card, CardContent } from \"@/app/components/ui/card\";\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n  AvatarImage,\r\n} from \"@/app/components/ui/avatar\";\r\n\r\ninterface EducationalInfoFormProps {\r\n  onSuccess: () => void;\r\n}\r\n\r\nexport function EducationalInfoForm({ onSuccess }: EducationalInfoFormProps) {\r\n  const {\r\n    userInfo,\r\n    educationInfo,\r\n    loading,\r\n    fetchEducationalBackground,\r\n    submitEducationInfo,\r\n    deleteEducationInfo,\r\n  } = useProfile();\r\n\r\n  const [formData, setFormData] = useState<EducationInfo[]>([]);\r\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\r\n\r\n  useEffect(() => {\r\n    fetchEducationalBackground();\r\n  }, [fetchEducationalBackground]);\r\n\r\n  useEffect(() => {\r\n    if (educationInfo) {\r\n      setFormData(\r\n        educationInfo.map((edu) => ({\r\n          ...edu,\r\n          institution_type: edu.institution_type || \"school\",\r\n        }))\r\n      );\r\n    } else {\r\n      setFormData([\r\n        {\r\n          current_education_level: \"\",\r\n          institution_name: \"\",\r\n          institution_type: \"school\",\r\n          start_date: \"\",\r\n          end_date: \"\",\r\n          is_current: false,\r\n        },\r\n      ]);\r\n    }\r\n  }, [educationInfo]);\r\n\r\n  const validateForm = () => {\r\n    const newErrors: { [key: string]: string } = {};\r\n    let isValid = true;\r\n\r\n    formData.forEach((education, index) => {\r\n      // Validate education level\r\n      if (!education.current_education_level) {\r\n        newErrors[`education_level_${index}`] = \"Education level is required\";\r\n        isValid = false;\r\n      }\r\n\r\n      // Validate institution name\r\n      if (!education.institution_name.trim()) {\r\n        newErrors[`institution_name_${index}`] = \"Institution name is required\";\r\n        isValid = false;\r\n      }\r\n\r\n      // Validate start date\r\n      if (!education.start_date) {\r\n        newErrors[`start_date_${index}`] = \"Start date is required\";\r\n        isValid = false;\r\n      }\r\n\r\n      // Validate end date\r\n      if (!education.is_current && !education.end_date) {\r\n        newErrors[`end_date_${index}`] = \"End date is required\";\r\n        isValid = false;\r\n      }\r\n\r\n      // Validate date logic\r\n      if (education.start_date && education.end_date && !education.is_current) {\r\n        const startDate = new Date(education.start_date);\r\n        const endDate = new Date(education.end_date);\r\n        if (endDate < startDate) {\r\n          newErrors[`end_date_${index}`] =\r\n            \"End date cannot be before start date\";\r\n          isValid = false;\r\n        }\r\n      }\r\n    });\r\n\r\n    setErrors(newErrors);\r\n    if (!isValid) {\r\n      toast.error(\"Please fill in all required fields correctly\");\r\n    }\r\n    return isValid;\r\n  };\r\n\r\n  const handleInputChange = (\r\n    index: number,\r\n    field: keyof EducationInfo,\r\n    value: any\r\n  ) => {\r\n    setFormData((prev) =>\r\n      prev.map((item, i) =>\r\n        i === index\r\n          ? {\r\n              ...item,\r\n              [field]:\r\n                field === \"start_date\" || field === \"end_date\"\r\n                  ? new Date(value).toISOString()\r\n                  : value,\r\n            }\r\n          : item\r\n      )\r\n    );\r\n\r\n    if (errors[`${field}_${index}`]) {\r\n      setErrors((prev) => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[`${field}_${index}`];\r\n        return newErrors;\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleAddMore = () => {\r\n    setFormData((prev) => [\r\n      ...prev,\r\n      {\r\n        current_education_level: \"\",\r\n        institution_name: \"\",\r\n        institution_type: \"school\",\r\n        start_date: \"\",\r\n        end_date: \"\",\r\n        is_current: false,\r\n      },\r\n    ]);\r\n  };\r\n\r\n  const handleDelete = async (index: number) => {\r\n    const education = formData[index];\r\n    if (education.id) {\r\n      await deleteEducationInfo(education.id);\r\n    }\r\n    setFormData((prev) => prev.filter((_, i) => i !== index));\r\n\r\n    setErrors((prev) => {\r\n      const newErrors = { ...prev };\r\n      Object.keys(newErrors).forEach((key) => {\r\n        if (key.includes(`_${index}`)) {\r\n          delete newErrors[key];\r\n        }\r\n      });\r\n      return newErrors;\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const validEducationData = formData.map((edu) => ({\r\n        ...edu,\r\n        end_date: edu.is_current ? undefined : edu.end_date,\r\n      }));\r\n\r\n      await submitEducationInfo(validEducationData);\r\n      toast.success(\"Educational information updated successfully\");\r\n      onSuccess();\r\n    } catch (error) {\r\n      console.error(\"Failed to submit education info:\", error);\r\n      toast.error(\"Failed to update educational information\");\r\n    }\r\n  };\r\n\r\n  if (!userInfo) return null;\r\n\r\n  return (\r\n    <Card>\r\n      <CardContent className=\"p-2.5 sm:p-6\">\r\n        <div className=\"flex items-center gap-4 mb-8\">\r\n          <Avatar className=\"w-12 h-12\">\r\n            <AvatarImage\r\n              src={userInfo?.profile_picture_url || \"/placeholder.svg\"}\r\n            />\r\n            <AvatarFallback>{`${userInfo?.firstName?.[0]}${userInfo?.lastName?.[0]}`}</AvatarFallback>\r\n          </Avatar>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold\">\r\n              {userInfo?.firstName} {userInfo?.lastName}\r\n            </h2>\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Edit Profile / Educational Information\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"grid gap-6\">\r\n          {formData.map((education, index) => (\r\n            <div key={index} className=\"space-y-6 pb-6 border-b last:border-0\">\r\n              <div className=\"flex justify-between items-start\">\r\n                <div className=\"space-y-2 flex-1\">\r\n                  <Label htmlFor={`education-${index}`}>\r\n                    Current Education Level{\" \"}\r\n                    <span className=\"text-red-500\">*</span>\r\n                  </Label>\r\n                  <Select\r\n                    value={education.current_education_level}\r\n                    onValueChange={(value) =>\r\n                      handleInputChange(index, \"current_education_level\", value)\r\n                    }\r\n                  >\r\n                    <SelectTrigger\r\n                      className={`bg-white cursor-pointer hover:bg-gray-50 text-gray-900 ${\r\n                        errors[`education_level_${index}`]\r\n                          ? \"border-red-500\"\r\n                          : \"\"\r\n                      }`}\r\n                    >\r\n                      <SelectValue placeholder=\"Select education level\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent className=\"bg-white\">\r\n                      <SelectItem\r\n                        className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                        value=\"high_school\"\r\n                      >\r\n                        High School\r\n                      </SelectItem>\r\n                      <SelectItem\r\n                        className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                        value=\"bachelors\"\r\n                      >\r\n                        Bachelor's Degree\r\n                      </SelectItem>\r\n                      <SelectItem\r\n                        className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                        value=\"masters\"\r\n                      >\r\n                        Master's Degree\r\n                      </SelectItem>\r\n                      <SelectItem\r\n                        className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                        value=\"phd\"\r\n                      >\r\n                        PhD\r\n                      </SelectItem>\r\n                      <SelectItem\r\n                        className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                        value=\"other\"\r\n                      >\r\n                        Other\r\n                      </SelectItem>\r\n                    </SelectContent>\r\n                  </Select>\r\n                  {errors[`education_level_${index}`] && (\r\n                    <p className=\"text-sm text-red-500\">\r\n                      {errors[`education_level_${index}`]}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n                {formData.length > 1 && (\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"ghost\"\r\n                    className=\"text-red-500 hover:text-red-600 hover:bg-red-50\"\r\n                    onClick={() => handleDelete(index)}\r\n                  >\r\n                    Delete\r\n                  </Button>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor={`institution-${index}`}>\r\n                  Institution Name <span className=\"text-red-500\">*</span>\r\n                </Label>\r\n                <Input\r\n                  id={`institution-${index}`}\r\n                  value={education.institution_name}\r\n                  onChange={(e) =>\r\n                    handleInputChange(index, \"institution_name\", e.target.value)\r\n                  }\r\n                  placeholder=\"Enter institution name\"\r\n                  className={\r\n                    errors[`institution_name_${index}`] ? \"border-red-500\" : \"\"\r\n                  }\r\n                />\r\n                {errors[`institution_name_${index}`] && (\r\n                  <p className=\"text-sm text-red-500\">\r\n                    {errors[`institution_name_${index}`]}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"grid sm:grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor={`start-date-${index}`}>\r\n                    Start Date <span className=\"text-red-500\">*</span>\r\n                  </Label>\r\n                  <Input\r\n                    type=\"date\"\r\n                    value={education.start_date.split(\"T\")[0]}\r\n                    onChange={(e) =>\r\n                      handleInputChange(index, \"start_date\", e.target.value)\r\n                    }\r\n                    className={\r\n                      errors[`start_date_${index}`] ? \"border-red-500\" : \"\"\r\n                    }\r\n                    max={new Date().toISOString().split(\"T\")[0]}\r\n                  />\r\n                  {errors[`start_date_${index}`] && (\r\n                    <p className=\"text-sm text-red-500\">\r\n                      {errors[`start_date_${index}`]}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor={`end-date-${index}`}>\r\n                    End Date{\" \"}\r\n                    {!education.is_current && (\r\n                      <span className=\"text-red-500\">*</span>\r\n                    )}\r\n                  </Label>\r\n                  <Input\r\n                    type=\"date\"\r\n                    value={education.end_date?.split(\"T\")[0] || \"\"}\r\n                    onChange={(e) =>\r\n                      handleInputChange(index, \"end_date\", e.target.value)\r\n                    }\r\n                    disabled={education.is_current}\r\n                    className={\r\n                      errors[`end_date_${index}`] ? \"border-red-500\" : \"\"\r\n                    }\r\n                    min={education.start_date.split(\"T\")[0]}\r\n                    max={new Date().toISOString().split(\"T\")[0]}\r\n                  />\r\n                  {errors[`end_date_${index}`] && (\r\n                    <p className=\"text-sm text-red-500\">\r\n                      {errors[`end_date_${index}`]}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <label className=\"flex items-center gap-2 mt-2\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={education.is_current}\r\n                  onChange={(e) =>\r\n                    handleInputChange(index, \"is_current\", e.target.checked)\r\n                  }\r\n                  className=\"rounded border-gray-300\"\r\n                />\r\n                <span className=\"text-sm\">Currently studying here</span>\r\n              </label>\r\n            </div>\r\n          ))}\r\n\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            className=\"w-fit\"\r\n            onClick={handleAddMore}\r\n          >\r\n            <Plus className=\"w-4 h-4 mr-2\" />\r\n            Add more education\r\n          </Button>\r\n\r\n          <div className=\"flex justify-end gap-4 mt-6\">\r\n            <Button type=\"button\" variant=\"ghost\" onClick={onSuccess}>\r\n              Cancel\r\n            </Button>\r\n            <Button type=\"submit\" disabled={loading}>\r\n              {loading ? \"Saving...\" : \"Save changes\"}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AAJA;AAdA;;;;;;;;;;;;AA4BO,SAAS,oBAAoB,EAAE,SAAS,EAA4B;IACzE,MAAM,EACJ,QAAQ,EACR,aAAa,EACb,OAAO,EACP,0BAA0B,EAC1B,mBAAmB,EACnB,mBAAmB,EACpB,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IAEjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAA2B;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,YACE,cAAc,GAAG,CAAC,CAAC,MAAQ,CAAC;oBAC1B,GAAG,GAAG;oBACN,kBAAkB,IAAI,gBAAgB,IAAI;gBAC5C,CAAC;QAEL,OAAO;YACL,YAAY;gBACV;oBACE,yBAAyB;oBACzB,kBAAkB;oBAClB,kBAAkB;oBAClB,YAAY;oBACZ,UAAU;oBACV,YAAY;gBACd;aACD;QACH;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,eAAe;QACnB,MAAM,YAAuC,CAAC;QAC9C,IAAI,UAAU;QAEd,SAAS,OAAO,CAAC,CAAC,WAAW;YAC3B,2BAA2B;YAC3B,IAAI,CAAC,UAAU,uBAAuB,EAAE;gBACtC,SAAS,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,GAAG;gBACxC,UAAU;YACZ;YAEA,4BAA4B;YAC5B,IAAI,CAAC,UAAU,gBAAgB,CAAC,IAAI,IAAI;gBACtC,SAAS,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,GAAG;gBACzC,UAAU;YACZ;YAEA,sBAAsB;YACtB,IAAI,CAAC,UAAU,UAAU,EAAE;gBACzB,SAAS,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG;gBACnC,UAAU;YACZ;YAEA,oBAAoB;YACpB,IAAI,CAAC,UAAU,UAAU,IAAI,CAAC,UAAU,QAAQ,EAAE;gBAChD,SAAS,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG;gBACjC,UAAU;YACZ;YAEA,sBAAsB;YACtB,IAAI,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,CAAC,UAAU,UAAU,EAAE;gBACvE,MAAM,YAAY,IAAI,KAAK,UAAU,UAAU;gBAC/C,MAAM,UAAU,IAAI,KAAK,UAAU,QAAQ;gBAC3C,IAAI,UAAU,WAAW;oBACvB,SAAS,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,GAC5B;oBACF,UAAU;gBACZ;YACF;QACF;QAEA,UAAU;QACV,IAAI,CAAC,SAAS;YACZ,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;QACA,OAAO;IACT;IAEA,MAAM,oBAAoB,CACxB,OACA,OACA;QAEA,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAM,IACd,MAAM,QACF;oBACE,GAAG,IAAI;oBACP,CAAC,MAAM,EACL,UAAU,gBAAgB,UAAU,aAChC,IAAI,KAAK,OAAO,WAAW,KAC3B;gBACR,IACA;QAIR,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE;YAC/B,UAAU,CAAC;gBACT,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,GAAG,MAAM,CAAC,EAAE,OAAO,CAAC;gBACrC,OAAO;YACT;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY,CAAC,OAAS;mBACjB;gBACH;oBACE,yBAAyB;oBACzB,kBAAkB;oBAClB,kBAAkB;oBAClB,YAAY;oBACZ,UAAU;oBACV,YAAY;gBACd;aACD;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,YAAY,QAAQ,CAAC,MAAM;QACjC,IAAI,UAAU,EAAE,EAAE;YAChB,MAAM,oBAAoB,UAAU,EAAE;QACxC;QACA,YAAY,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAElD,UAAU,CAAC;YACT,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAC;gBAC9B,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG;oBAC7B,OAAO,SAAS,CAAC,IAAI;gBACvB;YACF;YACA,OAAO;QACT;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI;YACF,MAAM,qBAAqB,SAAS,GAAG,CAAC,CAAC,MAAQ,CAAC;oBAChD,GAAG,GAAG;oBACN,UAAU,IAAI,UAAU,GAAG,YAAY,IAAI,QAAQ;gBACrD,CAAC;YAED,MAAM,oBAAoB;YAC1B,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;kBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCACV,KAAK,UAAU,uBAAuB;;;;;;8CAExC,8OAAC,kIAAA,CAAA,iBAAc;8CAAE,GAAG,UAAU,WAAW,CAAC,EAAE,GAAG,UAAU,UAAU,CAAC,EAAE,EAAE;;;;;;;;;;;;sCAE1E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCACX,UAAU;wCAAU;wCAAE,UAAU;;;;;;;8CAEnC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAMjD,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,SAAS,GAAG,CAAC,CAAC,WAAW,sBACxB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,CAAC,UAAU,EAAE,OAAO;;4DAAE;4DACZ;0EACxB,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEjC,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,UAAU,uBAAuB;wDACxC,eAAe,CAAC,QACd,kBAAkB,OAAO,2BAA2B;;0EAGtD,8OAAC,kIAAA,CAAA,gBAAa;gEACZ,WAAW,CAAC,uDAAuD,EACjE,MAAM,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,GAC9B,mBACA,IACJ;0EAEF,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;gEAAC,WAAU;;kFACvB,8OAAC,kIAAA,CAAA,aAAU;wEACT,WAAU;wEACV,OAAM;kFACP;;;;;;kFAGD,8OAAC,kIAAA,CAAA,aAAU;wEACT,WAAU;wEACV,OAAM;kFACP;;;;;;kFAGD,8OAAC,kIAAA,CAAA,aAAU;wEACT,WAAU;wEACV,OAAM;kFACP;;;;;;kFAGD,8OAAC,kIAAA,CAAA,aAAU;wEACT,WAAU;wEACV,OAAM;kFACP;;;;;;kFAGD,8OAAC,kIAAA,CAAA,aAAU;wEACT,WAAU;wEACV,OAAM;kFACP;;;;;;;;;;;;;;;;;;oDAKJ,MAAM,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,kBACjC,8OAAC;wDAAE,WAAU;kEACV,MAAM,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC;;;;;;;;;;;;4CAIxC,SAAS,MAAM,GAAG,mBACjB,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,aAAa;0DAC7B;;;;;;;;;;;;kDAML,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,CAAC,YAAY,EAAE,OAAO;;oDAAE;kEACrB,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAElD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAI,CAAC,YAAY,EAAE,OAAO;gDAC1B,OAAO,UAAU,gBAAgB;gDACjC,UAAU,CAAC,IACT,kBAAkB,OAAO,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDAE7D,aAAY;gDACZ,WACE,MAAM,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,GAAG,mBAAmB;;;;;;4CAG5D,MAAM,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,kBAClC,8OAAC;gDAAE,WAAU;0DACV,MAAM,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC;;;;;;;;;;;;kDAK1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,CAAC,WAAW,EAAE,OAAO;;4DAAE;0EAC1B,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAE5C,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,UAAU,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wDACzC,UAAU,CAAC,IACT,kBAAkB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;wDAEvD,WACE,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,mBAAmB;wDAErD,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;oDAE5C,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,kBAC5B,8OAAC;wDAAE,WAAU;kEACV,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC;;;;;;;;;;;;0DAKpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,CAAC,SAAS,EAAE,OAAO;;4DAAE;4DAC1B;4DACR,CAAC,UAAU,UAAU,kBACpB,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAGnC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,UAAU,QAAQ,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;wDAC5C,UAAU,CAAC,IACT,kBAAkB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;wDAErD,UAAU,UAAU,UAAU;wDAC9B,WACE,MAAM,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,mBAAmB;wDAEnD,KAAK,UAAU,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wDACvC,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;oDAE5C,MAAM,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,kBAC1B,8OAAC;wDAAE,WAAU;kEACV,MAAM,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;kDAMpC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,UAAU,UAAU;gDAC7B,UAAU,CAAC,IACT,kBAAkB,OAAO,cAAc,EAAE,MAAM,CAAC,OAAO;gDAEzD,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;+BA3JpB;;;;;sCAgKZ,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;;8CAET,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAQ,SAAS;8CAAW;;;;;;8CAG1D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC"}}, {"offset": {"line": 3688, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3694, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/edit-mode.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { PersonalInfoForm } from \"./edit-personal-info\";\r\nimport { EducationalInfoForm } from \"./edit-educational-info\";\r\nimport { cn } from \"@components/lib/utils\";\r\nimport { X } from \"lucide-react\";\r\n\r\ntype Tab = \"personal\" | \"educational\";\r\n\r\ninterface ProfileEditorProps {\r\n  initialTab?: Tab;\r\n  onClose: () => void;\r\n}\r\n\r\nexport function ProfileEditor({\r\n  initialTab = \"personal\",\r\n  onClose,\r\n}: ProfileEditorProps) {\r\n  const [activeTab, setActiveTab] = useState<Tab>(initialTab);\r\n\r\n  return (\r\n    <div className=\"container flex flex-col gap-y-4 sm:gap-y-8 mx-auto\">\r\n      <div className=\"flex justify-between items-start \">\r\n        <h1 className=\"text-2xl sm:text-3xl font-semibold\">Edit Profile</h1>\r\n        {onClose && (\r\n          <button\r\n            onClick={onClose}\r\n            className=\" text-gray-500 hover:text-gray-700\"\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n      <div className=\"flex lg:flex-row flex-col gap-6 \">\r\n        <div className=\"space-y-2 \">\r\n          <div\r\n            onClick={() => setActiveTab(\"personal\")}\r\n            className={cn(\r\n              \"sm:text-[1rem] text-sm cursor-pointer transition-colors\",\r\n              activeTab === \"personal\"\r\n                ? \"font-medium text-primary\"\r\n                : \"text-gray-600/70 hover:text-primary\"\r\n            )}\r\n          >\r\n            Personal information\r\n          </div>\r\n          <div\r\n            onClick={() => setActiveTab(\"educational\")}\r\n            className={cn(\r\n              \"sm:text-[1rem] text-sm cursor-pointer transition-colors\",\r\n              activeTab === \"educational\"\r\n                ? \"font-medium text-primary\"\r\n                : \"text-gray-600/70 hover:text-primary\"\r\n            )}\r\n          >\r\n            Educational background\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white rounded-xl flex-1\">\r\n          {activeTab === \"personal\" ? (\r\n            <PersonalInfoForm onSuccess={onClose} />\r\n          ) : (\r\n            <EducationalInfoForm onSuccess={onClose} />\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAeO,SAAS,cAAc,EAC5B,aAAa,UAAU,EACvB,OAAO,EACY;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEhD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;oBAClD,yBACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2DACA,cAAc,aACV,6BACA;0CAEP;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2DACA,cAAc,gBACV,6BACA;0CAEP;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;kCACZ,cAAc,2BACb,8OAAC,oKAAA,CAAA,mBAAgB;4BAAC,WAAW;;;;;iDAE7B,8OAAC,uKAAA,CAAA,sBAAmB;4BAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAM5C"}}, {"offset": {"line": 3810, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3816, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,gBAAgB,mKAAgB,OAAO;AAE7C,MAAM,eAAe,mKAAgB,MAAM;AAE3C,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,mKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,mKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,mKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 3942, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3948, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/delete-account-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  Di<PERSON><PERSON>ooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@components/ui/dialog\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { AlertTriangle, Loader2 } from \"lucide-react\";\r\n\r\ninterface DeleteAccountDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  isLoading?: boolean;\r\n  userType: \"student\" | \"counselor\";\r\n  errorMessage?: string;\r\n  cannotDeleteReason?: {\r\n    message: string;\r\n    upcoming_sessions?: Array<{\r\n      id: number;\r\n      event_name: string;\r\n      start_time: string;\r\n      end_time: string;\r\n    }>;\r\n    active_packages?: Array<{\r\n      id: number;\r\n      package_id: number;\r\n      status: string;\r\n      start_date?: string;\r\n      end_date?: string;\r\n    }>;\r\n    pending_payments?: Array<{\r\n      id: number;\r\n      amount: number;\r\n      service_type: string;\r\n      created_at: string;\r\n    }>;\r\n    count: number;\r\n  };\r\n}\r\n\r\nexport const DeleteAccountDialog: React.FC<DeleteAccountDialogProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  isLoading = false,\r\n  userType,\r\n  errorMessage,\r\n  cannotDeleteReason,\r\n}) => {\r\n  const canDelete = !cannotDeleteReason;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2 text-red-600\">\r\n            <AlertTriangle className=\"h-5 w-5\" />\r\n            Delete Account\r\n          </DialogTitle>\r\n          <div className=\"py-3 text-left space-y-2\">\r\n            {canDelete ? (\r\n              <>\r\n                Are you sure you want to delete your {userType} account? This\r\n                action cannot be undone.\r\n              </>\r\n            ) : (\r\n              <>\r\n                <strong className=\"text-red-600\">Cannot delete account</strong>\r\n                <h3>{cannotDeleteReason.message}</h3>\r\n                {cannotDeleteReason.upcoming_sessions &&\r\n                  cannotDeleteReason.upcoming_sessions.length > 0 && (\r\n                    <div className=\"mt-3\">\r\n                      <strong>\r\n                        Upcoming Sessions (\r\n                        {cannotDeleteReason.upcoming_sessions.length}):\r\n                      </strong>\r\n                      <ul className=\"list-disc list-inside mt-1 space-y-1 text-sm\">\r\n                        {cannotDeleteReason.upcoming_sessions\r\n                          .slice(0, 3)\r\n                          .map((session) => (\r\n                            <li key={session.id}>\r\n                              {session.event_name} -{\" \"}\r\n                              {new Date(\r\n                                session.start_time\r\n                              ).toLocaleDateString()}\r\n                            </li>\r\n                          ))}\r\n                        {cannotDeleteReason.upcoming_sessions.length > 3 && (\r\n                          <li>\r\n                            ...and{\" \"}\r\n                            {cannotDeleteReason.upcoming_sessions.length - 3}{\" \"}\r\n                            more\r\n                          </li>\r\n                        )}\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n\r\n                {cannotDeleteReason.active_packages &&\r\n                  cannotDeleteReason.active_packages.length > 0 && (\r\n                    <div className=\"mt-3\">\r\n                      <strong>\r\n                        Active Packages (\r\n                        {cannotDeleteReason.active_packages.length}):\r\n                      </strong>\r\n                      <ul className=\"list-disc list-inside mt-1 space-y-1 text-sm\">\r\n                        {cannotDeleteReason.active_packages\r\n                          .slice(0, 3)\r\n                          .map((pkg) => (\r\n                            <li key={pkg.id}>\r\n                              Package #{pkg.package_id} - Status: {pkg.status}\r\n                            </li>\r\n                          ))}\r\n                        {cannotDeleteReason.active_packages.length > 3 && (\r\n                          <li>\r\n                            ...and{\" \"}\r\n                            {cannotDeleteReason.active_packages.length - 3} more\r\n                          </li>\r\n                        )}\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n\r\n                {cannotDeleteReason.pending_payments &&\r\n                  cannotDeleteReason.pending_payments.length > 0 && (\r\n                    <div className=\"mt-3\">\r\n                      <strong>\r\n                        Pending Payments (\r\n                        {cannotDeleteReason.pending_payments.length}):\r\n                      </strong>\r\n                      <ul className=\"list-disc list-inside mt-1 space-y-1 text-sm\">\r\n                        {cannotDeleteReason.pending_payments\r\n                          .slice(0, 3)\r\n                          .map((payment) => (\r\n                            <li key={payment.id}>\r\n                              ${payment.amount} - {payment.service_type}\r\n                            </li>\r\n                          ))}\r\n                        {cannotDeleteReason.pending_payments.length > 3 && (\r\n                          <li>\r\n                            ...and{\" \"}\r\n                            {cannotDeleteReason.pending_payments.length - 3}{\" \"}\r\n                            more\r\n                          </li>\r\n                        )}\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n              </>\r\n            )}\r\n          </div>\r\n        </DialogHeader>\r\n\r\n        {errorMessage && (\r\n          <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\r\n            <p className=\"text-sm text-red-600\">{errorMessage}</p>\r\n          </div>\r\n        )}\r\n\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={onClose} disabled={isLoading}>\r\n            Cancel\r\n          </Button>\r\n          {canDelete && (\r\n            <Button\r\n              variant=\"destructive\"\r\n              onClick={onConfirm}\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? (\r\n                <>\r\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                  Deleting...\r\n                </>\r\n              ) : (\r\n                \"Delete Account\"\r\n              )}\r\n            </Button>\r\n          )}\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAQA;AACA;AAAA;AAZA;;;;;AA8CO,MAAM,sBAA0D,CAAC,EACtE,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,QAAQ,EACR,YAAY,EACZ,kBAAkB,EACnB;IACC,MAAM,YAAY,CAAC;IAEnB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;sCACZ,0BACC;;oCAAE;oCACsC;oCAAS;;6DAIjD;;kDACE,8OAAC;wCAAO,WAAU;kDAAe;;;;;;kDACjC,8OAAC;kDAAI,mBAAmB,OAAO;;;;;;oCAC9B,mBAAmB,iBAAiB,IACnC,mBAAmB,iBAAiB,CAAC,MAAM,GAAG,mBAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAO;oDAEL,mBAAmB,iBAAiB,CAAC,MAAM;oDAAC;;;;;;;0DAE/C,8OAAC;gDAAG,WAAU;;oDACX,mBAAmB,iBAAiB,CAClC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,wBACJ,8OAAC;;gEACE,QAAQ,UAAU;gEAAC;gEAAG;gEACtB,IAAI,KACH,QAAQ,UAAU,EAClB,kBAAkB;;2DAJb,QAAQ,EAAE;;;;;oDAOtB,mBAAmB,iBAAiB,CAAC,MAAM,GAAG,mBAC7C,8OAAC;;4DAAG;4DACK;4DACN,mBAAmB,iBAAiB,CAAC,MAAM,GAAG;4DAAG;4DAAI;;;;;;;;;;;;;;;;;;;oCAQjE,mBAAmB,eAAe,IACjC,mBAAmB,eAAe,CAAC,MAAM,GAAG,mBAC1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAO;oDAEL,mBAAmB,eAAe,CAAC,MAAM;oDAAC;;;;;;;0DAE7C,8OAAC;gDAAG,WAAU;;oDACX,mBAAmB,eAAe,CAChC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,oBACJ,8OAAC;;gEAAgB;gEACL,IAAI,UAAU;gEAAC;gEAAY,IAAI,MAAM;;2DADxC,IAAI,EAAE;;;;;oDAIlB,mBAAmB,eAAe,CAAC,MAAM,GAAG,mBAC3C,8OAAC;;4DAAG;4DACK;4DACN,mBAAmB,eAAe,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;oCAO1D,mBAAmB,gBAAgB,IAClC,mBAAmB,gBAAgB,CAAC,MAAM,GAAG,mBAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAO;oDAEL,mBAAmB,gBAAgB,CAAC,MAAM;oDAAC;;;;;;;0DAE9C,8OAAC;gDAAG,WAAU;;oDACX,mBAAmB,gBAAgB,CACjC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,wBACJ,8OAAC;;gEAAoB;gEACjB,QAAQ,MAAM;gEAAC;gEAAI,QAAQ,YAAY;;2DADlC,QAAQ,EAAE;;;;;oDAItB,mBAAmB,gBAAgB,CAAC,MAAM,GAAG,mBAC5C,8OAAC;;4DAAG;4DACK;4DACN,mBAAmB,gBAAgB,CAAC,MAAM,GAAG;4DAAG;4DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAYxE,8BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;8BAIzC,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAW;;;;;;wBAGhE,2BACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;sCAET,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;;;;;;;;;;;;;;;;;;AAQhB"}}, {"offset": {"line": 4253, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4259, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/profile/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { useAuth } from \"@/app/hooks/useAuth\";\r\nimport { ProfileHeader } from \"./profile-header\";\r\nimport { GoalsBlock } from \"./goals\";\r\nimport { ProfileInfoGrid } from \"./profile-info\";\r\nimport { EducationBlock } from \"./education\";\r\nimport { ProfileEditor } from \"./edit-mode\";\r\nimport { DeleteAccountDialog } from \"@/app/components/common/delete-account-dialog\";\r\nimport { toast } from \"react-toastify\";\r\nimport apiClient from \"@/lib/apiClient\";\r\n\r\ntype EditModeState = {\r\n  isEditing: boolean;\r\n  initialTab: \"personal\" | \"educational\";\r\n};\r\n\r\nexport default function Profile() {\r\n  const router = useRouter();\r\n  const { logout } = useAuth();\r\n  const {\r\n    loading,\r\n    userInfo,\r\n    personalInfo,\r\n    educationInfo,\r\n    fetchUserInfo,\r\n    fetchPersonalInfo,\r\n    fetchEducationalBackground,\r\n  } = useProfile();\r\n\r\n  const [editMode, setEditMode] = useState<EditModeState>({\r\n    isEditing: false,\r\n    initialTab: \"personal\",\r\n  });\r\n\r\n  const [deleteDialog, setDeleteDialog] = useState({\r\n    isOpen: false,\r\n    isLoading: false,\r\n    errorMessage: \"\",\r\n    cannotDeleteReason: null as any,\r\n  });\r\n\r\n  useEffect(() => {\r\n    fetchUserInfo();\r\n    fetchPersonalInfo();\r\n    fetchEducationalBackground();\r\n  }, [fetchUserInfo, fetchPersonalInfo, fetchEducationalBackground]);\r\n\r\n  const handleEditClick = (section: \"personal\" | \"educational\") => {\r\n    setEditMode({\r\n      isEditing: true,\r\n      initialTab: section,\r\n    });\r\n  };\r\n\r\n  const handleDeleteAccount = () => {\r\n    setDeleteDialog({\r\n      isOpen: true,\r\n      isLoading: false,\r\n      errorMessage: \"\",\r\n      cannotDeleteReason: null,\r\n    });\r\n  };\r\n\r\n  const handleDeleteConfirm = async () => {\r\n    setDeleteDialog((prev) => ({ ...prev, isLoading: true, errorMessage: \"\" }));\r\n\r\n    try {\r\n      await apiClient.delete(\"/user/delete-account\");\r\n\r\n      // Success - logout and redirect\r\n      toast.success(\"Account deleted successfully\");\r\n      logout();\r\n      router.push(\"/auth/signup\");\r\n    } catch (error: any) {\r\n      console.error(\"Delete account error:\", error);\r\n\r\n      if (error.response?.status === 400) {\r\n        // Cannot delete account - show reason\r\n        const errorDetail = error.response.data.detail;\r\n        setDeleteDialog((prev) => ({\r\n          ...prev,\r\n          isLoading: false,\r\n          cannotDeleteReason: errorDetail,\r\n        }));\r\n      } else {\r\n        // Other error\r\n        setDeleteDialog((prev) => ({\r\n          ...prev,\r\n          isLoading: false,\r\n          errorMessage:\r\n            error.response?.data?.detail ||\r\n            \"Failed to delete account. Please try again.\",\r\n        }));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleDeleteCancel = () => {\r\n    setDeleteDialog({\r\n      isOpen: false,\r\n      isLoading: false,\r\n      errorMessage: \"\",\r\n      cannotDeleteReason: null,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <div className=\"max-w-7xl mx-auto bg-white rounded-xl p-4 sm:p-6 lg:p-8 flex flex-col gap-y-4 lg:gap-y-6\">\r\n        {editMode.isEditing ? (\r\n          <ProfileEditor\r\n            initialTab={editMode.initialTab}\r\n            onClose={() =>\r\n              setEditMode({ isEditing: false, initialTab: \"personal\" })\r\n            }\r\n          />\r\n        ) : (\r\n          <>\r\n            <ProfileHeader\r\n              userInfo={userInfo}\r\n              educationInfo={educationInfo}\r\n              onEditClick={() => handleEditClick(\"personal\")}\r\n              onDeleteAccount={handleDeleteAccount}\r\n            />\r\n            <ProfileInfoGrid\r\n              userInfo={userInfo}\r\n              personalInfo={personalInfo}\r\n              loading={loading}\r\n            />\r\n            <GoalsBlock\r\n              loading={loading}\r\n              personalInfo={personalInfo}\r\n              onEditClick={() => handleEditClick(\"personal\")}\r\n            />\r\n            <EducationBlock\r\n              educationInfo={educationInfo}\r\n              onEditClick={() => handleEditClick(\"educational\")}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      <DeleteAccountDialog\r\n        isOpen={deleteDialog.isOpen}\r\n        onClose={handleDeleteCancel}\r\n        onConfirm={handleDeleteConfirm}\r\n        isLoading={deleteDialog.isLoading}\r\n        userType=\"student\"\r\n        errorMessage={deleteDialog.errorMessage}\r\n        cannotDeleteReason={deleteDialog.cannotDeleteReason}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACzB,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,0BAA0B,EAC3B,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,WAAW;QACX,YAAY;IACd;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,QAAQ;QACR,WAAW;QACX,cAAc;QACd,oBAAoB;IACtB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QACA;IACF,GAAG;QAAC;QAAe;QAAmB;KAA2B;IAEjE,MAAM,kBAAkB,CAAC;QACvB,YAAY;YACV,WAAW;YACX,YAAY;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;YACd,QAAQ;YACR,WAAW;YACX,cAAc;YACd,oBAAoB;QACtB;IACF;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;gBAAM,cAAc;YAAG,CAAC;QAEzE,IAAI;YACF,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CAAC;YAEvB,gCAAgC;YAChC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,sCAAsC;gBACtC,MAAM,cAAc,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;gBAC9C,gBAAgB,CAAC,OAAS,CAAC;wBACzB,GAAG,IAAI;wBACP,WAAW;wBACX,oBAAoB;oBACtB,CAAC;YACH,OAAO;gBACL,cAAc;gBACd,gBAAgB,CAAC,OAAS,CAAC;wBACzB,GAAG,IAAI;wBACP,WAAW;wBACX,cACE,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,CAAC;YACH;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,gBAAgB;YACd,QAAQ;YACR,WAAW;YACX,cAAc;YACd,oBAAoB;QACtB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,SAAS,SAAS,iBACjB,8OAAC,wJAAA,CAAA,gBAAa;oBACZ,YAAY,SAAS,UAAU;oBAC/B,SAAS,IACP,YAAY;4BAAE,WAAW;4BAAO,YAAY;wBAAW;;;;;yCAI3D;;sCACE,8OAAC,6JAAA,CAAA,gBAAa;4BACZ,UAAU;4BACV,eAAe;4BACf,aAAa,IAAM,gBAAgB;4BACnC,iBAAiB;;;;;;sCAEnB,8OAAC,2JAAA,CAAA,kBAAe;4BACd,UAAU;4BACV,cAAc;4BACd,SAAS;;;;;;sCAEX,8OAAC,iJAAA,CAAA,aAAU;4BACT,SAAS;4BACT,cAAc;4BACd,aAAa,IAAM,gBAAgB;;;;;;sCAErC,8OAAC,qJAAA,CAAA,iBAAc;4BACb,eAAe;4BACf,aAAa,IAAM,gBAAgB;;;;;;;;;;;;;0BAM3C,8OAAC,2JAAA,CAAA,sBAAmB;gBAClB,QAAQ,aAAa,MAAM;gBAC3B,SAAS;gBACT,WAAW;gBACX,WAAW,aAAa,SAAS;gBACjC,UAAS;gBACT,cAAc,aAAa,YAAY;gBACvC,oBAAoB,aAAa,kBAAkB;;;;;;;;;;;;AAI3D"}}, {"offset": {"line": 4446, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4452, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/profile/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Profile from \"@/app/components/student/profile\"\r\n\r\nexport default function ProfilePage() {\r\n  return <Profile />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,iJAAA,CAAA,UAAO;;;;;AACjB"}}, {"offset": {"line": 4467, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
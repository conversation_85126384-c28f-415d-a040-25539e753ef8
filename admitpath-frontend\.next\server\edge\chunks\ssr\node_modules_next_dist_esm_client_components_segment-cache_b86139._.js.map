{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/scheduler.ts"], "sourcesContent": ["import type { TreePrefetch } from '../../../server/app-render/collect-segment-data'\nimport {\n  requestRouteCacheEntryFromCache,\n  requestSegmentEntryFromCache,\n  EntryStatus,\n  type FulfilledRouteCacheEntry,\n  type RouteCacheEntry,\n} from './cache'\nimport type { RouteCacheKey } from './cache-key'\n\nconst scheduleMicrotask =\n  typeof queueMicrotask === 'function'\n    ? queueMicrotask\n    : (fn: () => unknown) =>\n        Promise.resolve()\n          .then(fn)\n          .catch((error) =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n\nexport type PrefetchTask = {\n  key: RouteCacheKey\n\n  /**\n   * sortId is an incrementing counter\n   *\n   * Newer prefetches are prioritized over older ones, so that as new links\n   * enter the viewport, they are not starved by older links that are no\n   * longer relevant. In the future, we can add additional prioritization\n   * heuristics, like removing prefetches once a link leaves the viewport.\n   *\n   * The sortId is assigned when the prefetch is initiated, and reassigned if\n   * the same URL is prefetched again (effectively bumping it to the top of\n   * the queue).\n   *\n   * TODO: We can add additional fields here to indicate what kind of prefetch\n   * it is. For example, was it initiated by a link? Or was it an imperative\n   * call? If it was initiated by a link, we can remove it from the queue when\n   * the link leaves the viewport, but if it was an imperative call, then we\n   * should keep it in the queue until it's fulfilled.\n   *\n   * We can also add priority levels. For example, hovering over a link could\n   * increase the priority of its prefetch.\n   */\n  sortId: number\n\n  /**\n   * True if the prefetch is blocked by network data. We remove tasks from the\n   * queue once they are blocked, and add them back when they receive data.\n   *\n   * isBlocked also indicates whether the task is currently in the queue; tasks\n   * are removed from the queue when they are blocked. Use this to avoid\n   * queueing the same task multiple times.\n   */\n  isBlocked: boolean\n\n  /**\n   * The index of the task in the heap's backing array. Used to efficiently\n   * change the priority of a task by re-sifting it, which requires knowing\n   * where it is in the array. This is only used internally by the heap\n   * algorithm. The naive alternative is indexOf every time a task is queued,\n   * which has O(n) complexity.\n   */\n  _heapIndex: number\n}\n\nconst enum PrefetchTaskExitStatus {\n  /**\n   * The task yielded because there are too many requests in progress.\n   */\n  InProgress,\n\n  /**\n   * The task is blocked. It needs more data before it can proceed.\n   *\n   * Currently the only reason this happens is we're still waiting to receive a\n   * route tree from the server, because we can't start prefetching the segments\n   * until we know what to prefetch.\n   */\n  Blocked,\n\n  /**\n   * There's nothing left to prefetch.\n   */\n  Done,\n}\n\nconst taskHeap: Array<PrefetchTask> = []\n\n// This is intentionally low so that when a navigation happens, the browser's\n// internal network queue is not already saturated with prefetch requests.\nconst MAX_CONCURRENT_PREFETCH_REQUESTS = 3\nlet inProgressRequests = 0\n\nlet sortIdCounter = 0\nlet didScheduleMicrotask = false\n\n/**\n * Initiates a prefetch task for the given URL. If a prefetch for the same URL\n * is already in progress, this will bump it to the top of the queue.\n *\n * This is not a user-facing function. By the time this is called, the href is\n * expected to be validated and normalized.\n *\n * @param key The RouteCacheKey to prefetch.\n */\nexport function schedulePrefetchTask(key: RouteCacheKey): void {\n  // Spawn a new prefetch task\n  const task: PrefetchTask = {\n    key,\n    sortId: sortIdCounter++,\n    isBlocked: false,\n    _heapIndex: -1,\n  }\n  heapPush(taskHeap, task)\n\n  // Schedule an async task to process the queue.\n  //\n  // The main reason we process the queue in an async task is for batching.\n  // It's common for a single JS task/event to trigger multiple prefetches.\n  // By deferring to a microtask, we only process the queue once per JS task.\n  // If they have different priorities, it also ensures they are processed in\n  // the optimal order.\n  ensureWorkIsScheduled()\n}\n\nfunction ensureWorkIsScheduled() {\n  if (didScheduleMicrotask || !hasNetworkBandwidth()) {\n    // Either we already scheduled a task to process the queue, or there are\n    // too many concurrent requests in progress. In the latter case, the\n    // queue will resume processing once more bandwidth is available.\n    return\n  }\n  didScheduleMicrotask = true\n  scheduleMicrotask(processQueueInMicrotask)\n}\n\n/**\n * Checks if we've exceeded the maximum number of concurrent prefetch requests,\n * to avoid saturating the browser's internal network queue. This is a\n * cooperative limit — prefetch tasks should check this before issuing\n * new requests.\n */\nfunction hasNetworkBandwidth(): boolean {\n  // TODO: Also check if there's an in-progress navigation. We should never\n  // add prefetch requests to the network queue if an actual navigation is\n  // taking place, to ensure there's sufficient bandwidth for render-blocking\n  // data and resources.\n  return inProgressRequests < MAX_CONCURRENT_PREFETCH_REQUESTS\n}\n\n/**\n * Notifies the scheduler of an in-progress prefetch request. This is used to\n * control network bandwidth by limiting the number of concurrent requests.\n *\n * @param promise A promise that resolves when the request has finished.\n */\nexport function trackPrefetchRequestBandwidth(\n  promiseForServerData: Promise<unknown>\n) {\n  inProgressRequests++\n  promiseForServerData.then(\n    onPrefetchRequestCompletion,\n    onPrefetchRequestCompletion\n  )\n}\n\nconst noop = () => {}\n\nexport function spawnPrefetchSubtask(promise: Promise<any>) {\n  // When the scheduler spawns an async task, we don't await its result\n  // directly. Instead, the async task writes its result directly into the\n  // cache, then pings the scheduler to continue.\n  //\n  // This function only exists to prevent warnings about unhandled promises.\n  promise.then(noop, noop)\n}\n\nfunction onPrefetchRequestCompletion(): void {\n  inProgressRequests--\n\n  // Notify the scheduler that we have more bandwidth, and can continue\n  // processing tasks.\n  ensureWorkIsScheduled()\n}\n\n/**\n * Notify the scheduler that we've received new data for an in-progress\n * prefetch. The corresponding task will be added back to the queue (unless the\n * task has been canceled in the meantime).\n */\nexport function pingPrefetchTask(task: PrefetchTask) {\n  // \"Ping\" a prefetch that's already in progress to notify it of new data.\n  if (!task.isBlocked) {\n    // Prefetch is already queued.\n    return\n  }\n  // Unblock the task and requeue it.\n  task.isBlocked = false\n  heapPush(taskHeap, task)\n  ensureWorkIsScheduled()\n}\n\nfunction processQueueInMicrotask() {\n  didScheduleMicrotask = false\n\n  // We aim to minimize how often we read the current time. Since nearly all\n  // functions in the prefetch scheduler are synchronous, we can read the time\n  // once and pass it as an argument wherever it's needed.\n  const now = Date.now()\n\n  // Process the task queue until we run out of network bandwidth.\n  let task = heapPeek(taskHeap)\n  while (task !== null && hasNetworkBandwidth()) {\n    const route = requestRouteCacheEntryFromCache(now, task)\n    const exitStatus = pingRouteTree(now, task, route)\n    switch (exitStatus) {\n      case PrefetchTaskExitStatus.InProgress:\n        // The task yielded because there are too many requests in progress.\n        // Stop processing tasks until we have more bandwidth.\n        return\n      case PrefetchTaskExitStatus.Blocked:\n        // The task is blocked. It needs more data before it can proceed.\n        // Keep the task out of the queue until the server responds.\n        task.isBlocked = true\n\n        // Continue to the next task\n        heapPop(taskHeap)\n        task = heapPeek(taskHeap)\n        continue\n      case PrefetchTaskExitStatus.Done:\n        // The prefetch is complete. Continue to the next task.\n        heapPop(taskHeap)\n        task = heapPeek(taskHeap)\n        continue\n      default: {\n        const _exhaustiveCheck: never = exitStatus\n        return\n      }\n    }\n  }\n}\n\nfunction pingRouteTree(\n  now: number,\n  task: PrefetchTask,\n  route: RouteCacheEntry\n): PrefetchTaskExitStatus {\n  switch (route.status) {\n    case EntryStatus.Pending: {\n      // Still pending. We can't start prefetching the segments until the route\n      // tree has loaded.\n      const blockedTasks = route.blockedTasks\n      if (blockedTasks === null) {\n        route.blockedTasks = new Set([task])\n      } else {\n        blockedTasks.add(task)\n      }\n      return PrefetchTaskExitStatus.Blocked\n    }\n    case EntryStatus.Rejected: {\n      // Route tree failed to load. Treat as a 404.\n      return PrefetchTaskExitStatus.Done\n    }\n    case EntryStatus.Fulfilled: {\n      // Recursively fill in the segment tree.\n      if (!hasNetworkBandwidth()) {\n        // Stop prefetching segments until there's more bandwidth.\n        return PrefetchTaskExitStatus.InProgress\n      }\n      const tree = route.tree\n      requestSegmentEntryFromCache(now, task, route, tree.path, '')\n      return pingSegmentTree(now, task, route, tree)\n    }\n    default: {\n      const _exhaustiveCheck: never = route\n      return PrefetchTaskExitStatus.Done\n    }\n  }\n}\n\nfunction pingSegmentTree(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  tree: TreePrefetch\n): PrefetchTaskExitStatus.InProgress | PrefetchTaskExitStatus.Done {\n  if (tree.slots !== null) {\n    // Recursively ping the children.\n    for (const parallelRouteKey in tree.slots) {\n      const childTree = tree.slots[parallelRouteKey]\n      if (!hasNetworkBandwidth()) {\n        // Stop prefetching segments until there's more bandwidth.\n        return PrefetchTaskExitStatus.InProgress\n      } else {\n        const childPath = childTree.path\n        const childToken = childTree.token\n        requestSegmentEntryFromCache(now, task, route, childPath, childToken)\n      }\n      const childExitStatus = pingSegmentTree(now, task, route, childTree)\n      if (childExitStatus === PrefetchTaskExitStatus.InProgress) {\n        // Child yielded without finishing.\n        return PrefetchTaskExitStatus.InProgress\n      }\n    }\n  }\n  // This segment and all its children have finished prefetching.\n  return PrefetchTaskExitStatus.Done\n}\n\n// -----------------------------------------------------------------------------\n// The remainider of the module is a MinHeap implementation. Try not to put any\n// logic below here unless it's related to the heap algorithm. We can extract\n// this to a separate module if/when we need multiple kinds of heaps.\n// -----------------------------------------------------------------------------\n\nfunction compareQueuePriority(a: PrefetchTask, b: PrefetchTask) {\n  // Since the queue is a MinHeap, this should return a positive number if b is\n  // higher priority than a, and a negative number if a is higher priority\n  // than b.\n  //\n  // sortId is an incrementing counter assigned to prefetches. We want to\n  // process the newest prefetches first.\n  return b.sortId - a.sortId\n}\n\nfunction heapPush(heap: Array<PrefetchTask>, node: PrefetchTask): void {\n  const index = heap.length\n  heap.push(node)\n  node._heapIndex = index\n  heapSiftUp(heap, node, index)\n}\n\nfunction heapPeek(heap: Array<PrefetchTask>): PrefetchTask | null {\n  return heap.length === 0 ? null : heap[0]\n}\n\nfunction heapPop(heap: Array<PrefetchTask>): PrefetchTask | null {\n  if (heap.length === 0) {\n    return null\n  }\n  const first = heap[0]\n  first._heapIndex = -1\n  const last = heap.pop() as PrefetchTask\n  if (last !== first) {\n    heap[0] = last\n    last._heapIndex = 0\n    heapSiftDown(heap, last, 0)\n  }\n  return first\n}\n\n// Not currently used, but will be once we add the ability to update a\n// task's priority.\n// function heapSift(heap: Array<PrefetchTask>, node: PrefetchTask) {\n//   const index = node._heapIndex\n//   if (index !== -1) {\n//     const parentIndex = (index - 1) >>> 1\n//     const parent = heap[parentIndex]\n//     if (compareQueuePriority(parent, node) > 0) {\n//       // The parent is larger. Sift up.\n//       heapSiftUp(heap, node, index)\n//     } else {\n//       // The parent is smaller (or equal). Sift down.\n//       heapSiftDown(heap, node, index)\n//     }\n//   }\n// }\n\nfunction heapSiftUp(\n  heap: Array<PrefetchTask>,\n  node: PrefetchTask,\n  i: number\n): void {\n  let index = i\n  while (index > 0) {\n    const parentIndex = (index - 1) >>> 1\n    const parent = heap[parentIndex]\n    if (compareQueuePriority(parent, node) > 0) {\n      // The parent is larger. Swap positions.\n      heap[parentIndex] = node\n      node._heapIndex = parentIndex\n      heap[index] = parent\n      parent._heapIndex = index\n\n      index = parentIndex\n    } else {\n      // The parent is smaller. Exit.\n      return\n    }\n  }\n}\n\nfunction heapSiftDown(\n  heap: Array<PrefetchTask>,\n  node: PrefetchTask,\n  i: number\n): void {\n  let index = i\n  const length = heap.length\n  const halfLength = length >>> 1\n  while (index < halfLength) {\n    const leftIndex = (index + 1) * 2 - 1\n    const left = heap[leftIndex]\n    const rightIndex = leftIndex + 1\n    const right = heap[rightIndex]\n\n    // If the left or right node is smaller, swap with the smaller of those.\n    if (compareQueuePriority(left, node) < 0) {\n      if (rightIndex < length && compareQueuePriority(right, left) < 0) {\n        heap[index] = right\n        right._heapIndex = index\n        heap[rightIndex] = node\n        node._heapIndex = rightIndex\n\n        index = rightIndex\n      } else {\n        heap[index] = left\n        left._heapIndex = index\n        heap[leftIndex] = node\n        node._heapIndex = leftIndex\n\n        index = leftIndex\n      }\n    } else if (rightIndex < length && compareQueuePriority(right, node) < 0) {\n      heap[index] = right\n      right._heapIndex = index\n      heap[rightIndex] = node\n      node._heapIndex = rightIndex\n\n      index = rightIndex\n    } else {\n      // Neither child is smaller. Exit.\n      return\n    }\n  }\n}\n"], "names": ["requestRouteCacheEntryFromCache", "requestSegmentEntryFromCache", "EntryStatus", "scheduleMicrotask", "queueMicrotask", "fn", "Promise", "resolve", "then", "catch", "error", "setTimeout", "taskHeap", "MAX_CONCURRENT_PREFETCH_REQUESTS", "inProgressRequests", "sortIdCounter", "didScheduleMicrotask", "schedulePrefetchTask", "key", "task", "sortId", "isBlocked", "_heapIndex", "heapPush", "ensureWorkIsScheduled", "hasNetworkBandwidth", "processQueueInMicrotask", "trackPrefetchRequestBandwidth", "promiseForServerData", "onPrefetchRequestCompletion", "noop", "spawnPrefetchSubtask", "promise", "pingPrefetchTask", "now", "Date", "heapPeek", "route", "exitStatus", "pingRouteTree", "heapPop", "_exhaustiveCheck", "status", "Pending", "blockedTasks", "Set", "add", "Rejected", "Fulfilled", "tree", "path", "pingSegmentTree", "slots", "parallelRouteKey", "childTree", "child<PERSON><PERSON>", "childToken", "token", "childExitStatus", "compareQueuePriority", "a", "b", "heap", "node", "index", "length", "push", "heapSiftUp", "first", "last", "pop", "heapSiftDown", "i", "parentIndex", "parent", "<PERSON><PERSON><PERSON><PERSON>", "leftIndex", "left", "rightIndex", "right"], "mappings": ";;;;;;AACA,SACEA,+BAA+B,EAC/BC,4BAA4B,EAC5BC,WAAW,QAGN,UAAS;;AAGhB,MAAMC,oBACJ,OAAOC,mBAAmB,aACtBA,iBACA,CAACC,KACCC,QAAQC,OAAO,GACZC,IAAI,CAACH,IACLI,KAAK,CAAC,CAACC,QACNC,WAAW;YACT,MAAMD;QACR;;AAsEZ,MAAME,WAAgC,EAAE;AAExC,6EAA6E;AAC7E,0EAA0E;AAC1E,MAAMC,mCAAmC;AACzC,IAAIC,qBAAqB;AAEzB,IAAIC,gBAAgB;AACpB,IAAIC,uBAAuB;AAWpB,SAASC,qBAAqBC,GAAkB;IACrD,4BAA4B;IAC5B,MAAMC,OAAqB;QACzBD;QACAE,QAAQL;QACRM,WAAW;QACXC,YAAY,CAAC;IACf;IACAC,SAASX,UAAUO;IAEnB,+CAA+C;IAC/C,EAAE;IACF,yEAAyE;IACzE,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,qBAAqB;IACrBK;AACF;AAEA,SAASA;IACP,IAAIR,wBAAwB,CAACS,uBAAuB;QAClD,wEAAwE;QACxE,oEAAoE;QACpE,iEAAiE;QACjE;IACF;IACAT,uBAAuB;IACvBb,kBAAkBuB;AACpB;AAEA;;;;;CAKC,GACD,SAASD;IACP,yEAAyE;IACzE,wEAAwE;IACxE,2EAA2E;IAC3E,sBAAsB;IACtB,OAAOX,qBAAqBD;AAC9B;AAQO,SAASc,8BACdC,oBAAsC;IAEtCd;IACAc,qBAAqBpB,IAAI,CACvBqB,6BACAA;AAEJ;AAEA,MAAMC,OAAO,KAAO;AAEb,SAASC,qBAAqBC,OAAqB;IACxD,qEAAqE;IACrE,wEAAwE;IACxE,+CAA+C;IAC/C,EAAE;IACF,0EAA0E;IAC1EA,QAAQxB,IAAI,CAACsB,MAAMA;AACrB;AAEA,SAASD;IACPf;IAEA,qEAAqE;IACrE,oBAAoB;IACpBU;AACF;AAOO,SAASS,iBAAiBd,IAAkB;IACjD,yEAAyE;IACzE,IAAI,CAACA,KAAKE,SAAS,EAAE;QACnB,8BAA8B;QAC9B;IACF;IACA,mCAAmC;IACnCF,KAAKE,SAAS,GAAG;IACjBE,SAASX,UAAUO;IACnBK;AACF;AAEA,SAASE;IACPV,uBAAuB;IAEvB,0EAA0E;IAC1E,4EAA4E;IAC5E,wDAAwD;IACxD,MAAMkB,MAAMC,KAAKD,GAAG;IAEpB,gEAAgE;IAChE,IAAIf,OAAOiB,SAASxB;IACpB,MAAOO,SAAS,QAAQM,sBAAuB;QAC7C,MAAMY,QAAQrC,uOAAAA,EAAgCkC,KAAKf;QACnD,MAAMmB,aAAaC,cAAcL,KAAKf,MAAMkB;QAC5C,OAAQC;YACN,KAAA;gBACE,oEAAoE;gBACpE,sDAAsD;gBACtD;YACF,KAAA;gBACE,iEAAiE;gBACjE,4DAA4D;gBAC5DnB,KAAKE,SAAS,GAAG;gBAEjB,4BAA4B;gBAC5BmB,QAAQ5B;gBACRO,OAAOiB,SAASxB;gBAChB;YACF,KAAA;gBACE,uDAAuD;gBACvD4B,QAAQ5B;gBACRO,OAAOiB,SAASxB;gBAChB;YACF;gBAAS;oBACP,MAAM6B,mBAA0BH;oBAChC;gBACF;QACF;IACF;AACF;AAEA,SAASC,cACPL,GAAW,EACXf,IAAkB,EAClBkB,KAAsB;IAEtB,OAAQA,MAAMK,MAAM;QAClB,sMAAKxC,cAAAA,CAAYyC,OAAO;YAAE;gBACxB,yEAAyE;gBACzE,mBAAmB;gBACnB,MAAMC,eAAeP,MAAMO,YAAY;gBACvC,IAAIA,iBAAiB,MAAM;oBACzBP,MAAMO,YAAY,GAAG,IAAIC,IAAI;wBAAC1B;qBAAK;gBACrC,OAAO;oBACLyB,aAAaE,GAAG,CAAC3B;gBACnB;gBACA,OAAA;YACF;QACA,KAAKjB,+MAAAA,CAAY6C,QAAQ;YAAE;gBACzB,6CAA6C;gBAC7C,OAAA;YACF;QACA,sMAAK7C,cAAAA,CAAY8C,SAAS;YAAE;gBAC1B,wCAAwC;gBACxC,IAAI,CAACvB,uBAAuB;oBAC1B,0DAA0D;oBAC1D,OAAA;gBACF;gBACA,MAAMwB,OAAOZ,MAAMY,IAAI;qNACvBhD,+BAAAA,EAA6BiC,KAAKf,MAAMkB,OAAOY,KAAKC,IAAI,EAAE;gBAC1D,OAAOC,gBAAgBjB,KAAKf,MAAMkB,OAAOY;YAC3C;QACA;YAAS;gBACP,MAAMR,mBAA0BJ;gBAChC,OAAA;YACF;IACF;AACF;AAEA,SAASc,gBACPjB,GAAW,EACXf,IAAkB,EAClBkB,KAA+B,EAC/BY,IAAkB;IAElB,IAAIA,KAAKG,KAAK,KAAK,MAAM;QACvB,iCAAiC;QACjC,IAAK,MAAMC,oBAAoBJ,KAAKG,KAAK,CAAE;YACzC,MAAME,YAAYL,KAAKG,KAAK,CAACC,iBAAiB;YAC9C,IAAI,CAAC5B,uBAAuB;gBAC1B,0DAA0D;gBAC1D,OAAA;YACF,OAAO;gBACL,MAAM8B,YAAYD,UAAUJ,IAAI;gBAChC,MAAMM,aAAaF,UAAUG,KAAK;qNAClCxD,+BAAAA,EAA6BiC,KAAKf,MAAMkB,OAAOkB,WAAWC;YAC5D;YACA,MAAME,kBAAkBP,gBAAgBjB,KAAKf,MAAMkB,OAAOiB;YAC1D,IAAII,oBAAAA,GAAuD;gBACzD,mCAAmC;gBACnC,OAAA;YACF;QACF;IACF;IACA,+DAA+D;IAC/D,OAAA;AACF;AAEA,gFAAgF;AAChF,+EAA+E;AAC/E,6EAA6E;AAC7E,qEAAqE;AACrE,gFAAgF;AAEhF,SAASC,qBAAqBC,CAAe,EAAEC,CAAe;IAC5D,6EAA6E;IAC7E,wEAAwE;IACxE,UAAU;IACV,EAAE;IACF,uEAAuE;IACvE,uCAAuC;IACvC,OAAOA,EAAEzC,MAAM,GAAGwC,EAAExC,MAAM;AAC5B;AAEA,SAASG,SAASuC,IAAyB,EAAEC,IAAkB;IAC7D,MAAMC,QAAQF,KAAKG,MAAM;IACzBH,KAAKI,IAAI,CAACH;IACVA,KAAKzC,UAAU,GAAG0C;IAClBG,WAAWL,MAAMC,MAAMC;AACzB;AAEA,SAAS5B,SAAS0B,IAAyB;IACzC,OAAOA,KAAKG,MAAM,KAAK,IAAI,OAAOH,IAAI,CAAC,EAAE;AAC3C;AAEA,SAAStB,QAAQsB,IAAyB;IACxC,IAAIA,KAAKG,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IACA,MAAMG,QAAQN,IAAI,CAAC,EAAE;IACrBM,MAAM9C,UAAU,GAAG,CAAC;IACpB,MAAM+C,OAAOP,KAAKQ,GAAG;IACrB,IAAID,SAASD,OAAO;QAClBN,IAAI,CAAC,EAAE,GAAGO;QACVA,KAAK/C,UAAU,GAAG;QAClBiD,aAAaT,MAAMO,MAAM;IAC3B;IACA,OAAOD;AACT;AAEA,sEAAsE;AACtE,mBAAmB;AACnB,qEAAqE;AACrE,kCAAkC;AAClC,wBAAwB;AACxB,4CAA4C;AAC5C,uCAAuC;AACvC,oDAAoD;AACpD,0CAA0C;AAC1C,sCAAsC;AACtC,eAAe;AACf,wDAAwD;AACxD,wCAAwC;AACxC,QAAQ;AACR,MAAM;AACN,IAAI;AAEJ,SAASD,WACPL,IAAyB,EACzBC,IAAkB,EAClBS,CAAS;IAET,IAAIR,QAAQQ;IACZ,MAAOR,QAAQ,EAAG;QAChB,MAAMS,cAAeT,QAAQ,MAAO;QACpC,MAAMU,SAASZ,IAAI,CAACW,YAAY;QAChC,IAAId,qBAAqBe,QAAQX,QAAQ,GAAG;YAC1C,wCAAwC;YACxCD,IAAI,CAACW,YAAY,GAAGV;YACpBA,KAAKzC,UAAU,GAAGmD;YAClBX,IAAI,CAACE,MAAM,GAAGU;YACdA,OAAOpD,UAAU,GAAG0C;YAEpBA,QAAQS;QACV,OAAO;YACL,+BAA+B;YAC/B;QACF;IACF;AACF;AAEA,SAASF,aACPT,IAAyB,EACzBC,IAAkB,EAClBS,CAAS;IAET,IAAIR,QAAQQ;IACZ,MAAMP,SAASH,KAAKG,MAAM;IAC1B,MAAMU,aAAaV,WAAW;IAC9B,MAAOD,QAAQW,WAAY;QACzB,MAAMC,YAAaZ,CAAAA,QAAQ,CAAA,IAAK,IAAI;QACpC,MAAMa,OAAOf,IAAI,CAACc,UAAU;QAC5B,MAAME,aAAaF,YAAY;QAC/B,MAAMG,QAAQjB,IAAI,CAACgB,WAAW;QAE9B,wEAAwE;QACxE,IAAInB,qBAAqBkB,MAAMd,QAAQ,GAAG;YACxC,IAAIe,aAAab,UAAUN,qBAAqBoB,OAAOF,QAAQ,GAAG;gBAChEf,IAAI,CAACE,MAAM,GAAGe;gBACdA,MAAMzD,UAAU,GAAG0C;gBACnBF,IAAI,CAACgB,WAAW,GAAGf;gBACnBA,KAAKzC,UAAU,GAAGwD;gBAElBd,QAAQc;YACV,OAAO;gBACLhB,IAAI,CAACE,MAAM,GAAGa;gBACdA,KAAKvD,UAAU,GAAG0C;gBAClBF,IAAI,CAACc,UAAU,GAAGb;gBAClBA,KAAKzC,UAAU,GAAGsD;gBAElBZ,QAAQY;YACV;QACF,OAAO,IAAIE,aAAab,UAAUN,qBAAqBoB,OAAOhB,QAAQ,GAAG;YACvED,IAAI,CAACE,MAAM,GAAGe;YACdA,MAAMzD,UAAU,GAAG0C;YACnBF,IAAI,CAACgB,WAAW,GAAGf;YACnBA,KAAKzC,UAAU,GAAGwD;YAElBd,QAAQc;QACV,OAAO;YACL,kCAAkC;YAClC;QACF;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/tuple-map.ts"], "sourcesContent": ["// Utility type. Prefix<[A, B, C, D]> matches [A], [A, B], [A, B, C] etc.\nexport type Prefix<T extends any[]> = T extends [infer First, ...infer Rest]\n  ? [] | [First] | [First, ...Prefix<Rest>]\n  : []\n\nexport type TupleMap<Keypath extends Array<any>, V> = {\n  set(keys: Prefix<Keypath>, value: V): void\n  get(keys: Prefix<Keypath>): V | null\n  delete(keys: Prefix<Keypath>): void\n}\n\n/**\n * Creates a map whose keys are tuples. Tuples are compared per-element. This\n * is useful when a key has multiple parts, but you don't want to concatenate\n * them into a single string value.\n *\n * In the Segment Cache, we use this to store cache entries by both their href\n * and their Next-URL.\n *\n * Example:\n *   map.set(['https://localhost', 'foo/bar/baz'], 'yay');\n *   map.get(['https://localhost', 'foo/bar/baz']); // returns 'yay'\n */\nexport function createTupleMap<Keypath extends Array<any>, V>(): TupleMap<\n  Keypath,\n  V\n> {\n  type MapEntryShared = {\n    parent: MapEntry | null\n    key: any\n    map: Map<any, MapEntry> | null\n  }\n\n  type EmptyMapEntry = MapEntryShared & {\n    value: null\n    hasValue: false\n  }\n\n  type FullMapEntry = MapEntryShared & {\n    value: V\n    hasValue: true\n  }\n\n  type MapEntry = EmptyMapEntry | FullMapEntry\n\n  let rootEntry: MapEntry = {\n    parent: null,\n    key: null,\n    hasValue: false,\n    value: null,\n    map: null,\n  }\n\n  // To optimize successive lookups, we cache the last accessed keypath.\n  // Although it's not encoded in the type, these are both null or\n  // both non-null. It uses object equality, so to take advantage of this\n  // optimization, you must pass the same array instance to each successive\n  // method call, and you must also not mutate the array between calls.\n  let lastAccessedEntry: MapEntry | null = null\n  let lastAccessedKeys: Prefix<Keypath> | null = null\n\n  function getOrCreateEntry(keys: Prefix<Keypath>): MapEntry {\n    if (lastAccessedKeys === keys) {\n      return lastAccessedEntry!\n    }\n\n    // Go through each level of keys until we find the entry that matches,\n    // or create a new one if it doesn't already exist.\n    let entry = rootEntry\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i]\n      let map = entry.map\n      if (map !== null) {\n        const existingEntry = map.get(key)\n        if (existingEntry !== undefined) {\n          // Found a match. Keep going.\n          entry = existingEntry\n          continue\n        }\n      } else {\n        map = new Map()\n        entry.map = map\n      }\n      // No entry exists yet at this level. Create a new one.\n      const newEntry: MapEntry = {\n        parent: entry,\n        key,\n        value: null,\n        hasValue: false,\n        map: null,\n      }\n      map.set(key, newEntry)\n      entry = newEntry\n    }\n\n    lastAccessedKeys = keys\n    lastAccessedEntry = entry\n\n    return entry\n  }\n\n  function getEntryIfExists(keys: Prefix<Keypath>): MapEntry | null {\n    if (lastAccessedKeys === keys) {\n      return lastAccessedEntry\n    }\n\n    // Go through each level of keys until we find the entry that matches, or\n    // return null if no match exists.\n    let entry = rootEntry\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i]\n      let map = entry.map\n      if (map !== null) {\n        const existingEntry = map.get(key)\n        if (existingEntry !== undefined) {\n          // Found a match. Keep going.\n          entry = existingEntry\n          continue\n        }\n      }\n      // No entry exists at this level.\n      return null\n    }\n\n    lastAccessedKeys = keys\n    lastAccessedEntry = entry\n\n    return entry\n  }\n\n  function set(keys: Prefix<Keypath>, value: V): void {\n    const entry = getOrCreateEntry(keys)\n    entry.hasValue = true\n    entry.value = value\n  }\n\n  function get(keys: Prefix<Keypath>): V | null {\n    const entry = getEntryIfExists(keys)\n    if (entry === null || !entry.hasValue) {\n      return null\n    }\n    return entry.value\n  }\n\n  function deleteEntry(keys: Prefix<Keypath>): void {\n    const entry = getEntryIfExists(keys)\n    if (entry === null || !entry.hasValue) {\n      return\n    }\n\n    // Found a match. Delete it from the cache.\n    const deletedEntry: EmptyMapEntry = entry as any\n    deletedEntry.hasValue = false\n    deletedEntry.value = null\n\n    // Check if we can garbage collect the entry.\n    if (deletedEntry.map === null) {\n      // Since this entry has no value, and also no child entries, we can\n      // garbage collect it. Remove it from its parent, and keep garbage\n      // collecting the parents until we reach a non-empty entry.\n\n      // Unlike a `set` operation, these are no longer valid because the entry\n      // itself is being modified, not just the value it contains.\n      lastAccessedEntry = null\n      lastAccessedKeys = null\n\n      let parent = deletedEntry.parent\n      let key = deletedEntry.key\n      while (parent !== null) {\n        const parentMap = parent.map\n        if (parentMap !== null) {\n          parentMap.delete(key)\n          if (parentMap.size === 0) {\n            // We just removed the last entry in the parent map.\n            parent.map = null\n            if (parent.value === null) {\n              // The parent node has no child entries, nor does it have a value\n              // on itself. It can be garbage collected. Keep going.\n              key = parent.key\n              parent = parent.parent\n              continue\n            }\n          }\n        }\n        // The parent is not empty. Stop garbage collecting.\n        break\n      }\n    }\n  }\n\n  return {\n    set,\n    get,\n    delete: deleteEntry,\n  }\n}\n"], "names": ["createTupleMap", "rootEntry", "parent", "key", "hasValue", "value", "map", "lastAccessedEntry", "lastAccessedKeys", "getOrCreateEntry", "keys", "entry", "i", "length", "existingEntry", "get", "undefined", "Map", "newEntry", "set", "getEntryIfExists", "deleteEntry", "deletedEntry", "parentMap", "delete", "size"], "mappings": "AAAA,yEAAyE;AAWzE;;;;;;;;;;;CAWC,GACD;;;AAAO,SAASA;IAsBd,IAAIC,YAAsB;QACxBC,QAAQ;QACRC,KAAK;QACLC,UAAU;QACVC,OAAO;QACPC,KAAK;IACP;IAEA,sEAAsE;IACtE,gEAAgE;IAChE,uEAAuE;IACvE,yEAAyE;IACzE,qEAAqE;IACrE,IAAIC,oBAAqC;IACzC,IAAIC,mBAA2C;IAE/C,SAASC,iBAAiBC,IAAqB;QAC7C,IAAIF,qBAAqBE,MAAM;YAC7B,OAAOH;QACT;QAEA,sEAAsE;QACtE,mDAAmD;QACnD,IAAII,QAAQV;QACZ,IAAK,IAAIW,IAAI,GAAGA,IAAIF,KAAKG,MAAM,EAAED,IAAK;YACpC,MAAMT,MAAMO,IAAI,CAACE,EAAE;YACnB,IAAIN,MAAMK,MAAML,GAAG;YACnB,IAAIA,QAAQ,MAAM;gBAChB,MAAMQ,gBAAgBR,IAAIS,GAAG,CAACZ;gBAC9B,IAAIW,kBAAkBE,WAAW;oBAC/B,6BAA6B;oBAC7BL,QAAQG;oBACR;gBACF;YACF,OAAO;gBACLR,MAAM,IAAIW;gBACVN,MAAML,GAAG,GAAGA;YACd;YACA,uDAAuD;YACvD,MAAMY,WAAqB;gBACzBhB,QAAQS;gBACRR;gBACAE,OAAO;gBACPD,UAAU;gBACVE,KAAK;YACP;YACAA,IAAIa,GAAG,CAAChB,KAAKe;YACbP,QAAQO;QACV;QAEAV,mBAAmBE;QACnBH,oBAAoBI;QAEpB,OAAOA;IACT;IAEA,SAASS,iBAAiBV,IAAqB;QAC7C,IAAIF,qBAAqBE,MAAM;YAC7B,OAAOH;QACT;QAEA,yEAAyE;QACzE,kCAAkC;QAClC,IAAII,QAAQV;QACZ,IAAK,IAAIW,IAAI,GAAGA,IAAIF,KAAKG,MAAM,EAAED,IAAK;YACpC,MAAMT,MAAMO,IAAI,CAACE,EAAE;YACnB,IAAIN,MAAMK,MAAML,GAAG;YACnB,IAAIA,QAAQ,MAAM;gBAChB,MAAMQ,gBAAgBR,IAAIS,GAAG,CAACZ;gBAC9B,IAAIW,kBAAkBE,WAAW;oBAC/B,6BAA6B;oBAC7BL,QAAQG;oBACR;gBACF;YACF;YACA,iCAAiC;YACjC,OAAO;QACT;QAEAN,mBAAmBE;QACnBH,oBAAoBI;QAEpB,OAAOA;IACT;IAEA,SAASQ,IAAIT,IAAqB,EAAEL,KAAQ;QAC1C,MAAMM,QAAQF,iBAAiBC;QAC/BC,MAAMP,QAAQ,GAAG;QACjBO,MAAMN,KAAK,GAAGA;IAChB;IAEA,SAASU,IAAIL,IAAqB;QAChC,MAAMC,QAAQS,iBAAiBV;QAC/B,IAAIC,UAAU,QAAQ,CAACA,MAAMP,QAAQ,EAAE;YACrC,OAAO;QACT;QACA,OAAOO,MAAMN,KAAK;IACpB;IAEA,SAASgB,YAAYX,IAAqB;QACxC,MAAMC,QAAQS,iBAAiBV;QAC/B,IAAIC,UAAU,QAAQ,CAACA,MAAMP,QAAQ,EAAE;YACrC;QACF;QAEA,2CAA2C;QAC3C,MAAMkB,eAA8BX;QACpCW,aAAalB,QAAQ,GAAG;QACxBkB,aAAajB,KAAK,GAAG;QAErB,6CAA6C;QAC7C,IAAIiB,aAAahB,GAAG,KAAK,MAAM;YAC7B,mEAAmE;YACnE,kEAAkE;YAClE,2DAA2D;YAE3D,wEAAwE;YACxE,4DAA4D;YAC5DC,oBAAoB;YACpBC,mBAAmB;YAEnB,IAAIN,SAASoB,aAAapB,MAAM;YAChC,IAAIC,MAAMmB,aAAanB,GAAG;YAC1B,MAAOD,WAAW,KAAM;gBACtB,MAAMqB,YAAYrB,OAAOI,GAAG;gBAC5B,IAAIiB,cAAc,MAAM;oBACtBA,UAAUC,MAAM,CAACrB;oBACjB,IAAIoB,UAAUE,IAAI,KAAK,GAAG;wBACxB,oDAAoD;wBACpDvB,OAAOI,GAAG,GAAG;wBACb,IAAIJ,OAAOG,KAAK,KAAK,MAAM;4BACzB,iEAAiE;4BACjE,sDAAsD;4BACtDF,MAAMD,OAAOC,GAAG;4BAChBD,SAASA,OAAOA,MAAM;4BACtB;wBACF;oBACF;gBACF;gBAEA;YACF;QACF;IACF;IAEA,OAAO;QACLiB;QACAJ;QACAS,QAAQH;IACV;AACF", "ignoreList": [0]}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/lru.ts"], "sourcesContent": ["export type LRU<T extends LRUNode> = {\n  put(node: T): void\n  delete(node: T): void\n  updateSize(node: T, size: number): void\n}\n\n// Doubly-linked list\ntype LRUNode<T = any> = {\n  // Although it's not encoded in the type, these are both null if the node is\n  // not in the LRU; both non-null if it is.\n  prev: T | null\n  next: T | null\n  size: number\n}\n\n// Rather than create an internal LRU node, the passed-in type must conform\n// the LRUNode interface. This is just a memory optimization to avoid creating\n// another object; we only use this for Segment Cache entries so it doesn't need\n// to be general purpose.\nexport function createLRU<T extends LRUNode>(\n  // From the LRU's perspective, the size unit is arbitrary, but for our\n  // purposes this is the byte size.\n  maxLruSize: number,\n  onEviction: (node: T) => void\n): LRU<T> {\n  let head: T | null = null\n  let didScheduleCleanup: boolean = false\n  let lruSize: number = 0\n\n  function put(node: T) {\n    if (head === node) {\n      // Already at the head\n      return\n    }\n    const prev = node.prev\n    const next = node.next\n    if (next === null || prev === null) {\n      // This is an insertion\n      lruSize += node.size\n      // Whenever we add an entry, we need to check if we've exceeded the\n      // max size. We don't evict entries immediately; they're evicted later in\n      // an asynchronous task.\n      ensureCleanupIsScheduled()\n    } else {\n      // This is a move. Remove from its current position.\n      prev.next = next\n      next.prev = prev\n    }\n\n    // Move to the front of the list\n    if (head === null) {\n      // This is the first entry\n      node.prev = node\n      node.next = node\n    } else {\n      // Add to the front of the list\n      const tail = head.prev\n      node.prev = tail\n      tail.next = node\n      node.next = head\n      head.prev = node\n    }\n    head = node\n  }\n\n  function updateSize(node: T, newNodeSize: number) {\n    // This is a separate function so that we can resize the entry after it's\n    // already been inserted.\n    if (node.next === null) {\n      // No longer part of LRU.\n      return\n    }\n    const prevNodeSize = node.size\n    node.size = newNodeSize\n    lruSize = lruSize - prevNodeSize + newNodeSize\n    ensureCleanupIsScheduled()\n  }\n\n  function deleteNode(deleted: T) {\n    const next = deleted.next\n    const prev = deleted.prev\n    if (next !== null && prev !== null) {\n      lruSize -= deleted.size\n\n      deleted.next = null\n      deleted.prev = null\n\n      // Remove from the list\n      if (head === deleted) {\n        // Update the head\n        if (next === head) {\n          // This was the last entry\n          head = null\n        } else {\n          head = next\n        }\n      } else {\n        prev.next = next\n        next.prev = prev\n      }\n    } else {\n      // Already deleted\n    }\n  }\n\n  function ensureCleanupIsScheduled() {\n    if (didScheduleCleanup || lruSize <= maxLruSize) {\n      return\n    }\n    didScheduleCleanup = true\n    requestCleanupCallback(cleanup)\n  }\n\n  function cleanup() {\n    didScheduleCleanup = false\n\n    // Evict entries until we're at 90% capacity. We can assume this won't\n    // infinite loop because even if `maxLruSize` were 0, eventually\n    // `deleteNode` sets `head` to `null` when we run out entries.\n    const ninetyPercentMax = maxLruSize * 0.9\n    while (lruSize > ninetyPercentMax && head !== null) {\n      const tail = head.prev\n      deleteNode(tail)\n      onEviction(tail)\n    }\n  }\n\n  return {\n    put,\n    delete: deleteNode,\n    updateSize,\n  }\n}\n\nconst requestCleanupCallback =\n  typeof requestIdleCallback === 'function'\n    ? requestIdleCallback\n    : (cb: () => void) => setTimeout(cb, 0)\n"], "names": ["createLRU", "maxLruSize", "onEviction", "head", "didScheduleCleanup", "lruSize", "put", "node", "prev", "next", "size", "ensureCleanupIsScheduled", "tail", "updateSize", "newNodeSize", "prevNodeSize", "deleteNode", "deleted", "requestCleanupCallback", "cleanup", "ninetyPercentMax", "delete", "requestIdleCallback", "cb", "setTimeout"], "mappings": "AAeA,2EAA2E;AAC3E,8EAA8E;AAC9E,gFAAgF;AAChF,yBAAyB;;;;AAClB,SAASA,UACd,AACA,kCAAkC,oCADoC;AAEtEC,UAAkB,EAClBC,UAA6B;IAE7B,IAAIC,OAAiB;IACrB,IAAIC,qBAA8B;IAClC,IAAIC,UAAkB;IAEtB,SAASC,IAAIC,IAAO;QAClB,IAAIJ,SAASI,MAAM;YACjB,sBAAsB;YACtB;QACF;QACA,MAAMC,OAAOD,KAAKC,IAAI;QACtB,MAAMC,OAAOF,KAAKE,IAAI;QACtB,IAAIA,SAAS,QAAQD,SAAS,MAAM;YAClC,uBAAuB;YACvBH,WAAWE,KAAKG,IAAI;YACpB,mEAAmE;YACnE,yEAAyE;YACzE,wBAAwB;YACxBC;QACF,OAAO;YACL,oDAAoD;YACpDH,KAAKC,IAAI,GAAGA;YACZA,KAAKD,IAAI,GAAGA;QACd;QAEA,gCAAgC;QAChC,IAAIL,SAAS,MAAM;YACjB,0BAA0B;YAC1BI,KAAKC,IAAI,GAAGD;YACZA,KAAKE,IAAI,GAAGF;QACd,OAAO;YACL,+BAA+B;YAC/B,MAAMK,OAAOT,KAAKK,IAAI;YACtBD,KAAKC,IAAI,GAAGI;YACZA,KAAKH,IAAI,GAAGF;YACZA,KAAKE,IAAI,GAAGN;YACZA,KAAKK,IAAI,GAAGD;QACd;QACAJ,OAAOI;IACT;IAEA,SAASM,WAAWN,IAAO,EAAEO,WAAmB;QAC9C,yEAAyE;QACzE,yBAAyB;QACzB,IAAIP,KAAKE,IAAI,KAAK,MAAM;YACtB,yBAAyB;YACzB;QACF;QACA,MAAMM,eAAeR,KAAKG,IAAI;QAC9BH,KAAKG,IAAI,GAAGI;QACZT,UAAUA,UAAUU,eAAeD;QACnCH;IACF;IAEA,SAASK,WAAWC,OAAU;QAC5B,MAAMR,OAAOQ,QAAQR,IAAI;QACzB,MAAMD,OAAOS,QAAQT,IAAI;QACzB,IAAIC,SAAS,QAAQD,SAAS,MAAM;YAClCH,WAAWY,QAAQP,IAAI;YAEvBO,QAAQR,IAAI,GAAG;YACfQ,QAAQT,IAAI,GAAG;YAEf,uBAAuB;YACvB,IAAIL,SAASc,SAAS;gBACpB,kBAAkB;gBAClB,IAAIR,SAASN,MAAM;oBACjB,0BAA0B;oBAC1BA,OAAO;gBACT,OAAO;oBACLA,OAAOM;gBACT;YACF,OAAO;gBACLD,KAAKC,IAAI,GAAGA;gBACZA,KAAKD,IAAI,GAAGA;YACd;QACF,OAAO;QACL,kBAAkB;QACpB;IACF;IAEA,SAASG;QACP,IAAIP,sBAAsBC,WAAWJ,YAAY;YAC/C;QACF;QACAG,qBAAqB;QACrBc,uBAAuBC;IACzB;IAEA,SAASA;QACPf,qBAAqB;QAErB,sEAAsE;QACtE,gEAAgE;QAChE,8DAA8D;QAC9D,MAAMgB,mBAAmBnB,aAAa;QACtC,MAAOI,UAAUe,oBAAoBjB,SAAS,KAAM;YAClD,MAAMS,OAAOT,KAAKK,IAAI;YACtBQ,WAAWJ;YACXV,WAAWU;QACb;IACF;IAEA,OAAO;QACLN;QACAe,QAAQL;QACRH;IACF;AACF;AAEA,MAAMK,yBACJ,OAAOI,wBAAwB,aAC3BA,sBACA,CAACC,KAAmBC,WAAWD,IAAI", "ignoreList": [0]}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/cache.ts"], "sourcesContent": ["import type {\n  TreePrefetch,\n  RootTreePrefetch,\n  SegmentPrefetch,\n} from '../../../server/app-render/collect-segment-data'\nimport type { LoadingModuleData } from '../../../shared/lib/app-router-context.shared-runtime'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n  RSC_HEADER,\n} from '../app-router-headers'\nimport {\n  createFetch,\n  createFromNextReadableStream,\n  urlToUrlWithoutFlightMarker,\n  type RequestHeaders,\n} from '../router-reducer/fetch-server-response'\nimport {\n  trackPrefetchRequestBandwidth,\n  pingPrefetchTask,\n  type PrefetchTask,\n  spawnPrefetchSubtask,\n} from './scheduler'\nimport { getAppBuildId } from '../../app-build-id'\nimport { createHrefFromUrl } from '../router-reducer/create-href-from-url'\nimport type {\n  NormalizedHref,\n  NormalizedNextUrl,\n  RouteCacheKey,\n} from './cache-key'\nimport { createTupleMap, type TupleMap, type Prefix } from './tuple-map'\nimport { createLRU, type LRU } from './lru'\n\n// A note on async/await when working in the prefetch cache:\n//\n// Most async operations in the prefetch cache should *not* use async/await,\n// Instead, spawn a subtask that writes the results to a cache entry, and attach\n// a \"ping\" listener to notify the prefetch queue to try again.\n//\n// The reason is we need to be able to access the segment cache and traverse its\n// data structures synchronously. For example, if there's a synchronous update\n// we can take an immediate snapshot of the cache to produce something we can\n// render. Limiting the use of async/await also makes it easier to avoid race\n// conditions, which is especially important because is cache is mutable.\n//\n// Another reason is that while we're performing async work, it's possible for\n// existing entries to become stale, or for Link prefetches to be removed from\n// the queue. For optimal scheduling, we need to be able to \"cancel\" subtasks\n// that are no longer needed. So, when a segment is received from the server, we\n// restart from the root of the tree that's being prefetched, to confirm all the\n// parent segments are still cached. If the segment is no longer reachable from\n// the root, then it's effectively canceled. This is similar to the design of\n// Rust Futures, or React Suspense.\n\ntype RouteCacheEntryShared = {\n  staleAt: number\n  // This is false only if we're certain the route cannot be intercepted. It's\n  // true in all other cases, including on initialization when we haven't yet\n  // received a response from the server.\n  couldBeIntercepted: boolean\n\n  // LRU-related fields\n  keypath: null | Prefix<RouteCacheKeypath>\n  next: null | RouteCacheEntry\n  prev: null | RouteCacheEntry\n  size: number\n}\n\nexport const enum EntryStatus {\n  Pending,\n  Rejected,\n  Fulfilled,\n}\n\ntype PendingRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Pending\n  blockedTasks: Set<PrefetchTask> | null\n  canonicalUrl: null\n  tree: null\n  head: null\n  isHeadPartial: true\n}\n\ntype RejectedRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Rejected\n  blockedTasks: Set<PrefetchTask> | null\n  canonicalUrl: null\n  tree: null\n  head: null\n  isHeadPartial: true\n}\n\nexport type FulfilledRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Fulfilled\n  blockedTasks: null\n  canonicalUrl: string\n  tree: TreePrefetch\n  head: React.ReactNode | null\n  isHeadPartial: boolean\n}\n\nexport type RouteCacheEntry =\n  | PendingRouteCacheEntry\n  | FulfilledRouteCacheEntry\n  | RejectedRouteCacheEntry\n\ntype SegmentCacheEntryShared = {\n  staleAt: number\n\n  // LRU-related fields\n  key: null | string\n  next: null | RouteCacheEntry\n  prev: null | RouteCacheEntry\n  size: number\n}\n\ntype PendingSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Pending\n  rsc: null\n  loading: null\n  isPartial: true\n  promise: null | PromiseWithResolvers<FulfilledSegmentCacheEntry | null>\n}\n\ntype RejectedSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Rejected\n  rsc: null\n  loading: null\n  isPartial: true\n  promise: null\n}\n\ntype FulfilledSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Fulfilled\n  rsc: React.ReactNode | null\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  isPartial: boolean\n  promise: null\n}\n\nexport type SegmentCacheEntry =\n  | PendingSegmentCacheEntry\n  | RejectedSegmentCacheEntry\n  | FulfilledSegmentCacheEntry\n\n// Route cache entries vary on multiple keys: the href and the Next-Url. Each of\n// these parts needs to be included in the internal cache key. Rather than\n// concatenate the keys into a single key, we use a multi-level map, where the\n// first level is keyed by href, the second level is keyed by Next-Url, and so\n// on (if were to add more levels).\ntype RouteCacheKeypath = [NormalizedHref, NormalizedNextUrl]\nconst routeCacheMap: TupleMap<RouteCacheKeypath, RouteCacheEntry> =\n  createTupleMap()\n\n// We use an LRU for memory management. We must update this whenever we add or\n// remove a new cache entry, or when an entry changes size.\n// TODO: I chose the max size somewhat arbitrarily. Consider setting this based\n// on navigator.deviceMemory, or some other heuristic. We should make this\n// customizable via the Next.js config, too.\nconst maxRouteLruSize = 10 * 1024 * 1024 // 10 MB\nconst routeCacheLru = createLRU<RouteCacheEntry>(\n  maxRouteLruSize,\n  onRouteLRUEviction\n)\n\n// TODO: We may eventually store segment entries in a tuple map, too, to\n// account for search params.\nconst segmentCacheMap = new Map<string, SegmentCacheEntry>()\n// NOTE: Segments and Route entries are managed by separate LRUs. We could\n// combine them into a single LRU, but because they are separate types, we'd\n// need to wrap each one in an extra LRU node (to maintain monomorphism, at the\n// cost of additional memory).\nconst maxSegmentLruSize = 50 * 1024 * 1024 // 50 MB\nconst segmentCacheLru = createLRU<SegmentCacheEntry>(\n  maxSegmentLruSize,\n  onSegmentLRUEviction\n)\n\nexport function readExactRouteCacheEntry(\n  now: number,\n  href: NormalizedHref,\n  nextUrl: NormalizedNextUrl | null\n): RouteCacheEntry | null {\n  const keypath: Prefix<RouteCacheKeypath> =\n    nextUrl === null ? [href] : [href, nextUrl]\n  const existingEntry = routeCacheMap.get(keypath)\n  if (existingEntry !== null) {\n    // Check if the entry is stale\n    if (existingEntry.staleAt > now) {\n      // Reuse the existing entry.\n\n      // Since this is an access, move the entry to the front of the LRU.\n      routeCacheLru.put(existingEntry)\n\n      return existingEntry\n    } else {\n      // Evict the stale entry from the cache.\n      deleteRouteFromCache(existingEntry, keypath)\n    }\n  }\n  return null\n}\n\nexport function readRouteCacheEntry(\n  now: number,\n  key: RouteCacheKey\n): RouteCacheEntry | null {\n  // First check if there's a non-intercepted entry. Most routes cannot be\n  // intercepted, so this is the common case.\n  const nonInterceptedEntry = readExactRouteCacheEntry(now, key.href, null)\n  if (nonInterceptedEntry !== null && !nonInterceptedEntry.couldBeIntercepted) {\n    // Found a match, and the route cannot be intercepted. We can reuse it.\n    return nonInterceptedEntry\n  }\n  // There was no match. Check again but include the Next-Url this time.\n  return readExactRouteCacheEntry(now, key.href, key.nextUrl)\n}\n\nexport function readSegmentCacheEntry(\n  now: number,\n  path: string\n): SegmentCacheEntry | null {\n  const existingEntry = segmentCacheMap.get(path)\n  if (existingEntry !== undefined) {\n    // Check if the entry is stale\n    if (existingEntry.staleAt > now) {\n      // Reuse the existing entry.\n\n      // Since this is an access, move the entry to the front of the LRU.\n      segmentCacheLru.put(existingEntry)\n\n      return existingEntry\n    } else {\n      // Evict the stale entry from the cache.\n      deleteSegmentFromCache(existingEntry, path)\n    }\n  }\n  return null\n}\n\nexport function waitForSegmentCacheEntry(\n  pendingEntry: PendingSegmentCacheEntry\n): Promise<FulfilledSegmentCacheEntry | null> {\n  // Because the entry is pending, there's already a in-progress request.\n  // Attach a promise to the entry that will resolve when the server responds.\n  let promiseWithResolvers = pendingEntry.promise\n  if (promiseWithResolvers === null) {\n    promiseWithResolvers = pendingEntry.promise =\n      createPromiseWithResolvers<FulfilledSegmentCacheEntry | null>()\n  } else {\n    // There's already a promise we can use\n  }\n  return promiseWithResolvers.promise\n}\n\n/**\n * Reads the route cache for a matching entry *and* spawns a request if there's\n * no match. Because this may issue a network request, it should only be called\n * from within the context of a prefetch task.\n */\nexport function requestRouteCacheEntryFromCache(\n  now: number,\n  task: PrefetchTask\n): RouteCacheEntry {\n  const key = task.key\n  // First check if there's a non-intercepted entry. Most routes cannot be\n  // intercepted, so this is the common case.\n  const nonInterceptedEntry = readExactRouteCacheEntry(now, key.href, null)\n  if (nonInterceptedEntry !== null && !nonInterceptedEntry.couldBeIntercepted) {\n    // Found a match, and the route cannot be intercepted. We can reuse it.\n    return nonInterceptedEntry\n  }\n  // There was no match. Check again but include the Next-Url this time.\n  const exactEntry = readExactRouteCacheEntry(now, key.href, key.nextUrl)\n  if (exactEntry !== null) {\n    return exactEntry\n  }\n  // Create a pending entry and spawn a request for its data.\n  const pendingEntry: PendingRouteCacheEntry = {\n    canonicalUrl: null,\n    status: EntryStatus.Pending,\n    blockedTasks: null,\n    tree: null,\n    head: null,\n    isHeadPartial: true,\n    // If the request takes longer than a minute, a subsequent request should\n    // retry instead of waiting for this one.\n    //\n    // When the response is received, this value will be replaced by a new value\n    // based on the stale time sent from the server.\n    staleAt: now + 60 * 1000,\n    // This is initialized to true because we don't know yet whether the route\n    // could be intercepted. It's only set to false once we receive a response\n    // from the server.\n    couldBeIntercepted: true,\n\n    // LRU-related fields\n    keypath: null,\n    next: null,\n    prev: null,\n    size: 0,\n  }\n  spawnPrefetchSubtask(fetchRouteOnCacheMiss(pendingEntry, task))\n  const keypath: Prefix<RouteCacheKeypath> =\n    key.nextUrl === null ? [key.href] : [key.href, key.nextUrl]\n  routeCacheMap.set(keypath, pendingEntry)\n  // Stash the keypath on the entry so we know how to remove it from the map\n  // if it gets evicted from the LRU.\n  pendingEntry.keypath = keypath\n  routeCacheLru.put(pendingEntry)\n  return pendingEntry\n}\n\n/**\n * Reads the route cache for a matching entry *and* spawns a request if there's\n * no match. Because this may issue a network request, it should only be called\n * from within the context of a prefetch task.\n */\nexport function requestSegmentEntryFromCache(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  path: string,\n  accessToken: string\n): SegmentCacheEntry {\n  const existingEntry = readSegmentCacheEntry(now, path)\n  if (existingEntry !== null) {\n    return existingEntry\n  }\n  // Create a pending entry and spawn a request for its data.\n  const pendingEntry: PendingSegmentCacheEntry = {\n    status: EntryStatus.Pending,\n    rsc: null,\n    loading: null,\n    staleAt: route.staleAt,\n    isPartial: true,\n    promise: null,\n\n    // LRU-related fields\n    key: null,\n    next: null,\n    prev: null,\n    size: 0,\n  }\n  spawnPrefetchSubtask(\n    fetchSegmentEntryOnCacheMiss(\n      route,\n      pendingEntry,\n      task.key,\n      path,\n      accessToken\n    )\n  )\n  segmentCacheMap.set(path, pendingEntry)\n  // Stash the keypath on the entry so we know how to remove it from the map\n  // if it gets evicted from the LRU.\n  pendingEntry.key = path\n  segmentCacheLru.put(pendingEntry)\n  return pendingEntry\n}\n\nfunction deleteRouteFromCache(\n  entry: RouteCacheEntry,\n  keypath: Prefix<RouteCacheKeypath>\n): void {\n  pingBlockedTasks(entry)\n  routeCacheMap.delete(keypath)\n  routeCacheLru.delete(entry)\n}\n\nfunction deleteSegmentFromCache(entry: SegmentCacheEntry, key: string): void {\n  cancelEntryListeners(entry)\n  segmentCacheMap.delete(key)\n  segmentCacheLru.delete(entry)\n}\n\nfunction onRouteLRUEviction(entry: RouteCacheEntry): void {\n  // The LRU evicted this entry. Remove it from the map.\n  const keypath = entry.keypath\n  if (keypath !== null) {\n    entry.keypath = null\n    pingBlockedTasks(entry)\n    routeCacheMap.delete(keypath)\n  }\n}\n\nfunction onSegmentLRUEviction(entry: SegmentCacheEntry): void {\n  // The LRU evicted this entry. Remove it from the map.\n  const key = entry.key\n  if (key !== null) {\n    entry.key = null\n    cancelEntryListeners(entry)\n    segmentCacheMap.delete(key)\n  }\n}\n\nfunction cancelEntryListeners(entry: SegmentCacheEntry): void {\n  if (entry.status === EntryStatus.Pending && entry.promise !== null) {\n    // There were listeners for this entry. Resolve them with `null` to indicate\n    // that the prefetch failed. It's up to the listener to decide how to handle\n    // this case.\n    // NOTE: We don't currently propagate the reason the prefetch was canceled\n    // but we could by accepting a `reason` argument.\n    entry.promise.resolve(null)\n    entry.promise = null\n  }\n}\n\nfunction pingBlockedTasks(entry: {\n  blockedTasks: Set<PrefetchTask> | null\n}): void {\n  const blockedTasks = entry.blockedTasks\n  if (blockedTasks !== null) {\n    for (const task of blockedTasks) {\n      pingPrefetchTask(task)\n    }\n    entry.blockedTasks = null\n  }\n}\n\nfunction fulfillRouteCacheEntry(\n  entry: PendingRouteCacheEntry,\n  tree: TreePrefetch,\n  head: React.ReactNode,\n  isHeadPartial: boolean,\n  staleAt: number,\n  couldBeIntercepted: boolean,\n  canonicalUrl: string\n): FulfilledRouteCacheEntry {\n  const fulfilledEntry: FulfilledRouteCacheEntry = entry as any\n  fulfilledEntry.status = EntryStatus.Fulfilled\n  fulfilledEntry.tree = tree\n  fulfilledEntry.head = head\n  fulfilledEntry.isHeadPartial = isHeadPartial\n  fulfilledEntry.staleAt = staleAt\n  fulfilledEntry.couldBeIntercepted = couldBeIntercepted\n  fulfilledEntry.canonicalUrl = canonicalUrl\n  pingBlockedTasks(entry)\n  return fulfilledEntry\n}\n\nfunction fulfillSegmentCacheEntry(\n  segmentCacheEntry: PendingSegmentCacheEntry,\n  rsc: React.ReactNode,\n  loading: LoadingModuleData | Promise<LoadingModuleData>,\n  staleAt: number,\n  isPartial: boolean\n) {\n  const fulfilledEntry: FulfilledSegmentCacheEntry = segmentCacheEntry as any\n  fulfilledEntry.status = EntryStatus.Fulfilled\n  fulfilledEntry.rsc = rsc\n  fulfilledEntry.loading = loading\n  fulfilledEntry.staleAt = staleAt\n  fulfilledEntry.isPartial = isPartial\n  // Resolve any listeners that were waiting for this data.\n  if (segmentCacheEntry.promise !== null) {\n    segmentCacheEntry.promise.resolve(fulfilledEntry)\n    // Free the promise for garbage collection.\n    fulfilledEntry.promise = null\n  }\n}\n\nfunction rejectRouteCacheEntry(\n  entry: PendingRouteCacheEntry,\n  staleAt: number\n): void {\n  const rejectedEntry: RejectedRouteCacheEntry = entry as any\n  rejectedEntry.status = EntryStatus.Rejected\n  rejectedEntry.staleAt = staleAt\n  pingBlockedTasks(entry)\n}\n\nfunction rejectSegmentCacheEntry(\n  entry: PendingSegmentCacheEntry,\n  staleAt: number\n): void {\n  const rejectedEntry: RejectedSegmentCacheEntry = entry as any\n  rejectedEntry.status = EntryStatus.Rejected\n  rejectedEntry.staleAt = staleAt\n  if (entry.promise !== null) {\n    // NOTE: We don't currently propagate the reason the prefetch was canceled\n    // but we could by accepting a `reason` argument.\n    entry.promise.resolve(null)\n    entry.promise = null\n  }\n}\n\nasync function fetchRouteOnCacheMiss(\n  entry: PendingRouteCacheEntry,\n  task: PrefetchTask\n): Promise<void> {\n  // This function is allowed to use async/await because it contains the actual\n  // fetch that gets issued on a cache miss. Notice though that it does not\n  // return anything; it writes the result to the cache entry directly, then\n  // pings the scheduler to unblock the corresponding prefetch task.\n  const key = task.key\n  const href = key.href\n  const nextUrl = key.nextUrl\n  try {\n    const response = await fetchSegmentPrefetchResponse(href, '/_tree', nextUrl)\n    if (\n      !response ||\n      !response.ok ||\n      // 204 is a Cache miss. Though theoretically this shouldn't happen when\n      // PPR is enabled, because we always respond to route tree requests, even\n      // if it needs to be blockingly generated on demand.\n      response.status === 204 ||\n      !response.body\n    ) {\n      // Server responded with an error, or with a miss. We should still cache\n      // the response, but we can try again after 10 seconds.\n      rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n      return\n    }\n    const prefetchStream = createPrefetchResponseStream(\n      response.body,\n      routeCacheLru,\n      entry\n    )\n    const serverData: RootTreePrefetch = await (createFromNextReadableStream(\n      prefetchStream\n    ) as Promise<RootTreePrefetch>)\n    if (serverData.buildId !== getAppBuildId()) {\n      // The server build does not match the client. Treat as a 404. During\n      // an actual navigation, the router will trigger an MPA navigation.\n      // TODO: Consider moving the build ID to a response header so we can check\n      // it before decoding the response, and so there's one way of checking\n      // across all response types.\n      rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n      return\n    }\n\n    // This is a bit convoluted but it's taken from router-reducer and\n    // fetch-server-response\n    const canonicalUrl = response.redirected\n      ? createHrefFromUrl(urlToUrlWithoutFlightMarker(response.url))\n      : href\n\n    // Check whether the response varies based on the Next-Url header.\n    const varyHeader = response.headers.get('vary')\n    const couldBeIntercepted =\n      varyHeader !== null && varyHeader.includes(NEXT_URL)\n\n    fulfillRouteCacheEntry(\n      entry,\n      serverData.tree,\n      serverData.head,\n      serverData.isHeadPartial,\n      Date.now() + serverData.staleTime,\n      couldBeIntercepted,\n      canonicalUrl\n    )\n\n    if (!couldBeIntercepted && nextUrl !== null) {\n      // This route will never be intercepted. So we can use this entry for all\n      // requests to this route, regardless of the Next-Url header. This works\n      // because when reading the cache we always check for a valid\n      // non-intercepted entry first.\n      //\n      // Re-key the entry. Since we're in an async task, we must first confirm\n      // that the entry hasn't been concurrently modified by a different task.\n      const currentKeypath: Prefix<RouteCacheKeypath> = [href, nextUrl]\n      const expectedEntry = routeCacheMap.get(currentKeypath)\n      if (expectedEntry === entry) {\n        routeCacheMap.delete(currentKeypath)\n        const newKeypath: Prefix<RouteCacheKeypath> = [href]\n        routeCacheMap.set(newKeypath, entry)\n        // We don't need to update the LRU because the entry is already in it.\n        // But since we changed the keypath, we do need to update that, so we\n        // know how to remove it from the map if it gets evicted from the LRU.\n        entry.keypath = newKeypath\n      } else {\n        // Something else modified this entry already. Since the re-keying is\n        // just a performance optimization, we can safely skip it.\n      }\n    }\n  } catch (error) {\n    // Either the connection itself failed, or something bad happened while\n    // decoding the response.\n    rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n  }\n}\n\nasync function fetchSegmentEntryOnCacheMiss(\n  route: FulfilledRouteCacheEntry,\n  segmentCacheEntry: PendingSegmentCacheEntry,\n  routeKey: RouteCacheKey,\n  segmentPath: string,\n  accessToken: string | null\n): Promise<void> {\n  // This function is allowed to use async/await because it contains the actual\n  // fetch that gets issued on a cache miss. Notice though that it does not\n  // return anything; it writes the result to the cache entry directly.\n  //\n  // Segment fetches are non-blocking so we don't need to ping the scheduler\n  // on completion.\n  const href = routeKey.href\n  try {\n    const response = await fetchSegmentPrefetchResponse(\n      href,\n      accessToken === '' ? segmentPath : `${segmentPath}.${accessToken}`,\n      routeKey.nextUrl\n    )\n    if (\n      !response ||\n      !response.ok ||\n      response.status === 204 || // Cache miss\n      !response.body\n    ) {\n      // Server responded with an error, or with a miss. We should still cache\n      // the response, but we can try again after 10 seconds.\n      rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n      return\n    }\n    // Wrap the original stream in a new stream that never closes. That way the\n    // Flight client doesn't error if there's a hanging promise.\n    const prefetchStream = createPrefetchResponseStream(\n      response.body,\n      segmentCacheLru,\n      segmentCacheEntry\n    )\n    const serverData = await (createFromNextReadableStream(\n      prefetchStream\n    ) as Promise<SegmentPrefetch>)\n    if (serverData.buildId !== getAppBuildId()) {\n      // The server build does not match the client. Treat as a 404. During\n      // an actual navigation, the router will trigger an MPA navigation.\n      // TODO: Consider moving the build ID to a response header so we can check\n      // it before decoding the response, and so there's one way of checking\n      // across all response types.\n      rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n      return\n    }\n    fulfillSegmentCacheEntry(\n      segmentCacheEntry,\n      serverData.rsc,\n      serverData.loading,\n      // TODO: The server does not currently provide per-segment stale time.\n      // So we use the stale time of the route.\n      route.staleAt,\n      serverData.isPartial\n    )\n  } catch (error) {\n    // Either the connection itself failed, or something bad happened while\n    // decoding the response.\n    rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n  }\n}\n\nasync function fetchSegmentPrefetchResponse(\n  href: NormalizedHref,\n  segmentPath: string,\n  nextUrl: NormalizedNextUrl | null\n): Promise<Response | null> {\n  const headers: RequestHeaders = {\n    [RSC_HEADER]: '1',\n    [NEXT_ROUTER_PREFETCH_HEADER]: '1',\n    [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]: segmentPath,\n  }\n  if (nextUrl !== null) {\n    headers[NEXT_URL] = nextUrl\n  }\n  const fetchPriority = 'low'\n  const responsePromise = createFetch(new URL(href), headers, fetchPriority)\n  trackPrefetchRequestBandwidth(responsePromise)\n  const response = await responsePromise\n  const contentType = response.headers.get('content-type')\n  const isFlightResponse =\n    contentType && contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n  if (!response.ok || !isFlightResponse) {\n    return null\n  }\n  return response\n}\n\nfunction createPrefetchResponseStream<\n  T extends RouteCacheEntry | SegmentCacheEntry,\n>(\n  originalFlightStream: ReadableStream<Uint8Array>,\n  lru: LRU<T>,\n  lruEntry: T\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  //\n  // While processing the original stream, we also incrementally update the size\n  // of the cache entry in the LRU.\n  let totalByteLength = 0\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n\n          // Incrementally update the size of the cache entry in the LRU.\n          // NOTE: Since prefetch responses are delivered in a single chunk,\n          // it's not really necessary to do this streamingly, but I'm doing it\n          // anyway in case this changes in the future.\n          totalByteLength += value.byteLength\n          lru.updateSize(lruEntry, totalByteLength)\n\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n\nfunction createPromiseWithResolvers<T>(): PromiseWithResolvers<T> {\n  // Shim of Stage 4 Promise.withResolvers proposal\n  let resolve: (value: T | PromiseLike<T>) => void\n  let reject: (reason: any) => void\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res\n    reject = rej\n  })\n  return { resolve: resolve!, reject: reject!, promise }\n}\n"], "names": ["NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER", "createFetch", "createFromNextReadableStream", "urlToUrlWithoutFlightMarker", "trackPrefetchRequestBandwidth", "pingPrefetchTask", "spawnPrefetchSubtask", "getAppBuildId", "createHrefFromUrl", "createTupleMap", "createLRU", "EntryStatus", "routeCacheMap", "maxRouteLruSize", "routeCacheLru", "onRouteLRUEviction", "segmentCacheMap", "Map", "maxSegmentLruSize", "segmentCacheLru", "onSegmentLRUEviction", "readExactRouteCacheEntry", "now", "href", "nextUrl", "keypath", "existingEntry", "get", "staleAt", "put", "deleteRouteFromCache", "readRouteCacheEntry", "key", "nonInterceptedEntry", "couldBeIntercepted", "readSegmentCacheEntry", "path", "undefined", "deleteSegmentFromCache", "waitForSegmentCacheEntry", "pendingEntry", "promiseWithResolvers", "promise", "createPromiseWithResolvers", "requestRouteCacheEntryFromCache", "task", "exactEntry", "canonicalUrl", "status", "blockedTasks", "tree", "head", "isHeadPartial", "next", "prev", "size", "fetchRouteOnCacheMiss", "set", "requestSegmentEntryFromCache", "route", "accessToken", "rsc", "loading", "isPartial", "fetchSegmentEntryOnCacheMiss", "entry", "pingBlockedTasks", "delete", "cancelEntryListeners", "resolve", "fulfillRouteCacheEntry", "fulfilledEntry", "fulfillSegmentCacheEntry", "segmentCacheEntry", "rejectRouteCacheEntry", "rejectedEntry", "rejectSegmentCacheEntry", "response", "fetchSegmentPrefetchResponse", "ok", "body", "Date", "prefetchStream", "createPrefetchResponseStream", "serverData", "buildId", "redirected", "url", "<PERSON><PERSON><PERSON><PERSON>", "headers", "includes", "staleTime", "currentKeypath", "expectedEntry", "newKeypath", "error", "routeKey", "segmentPath", "fetchPriority", "responsePromise", "URL", "contentType", "isFlightResponse", "startsWith", "originalFlightStream", "lru", "lruEntry", "totalByteLength", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue", "byteLength", "updateSize", "reject", "Promise", "res", "rej"], "mappings": ";;;;;;;;;AAMA,SACEA,2BAA2B,EAC3BC,mCAAmC,EACnCC,QAAQ,EACRC,uBAAuB,EACvBC,UAAU,QACL,wBAAuB;AAC9B,SACEC,WAAW,EACXC,4BAA4B,EAC5BC,2BAA2B,QAEtB,0CAAyC;AAChD,SACEC,6BAA6B,EAC7BC,gBAAgB,EAEhBC,oBAAoB,QACf,cAAa;AACpB,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,iBAAiB,QAAQ,yCAAwC;AAM1E,SAASC,cAAc,QAAoC,cAAa;AACxE,SAASC,SAAS,QAAkB,QAAO;;;;;;;;AAqCpC,IAAWC,cAAAA,WAAAA,GAAAA,SAAAA,WAAAA;;;;WAAAA;MAIjB;AA+ED,MAAMC,4NACJH,iBAAAA;AAEF,8EAA8E;AAC9E,2DAA2D;AAC3D,+EAA+E;AAC/E,0EAA0E;AAC1E,4CAA4C;AAC5C,MAAMI,kBAAkB,KAAK,OAAO,KAAK,QAAQ;;AACjD,MAAMC,mNAAgBJ,YAAAA,EACpBG,iBACAE;AAGF,wEAAwE;AACxE,6BAA6B;AAC7B,MAAMC,kBAAkB,IAAIC;AAC5B,0EAA0E;AAC1E,4EAA4E;AAC5E,+EAA+E;AAC/E,8BAA8B;AAC9B,MAAMC,oBAAoB,KAAK,OAAO,KAAK,QAAQ;;AACnD,MAAMC,qNAAkBT,YAAAA,EACtBQ,mBACAE;AAGK,SAASC,yBACdC,GAAW,EACXC,IAAoB,EACpBC,OAAiC;IAEjC,MAAMC,UACJD,YAAY,OAAO;QAACD;KAAK,GAAG;QAACA;QAAMC;KAAQ;IAC7C,MAAME,gBAAgBd,cAAce,GAAG,CAACF;IACxC,IAAIC,kBAAkB,MAAM;QAC1B,8BAA8B;QAC9B,IAAIA,cAAcE,OAAO,GAAGN,KAAK;YAC/B,4BAA4B;YAE5B,mEAAmE;YACnER,cAAce,GAAG,CAACH;YAElB,OAAOA;QACT,OAAO;YACL,wCAAwC;YACxCI,qBAAqBJ,eAAeD;QACtC;IACF;IACA,OAAO;AACT;AAEO,SAASM,oBACdT,GAAW,EACXU,GAAkB;IAElB,wEAAwE;IACxE,2CAA2C;IAC3C,MAAMC,sBAAsBZ,yBAAyBC,KAAKU,IAAIT,IAAI,EAAE;IACpE,IAAIU,wBAAwB,QAAQ,CAACA,oBAAoBC,kBAAkB,EAAE;QAC3E,uEAAuE;QACvE,OAAOD;IACT;IACA,sEAAsE;IACtE,OAAOZ,yBAAyBC,KAAKU,IAAIT,IAAI,EAAES,IAAIR,OAAO;AAC5D;AAEO,SAASW,sBACdb,GAAW,EACXc,IAAY;IAEZ,MAAMV,gBAAgBV,gBAAgBW,GAAG,CAACS;IAC1C,IAAIV,kBAAkBW,WAAW;QAC/B,8BAA8B;QAC9B,IAAIX,cAAcE,OAAO,GAAGN,KAAK;YAC/B,4BAA4B;YAE5B,mEAAmE;YACnEH,gBAAgBU,GAAG,CAACH;YAEpB,OAAOA;QACT,OAAO;YACL,wCAAwC;YACxCY,uBAAuBZ,eAAeU;QACxC;IACF;IACA,OAAO;AACT;AAEO,SAASG,yBACdC,YAAsC;IAEtC,uEAAuE;IACvE,4EAA4E;IAC5E,IAAIC,uBAAuBD,aAAaE,OAAO;IAC/C,IAAID,yBAAyB,MAAM;QACjCA,uBAAuBD,aAAaE,OAAO,GACzCC;IACJ,OAAO;IACL,uCAAuC;IACzC;IACA,OAAOF,qBAAqBC,OAAO;AACrC;AAOO,SAASE,gCACdtB,GAAW,EACXuB,IAAkB;IAElB,MAAMb,MAAMa,KAAKb,GAAG;IACpB,wEAAwE;IACxE,2CAA2C;IAC3C,MAAMC,sBAAsBZ,yBAAyBC,KAAKU,IAAIT,IAAI,EAAE;IACpE,IAAIU,wBAAwB,QAAQ,CAACA,oBAAoBC,kBAAkB,EAAE;QAC3E,uEAAuE;QACvE,OAAOD;IACT;IACA,sEAAsE;IACtE,MAAMa,aAAazB,yBAAyBC,KAAKU,IAAIT,IAAI,EAAES,IAAIR,OAAO;IACtE,IAAIsB,eAAe,MAAM;QACvB,OAAOA;IACT;IACA,2DAA2D;IAC3D,MAAMN,eAAuC;QAC3CO,cAAc;QACdC,MAAM,EAAA;QACNC,cAAc;QACdC,MAAM;QACNC,MAAM;QACNC,eAAe;QACf,yEAAyE;QACzE,yCAAyC;QACzC,EAAE;QACF,4EAA4E;QAC5E,gDAAgD;QAChDxB,SAASN,MAAM,KAAK;QACpB,0EAA0E;QAC1E,0EAA0E;QAC1E,mBAAmB;QACnBY,oBAAoB;QAEpB,qBAAqB;QACrBT,SAAS;QACT4B,MAAM;QACNC,MAAM;QACNC,MAAM;IACR;KACAjD,+NAAAA,EAAqBkD,sBAAsBhB,cAAcK;IACzD,MAAMpB,UACJO,IAAIR,OAAO,KAAK,OAAO;QAACQ,IAAIT,IAAI;KAAC,GAAG;QAACS,IAAIT,IAAI;QAAES,IAAIR,OAAO;KAAC;IAC7DZ,cAAc6C,GAAG,CAAChC,SAASe;IAC3B,0EAA0E;IAC1E,mCAAmC;IACnCA,aAAaf,OAAO,GAAGA;IACvBX,cAAce,GAAG,CAACW;IAClB,OAAOA;AACT;AAOO,SAASkB,6BACdpC,GAAW,EACXuB,IAAkB,EAClBc,KAA+B,EAC/BvB,IAAY,EACZwB,WAAmB;IAEnB,MAAMlC,gBAAgBS,sBAAsBb,KAAKc;IACjD,IAAIV,kBAAkB,MAAM;QAC1B,OAAOA;IACT;IACA,2DAA2D;IAC3D,MAAMc,eAAyC;QAC7CQ,MAAM,EAAA;QACNa,KAAK;QACLC,SAAS;QACTlC,SAAS+B,MAAM/B,OAAO;QACtBmC,WAAW;QACXrB,SAAS;QAET,qBAAqB;QACrBV,KAAK;QACLqB,MAAM;QACNC,MAAM;QACNC,MAAM;IACR;6MACAjD,uBAAAA,EACE0D,6BACEL,OACAnB,cACAK,KAAKb,GAAG,EACRI,MACAwB;IAGJ5C,gBAAgByC,GAAG,CAACrB,MAAMI;IAC1B,0EAA0E;IAC1E,mCAAmC;IACnCA,aAAaR,GAAG,GAAGI;IACnBjB,gBAAgBU,GAAG,CAACW;IACpB,OAAOA;AACT;AAEA,SAASV,qBACPmC,KAAsB,EACtBxC,OAAkC;IAElCyC,iBAAiBD;IACjBrD,cAAcuD,MAAM,CAAC1C;IACrBX,cAAcqD,MAAM,CAACF;AACvB;AAEA,SAAS3B,uBAAuB2B,KAAwB,EAAEjC,GAAW;IACnEoC,qBAAqBH;IACrBjD,gBAAgBmD,MAAM,CAACnC;IACvBb,gBAAgBgD,MAAM,CAACF;AACzB;AAEA,SAASlD,mBAAmBkD,KAAsB;IAChD,sDAAsD;IACtD,MAAMxC,UAAUwC,MAAMxC,OAAO;IAC7B,IAAIA,YAAY,MAAM;QACpBwC,MAAMxC,OAAO,GAAG;QAChByC,iBAAiBD;QACjBrD,cAAcuD,MAAM,CAAC1C;IACvB;AACF;AAEA,SAASL,qBAAqB6C,KAAwB;IACpD,sDAAsD;IACtD,MAAMjC,MAAMiC,MAAMjC,GAAG;IACrB,IAAIA,QAAQ,MAAM;QAChBiC,MAAMjC,GAAG,GAAG;QACZoC,qBAAqBH;QACrBjD,gBAAgBmD,MAAM,CAACnC;IACzB;AACF;AAEA,SAASoC,qBAAqBH,KAAwB;IACpD,IAAIA,MAAMjB,MAAM,KAAA,KAA4BiB,MAAMvB,OAAO,KAAK,MAAM;QAClE,4EAA4E;QAC5E,4EAA4E;QAC5E,aAAa;QACb,0EAA0E;QAC1E,iDAAiD;QACjDuB,MAAMvB,OAAO,CAAC2B,OAAO,CAAC;QACtBJ,MAAMvB,OAAO,GAAG;IAClB;AACF;AAEA,SAASwB,iBAAiBD,KAEzB;IACC,MAAMhB,eAAegB,MAAMhB,YAAY;IACvC,IAAIA,iBAAiB,MAAM;QACzB,KAAK,MAAMJ,QAAQI,aAAc;gBAC/B5C,wNAAAA,EAAiBwC;QACnB;QACAoB,MAAMhB,YAAY,GAAG;IACvB;AACF;AAEA,SAASqB,uBACPL,KAA6B,EAC7Bf,IAAkB,EAClBC,IAAqB,EACrBC,aAAsB,EACtBxB,OAAe,EACfM,kBAA2B,EAC3Ba,YAAoB;IAEpB,MAAMwB,iBAA2CN;IACjDM,eAAevB,MAAM,GAAA;IACrBuB,eAAerB,IAAI,GAAGA;IACtBqB,eAAepB,IAAI,GAAGA;IACtBoB,eAAenB,aAAa,GAAGA;IAC/BmB,eAAe3C,OAAO,GAAGA;IACzB2C,eAAerC,kBAAkB,GAAGA;IACpCqC,eAAexB,YAAY,GAAGA;IAC9BmB,iBAAiBD;IACjB,OAAOM;AACT;AAEA,SAASC,yBACPC,iBAA2C,EAC3CZ,GAAoB,EACpBC,OAAuD,EACvDlC,OAAe,EACfmC,SAAkB;IAElB,MAAMQ,iBAA6CE;IACnDF,eAAevB,MAAM,GAAA;IACrBuB,eAAeV,GAAG,GAAGA;IACrBU,eAAeT,OAAO,GAAGA;IACzBS,eAAe3C,OAAO,GAAGA;IACzB2C,eAAeR,SAAS,GAAGA;IAC3B,yDAAyD;IACzD,IAAIU,kBAAkB/B,OAAO,KAAK,MAAM;QACtC+B,kBAAkB/B,OAAO,CAAC2B,OAAO,CAACE;QAClC,2CAA2C;QAC3CA,eAAe7B,OAAO,GAAG;IAC3B;AACF;AAEA,SAASgC,sBACPT,KAA6B,EAC7BrC,OAAe;IAEf,MAAM+C,gBAAyCV;IAC/CU,cAAc3B,MAAM,GAAA;IACpB2B,cAAc/C,OAAO,GAAGA;IACxBsC,iBAAiBD;AACnB;AAEA,SAASW,wBACPX,KAA+B,EAC/BrC,OAAe;IAEf,MAAM+C,gBAA2CV;IACjDU,cAAc3B,MAAM,GAAA;IACpB2B,cAAc/C,OAAO,GAAGA;IACxB,IAAIqC,MAAMvB,OAAO,KAAK,MAAM;QAC1B,0EAA0E;QAC1E,iDAAiD;QACjDuB,MAAMvB,OAAO,CAAC2B,OAAO,CAAC;QACtBJ,MAAMvB,OAAO,GAAG;IAClB;AACF;AAEA,eAAec,sBACbS,KAA6B,EAC7BpB,IAAkB;IAElB,6EAA6E;IAC7E,yEAAyE;IACzE,0EAA0E;IAC1E,kEAAkE;IAClE,MAAMb,MAAMa,KAAKb,GAAG;IACpB,MAAMT,OAAOS,IAAIT,IAAI;IACrB,MAAMC,UAAUQ,IAAIR,OAAO;IAC3B,IAAI;QACF,MAAMqD,WAAW,MAAMC,6BAA6BvD,MAAM,UAAUC;QACpE,IACE,CAACqD,YACD,CAACA,SAASE,EAAE,IACZ,uEAAuE;QACvE,yEAAyE;QACzE,oDAAoD;QACpDF,SAAS7B,MAAM,KAAK,OACpB,CAAC6B,SAASG,IAAI,EACd;YACA,wEAAwE;YACxE,uDAAuD;YACvDN,sBAAsBT,OAAOgB,KAAK3D,GAAG,KAAK,KAAK;YAC/C;QACF;QACA,MAAM4D,iBAAiBC,6BACrBN,SAASG,IAAI,EACblE,eACAmD;QAEF,MAAMmB,aAA+B,kOAAOlF,+BAAAA,EAC1CgF;QAEF,IAAIE,WAAWC,OAAO,qLAAK9E,gBAAAA,KAAiB;YAC1C,qEAAqE;YACrE,mEAAmE;YACnE,0EAA0E;YAC1E,sEAAsE;YACtE,6BAA6B;YAC7BmE,sBAAsBT,OAAOgB,KAAK3D,GAAG,KAAK,KAAK;YAC/C;QACF;QAEA,kEAAkE;QAClE,wBAAwB;QACxB,MAAMyB,eAAe8B,SAASS,UAAU,GACpC9E,kPAAAA,8NAAkBL,8BAAAA,EAA4B0E,SAASU,GAAG,KAC1DhE;QAEJ,kEAAkE;QAClE,MAAMiE,aAAaX,SAASY,OAAO,CAAC9D,GAAG,CAAC;QACxC,MAAMO,qBACJsD,eAAe,QAAQA,WAAWE,QAAQ,iMAAC5F,WAAAA;QAE7CwE,uBACEL,OACAmB,WAAWlC,IAAI,EACfkC,WAAWjC,IAAI,EACfiC,WAAWhC,aAAa,EACxB6B,KAAK3D,GAAG,KAAK8D,WAAWO,SAAS,EACjCzD,oBACAa;QAGF,IAAI,CAACb,sBAAsBV,YAAY,MAAM;YAC3C,yEAAyE;YACzE,wEAAwE;YACxE,6DAA6D;YAC7D,+BAA+B;YAC/B,EAAE;YACF,wEAAwE;YACxE,wEAAwE;YACxE,MAAMoE,iBAA4C;gBAACrE;gBAAMC;aAAQ;YACjE,MAAMqE,gBAAgBjF,cAAce,GAAG,CAACiE;YACxC,IAAIC,kBAAkB5B,OAAO;gBAC3BrD,cAAcuD,MAAM,CAACyB;gBACrB,MAAME,aAAwC;oBAACvE;iBAAK;gBACpDX,cAAc6C,GAAG,CAACqC,YAAY7B;gBAC9B,sEAAsE;gBACtE,qEAAqE;gBACrE,sEAAsE;gBACtEA,MAAMxC,OAAO,GAAGqE;YAClB,OAAO;YACL,qEAAqE;YACrE,0DAA0D;YAC5D;QACF;IACF,EAAE,OAAOC,OAAO;QACd,uEAAuE;QACvE,yBAAyB;QACzBrB,sBAAsBT,OAAOgB,KAAK3D,GAAG,KAAK,KAAK;IACjD;AACF;AAEA,eAAe0C,6BACbL,KAA+B,EAC/Bc,iBAA2C,EAC3CuB,QAAuB,EACvBC,WAAmB,EACnBrC,WAA0B;IAE1B,6EAA6E;IAC7E,yEAAyE;IACzE,qEAAqE;IACrE,EAAE;IACF,0EAA0E;IAC1E,iBAAiB;IACjB,MAAMrC,OAAOyE,SAASzE,IAAI;IAC1B,IAAI;QACF,MAAMsD,WAAW,MAAMC,6BACrBvD,MACAqC,gBAAgB,KAAKqC,cAAiBA,cAAY,MAAGrC,aACrDoC,SAASxE,OAAO;QAElB,IACE,CAACqD,YACD,CAACA,SAASE,EAAE,IACZF,SAAS7B,MAAM,KAAK,OAAO,aAAa;QACxC,CAAC6B,SAASG,IAAI,EACd;YACA,wEAAwE;YACxE,uDAAuD;YACvDJ,wBAAwBH,mBAAmBQ,KAAK3D,GAAG,KAAK,KAAK;YAC7D;QACF;QACA,2EAA2E;QAC3E,4DAA4D;QAC5D,MAAM4D,iBAAiBC,6BACrBN,SAASG,IAAI,EACb7D,iBACAsD;QAEF,MAAMW,aAAa,kOAAOlF,+BAAAA,EACxBgF;QAEF,IAAIE,WAAWC,OAAO,qLAAK9E,gBAAAA,KAAiB;YAC1C,qEAAqE;YACrE,mEAAmE;YACnE,0EAA0E;YAC1E,sEAAsE;YACtE,6BAA6B;YAC7BqE,wBAAwBH,mBAAmBQ,KAAK3D,GAAG,KAAK,KAAK;YAC7D;QACF;QACAkD,yBACEC,mBACAW,WAAWvB,GAAG,EACduB,WAAWtB,OAAO,EAClB,AACA,yCAAyC,6BAD6B;QAEtEH,MAAM/B,OAAO,EACbwD,WAAWrB,SAAS;IAExB,EAAE,OAAOgC,OAAO;QACd,uEAAuE;QACvE,yBAAyB;QACzBnB,wBAAwBH,mBAAmBQ,KAAK3D,GAAG,KAAK,KAAK;IAC/D;AACF;AAEA,eAAewD,6BACbvD,IAAoB,EACpB0E,WAAmB,EACnBzE,OAAiC;IAEjC,MAAMiE,UAA0B;QAC9B,iMAACzF,aAAAA,CAAW,EAAE;QACd,iMAACJ,8BAAAA,CAA4B,EAAE;QAC/B,CAACC,sOAAAA,CAAoC,EAAEoG;IACzC;IACA,IAAIzE,YAAY,MAAM;QACpBiE,OAAO,iMAAC3F,WAAAA,CAAS,GAAG0B;IACtB;IACA,MAAM0E,gBAAgB;IACtB,MAAMC,8OAAkBlG,cAAAA,EAAY,IAAImG,IAAI7E,OAAOkE,SAASS;QAC5D9F,qOAAAA,EAA8B+F;IAC9B,MAAMtB,WAAW,MAAMsB;IACvB,MAAME,cAAcxB,SAASY,OAAO,CAAC9D,GAAG,CAAC;IACzC,MAAM2E,mBACJD,eAAeA,YAAYE,UAAU,iMAACxG,0BAAAA;IACxC,IAAI,CAAC8E,SAASE,EAAE,IAAI,CAACuB,kBAAkB;QACrC,OAAO;IACT;IACA,OAAOzB;AACT;AAEA,SAASM,6BAGPqB,oBAAgD,EAChDC,GAAW,EACXC,QAAW;IAEX,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,EAAE;IACF,8EAA8E;IAC9E,iCAAiC;IACjC,IAAIC,kBAAkB;IACtB,MAAMC,SAASJ,qBAAqBK,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMN,OAAOO,IAAI;gBACzC,IAAI,CAACF,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWI,OAAO,CAACF;oBAEnB,+DAA+D;oBAC/D,kEAAkE;oBAClE,qEAAqE;oBACrE,6CAA6C;oBAC7CP,mBAAmBO,MAAMG,UAAU;oBACnCZ,IAAIa,UAAU,CAACZ,UAAUC;oBAEzB;gBACF;gBACA,qEAAqE;gBACrE,qBAAqB;gBACrB;YACF;QACF;IACF;AACF;AAEA,SAAShE;IACP,iDAAiD;IACjD,IAAI0B;IACJ,IAAIkD;IACJ,MAAM7E,UAAU,IAAI8E,QAAW,CAACC,KAAKC;QACnCrD,UAAUoD;QACVF,SAASG;IACX;IACA,OAAO;QAAErD,SAASA;QAAUkD,QAAQA;QAAS7E;IAAQ;AACvD", "ignoreList": [0]}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/cache-key.ts"], "sourcesContent": ["// TypeScript trick to simulate opaque types, like in Flow.\ntype Opaque<K, T> = T & { __brand: K }\n\n// Only functions in this module should be allowed to create CacheKeys.\nexport type NormalizedHref = Opaque<'NormalizedHref', string>\nexport type NormalizedNextUrl = Opaque<'NormalizedNextUrl', string>\n\nexport type RouteCacheKey = Opaque<\n  'RouteCacheKey',\n  {\n    href: NormalizedHref\n    nextUrl: NormalizedNextUrl | null\n  }\n>\n\nexport function createCacheKey(\n  originalHref: string,\n  nextUrl: string | null\n): RouteCacheKey {\n  const originalUrl = new URL(originalHref)\n\n  // TODO: As of now, we never include search params in the cache key because\n  // per-segment prefetch requests are always static, and cannot contain search\n  // params. But to support <Link prefetch={true}>, we will sometimes populate\n  // the cache with dynamic data, so this will have to change.\n  originalUrl.search = ''\n\n  const normalizedHref = originalUrl.href as NormalizedHref\n  const normalizedNextUrl = nextUrl as NormalizedNextUrl | null\n\n  const cacheKey = {\n    href: normalizedHref,\n    nextUrl: normalizedNextUrl,\n  } as <PERSON><PERSON><PERSON><PERSON><PERSON>\n\n  return cacheKey\n}\n"], "names": ["createCacheKey", "originalHref", "nextUrl", "originalUrl", "URL", "search", "normalizedHref", "href", "normalizedNextUrl", "cache<PERSON>ey"], "mappings": "AAAA,2DAA2D;;;;AAepD,SAASA,eACdC,YAAoB,EACpBC,OAAsB;IAEtB,MAAMC,cAAc,IAAIC,IAAIH;IAE5B,2EAA2E;IAC3E,6EAA6E;IAC7E,4EAA4E;IAC5E,4DAA4D;IAC5DE,YAAYE,MAAM,GAAG;IAErB,MAAMC,iBAAiBH,YAAYI,IAAI;IACvC,MAAMC,oBAAoBN;IAE1B,MAAMO,WAAW;QACfF,MAAMD;QACNJ,SAASM;IACX;IAEA,OAAOC;AACT", "ignoreList": [0]}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/navigation.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport type {\n  CacheNode,\n  LoadingModuleData,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\nimport { fetchServerResponse } from '../router-reducer/fetch-server-response'\nimport {\n  updateCacheNodeOnNavigation,\n  listenForDynamicRequest,\n  type Task as PPRNavigationTask,\n} from '../router-reducer/ppr-navigations'\nimport { createHrefFromUrl as createCanonicalUrl } from '../router-reducer/create-href-from-url'\nimport {\n  EntryStatus,\n  readRouteCacheEntry,\n  readSegmentCacheEntry,\n  waitForSegmentCacheEntry,\n} from './cache'\nimport type { TreePrefetch } from '../../../server/app-render/collect-segment-data'\nimport { createCacheKey } from './cache-key'\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\ntype MPANavigationResult = {\n  tag: NavigationResultTag.MPA\n  data: string\n}\n\ntype NoOpNavigationResult = {\n  tag: NavigationResultTag.NoOp\n  data: null\n}\n\ntype SuccessfulNavigationResult = {\n  tag: NavigationResultTag.Success\n  data: {\n    flightRouterState: FlightRouterState\n    cacheNode: CacheNode\n    canonicalUrl: string\n  }\n}\n\ntype AsyncNavigationResult = {\n  tag: NavigationResultTag.Async\n  data: Promise<\n    MPANavigationResult | NoOpNavigationResult | SuccessfulNavigationResult\n  >\n}\n\nexport type NavigationResult =\n  | MPANavigationResult\n  | SuccessfulNavigationResult\n  | NoOpNavigationResult\n  | AsyncNavigationResult\n\nconst noOpNavigationResult: NoOpNavigationResult = {\n  tag: NavigationResultTag.NoOp,\n  data: null,\n}\n\n/**\n * Navigate to a new URL, using the Segment Cache to construct a response.\n *\n * To allow for synchronous navigations whenever possible, this is not an async\n * function. It returns a promise only if there's no matching prefetch in\n * the cache. Otherwise it returns an immediate result and uses Suspense/RSC to\n * stream in any missing data.\n */\nexport function navigate(\n  url: URL,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState,\n  nextUrl: string | null\n): AsyncNavigationResult | SuccessfulNavigationResult | NoOpNavigationResult {\n  const now = Date.now()\n\n  const cacheKey = createCacheKey(url.href, nextUrl)\n  const route = readRouteCacheEntry(now, cacheKey)\n  if (route !== null && route.status === EntryStatus.Fulfilled) {\n    // We have a matching prefetch.\n    const snapshot = readRenderSnapshotFromCache(now, route.tree)\n    const prefetchFlightRouterState = snapshot.flightRouterState\n    const prefetchSeedData = snapshot.seedData\n    const prefetchHead = route.head\n    const isPrefetchHeadPartial = route.isHeadPartial\n    const canonicalUrl = route.canonicalUrl\n    return navigateUsingPrefetchedRouteTree(\n      url,\n      nextUrl,\n      currentCacheNode,\n      currentFlightRouterState,\n      prefetchFlightRouterState,\n      prefetchSeedData,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      canonicalUrl\n    )\n  }\n  // There's no matching prefetch for this route in the cache.\n  return {\n    tag: NavigationResultTag.Async,\n    data: navigateDynamicallyWithNoPrefetch(\n      url,\n      nextUrl,\n      currentCacheNode,\n      currentFlightRouterState\n    ),\n  }\n}\n\nfunction navigateUsingPrefetchedRouteTree(\n  url: URL,\n  nextUrl: string | null,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState,\n  prefetchFlightRouterState: FlightRouterState,\n  prefetchSeedData: CacheNodeSeedData | null,\n  prefetchHead: React.ReactNode | null,\n  isPrefetchHeadPartial: boolean,\n  canonicalUrl: string\n): SuccessfulNavigationResult | NoOpNavigationResult {\n  // Recursively construct a prefetch tree by reading from the Segment Cache. To\n  // maintain compatibility, we output the same data structures as the old\n  // prefetching implementation: FlightRouterState and CacheNodeSeedData.\n  // TODO: Eventually updateCacheNodeOnNavigation (or the equivalent) should\n  // read from the Segment Cache directly. It's only structured this way for now\n  // so we can share code with the old prefetching implementation.\n  const task = updateCacheNodeOnNavigation(\n    currentCacheNode,\n    currentFlightRouterState,\n    prefetchFlightRouterState,\n    prefetchSeedData,\n    prefetchHead,\n    isPrefetchHeadPartial\n  )\n  if (task !== null) {\n    if (task.needsDynamicRequest) {\n      const promiseForDynamicServerResponse = fetchServerResponse(url, {\n        flightRouterState: currentFlightRouterState,\n        nextUrl,\n      })\n      listenForDynamicRequest(task, promiseForDynamicServerResponse)\n    } else {\n      // The prefetched tree does not contain dynamic holes — it's\n      // fully static. We can skip the dynamic request.\n    }\n    return navigationTaskToResult(task, currentCacheNode, canonicalUrl)\n  }\n  // The server sent back an empty tree patch. There's nothing to update.\n  return noOpNavigationResult\n}\n\nfunction navigationTaskToResult(\n  task: PPRNavigationTask,\n  currentCacheNode: CacheNode,\n  canonicalUrl: string\n): SuccessfulNavigationResult {\n  const newCacheNode = task.node\n  return {\n    tag: NavigationResultTag.Success,\n    data: {\n      flightRouterState: task.route,\n      cacheNode: newCacheNode !== null ? newCacheNode : currentCacheNode,\n      canonicalUrl,\n    },\n  }\n}\n\nfunction readRenderSnapshotFromCache(\n  now: number,\n  tree: TreePrefetch\n): { flightRouterState: FlightRouterState; seedData: CacheNodeSeedData } {\n  let childRouterStates: { [parallelRouteKey: string]: FlightRouterState } = {}\n  let childSeedDatas: {\n    [parallelRouteKey: string]: CacheNodeSeedData | null\n  } = {}\n  const slots = tree.slots\n  if (slots !== null) {\n    for (const parallelRouteKey in slots) {\n      const childTree = slots[parallelRouteKey]\n      const childResult = readRenderSnapshotFromCache(now, childTree)\n      childRouterStates[parallelRouteKey] = childResult.flightRouterState\n      childSeedDatas[parallelRouteKey] = childResult.seedData\n    }\n  }\n\n  let rsc: React.ReactNode | null = null\n  let loading: LoadingModuleData | Promise<LoadingModuleData> = null\n  let isPartial: boolean = true\n\n  const segmentEntry = readSegmentCacheEntry(now, tree.path)\n  if (segmentEntry !== null) {\n    switch (segmentEntry.status) {\n      case EntryStatus.Fulfilled: {\n        // Happy path: a cache hit\n        rsc = segmentEntry.rsc\n        loading = segmentEntry.loading\n        isPartial = segmentEntry.isPartial\n        break\n      }\n      case EntryStatus.Pending: {\n        // We haven't received data for this segment yet, but there's already\n        // an in-progress request. Since it's extremely likely to arrive\n        // before the dynamic data response, we might as well use it.\n        const promiseForFulfilledEntry = waitForSegmentCacheEntry(segmentEntry)\n        rsc = promiseForFulfilledEntry.then((entry) =>\n          entry !== null ? entry.rsc : null\n        )\n        loading = promiseForFulfilledEntry.then((entry) =>\n          entry !== null ? entry.loading : null\n        )\n        // Since we don't know yet whether the segment is partial or fully\n        // static, we must assume it's partial; we can't skip the\n        // dynamic request.\n        isPartial = true\n        break\n      }\n      case EntryStatus.Rejected:\n        break\n      default: {\n        const _exhaustiveCheck: never = segmentEntry\n        break\n      }\n    }\n  }\n\n  const extra = tree.extra\n  const flightRouterStateSegment = extra[0]\n  const isRootLayout = extra[1]\n\n  return {\n    flightRouterState: [\n      flightRouterStateSegment,\n      childRouterStates,\n      null,\n      null,\n      isRootLayout,\n    ],\n    seedData: [\n      flightRouterStateSegment,\n      rsc,\n      childSeedDatas,\n      loading,\n      isPartial,\n    ],\n  }\n}\n\nasync function navigateDynamicallyWithNoPrefetch(\n  url: URL,\n  nextUrl: string | null,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState\n): Promise<\n  MPANavigationResult | SuccessfulNavigationResult | NoOpNavigationResult\n> {\n  // Runs when a navigation happens but there's no cached prefetch we can use.\n  // Don't bother to wait for a prefetch response; go straight to a full\n  // navigation that contains both static and dynamic data in a single stream.\n  // (This is unlike the old navigation implementation, which instead blocks\n  // the dynamic request until a prefetch request is received.)\n  //\n  // To avoid duplication of logic, we're going to pretend that the tree\n  // returned by the dynamic request is, in fact, a prefetch tree. Then we can\n  // use the same server response to write the actual data into the CacheNode\n  // tree. So it's the same flow as the \"happy path\" (prefetch, then\n  // navigation), except we use a single server response for both stages.\n\n  const promiseForDynamicServerResponse = fetchServerResponse(url, {\n    flightRouterState: currentFlightRouterState,\n    nextUrl,\n  })\n  const { flightData, canonicalUrl: canonicalUrlOverride } =\n    await promiseForDynamicServerResponse\n\n  // TODO: Detect if the only thing that changed was the hash, like we do in\n  // in navigateReducer\n\n  if (typeof flightData === 'string') {\n    // This is an MPA navigation.\n    const newUrl = flightData\n    return {\n      tag: NavigationResultTag.MPA,\n      data: newUrl,\n    }\n  }\n\n  // Since the response format of dynamic requests and prefetches is slightly\n  // different, we'll need to massage the data a bit. Create FlightRouterState\n  // tree that simulates what we'd receive as the result of a prefetch.\n  const prefetchFlightRouterState = simulatePrefetchTreeUsingDynamicTreePatch(\n    currentFlightRouterState,\n    flightData\n  )\n\n  // In our simulated prefetch payload, we pretend that there's no seed data\n  // nor a prefetch head.\n  const prefetchSeedData = null\n  const prefetchHead = null\n  const isPrefetchHeadPartial = true\n\n  const canonicalUrl = createCanonicalUrl(\n    canonicalUrlOverride ? canonicalUrlOverride : url\n  )\n\n  // Now we proceed exactly as we would for normal navigation.\n  const task = updateCacheNodeOnNavigation(\n    currentCacheNode,\n    currentFlightRouterState,\n    prefetchFlightRouterState,\n    prefetchSeedData,\n    prefetchHead,\n    isPrefetchHeadPartial\n  )\n  if (task !== null) {\n    if (task.needsDynamicRequest) {\n      listenForDynamicRequest(task, promiseForDynamicServerResponse)\n    } else {\n      // The prefetched tree does not contain dynamic holes — it's\n      // fully static. We can skip the dynamic request.\n    }\n    return navigationTaskToResult(task, currentCacheNode, canonicalUrl)\n  }\n  // The server sent back an empty tree patch. There's nothing to update.\n  return noOpNavigationResult\n}\n\nfunction simulatePrefetchTreeUsingDynamicTreePatch(\n  currentTree: FlightRouterState,\n  flightData: Array<NormalizedFlightData>\n): FlightRouterState {\n  // Takes the current FlightRouterState and applies the router state patch\n  // received from the server, to create a full FlightRouterState tree that we\n  // can pretend was returned by a prefetch.\n  //\n  // (It sounds similar to what applyRouterStatePatch does, but it doesn't need\n  // to handle stuff like interception routes or diffing since that will be\n  // handled later.)\n  let baseTree = currentTree\n  for (const { segmentPath, tree: treePatch } of flightData) {\n    // If the server sends us multiple tree patches, we only need to clone the\n    // base tree when applying the first patch. After the first patch, we can\n    // apply the remaining patches in place without copying.\n    const canMutateInPlace = baseTree !== currentTree\n    baseTree = simulatePrefetchTreeUsingDynamicTreePatchImpl(\n      baseTree,\n      treePatch,\n      segmentPath,\n      canMutateInPlace,\n      0\n    )\n  }\n\n  return baseTree\n}\n\nfunction simulatePrefetchTreeUsingDynamicTreePatchImpl(\n  baseRouterState: FlightRouterState,\n  patch: FlightRouterState,\n  segmentPath: FlightSegmentPath,\n  canMutateInPlace: boolean,\n  index: number\n) {\n  if (index === segmentPath.length) {\n    // We reached the part of the tree that we need to patch.\n    return patch\n  }\n\n  // segmentPath represents the parent path of subtree. It's a repeating\n  // pattern of parallel route key and segment:\n  //\n  //   [string, Segment, string, Segment, string, Segment, ...]\n  //\n  // This path tells us which part of the base tree to apply the tree patch.\n  //\n  // NOTE: In the case of a fully dynamic request with no prefetch, we receive\n  // the FlightRouterState patch in the same request as the dynamic data.\n  // Therefore we don't need to worry about diffing the segment values; we can\n  // assume the server sent us a correct result.\n  const updatedParallelRouteKey: string = segmentPath[index]\n  // const segment: Segment = segmentPath[index + 1] <-- Not used, see note above\n\n  const baseChildren = baseRouterState[1]\n  const newChildren: { [parallelRouteKey: string]: FlightRouterState } = {}\n  for (const parallelRouteKey in baseChildren) {\n    if (parallelRouteKey === updatedParallelRouteKey) {\n      const childBaseRouterState = baseChildren[parallelRouteKey]\n      newChildren[parallelRouteKey] =\n        simulatePrefetchTreeUsingDynamicTreePatchImpl(\n          childBaseRouterState,\n          patch,\n          segmentPath,\n          canMutateInPlace,\n          // Advance the index by two and keep cloning until we reach\n          // the end of the segment path.\n          index + 2\n        )\n    } else {\n      // This child is not being patched. Copy it over as-is.\n      newChildren[parallelRouteKey] = baseChildren[parallelRouteKey]\n    }\n  }\n\n  if (canMutateInPlace) {\n    // We can mutate the base tree in place, because the base tree is already\n    // a clone.\n    baseRouterState[1] = newChildren\n    return baseRouterState\n  }\n\n  // Clone all the fields except the children.\n  //\n  // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n  // confirm whether we need to copy all of these fields. Not sure the server\n  // ever sends, e.g. the refetch marker.\n  const clone: FlightRouterState = [baseRouterState[0], newChildren]\n  if (2 in baseRouterState) {\n    clone[2] = baseRouterState[2]\n  }\n  if (3 in baseRouterState) {\n    clone[3] = baseRouterState[3]\n  }\n  if (4 in baseRouterState) {\n    clone[4] = baseRouterState[4]\n  }\n  return clone\n}\n"], "names": ["fetchServerResponse", "updateCacheNodeOnNavigation", "listenForDynamicRequest", "createHrefFromUrl", "createCanonicalUrl", "EntryStatus", "readRouteCacheEntry", "readSegmentCacheEntry", "waitForSegmentCacheEntry", "createCacheKey", "NavigationResultTag", "noOpNavigationResult", "tag", "data", "navigate", "url", "currentCacheNode", "currentFlightRouterState", "nextUrl", "now", "Date", "cache<PERSON>ey", "href", "route", "status", "Fulfilled", "snapshot", "readRenderSnapshotFromCache", "tree", "prefetchFlightRouterState", "flightRouterState", "prefetchSeedData", "seedData", "prefetchHead", "head", "isPrefetchHeadPartial", "isHeadPartial", "canonicalUrl", "navigateUsingPrefetchedRouteTree", "navigateDynamicallyWithNoPrefetch", "task", "needsDynamicRequest", "promiseForDynamicServerResponse", "navigationTaskToResult", "newCacheNode", "node", "cacheNode", "childRouterStates", "childSeedDatas", "slots", "parallelRouteKey", "childTree", "childResult", "rsc", "loading", "isPartial", "segmentEntry", "path", "Pending", "promiseForFulfilledEntry", "then", "entry", "Rejected", "_exhaustiveCheck", "extra", "flightRouterStateSegment", "isRootLayout", "flightData", "canonicalUrlOverride", "newUrl", "simulatePrefetchTreeUsingDynamicTreePatch", "currentTree", "baseTree", "segmentPath", "treePatch", "canMutateInPlace", "simulatePrefetchTreeUsingDynamicTreePatchImpl", "baseRouterState", "patch", "index", "length", "updatedParallelRouteKey", "baseChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childBaseRouterState", "clone"], "mappings": ";;;;AAUA,SAASA,mBAAmB,QAAQ,0CAAyC;AAC7E,SACEC,2BAA2B,EAC3BC,uBAAuB,QAElB,oCAAmC;AAC1C,SAASC,qBAAqBC,kBAAkB,QAAQ,yCAAwC;AAChG,SACEC,WAAW,EACXC,mBAAmB,EACnBC,qBAAqB,EACrBC,wBAAwB,QACnB,UAAS;AAEhB,SAASC,cAAc,QAAQ,cAAa;;;;;;AAErC,IAAWC,sBAAAA,WAAAA,GAAAA,SAAAA,mBAAAA;;;;;WAAAA;MAKjB;AAkCD,MAAMC,uBAA6C;IACjDC,GAAG,EAAA;IACHC,MAAM;AACR;AAUO,SAASC,SACdC,GAAQ,EACRC,gBAA2B,EAC3BC,wBAA2C,EAC3CC,OAAsB;IAEtB,MAAMC,MAAMC,KAAKD,GAAG;IAEpB,MAAME,uNAAWZ,iBAAAA,EAAeM,IAAIO,IAAI,EAAEJ;IAC1C,MAAMK,4MAAQjB,uBAAAA,EAAoBa,KAAKE;IACvC,IAAIE,UAAU,QAAQA,MAAMC,MAAM,sMAAKnB,cAAAA,CAAYoB,SAAS,EAAE;QAC5D,+BAA+B;QAC/B,MAAMC,WAAWC,4BAA4BR,KAAKI,MAAMK,IAAI;QAC5D,MAAMC,4BAA4BH,SAASI,iBAAiB;QAC5D,MAAMC,mBAAmBL,SAASM,QAAQ;QAC1C,MAAMC,eAAeV,MAAMW,IAAI;QAC/B,MAAMC,wBAAwBZ,MAAMa,aAAa;QACjD,MAAMC,eAAed,MAAMc,YAAY;QACvC,OAAOC,iCACLvB,KACAG,SACAF,kBACAC,0BACAY,2BACAE,kBACAE,cACAE,uBACAE;IAEJ;IACA,4DAA4D;IAC5D,OAAO;QACLzB,GAAG,EAAA;QACHC,MAAM0B,kCACJxB,KACAG,SACAF,kBACAC;IAEJ;AACF;AAEA,SAASqB,iCACPvB,GAAQ,EACRG,OAAsB,EACtBF,gBAA2B,EAC3BC,wBAA2C,EAC3CY,yBAA4C,EAC5CE,gBAA0C,EAC1CE,YAAoC,EACpCE,qBAA8B,EAC9BE,YAAoB;IAEpB,8EAA8E;IAC9E,wEAAwE;IACxE,uEAAuE;IACvE,0EAA0E;IAC1E,8EAA8E;IAC9E,gEAAgE;IAChE,MAAMG,OAAOvC,iPAAAA,EACXe,kBACAC,0BACAY,2BACAE,kBACAE,cACAE;IAEF,IAAIK,SAAS,MAAM;QACjB,IAAIA,KAAKC,mBAAmB,EAAE;YAC5B,MAAMC,8PAAkC1C,sBAAAA,EAAoBe,KAAK;gBAC/De,mBAAmBb;gBACnBC;YACF;YACAhB,6OAAAA,EAAwBsC,MAAME;QAChC,OAAO;QACL,4DAA4D;QAC5D,iDAAiD;QACnD;QACA,OAAOC,uBAAuBH,MAAMxB,kBAAkBqB;IACxD;IACA,uEAAuE;IACvE,OAAO1B;AACT;AAEA,SAASgC,uBACPH,IAAuB,EACvBxB,gBAA2B,EAC3BqB,YAAoB;IAEpB,MAAMO,eAAeJ,KAAKK,IAAI;IAC9B,OAAO;QACLjC,GAAG,EAAA;QACHC,MAAM;YACJiB,mBAAmBU,KAAKjB,KAAK;YAC7BuB,WAAWF,iBAAiB,OAAOA,eAAe5B;YAClDqB;QACF;IACF;AACF;AAEA,SAASV,4BACPR,GAAW,EACXS,IAAkB;IAElB,IAAImB,oBAAuE,CAAC;IAC5E,IAAIC,iBAEA,CAAC;IACL,MAAMC,QAAQrB,KAAKqB,KAAK;IACxB,IAAIA,UAAU,MAAM;QAClB,IAAK,MAAMC,oBAAoBD,MAAO;YACpC,MAAME,YAAYF,KAAK,CAACC,iBAAiB;YACzC,MAAME,cAAczB,4BAA4BR,KAAKgC;YACrDJ,iBAAiB,CAACG,iBAAiB,GAAGE,YAAYtB,iBAAiB;YACnEkB,cAAc,CAACE,iBAAiB,GAAGE,YAAYpB,QAAQ;QACzD;IACF;IAEA,IAAIqB,MAA8B;IAClC,IAAIC,UAA0D;IAC9D,IAAIC,YAAqB;IAEzB,MAAMC,oNAAejD,wBAAAA,EAAsBY,KAAKS,KAAK6B,IAAI;IACzD,IAAID,iBAAiB,MAAM;QACzB,OAAQA,aAAahC,MAAM;YACzB,sMAAKnB,cAAAA,CAAYoB,SAAS;gBAAE;oBAC1B,0BAA0B;oBAC1B4B,MAAMG,aAAaH,GAAG;oBACtBC,UAAUE,aAAaF,OAAO;oBAC9BC,YAAYC,aAAaD,SAAS;oBAClC;gBACF;YACA,sMAAKlD,cAAAA,CAAYqD,OAAO;gBAAE;oBACxB,qEAAqE;oBACrE,gEAAgE;oBAChE,6DAA6D;oBAC7D,MAAMC,gOAA2BnD,2BAAAA,EAAyBgD;oBAC1DH,MAAMM,yBAAyBC,IAAI,CAAC,CAACC,QACnCA,UAAU,OAAOA,MAAMR,GAAG,GAAG;oBAE/BC,UAAUK,yBAAyBC,IAAI,CAAC,CAACC,QACvCA,UAAU,OAAOA,MAAMP,OAAO,GAAG;oBAEnC,kEAAkE;oBAClE,yDAAyD;oBACzD,mBAAmB;oBACnBC,YAAY;oBACZ;gBACF;YACA,sMAAKlD,cAAAA,CAAYyD,QAAQ;gBACvB;YACF;gBAAS;oBACP,MAAMC,mBAA0BP;oBAChC;gBACF;QACF;IACF;IAEA,MAAMQ,QAAQpC,KAAKoC,KAAK;IACxB,MAAMC,2BAA2BD,KAAK,CAAC,EAAE;IACzC,MAAME,eAAeF,KAAK,CAAC,EAAE;IAE7B,OAAO;QACLlC,mBAAmB;YACjBmC;YACAlB;YACA;YACA;YACAmB;SACD;QACDlC,UAAU;YACRiC;YACAZ;YACAL;YACAM;YACAC;SACD;IACH;AACF;AAEA,eAAehB,kCACbxB,GAAQ,EACRG,OAAsB,EACtBF,gBAA2B,EAC3BC,wBAA2C;IAI3C,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,6DAA6D;IAC7D,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,2EAA2E;IAC3E,kEAAkE;IAClE,uEAAuE;IAEvE,MAAMyB,8PAAkC1C,sBAAAA,EAAoBe,KAAK;QAC/De,mBAAmBb;QACnBC;IACF;IACA,MAAM,EAAEiD,UAAU,EAAE9B,cAAc+B,oBAAoB,EAAE,GACtD,MAAM1B;IAER,0EAA0E;IAC1E,qBAAqB;IAErB,IAAI,OAAOyB,eAAe,UAAU;QAClC,6BAA6B;QAC7B,MAAME,SAASF;QACf,OAAO;YACLvD,GAAG,EAAA;YACHC,MAAMwD;QACR;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,qEAAqE;IACrE,MAAMxC,4BAA4ByC,0CAChCrD,0BACAkD;IAGF,0EAA0E;IAC1E,uBAAuB;IACvB,MAAMpC,mBAAmB;IACzB,MAAME,eAAe;IACrB,MAAME,wBAAwB;IAE9B,MAAME,6OAAejC,oBAAAA,EACnBgE,uBAAuBA,uBAAuBrD;IAGhD,4DAA4D;IAC5D,MAAMyB,0NAAOvC,8BAAAA,EACXe,kBACAC,0BACAY,2BACAE,kBACAE,cACAE;IAEF,IAAIK,SAAS,MAAM;QACjB,IAAIA,KAAKC,mBAAmB,EAAE;+NAC5BvC,0BAAAA,EAAwBsC,MAAME;QAChC,OAAO;QACL,4DAA4D;QAC5D,iDAAiD;QACnD;QACA,OAAOC,uBAAuBH,MAAMxB,kBAAkBqB;IACxD;IACA,uEAAuE;IACvE,OAAO1B;AACT;AAEA,SAAS2D,0CACPC,WAA8B,EAC9BJ,UAAuC;IAEvC,yEAAyE;IACzE,4EAA4E;IAC5E,0CAA0C;IAC1C,EAAE;IACF,6EAA6E;IAC7E,yEAAyE;IACzE,kBAAkB;IAClB,IAAIK,WAAWD;IACf,KAAK,MAAM,EAAEE,WAAW,EAAE7C,MAAM8C,SAAS,EAAE,IAAIP,WAAY;QACzD,0EAA0E;QAC1E,yEAAyE;QACzE,wDAAwD;QACxD,MAAMQ,mBAAmBH,aAAaD;QACtCC,WAAWI,8CACTJ,UACAE,WACAD,aACAE,kBACA;IAEJ;IAEA,OAAOH;AACT;AAEA,SAASI,8CACPC,eAAkC,EAClCC,KAAwB,EACxBL,WAA8B,EAC9BE,gBAAyB,EACzBI,KAAa;IAEb,IAAIA,UAAUN,YAAYO,MAAM,EAAE;QAChC,yDAAyD;QACzD,OAAOF;IACT;IAEA,sEAAsE;IACtE,6CAA6C;IAC7C,EAAE;IACF,6DAA6D;IAC7D,EAAE;IACF,0EAA0E;IAC1E,EAAE;IACF,4EAA4E;IAC5E,uEAAuE;IACvE,4EAA4E;IAC5E,8CAA8C;IAC9C,MAAMG,0BAAkCR,WAAW,CAACM,MAAM;IAC1D,+EAA+E;IAE/E,MAAMG,eAAeL,eAAe,CAAC,EAAE;IACvC,MAAMM,cAAiE,CAAC;IACxE,IAAK,MAAMjC,oBAAoBgC,aAAc;QAC3C,IAAIhC,qBAAqB+B,yBAAyB;YAChD,MAAMG,uBAAuBF,YAAY,CAAChC,iBAAiB;YAC3DiC,WAAW,CAACjC,iBAAiB,GAC3B0B,8CACEQ,sBACAN,OACAL,aACAE,kBACA,AACA,+BAA+B,4BAD4B;YAE3DI,QAAQ;QAEd,OAAO;YACL,uDAAuD;YACvDI,WAAW,CAACjC,iBAAiB,GAAGgC,YAAY,CAAChC,iBAAiB;QAChE;IACF;IAEA,IAAIyB,kBAAkB;QACpB,yEAAyE;QACzE,WAAW;QACXE,eAAe,CAAC,EAAE,GAAGM;QACrB,OAAON;IACT;IAEA,4CAA4C;IAC5C,EAAE;IACF,4EAA4E;IAC5E,2EAA2E;IAC3E,uCAAuC;IACvC,MAAMQ,QAA2B;QAACR,eAAe,CAAC,EAAE;QAAEM;KAAY;IAClE,IAAI,KAAKN,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,OAAOQ;AACT", "ignoreList": [0]}}, {"offset": {"line": 1343, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/cache-key.ts"], "sourcesContent": ["// TypeScript trick to simulate opaque types, like in Flow.\ntype Opaque<K, T> = T & { __brand: K }\n\n// Only functions in this module should be allowed to create CacheKeys.\nexport type NormalizedHref = Opaque<'NormalizedHref', string>\nexport type NormalizedNextUrl = Opaque<'NormalizedNextUrl', string>\n\nexport type RouteCacheKey = Opaque<\n  'RouteCacheKey',\n  {\n    href: NormalizedHref\n    nextUrl: NormalizedNextUrl | null\n  }\n>\n\nexport function createCacheKey(\n  originalHref: string,\n  nextUrl: string | null\n): RouteCacheKey {\n  const originalUrl = new URL(originalHref)\n\n  // TODO: As of now, we never include search params in the cache key because\n  // per-segment prefetch requests are always static, and cannot contain search\n  // params. But to support <Link prefetch={true}>, we will sometimes populate\n  // the cache with dynamic data, so this will have to change.\n  originalUrl.search = ''\n\n  const normalizedHref = originalUrl.href as NormalizedHref\n  const normalizedNextUrl = nextUrl as NormalizedNextUrl | null\n\n  const cacheKey = {\n    href: normalizedHref,\n    nextUrl: normalizedNextUrl,\n  } as <PERSON><PERSON><PERSON><PERSON><PERSON>\n\n  return cacheKey\n}\n"], "names": ["createCacheKey", "originalHref", "nextUrl", "originalUrl", "URL", "search", "normalizedHref", "href", "normalizedNextUrl", "cache<PERSON>ey"], "mappings": "AAAA,2DAA2D;;;;AAepD,SAASA,eACdC,YAAoB,EACpBC,OAAsB;IAEtB,MAAMC,cAAc,IAAIC,IAAIH;IAE5B,2EAA2E;IAC3E,6EAA6E;IAC7E,4EAA4E;IAC5E,4DAA4D;IAC5DE,YAAYE,MAAM,GAAG;IAErB,MAAMC,iBAAiBH,YAAYI,IAAI;IACvC,MAAMC,oBAAoBN;IAE1B,MAAMO,WAAW;QACfF,MAAMD;QACNJ,SAASM;IACX;IAEA,OAAOC;AACT", "ignoreList": [0]}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/tuple-map.ts"], "sourcesContent": ["// Utility type. Prefix<[A, B, C, D]> matches [A], [A, B], [A, B, C] etc.\nexport type Prefix<T extends any[]> = T extends [infer First, ...infer Rest]\n  ? [] | [First] | [First, ...Prefix<Rest>]\n  : []\n\nexport type TupleMap<Keypath extends Array<any>, V> = {\n  set(keys: Prefix<Keypath>, value: V): void\n  get(keys: Prefix<Keypath>): V | null\n  delete(keys: Prefix<Keypath>): void\n}\n\n/**\n * Creates a map whose keys are tuples. Tuples are compared per-element. This\n * is useful when a key has multiple parts, but you don't want to concatenate\n * them into a single string value.\n *\n * In the Segment Cache, we use this to store cache entries by both their href\n * and their Next-URL.\n *\n * Example:\n *   map.set(['https://localhost', 'foo/bar/baz'], 'yay');\n *   map.get(['https://localhost', 'foo/bar/baz']); // returns 'yay'\n */\nexport function createTupleMap<Keypath extends Array<any>, V>(): TupleMap<\n  Keypath,\n  V\n> {\n  type MapEntryShared = {\n    parent: MapEntry | null\n    key: any\n    map: Map<any, MapEntry> | null\n  }\n\n  type EmptyMapEntry = MapEntryShared & {\n    value: null\n    hasValue: false\n  }\n\n  type FullMapEntry = MapEntryShared & {\n    value: V\n    hasValue: true\n  }\n\n  type MapEntry = EmptyMapEntry | FullMapEntry\n\n  let rootEntry: MapEntry = {\n    parent: null,\n    key: null,\n    hasValue: false,\n    value: null,\n    map: null,\n  }\n\n  // To optimize successive lookups, we cache the last accessed keypath.\n  // Although it's not encoded in the type, these are both null or\n  // both non-null. It uses object equality, so to take advantage of this\n  // optimization, you must pass the same array instance to each successive\n  // method call, and you must also not mutate the array between calls.\n  let lastAccessedEntry: MapEntry | null = null\n  let lastAccessedKeys: Prefix<Keypath> | null = null\n\n  function getOrCreateEntry(keys: Prefix<Keypath>): MapEntry {\n    if (lastAccessedKeys === keys) {\n      return lastAccessedEntry!\n    }\n\n    // Go through each level of keys until we find the entry that matches,\n    // or create a new one if it doesn't already exist.\n    let entry = rootEntry\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i]\n      let map = entry.map\n      if (map !== null) {\n        const existingEntry = map.get(key)\n        if (existingEntry !== undefined) {\n          // Found a match. Keep going.\n          entry = existingEntry\n          continue\n        }\n      } else {\n        map = new Map()\n        entry.map = map\n      }\n      // No entry exists yet at this level. Create a new one.\n      const newEntry: MapEntry = {\n        parent: entry,\n        key,\n        value: null,\n        hasValue: false,\n        map: null,\n      }\n      map.set(key, newEntry)\n      entry = newEntry\n    }\n\n    lastAccessedKeys = keys\n    lastAccessedEntry = entry\n\n    return entry\n  }\n\n  function getEntryIfExists(keys: Prefix<Keypath>): MapEntry | null {\n    if (lastAccessedKeys === keys) {\n      return lastAccessedEntry\n    }\n\n    // Go through each level of keys until we find the entry that matches, or\n    // return null if no match exists.\n    let entry = rootEntry\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i]\n      let map = entry.map\n      if (map !== null) {\n        const existingEntry = map.get(key)\n        if (existingEntry !== undefined) {\n          // Found a match. Keep going.\n          entry = existingEntry\n          continue\n        }\n      }\n      // No entry exists at this level.\n      return null\n    }\n\n    lastAccessedKeys = keys\n    lastAccessedEntry = entry\n\n    return entry\n  }\n\n  function set(keys: Prefix<Keypath>, value: V): void {\n    const entry = getOrCreateEntry(keys)\n    entry.hasValue = true\n    entry.value = value\n  }\n\n  function get(keys: Prefix<Keypath>): V | null {\n    const entry = getEntryIfExists(keys)\n    if (entry === null || !entry.hasValue) {\n      return null\n    }\n    return entry.value\n  }\n\n  function deleteEntry(keys: Prefix<Keypath>): void {\n    const entry = getEntryIfExists(keys)\n    if (entry === null || !entry.hasValue) {\n      return\n    }\n\n    // Found a match. Delete it from the cache.\n    const deletedEntry: EmptyMapEntry = entry as any\n    deletedEntry.hasValue = false\n    deletedEntry.value = null\n\n    // Check if we can garbage collect the entry.\n    if (deletedEntry.map === null) {\n      // Since this entry has no value, and also no child entries, we can\n      // garbage collect it. Remove it from its parent, and keep garbage\n      // collecting the parents until we reach a non-empty entry.\n\n      // Unlike a `set` operation, these are no longer valid because the entry\n      // itself is being modified, not just the value it contains.\n      lastAccessedEntry = null\n      lastAccessedKeys = null\n\n      let parent = deletedEntry.parent\n      let key = deletedEntry.key\n      while (parent !== null) {\n        const parentMap = parent.map\n        if (parentMap !== null) {\n          parentMap.delete(key)\n          if (parentMap.size === 0) {\n            // We just removed the last entry in the parent map.\n            parent.map = null\n            if (parent.value === null) {\n              // The parent node has no child entries, nor does it have a value\n              // on itself. It can be garbage collected. Keep going.\n              key = parent.key\n              parent = parent.parent\n              continue\n            }\n          }\n        }\n        // The parent is not empty. Stop garbage collecting.\n        break\n      }\n    }\n  }\n\n  return {\n    set,\n    get,\n    delete: deleteEntry,\n  }\n}\n"], "names": ["createTupleMap", "rootEntry", "parent", "key", "hasValue", "value", "map", "lastAccessedEntry", "lastAccessedKeys", "getOrCreateEntry", "keys", "entry", "i", "length", "existingEntry", "get", "undefined", "Map", "newEntry", "set", "getEntryIfExists", "deleteEntry", "deletedEntry", "parentMap", "delete", "size"], "mappings": "AAAA,yEAAyE;AAWzE;;;;;;;;;;;CAWC,GACD;;;AAAO,SAASA;IAsBd,IAAIC,YAAsB;QACxBC,QAAQ;QACRC,KAAK;QACLC,UAAU;QACVC,OAAO;QACPC,KAAK;IACP;IAEA,sEAAsE;IACtE,gEAAgE;IAChE,uEAAuE;IACvE,yEAAyE;IACzE,qEAAqE;IACrE,IAAIC,oBAAqC;IACzC,IAAIC,mBAA2C;IAE/C,SAASC,iBAAiBC,IAAqB;QAC7C,IAAIF,qBAAqBE,MAAM;YAC7B,OAAOH;QACT;QAEA,sEAAsE;QACtE,mDAAmD;QACnD,IAAII,QAAQV;QACZ,IAAK,IAAIW,IAAI,GAAGA,IAAIF,KAAKG,MAAM,EAAED,IAAK;YACpC,MAAMT,MAAMO,IAAI,CAACE,EAAE;YACnB,IAAIN,MAAMK,MAAML,GAAG;YACnB,IAAIA,QAAQ,MAAM;gBAChB,MAAMQ,gBAAgBR,IAAIS,GAAG,CAACZ;gBAC9B,IAAIW,kBAAkBE,WAAW;oBAC/B,6BAA6B;oBAC7BL,QAAQG;oBACR;gBACF;YACF,OAAO;gBACLR,MAAM,IAAIW;gBACVN,MAAML,GAAG,GAAGA;YACd;YACA,uDAAuD;YACvD,MAAMY,WAAqB;gBACzBhB,QAAQS;gBACRR;gBACAE,OAAO;gBACPD,UAAU;gBACVE,KAAK;YACP;YACAA,IAAIa,GAAG,CAAChB,KAAKe;YACbP,QAAQO;QACV;QAEAV,mBAAmBE;QACnBH,oBAAoBI;QAEpB,OAAOA;IACT;IAEA,SAASS,iBAAiBV,IAAqB;QAC7C,IAAIF,qBAAqBE,MAAM;YAC7B,OAAOH;QACT;QAEA,yEAAyE;QACzE,kCAAkC;QAClC,IAAII,QAAQV;QACZ,IAAK,IAAIW,IAAI,GAAGA,IAAIF,KAAKG,MAAM,EAAED,IAAK;YACpC,MAAMT,MAAMO,IAAI,CAACE,EAAE;YACnB,IAAIN,MAAMK,MAAML,GAAG;YACnB,IAAIA,QAAQ,MAAM;gBAChB,MAAMQ,gBAAgBR,IAAIS,GAAG,CAACZ;gBAC9B,IAAIW,kBAAkBE,WAAW;oBAC/B,6BAA6B;oBAC7BL,QAAQG;oBACR;gBACF;YACF;YACA,iCAAiC;YACjC,OAAO;QACT;QAEAN,mBAAmBE;QACnBH,oBAAoBI;QAEpB,OAAOA;IACT;IAEA,SAASQ,IAAIT,IAAqB,EAAEL,KAAQ;QAC1C,MAAMM,QAAQF,iBAAiBC;QAC/BC,MAAMP,QAAQ,GAAG;QACjBO,MAAMN,KAAK,GAAGA;IAChB;IAEA,SAASU,IAAIL,IAAqB;QAChC,MAAMC,QAAQS,iBAAiBV;QAC/B,IAAIC,UAAU,QAAQ,CAACA,MAAMP,QAAQ,EAAE;YACrC,OAAO;QACT;QACA,OAAOO,MAAMN,KAAK;IACpB;IAEA,SAASgB,YAAYX,IAAqB;QACxC,MAAMC,QAAQS,iBAAiBV;QAC/B,IAAIC,UAAU,QAAQ,CAACA,MAAMP,QAAQ,EAAE;YACrC;QACF;QAEA,2CAA2C;QAC3C,MAAMkB,eAA8BX;QACpCW,aAAalB,QAAQ,GAAG;QACxBkB,aAAajB,KAAK,GAAG;QAErB,6CAA6C;QAC7C,IAAIiB,aAAahB,GAAG,KAAK,MAAM;YAC7B,mEAAmE;YACnE,kEAAkE;YAClE,2DAA2D;YAE3D,wEAAwE;YACxE,4DAA4D;YAC5DC,oBAAoB;YACpBC,mBAAmB;YAEnB,IAAIN,SAASoB,aAAapB,MAAM;YAChC,IAAIC,MAAMmB,aAAanB,GAAG;YAC1B,MAAOD,WAAW,KAAM;gBACtB,MAAMqB,YAAYrB,OAAOI,GAAG;gBAC5B,IAAIiB,cAAc,MAAM;oBACtBA,UAAUC,MAAM,CAACrB;oBACjB,IAAIoB,UAAUE,IAAI,KAAK,GAAG;wBACxB,oDAAoD;wBACpDvB,OAAOI,GAAG,GAAG;wBACb,IAAIJ,OAAOG,KAAK,KAAK,MAAM;4BACzB,iEAAiE;4BACjE,sDAAsD;4BACtDF,MAAMD,OAAOC,GAAG;4BAChBD,SAASA,OAAOA,MAAM;4BACtB;wBACF;oBACF;gBACF;gBAEA;YACF;QACF;IACF;IAEA,OAAO;QACLiB;QACAJ;QACAS,QAAQH;IACV;AACF", "ignoreList": [0]}}, {"offset": {"line": 1523, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/lru.ts"], "sourcesContent": ["export type LRU<T extends LRUNode> = {\n  put(node: T): void\n  delete(node: T): void\n  updateSize(node: T, size: number): void\n}\n\n// Doubly-linked list\ntype LRUNode<T = any> = {\n  // Although it's not encoded in the type, these are both null if the node is\n  // not in the LRU; both non-null if it is.\n  prev: T | null\n  next: T | null\n  size: number\n}\n\n// Rather than create an internal LRU node, the passed-in type must conform\n// the LRUNode interface. This is just a memory optimization to avoid creating\n// another object; we only use this for Segment Cache entries so it doesn't need\n// to be general purpose.\nexport function createLRU<T extends LRUNode>(\n  // From the LRU's perspective, the size unit is arbitrary, but for our\n  // purposes this is the byte size.\n  maxLruSize: number,\n  onEviction: (node: T) => void\n): LRU<T> {\n  let head: T | null = null\n  let didScheduleCleanup: boolean = false\n  let lruSize: number = 0\n\n  function put(node: T) {\n    if (head === node) {\n      // Already at the head\n      return\n    }\n    const prev = node.prev\n    const next = node.next\n    if (next === null || prev === null) {\n      // This is an insertion\n      lruSize += node.size\n      // Whenever we add an entry, we need to check if we've exceeded the\n      // max size. We don't evict entries immediately; they're evicted later in\n      // an asynchronous task.\n      ensureCleanupIsScheduled()\n    } else {\n      // This is a move. Remove from its current position.\n      prev.next = next\n      next.prev = prev\n    }\n\n    // Move to the front of the list\n    if (head === null) {\n      // This is the first entry\n      node.prev = node\n      node.next = node\n    } else {\n      // Add to the front of the list\n      const tail = head.prev\n      node.prev = tail\n      tail.next = node\n      node.next = head\n      head.prev = node\n    }\n    head = node\n  }\n\n  function updateSize(node: T, newNodeSize: number) {\n    // This is a separate function so that we can resize the entry after it's\n    // already been inserted.\n    if (node.next === null) {\n      // No longer part of LRU.\n      return\n    }\n    const prevNodeSize = node.size\n    node.size = newNodeSize\n    lruSize = lruSize - prevNodeSize + newNodeSize\n    ensureCleanupIsScheduled()\n  }\n\n  function deleteNode(deleted: T) {\n    const next = deleted.next\n    const prev = deleted.prev\n    if (next !== null && prev !== null) {\n      lruSize -= deleted.size\n\n      deleted.next = null\n      deleted.prev = null\n\n      // Remove from the list\n      if (head === deleted) {\n        // Update the head\n        if (next === head) {\n          // This was the last entry\n          head = null\n        } else {\n          head = next\n        }\n      } else {\n        prev.next = next\n        next.prev = prev\n      }\n    } else {\n      // Already deleted\n    }\n  }\n\n  function ensureCleanupIsScheduled() {\n    if (didScheduleCleanup || lruSize <= maxLruSize) {\n      return\n    }\n    didScheduleCleanup = true\n    requestCleanupCallback(cleanup)\n  }\n\n  function cleanup() {\n    didScheduleCleanup = false\n\n    // Evict entries until we're at 90% capacity. We can assume this won't\n    // infinite loop because even if `maxLruSize` were 0, eventually\n    // `deleteNode` sets `head` to `null` when we run out entries.\n    const ninetyPercentMax = maxLruSize * 0.9\n    while (lruSize > ninetyPercentMax && head !== null) {\n      const tail = head.prev\n      deleteNode(tail)\n      onEviction(tail)\n    }\n  }\n\n  return {\n    put,\n    delete: deleteNode,\n    updateSize,\n  }\n}\n\nconst requestCleanupCallback =\n  typeof requestIdleCallback === 'function'\n    ? requestIdleCallback\n    : (cb: () => void) => setTimeout(cb, 0)\n"], "names": ["createLRU", "maxLruSize", "onEviction", "head", "didScheduleCleanup", "lruSize", "put", "node", "prev", "next", "size", "ensureCleanupIsScheduled", "tail", "updateSize", "newNodeSize", "prevNodeSize", "deleteNode", "deleted", "requestCleanupCallback", "cleanup", "ninetyPercentMax", "delete", "requestIdleCallback", "cb", "setTimeout"], "mappings": "AAeA,2EAA2E;AAC3E,8EAA8E;AAC9E,gFAAgF;AAChF,yBAAyB;;;;AAClB,SAASA,UACd,AACA,kCAAkC,oCADoC;AAEtEC,UAAkB,EAClBC,UAA6B;IAE7B,IAAIC,OAAiB;IACrB,IAAIC,qBAA8B;IAClC,IAAIC,UAAkB;IAEtB,SAASC,IAAIC,IAAO;QAClB,IAAIJ,SAASI,MAAM;YACjB,sBAAsB;YACtB;QACF;QACA,MAAMC,OAAOD,KAAKC,IAAI;QACtB,MAAMC,OAAOF,KAAKE,IAAI;QACtB,IAAIA,SAAS,QAAQD,SAAS,MAAM;YAClC,uBAAuB;YACvBH,WAAWE,KAAKG,IAAI;YACpB,mEAAmE;YACnE,yEAAyE;YACzE,wBAAwB;YACxBC;QACF,OAAO;YACL,oDAAoD;YACpDH,KAAKC,IAAI,GAAGA;YACZA,KAAKD,IAAI,GAAGA;QACd;QAEA,gCAAgC;QAChC,IAAIL,SAAS,MAAM;YACjB,0BAA0B;YAC1BI,KAAKC,IAAI,GAAGD;YACZA,KAAKE,IAAI,GAAGF;QACd,OAAO;YACL,+BAA+B;YAC/B,MAAMK,OAAOT,KAAKK,IAAI;YACtBD,KAAKC,IAAI,GAAGI;YACZA,KAAKH,IAAI,GAAGF;YACZA,KAAKE,IAAI,GAAGN;YACZA,KAAKK,IAAI,GAAGD;QACd;QACAJ,OAAOI;IACT;IAEA,SAASM,WAAWN,IAAO,EAAEO,WAAmB;QAC9C,yEAAyE;QACzE,yBAAyB;QACzB,IAAIP,KAAKE,IAAI,KAAK,MAAM;YACtB,yBAAyB;YACzB;QACF;QACA,MAAMM,eAAeR,KAAKG,IAAI;QAC9BH,KAAKG,IAAI,GAAGI;QACZT,UAAUA,UAAUU,eAAeD;QACnCH;IACF;IAEA,SAASK,WAAWC,OAAU;QAC5B,MAAMR,OAAOQ,QAAQR,IAAI;QACzB,MAAMD,OAAOS,QAAQT,IAAI;QACzB,IAAIC,SAAS,QAAQD,SAAS,MAAM;YAClCH,WAAWY,QAAQP,IAAI;YAEvBO,QAAQR,IAAI,GAAG;YACfQ,QAAQT,IAAI,GAAG;YAEf,uBAAuB;YACvB,IAAIL,SAASc,SAAS;gBACpB,kBAAkB;gBAClB,IAAIR,SAASN,MAAM;oBACjB,0BAA0B;oBAC1BA,OAAO;gBACT,OAAO;oBACLA,OAAOM;gBACT;YACF,OAAO;gBACLD,KAAKC,IAAI,GAAGA;gBACZA,KAAKD,IAAI,GAAGA;YACd;QACF,OAAO;QACL,kBAAkB;QACpB;IACF;IAEA,SAASG;QACP,IAAIP,sBAAsBC,WAAWJ,YAAY;YAC/C;QACF;QACAG,qBAAqB;QACrBc,uBAAuBC;IACzB;IAEA,SAASA;QACPf,qBAAqB;QAErB,sEAAsE;QACtE,gEAAgE;QAChE,8DAA8D;QAC9D,MAAMgB,mBAAmBnB,aAAa;QACtC,MAAOI,UAAUe,oBAAoBjB,SAAS,KAAM;YAClD,MAAMS,OAAOT,KAAKK,IAAI;YACtBQ,WAAWJ;YACXV,WAAWU;QACb;IACF;IAEA,OAAO;QACLN;QACAe,QAAQL;QACRH;IACF;AACF;AAEA,MAAMK,yBACJ,OAAOI,wBAAwB,aAC3BA,sBACA,CAACC,KAAmBC,WAAWD,IAAI", "ignoreList": [0]}}, {"offset": {"line": 1637, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/cache.ts"], "sourcesContent": ["import type {\n  TreePrefetch,\n  RootTreePrefetch,\n  SegmentPrefetch,\n} from '../../../server/app-render/collect-segment-data'\nimport type { LoadingModuleData } from '../../../shared/lib/app-router-context.shared-runtime'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n  RSC_HEADER,\n} from '../app-router-headers'\nimport {\n  createFetch,\n  createFromNextReadableStream,\n  urlToUrlWithoutFlightMarker,\n  type RequestHeaders,\n} from '../router-reducer/fetch-server-response'\nimport {\n  trackPrefetchRequestBandwidth,\n  pingPrefetchTask,\n  type PrefetchTask,\n  spawnPrefetchSubtask,\n} from './scheduler'\nimport { getAppBuildId } from '../../app-build-id'\nimport { createHrefFromUrl } from '../router-reducer/create-href-from-url'\nimport type {\n  NormalizedHref,\n  NormalizedNextUrl,\n  RouteCacheKey,\n} from './cache-key'\nimport { createTupleMap, type TupleMap, type Prefix } from './tuple-map'\nimport { createLRU, type LRU } from './lru'\n\n// A note on async/await when working in the prefetch cache:\n//\n// Most async operations in the prefetch cache should *not* use async/await,\n// Instead, spawn a subtask that writes the results to a cache entry, and attach\n// a \"ping\" listener to notify the prefetch queue to try again.\n//\n// The reason is we need to be able to access the segment cache and traverse its\n// data structures synchronously. For example, if there's a synchronous update\n// we can take an immediate snapshot of the cache to produce something we can\n// render. Limiting the use of async/await also makes it easier to avoid race\n// conditions, which is especially important because is cache is mutable.\n//\n// Another reason is that while we're performing async work, it's possible for\n// existing entries to become stale, or for Link prefetches to be removed from\n// the queue. For optimal scheduling, we need to be able to \"cancel\" subtasks\n// that are no longer needed. So, when a segment is received from the server, we\n// restart from the root of the tree that's being prefetched, to confirm all the\n// parent segments are still cached. If the segment is no longer reachable from\n// the root, then it's effectively canceled. This is similar to the design of\n// Rust Futures, or React Suspense.\n\ntype RouteCacheEntryShared = {\n  staleAt: number\n  // This is false only if we're certain the route cannot be intercepted. It's\n  // true in all other cases, including on initialization when we haven't yet\n  // received a response from the server.\n  couldBeIntercepted: boolean\n\n  // LRU-related fields\n  keypath: null | Prefix<RouteCacheKeypath>\n  next: null | RouteCacheEntry\n  prev: null | RouteCacheEntry\n  size: number\n}\n\nexport const enum EntryStatus {\n  Pending,\n  Rejected,\n  Fulfilled,\n}\n\ntype PendingRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Pending\n  blockedTasks: Set<PrefetchTask> | null\n  canonicalUrl: null\n  tree: null\n  head: null\n  isHeadPartial: true\n}\n\ntype RejectedRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Rejected\n  blockedTasks: Set<PrefetchTask> | null\n  canonicalUrl: null\n  tree: null\n  head: null\n  isHeadPartial: true\n}\n\nexport type FulfilledRouteCacheEntry = RouteCacheEntryShared & {\n  status: EntryStatus.Fulfilled\n  blockedTasks: null\n  canonicalUrl: string\n  tree: TreePrefetch\n  head: React.ReactNode | null\n  isHeadPartial: boolean\n}\n\nexport type RouteCacheEntry =\n  | PendingRouteCacheEntry\n  | FulfilledRouteCacheEntry\n  | RejectedRouteCacheEntry\n\ntype SegmentCacheEntryShared = {\n  staleAt: number\n\n  // LRU-related fields\n  key: null | string\n  next: null | RouteCacheEntry\n  prev: null | RouteCacheEntry\n  size: number\n}\n\ntype PendingSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Pending\n  rsc: null\n  loading: null\n  isPartial: true\n  promise: null | PromiseWithResolvers<FulfilledSegmentCacheEntry | null>\n}\n\ntype RejectedSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Rejected\n  rsc: null\n  loading: null\n  isPartial: true\n  promise: null\n}\n\ntype FulfilledSegmentCacheEntry = SegmentCacheEntryShared & {\n  status: EntryStatus.Fulfilled\n  rsc: React.ReactNode | null\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  isPartial: boolean\n  promise: null\n}\n\nexport type SegmentCacheEntry =\n  | PendingSegmentCacheEntry\n  | RejectedSegmentCacheEntry\n  | FulfilledSegmentCacheEntry\n\n// Route cache entries vary on multiple keys: the href and the Next-Url. Each of\n// these parts needs to be included in the internal cache key. Rather than\n// concatenate the keys into a single key, we use a multi-level map, where the\n// first level is keyed by href, the second level is keyed by Next-Url, and so\n// on (if were to add more levels).\ntype RouteCacheKeypath = [NormalizedHref, NormalizedNextUrl]\nconst routeCacheMap: TupleMap<RouteCacheKeypath, RouteCacheEntry> =\n  createTupleMap()\n\n// We use an LRU for memory management. We must update this whenever we add or\n// remove a new cache entry, or when an entry changes size.\n// TODO: I chose the max size somewhat arbitrarily. Consider setting this based\n// on navigator.deviceMemory, or some other heuristic. We should make this\n// customizable via the Next.js config, too.\nconst maxRouteLruSize = 10 * 1024 * 1024 // 10 MB\nconst routeCacheLru = createLRU<RouteCacheEntry>(\n  maxRouteLruSize,\n  onRouteLRUEviction\n)\n\n// TODO: We may eventually store segment entries in a tuple map, too, to\n// account for search params.\nconst segmentCacheMap = new Map<string, SegmentCacheEntry>()\n// NOTE: Segments and Route entries are managed by separate LRUs. We could\n// combine them into a single LRU, but because they are separate types, we'd\n// need to wrap each one in an extra LRU node (to maintain monomorphism, at the\n// cost of additional memory).\nconst maxSegmentLruSize = 50 * 1024 * 1024 // 50 MB\nconst segmentCacheLru = createLRU<SegmentCacheEntry>(\n  maxSegmentLruSize,\n  onSegmentLRUEviction\n)\n\nexport function readExactRouteCacheEntry(\n  now: number,\n  href: NormalizedHref,\n  nextUrl: NormalizedNextUrl | null\n): RouteCacheEntry | null {\n  const keypath: Prefix<RouteCacheKeypath> =\n    nextUrl === null ? [href] : [href, nextUrl]\n  const existingEntry = routeCacheMap.get(keypath)\n  if (existingEntry !== null) {\n    // Check if the entry is stale\n    if (existingEntry.staleAt > now) {\n      // Reuse the existing entry.\n\n      // Since this is an access, move the entry to the front of the LRU.\n      routeCacheLru.put(existingEntry)\n\n      return existingEntry\n    } else {\n      // Evict the stale entry from the cache.\n      deleteRouteFromCache(existingEntry, keypath)\n    }\n  }\n  return null\n}\n\nexport function readRouteCacheEntry(\n  now: number,\n  key: RouteCacheKey\n): RouteCacheEntry | null {\n  // First check if there's a non-intercepted entry. Most routes cannot be\n  // intercepted, so this is the common case.\n  const nonInterceptedEntry = readExactRouteCacheEntry(now, key.href, null)\n  if (nonInterceptedEntry !== null && !nonInterceptedEntry.couldBeIntercepted) {\n    // Found a match, and the route cannot be intercepted. We can reuse it.\n    return nonInterceptedEntry\n  }\n  // There was no match. Check again but include the Next-Url this time.\n  return readExactRouteCacheEntry(now, key.href, key.nextUrl)\n}\n\nexport function readSegmentCacheEntry(\n  now: number,\n  path: string\n): SegmentCacheEntry | null {\n  const existingEntry = segmentCacheMap.get(path)\n  if (existingEntry !== undefined) {\n    // Check if the entry is stale\n    if (existingEntry.staleAt > now) {\n      // Reuse the existing entry.\n\n      // Since this is an access, move the entry to the front of the LRU.\n      segmentCacheLru.put(existingEntry)\n\n      return existingEntry\n    } else {\n      // Evict the stale entry from the cache.\n      deleteSegmentFromCache(existingEntry, path)\n    }\n  }\n  return null\n}\n\nexport function waitForSegmentCacheEntry(\n  pendingEntry: PendingSegmentCacheEntry\n): Promise<FulfilledSegmentCacheEntry | null> {\n  // Because the entry is pending, there's already a in-progress request.\n  // Attach a promise to the entry that will resolve when the server responds.\n  let promiseWithResolvers = pendingEntry.promise\n  if (promiseWithResolvers === null) {\n    promiseWithResolvers = pendingEntry.promise =\n      createPromiseWithResolvers<FulfilledSegmentCacheEntry | null>()\n  } else {\n    // There's already a promise we can use\n  }\n  return promiseWithResolvers.promise\n}\n\n/**\n * Reads the route cache for a matching entry *and* spawns a request if there's\n * no match. Because this may issue a network request, it should only be called\n * from within the context of a prefetch task.\n */\nexport function requestRouteCacheEntryFromCache(\n  now: number,\n  task: PrefetchTask\n): RouteCacheEntry {\n  const key = task.key\n  // First check if there's a non-intercepted entry. Most routes cannot be\n  // intercepted, so this is the common case.\n  const nonInterceptedEntry = readExactRouteCacheEntry(now, key.href, null)\n  if (nonInterceptedEntry !== null && !nonInterceptedEntry.couldBeIntercepted) {\n    // Found a match, and the route cannot be intercepted. We can reuse it.\n    return nonInterceptedEntry\n  }\n  // There was no match. Check again but include the Next-Url this time.\n  const exactEntry = readExactRouteCacheEntry(now, key.href, key.nextUrl)\n  if (exactEntry !== null) {\n    return exactEntry\n  }\n  // Create a pending entry and spawn a request for its data.\n  const pendingEntry: PendingRouteCacheEntry = {\n    canonicalUrl: null,\n    status: EntryStatus.Pending,\n    blockedTasks: null,\n    tree: null,\n    head: null,\n    isHeadPartial: true,\n    // If the request takes longer than a minute, a subsequent request should\n    // retry instead of waiting for this one.\n    //\n    // When the response is received, this value will be replaced by a new value\n    // based on the stale time sent from the server.\n    staleAt: now + 60 * 1000,\n    // This is initialized to true because we don't know yet whether the route\n    // could be intercepted. It's only set to false once we receive a response\n    // from the server.\n    couldBeIntercepted: true,\n\n    // LRU-related fields\n    keypath: null,\n    next: null,\n    prev: null,\n    size: 0,\n  }\n  spawnPrefetchSubtask(fetchRouteOnCacheMiss(pendingEntry, task))\n  const keypath: Prefix<RouteCacheKeypath> =\n    key.nextUrl === null ? [key.href] : [key.href, key.nextUrl]\n  routeCacheMap.set(keypath, pendingEntry)\n  // Stash the keypath on the entry so we know how to remove it from the map\n  // if it gets evicted from the LRU.\n  pendingEntry.keypath = keypath\n  routeCacheLru.put(pendingEntry)\n  return pendingEntry\n}\n\n/**\n * Reads the route cache for a matching entry *and* spawns a request if there's\n * no match. Because this may issue a network request, it should only be called\n * from within the context of a prefetch task.\n */\nexport function requestSegmentEntryFromCache(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  path: string,\n  accessToken: string\n): SegmentCacheEntry {\n  const existingEntry = readSegmentCacheEntry(now, path)\n  if (existingEntry !== null) {\n    return existingEntry\n  }\n  // Create a pending entry and spawn a request for its data.\n  const pendingEntry: PendingSegmentCacheEntry = {\n    status: EntryStatus.Pending,\n    rsc: null,\n    loading: null,\n    staleAt: route.staleAt,\n    isPartial: true,\n    promise: null,\n\n    // LRU-related fields\n    key: null,\n    next: null,\n    prev: null,\n    size: 0,\n  }\n  spawnPrefetchSubtask(\n    fetchSegmentEntryOnCacheMiss(\n      route,\n      pendingEntry,\n      task.key,\n      path,\n      accessToken\n    )\n  )\n  segmentCacheMap.set(path, pendingEntry)\n  // Stash the keypath on the entry so we know how to remove it from the map\n  // if it gets evicted from the LRU.\n  pendingEntry.key = path\n  segmentCacheLru.put(pendingEntry)\n  return pendingEntry\n}\n\nfunction deleteRouteFromCache(\n  entry: RouteCacheEntry,\n  keypath: Prefix<RouteCacheKeypath>\n): void {\n  pingBlockedTasks(entry)\n  routeCacheMap.delete(keypath)\n  routeCacheLru.delete(entry)\n}\n\nfunction deleteSegmentFromCache(entry: SegmentCacheEntry, key: string): void {\n  cancelEntryListeners(entry)\n  segmentCacheMap.delete(key)\n  segmentCacheLru.delete(entry)\n}\n\nfunction onRouteLRUEviction(entry: RouteCacheEntry): void {\n  // The LRU evicted this entry. Remove it from the map.\n  const keypath = entry.keypath\n  if (keypath !== null) {\n    entry.keypath = null\n    pingBlockedTasks(entry)\n    routeCacheMap.delete(keypath)\n  }\n}\n\nfunction onSegmentLRUEviction(entry: SegmentCacheEntry): void {\n  // The LRU evicted this entry. Remove it from the map.\n  const key = entry.key\n  if (key !== null) {\n    entry.key = null\n    cancelEntryListeners(entry)\n    segmentCacheMap.delete(key)\n  }\n}\n\nfunction cancelEntryListeners(entry: SegmentCacheEntry): void {\n  if (entry.status === EntryStatus.Pending && entry.promise !== null) {\n    // There were listeners for this entry. Resolve them with `null` to indicate\n    // that the prefetch failed. It's up to the listener to decide how to handle\n    // this case.\n    // NOTE: We don't currently propagate the reason the prefetch was canceled\n    // but we could by accepting a `reason` argument.\n    entry.promise.resolve(null)\n    entry.promise = null\n  }\n}\n\nfunction pingBlockedTasks(entry: {\n  blockedTasks: Set<PrefetchTask> | null\n}): void {\n  const blockedTasks = entry.blockedTasks\n  if (blockedTasks !== null) {\n    for (const task of blockedTasks) {\n      pingPrefetchTask(task)\n    }\n    entry.blockedTasks = null\n  }\n}\n\nfunction fulfillRouteCacheEntry(\n  entry: PendingRouteCacheEntry,\n  tree: TreePrefetch,\n  head: React.ReactNode,\n  isHeadPartial: boolean,\n  staleAt: number,\n  couldBeIntercepted: boolean,\n  canonicalUrl: string\n): FulfilledRouteCacheEntry {\n  const fulfilledEntry: FulfilledRouteCacheEntry = entry as any\n  fulfilledEntry.status = EntryStatus.Fulfilled\n  fulfilledEntry.tree = tree\n  fulfilledEntry.head = head\n  fulfilledEntry.isHeadPartial = isHeadPartial\n  fulfilledEntry.staleAt = staleAt\n  fulfilledEntry.couldBeIntercepted = couldBeIntercepted\n  fulfilledEntry.canonicalUrl = canonicalUrl\n  pingBlockedTasks(entry)\n  return fulfilledEntry\n}\n\nfunction fulfillSegmentCacheEntry(\n  segmentCacheEntry: PendingSegmentCacheEntry,\n  rsc: React.ReactNode,\n  loading: LoadingModuleData | Promise<LoadingModuleData>,\n  staleAt: number,\n  isPartial: boolean\n) {\n  const fulfilledEntry: FulfilledSegmentCacheEntry = segmentCacheEntry as any\n  fulfilledEntry.status = EntryStatus.Fulfilled\n  fulfilledEntry.rsc = rsc\n  fulfilledEntry.loading = loading\n  fulfilledEntry.staleAt = staleAt\n  fulfilledEntry.isPartial = isPartial\n  // Resolve any listeners that were waiting for this data.\n  if (segmentCacheEntry.promise !== null) {\n    segmentCacheEntry.promise.resolve(fulfilledEntry)\n    // Free the promise for garbage collection.\n    fulfilledEntry.promise = null\n  }\n}\n\nfunction rejectRouteCacheEntry(\n  entry: PendingRouteCacheEntry,\n  staleAt: number\n): void {\n  const rejectedEntry: RejectedRouteCacheEntry = entry as any\n  rejectedEntry.status = EntryStatus.Rejected\n  rejectedEntry.staleAt = staleAt\n  pingBlockedTasks(entry)\n}\n\nfunction rejectSegmentCacheEntry(\n  entry: PendingSegmentCacheEntry,\n  staleAt: number\n): void {\n  const rejectedEntry: RejectedSegmentCacheEntry = entry as any\n  rejectedEntry.status = EntryStatus.Rejected\n  rejectedEntry.staleAt = staleAt\n  if (entry.promise !== null) {\n    // NOTE: We don't currently propagate the reason the prefetch was canceled\n    // but we could by accepting a `reason` argument.\n    entry.promise.resolve(null)\n    entry.promise = null\n  }\n}\n\nasync function fetchRouteOnCacheMiss(\n  entry: PendingRouteCacheEntry,\n  task: PrefetchTask\n): Promise<void> {\n  // This function is allowed to use async/await because it contains the actual\n  // fetch that gets issued on a cache miss. Notice though that it does not\n  // return anything; it writes the result to the cache entry directly, then\n  // pings the scheduler to unblock the corresponding prefetch task.\n  const key = task.key\n  const href = key.href\n  const nextUrl = key.nextUrl\n  try {\n    const response = await fetchSegmentPrefetchResponse(href, '/_tree', nextUrl)\n    if (\n      !response ||\n      !response.ok ||\n      // 204 is a Cache miss. Though theoretically this shouldn't happen when\n      // PPR is enabled, because we always respond to route tree requests, even\n      // if it needs to be blockingly generated on demand.\n      response.status === 204 ||\n      !response.body\n    ) {\n      // Server responded with an error, or with a miss. We should still cache\n      // the response, but we can try again after 10 seconds.\n      rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n      return\n    }\n    const prefetchStream = createPrefetchResponseStream(\n      response.body,\n      routeCacheLru,\n      entry\n    )\n    const serverData: RootTreePrefetch = await (createFromNextReadableStream(\n      prefetchStream\n    ) as Promise<RootTreePrefetch>)\n    if (serverData.buildId !== getAppBuildId()) {\n      // The server build does not match the client. Treat as a 404. During\n      // an actual navigation, the router will trigger an MPA navigation.\n      // TODO: Consider moving the build ID to a response header so we can check\n      // it before decoding the response, and so there's one way of checking\n      // across all response types.\n      rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n      return\n    }\n\n    // This is a bit convoluted but it's taken from router-reducer and\n    // fetch-server-response\n    const canonicalUrl = response.redirected\n      ? createHrefFromUrl(urlToUrlWithoutFlightMarker(response.url))\n      : href\n\n    // Check whether the response varies based on the Next-Url header.\n    const varyHeader = response.headers.get('vary')\n    const couldBeIntercepted =\n      varyHeader !== null && varyHeader.includes(NEXT_URL)\n\n    fulfillRouteCacheEntry(\n      entry,\n      serverData.tree,\n      serverData.head,\n      serverData.isHeadPartial,\n      Date.now() + serverData.staleTime,\n      couldBeIntercepted,\n      canonicalUrl\n    )\n\n    if (!couldBeIntercepted && nextUrl !== null) {\n      // This route will never be intercepted. So we can use this entry for all\n      // requests to this route, regardless of the Next-Url header. This works\n      // because when reading the cache we always check for a valid\n      // non-intercepted entry first.\n      //\n      // Re-key the entry. Since we're in an async task, we must first confirm\n      // that the entry hasn't been concurrently modified by a different task.\n      const currentKeypath: Prefix<RouteCacheKeypath> = [href, nextUrl]\n      const expectedEntry = routeCacheMap.get(currentKeypath)\n      if (expectedEntry === entry) {\n        routeCacheMap.delete(currentKeypath)\n        const newKeypath: Prefix<RouteCacheKeypath> = [href]\n        routeCacheMap.set(newKeypath, entry)\n        // We don't need to update the LRU because the entry is already in it.\n        // But since we changed the keypath, we do need to update that, so we\n        // know how to remove it from the map if it gets evicted from the LRU.\n        entry.keypath = newKeypath\n      } else {\n        // Something else modified this entry already. Since the re-keying is\n        // just a performance optimization, we can safely skip it.\n      }\n    }\n  } catch (error) {\n    // Either the connection itself failed, or something bad happened while\n    // decoding the response.\n    rejectRouteCacheEntry(entry, Date.now() + 10 * 1000)\n  }\n}\n\nasync function fetchSegmentEntryOnCacheMiss(\n  route: FulfilledRouteCacheEntry,\n  segmentCacheEntry: PendingSegmentCacheEntry,\n  routeKey: RouteCacheKey,\n  segmentPath: string,\n  accessToken: string | null\n): Promise<void> {\n  // This function is allowed to use async/await because it contains the actual\n  // fetch that gets issued on a cache miss. Notice though that it does not\n  // return anything; it writes the result to the cache entry directly.\n  //\n  // Segment fetches are non-blocking so we don't need to ping the scheduler\n  // on completion.\n  const href = routeKey.href\n  try {\n    const response = await fetchSegmentPrefetchResponse(\n      href,\n      accessToken === '' ? segmentPath : `${segmentPath}.${accessToken}`,\n      routeKey.nextUrl\n    )\n    if (\n      !response ||\n      !response.ok ||\n      response.status === 204 || // Cache miss\n      !response.body\n    ) {\n      // Server responded with an error, or with a miss. We should still cache\n      // the response, but we can try again after 10 seconds.\n      rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n      return\n    }\n    // Wrap the original stream in a new stream that never closes. That way the\n    // Flight client doesn't error if there's a hanging promise.\n    const prefetchStream = createPrefetchResponseStream(\n      response.body,\n      segmentCacheLru,\n      segmentCacheEntry\n    )\n    const serverData = await (createFromNextReadableStream(\n      prefetchStream\n    ) as Promise<SegmentPrefetch>)\n    if (serverData.buildId !== getAppBuildId()) {\n      // The server build does not match the client. Treat as a 404. During\n      // an actual navigation, the router will trigger an MPA navigation.\n      // TODO: Consider moving the build ID to a response header so we can check\n      // it before decoding the response, and so there's one way of checking\n      // across all response types.\n      rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n      return\n    }\n    fulfillSegmentCacheEntry(\n      segmentCacheEntry,\n      serverData.rsc,\n      serverData.loading,\n      // TODO: The server does not currently provide per-segment stale time.\n      // So we use the stale time of the route.\n      route.staleAt,\n      serverData.isPartial\n    )\n  } catch (error) {\n    // Either the connection itself failed, or something bad happened while\n    // decoding the response.\n    rejectSegmentCacheEntry(segmentCacheEntry, Date.now() + 10 * 1000)\n  }\n}\n\nasync function fetchSegmentPrefetchResponse(\n  href: NormalizedHref,\n  segmentPath: string,\n  nextUrl: NormalizedNextUrl | null\n): Promise<Response | null> {\n  const headers: RequestHeaders = {\n    [RSC_HEADER]: '1',\n    [NEXT_ROUTER_PREFETCH_HEADER]: '1',\n    [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]: segmentPath,\n  }\n  if (nextUrl !== null) {\n    headers[NEXT_URL] = nextUrl\n  }\n  const fetchPriority = 'low'\n  const responsePromise = createFetch(new URL(href), headers, fetchPriority)\n  trackPrefetchRequestBandwidth(responsePromise)\n  const response = await responsePromise\n  const contentType = response.headers.get('content-type')\n  const isFlightResponse =\n    contentType && contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n  if (!response.ok || !isFlightResponse) {\n    return null\n  }\n  return response\n}\n\nfunction createPrefetchResponseStream<\n  T extends RouteCacheEntry | SegmentCacheEntry,\n>(\n  originalFlightStream: ReadableStream<Uint8Array>,\n  lru: LRU<T>,\n  lruEntry: T\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  //\n  // While processing the original stream, we also incrementally update the size\n  // of the cache entry in the LRU.\n  let totalByteLength = 0\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n\n          // Incrementally update the size of the cache entry in the LRU.\n          // NOTE: Since prefetch responses are delivered in a single chunk,\n          // it's not really necessary to do this streamingly, but I'm doing it\n          // anyway in case this changes in the future.\n          totalByteLength += value.byteLength\n          lru.updateSize(lruEntry, totalByteLength)\n\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n\nfunction createPromiseWithResolvers<T>(): PromiseWithResolvers<T> {\n  // Shim of Stage 4 Promise.withResolvers proposal\n  let resolve: (value: T | PromiseLike<T>) => void\n  let reject: (reason: any) => void\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res\n    reject = rej\n  })\n  return { resolve: resolve!, reject: reject!, promise }\n}\n"], "names": ["NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER", "createFetch", "createFromNextReadableStream", "urlToUrlWithoutFlightMarker", "trackPrefetchRequestBandwidth", "pingPrefetchTask", "spawnPrefetchSubtask", "getAppBuildId", "createHrefFromUrl", "createTupleMap", "createLRU", "EntryStatus", "routeCacheMap", "maxRouteLruSize", "routeCacheLru", "onRouteLRUEviction", "segmentCacheMap", "Map", "maxSegmentLruSize", "segmentCacheLru", "onSegmentLRUEviction", "readExactRouteCacheEntry", "now", "href", "nextUrl", "keypath", "existingEntry", "get", "staleAt", "put", "deleteRouteFromCache", "readRouteCacheEntry", "key", "nonInterceptedEntry", "couldBeIntercepted", "readSegmentCacheEntry", "path", "undefined", "deleteSegmentFromCache", "waitForSegmentCacheEntry", "pendingEntry", "promiseWithResolvers", "promise", "createPromiseWithResolvers", "requestRouteCacheEntryFromCache", "task", "exactEntry", "canonicalUrl", "status", "blockedTasks", "tree", "head", "isHeadPartial", "next", "prev", "size", "fetchRouteOnCacheMiss", "set", "requestSegmentEntryFromCache", "route", "accessToken", "rsc", "loading", "isPartial", "fetchSegmentEntryOnCacheMiss", "entry", "pingBlockedTasks", "delete", "cancelEntryListeners", "resolve", "fulfillRouteCacheEntry", "fulfilledEntry", "fulfillSegmentCacheEntry", "segmentCacheEntry", "rejectRouteCacheEntry", "rejectedEntry", "rejectSegmentCacheEntry", "response", "fetchSegmentPrefetchResponse", "ok", "body", "Date", "prefetchStream", "createPrefetchResponseStream", "serverData", "buildId", "redirected", "url", "<PERSON><PERSON><PERSON><PERSON>", "headers", "includes", "staleTime", "currentKeypath", "expectedEntry", "newKeypath", "error", "routeKey", "segmentPath", "fetchPriority", "responsePromise", "URL", "contentType", "isFlightResponse", "startsWith", "originalFlightStream", "lru", "lruEntry", "totalByteLength", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue", "byteLength", "updateSize", "reject", "Promise", "res", "rej"], "mappings": ";;;;;;;;;AAMA,SACEA,2BAA2B,EAC3BC,mCAAmC,EACnCC,QAAQ,EACRC,uBAAuB,EACvBC,UAAU,QACL,wBAAuB;AAC9B,SACEC,WAAW,EACXC,4BAA4B,EAC5BC,2BAA2B,QAEtB,0CAAyC;AAChD,SACEC,6BAA6B,EAC7BC,gBAAgB,EAEhBC,oBAAoB,QACf,cAAa;AACpB,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,iBAAiB,QAAQ,yCAAwC;AAM1E,SAASC,cAAc,QAAoC,cAAa;AACxE,SAASC,SAAS,QAAkB,QAAO;;;;;;;;AAqCpC,IAAWC,cAAAA,WAAAA,GAAAA,SAAAA,WAAAA;;;;WAAAA;MAIjB;AA+ED,MAAMC,4NACJH,iBAAAA;AAEF,8EAA8E;AAC9E,2DAA2D;AAC3D,+EAA+E;AAC/E,0EAA0E;AAC1E,4CAA4C;AAC5C,MAAMI,kBAAkB,KAAK,OAAO,KAAK,QAAQ;;AACjD,MAAMC,mNAAgBJ,YAAAA,EACpBG,iBACAE;AAGF,wEAAwE;AACxE,6BAA6B;AAC7B,MAAMC,kBAAkB,IAAIC;AAC5B,0EAA0E;AAC1E,4EAA4E;AAC5E,+EAA+E;AAC/E,8BAA8B;AAC9B,MAAMC,oBAAoB,KAAK,OAAO,KAAK,QAAQ;;AACnD,MAAMC,qNAAkBT,YAAAA,EACtBQ,mBACAE;AAGK,SAASC,yBACdC,GAAW,EACXC,IAAoB,EACpBC,OAAiC;IAEjC,MAAMC,UACJD,YAAY,OAAO;QAACD;KAAK,GAAG;QAACA;QAAMC;KAAQ;IAC7C,MAAME,gBAAgBd,cAAce,GAAG,CAACF;IACxC,IAAIC,kBAAkB,MAAM;QAC1B,8BAA8B;QAC9B,IAAIA,cAAcE,OAAO,GAAGN,KAAK;YAC/B,4BAA4B;YAE5B,mEAAmE;YACnER,cAAce,GAAG,CAACH;YAElB,OAAOA;QACT,OAAO;YACL,wCAAwC;YACxCI,qBAAqBJ,eAAeD;QACtC;IACF;IACA,OAAO;AACT;AAEO,SAASM,oBACdT,GAAW,EACXU,GAAkB;IAElB,wEAAwE;IACxE,2CAA2C;IAC3C,MAAMC,sBAAsBZ,yBAAyBC,KAAKU,IAAIT,IAAI,EAAE;IACpE,IAAIU,wBAAwB,QAAQ,CAACA,oBAAoBC,kBAAkB,EAAE;QAC3E,uEAAuE;QACvE,OAAOD;IACT;IACA,sEAAsE;IACtE,OAAOZ,yBAAyBC,KAAKU,IAAIT,IAAI,EAAES,IAAIR,OAAO;AAC5D;AAEO,SAASW,sBACdb,GAAW,EACXc,IAAY;IAEZ,MAAMV,gBAAgBV,gBAAgBW,GAAG,CAACS;IAC1C,IAAIV,kBAAkBW,WAAW;QAC/B,8BAA8B;QAC9B,IAAIX,cAAcE,OAAO,GAAGN,KAAK;YAC/B,4BAA4B;YAE5B,mEAAmE;YACnEH,gBAAgBU,GAAG,CAACH;YAEpB,OAAOA;QACT,OAAO;YACL,wCAAwC;YACxCY,uBAAuBZ,eAAeU;QACxC;IACF;IACA,OAAO;AACT;AAEO,SAASG,yBACdC,YAAsC;IAEtC,uEAAuE;IACvE,4EAA4E;IAC5E,IAAIC,uBAAuBD,aAAaE,OAAO;IAC/C,IAAID,yBAAyB,MAAM;QACjCA,uBAAuBD,aAAaE,OAAO,GACzCC;IACJ,OAAO;IACL,uCAAuC;IACzC;IACA,OAAOF,qBAAqBC,OAAO;AACrC;AAOO,SAASE,gCACdtB,GAAW,EACXuB,IAAkB;IAElB,MAAMb,MAAMa,KAAKb,GAAG;IACpB,wEAAwE;IACxE,2CAA2C;IAC3C,MAAMC,sBAAsBZ,yBAAyBC,KAAKU,IAAIT,IAAI,EAAE;IACpE,IAAIU,wBAAwB,QAAQ,CAACA,oBAAoBC,kBAAkB,EAAE;QAC3E,uEAAuE;QACvE,OAAOD;IACT;IACA,sEAAsE;IACtE,MAAMa,aAAazB,yBAAyBC,KAAKU,IAAIT,IAAI,EAAES,IAAIR,OAAO;IACtE,IAAIsB,eAAe,MAAM;QACvB,OAAOA;IACT;IACA,2DAA2D;IAC3D,MAAMN,eAAuC;QAC3CO,cAAc;QACdC,MAAM,EAAA;QACNC,cAAc;QACdC,MAAM;QACNC,MAAM;QACNC,eAAe;QACf,yEAAyE;QACzE,yCAAyC;QACzC,EAAE;QACF,4EAA4E;QAC5E,gDAAgD;QAChDxB,SAASN,MAAM,KAAK;QACpB,0EAA0E;QAC1E,0EAA0E;QAC1E,mBAAmB;QACnBY,oBAAoB;QAEpB,qBAAqB;QACrBT,SAAS;QACT4B,MAAM;QACNC,MAAM;QACNC,MAAM;IACR;KACAjD,+NAAAA,EAAqBkD,sBAAsBhB,cAAcK;IACzD,MAAMpB,UACJO,IAAIR,OAAO,KAAK,OAAO;QAACQ,IAAIT,IAAI;KAAC,GAAG;QAACS,IAAIT,IAAI;QAAES,IAAIR,OAAO;KAAC;IAC7DZ,cAAc6C,GAAG,CAAChC,SAASe;IAC3B,0EAA0E;IAC1E,mCAAmC;IACnCA,aAAaf,OAAO,GAAGA;IACvBX,cAAce,GAAG,CAACW;IAClB,OAAOA;AACT;AAOO,SAASkB,6BACdpC,GAAW,EACXuB,IAAkB,EAClBc,KAA+B,EAC/BvB,IAAY,EACZwB,WAAmB;IAEnB,MAAMlC,gBAAgBS,sBAAsBb,KAAKc;IACjD,IAAIV,kBAAkB,MAAM;QAC1B,OAAOA;IACT;IACA,2DAA2D;IAC3D,MAAMc,eAAyC;QAC7CQ,MAAM,EAAA;QACNa,KAAK;QACLC,SAAS;QACTlC,SAAS+B,MAAM/B,OAAO;QACtBmC,WAAW;QACXrB,SAAS;QAET,qBAAqB;QACrBV,KAAK;QACLqB,MAAM;QACNC,MAAM;QACNC,MAAM;IACR;6MACAjD,uBAAAA,EACE0D,6BACEL,OACAnB,cACAK,KAAKb,GAAG,EACRI,MACAwB;IAGJ5C,gBAAgByC,GAAG,CAACrB,MAAMI;IAC1B,0EAA0E;IAC1E,mCAAmC;IACnCA,aAAaR,GAAG,GAAGI;IACnBjB,gBAAgBU,GAAG,CAACW;IACpB,OAAOA;AACT;AAEA,SAASV,qBACPmC,KAAsB,EACtBxC,OAAkC;IAElCyC,iBAAiBD;IACjBrD,cAAcuD,MAAM,CAAC1C;IACrBX,cAAcqD,MAAM,CAACF;AACvB;AAEA,SAAS3B,uBAAuB2B,KAAwB,EAAEjC,GAAW;IACnEoC,qBAAqBH;IACrBjD,gBAAgBmD,MAAM,CAACnC;IACvBb,gBAAgBgD,MAAM,CAACF;AACzB;AAEA,SAASlD,mBAAmBkD,KAAsB;IAChD,sDAAsD;IACtD,MAAMxC,UAAUwC,MAAMxC,OAAO;IAC7B,IAAIA,YAAY,MAAM;QACpBwC,MAAMxC,OAAO,GAAG;QAChByC,iBAAiBD;QACjBrD,cAAcuD,MAAM,CAAC1C;IACvB;AACF;AAEA,SAASL,qBAAqB6C,KAAwB;IACpD,sDAAsD;IACtD,MAAMjC,MAAMiC,MAAMjC,GAAG;IACrB,IAAIA,QAAQ,MAAM;QAChBiC,MAAMjC,GAAG,GAAG;QACZoC,qBAAqBH;QACrBjD,gBAAgBmD,MAAM,CAACnC;IACzB;AACF;AAEA,SAASoC,qBAAqBH,KAAwB;IACpD,IAAIA,MAAMjB,MAAM,KAAA,KAA4BiB,MAAMvB,OAAO,KAAK,MAAM;QAClE,4EAA4E;QAC5E,4EAA4E;QAC5E,aAAa;QACb,0EAA0E;QAC1E,iDAAiD;QACjDuB,MAAMvB,OAAO,CAAC2B,OAAO,CAAC;QACtBJ,MAAMvB,OAAO,GAAG;IAClB;AACF;AAEA,SAASwB,iBAAiBD,KAEzB;IACC,MAAMhB,eAAegB,MAAMhB,YAAY;IACvC,IAAIA,iBAAiB,MAAM;QACzB,KAAK,MAAMJ,QAAQI,aAAc;gBAC/B5C,wNAAAA,EAAiBwC;QACnB;QACAoB,MAAMhB,YAAY,GAAG;IACvB;AACF;AAEA,SAASqB,uBACPL,KAA6B,EAC7Bf,IAAkB,EAClBC,IAAqB,EACrBC,aAAsB,EACtBxB,OAAe,EACfM,kBAA2B,EAC3Ba,YAAoB;IAEpB,MAAMwB,iBAA2CN;IACjDM,eAAevB,MAAM,GAAA;IACrBuB,eAAerB,IAAI,GAAGA;IACtBqB,eAAepB,IAAI,GAAGA;IACtBoB,eAAenB,aAAa,GAAGA;IAC/BmB,eAAe3C,OAAO,GAAGA;IACzB2C,eAAerC,kBAAkB,GAAGA;IACpCqC,eAAexB,YAAY,GAAGA;IAC9BmB,iBAAiBD;IACjB,OAAOM;AACT;AAEA,SAASC,yBACPC,iBAA2C,EAC3CZ,GAAoB,EACpBC,OAAuD,EACvDlC,OAAe,EACfmC,SAAkB;IAElB,MAAMQ,iBAA6CE;IACnDF,eAAevB,MAAM,GAAA;IACrBuB,eAAeV,GAAG,GAAGA;IACrBU,eAAeT,OAAO,GAAGA;IACzBS,eAAe3C,OAAO,GAAGA;IACzB2C,eAAeR,SAAS,GAAGA;IAC3B,yDAAyD;IACzD,IAAIU,kBAAkB/B,OAAO,KAAK,MAAM;QACtC+B,kBAAkB/B,OAAO,CAAC2B,OAAO,CAACE;QAClC,2CAA2C;QAC3CA,eAAe7B,OAAO,GAAG;IAC3B;AACF;AAEA,SAASgC,sBACPT,KAA6B,EAC7BrC,OAAe;IAEf,MAAM+C,gBAAyCV;IAC/CU,cAAc3B,MAAM,GAAA;IACpB2B,cAAc/C,OAAO,GAAGA;IACxBsC,iBAAiBD;AACnB;AAEA,SAASW,wBACPX,KAA+B,EAC/BrC,OAAe;IAEf,MAAM+C,gBAA2CV;IACjDU,cAAc3B,MAAM,GAAA;IACpB2B,cAAc/C,OAAO,GAAGA;IACxB,IAAIqC,MAAMvB,OAAO,KAAK,MAAM;QAC1B,0EAA0E;QAC1E,iDAAiD;QACjDuB,MAAMvB,OAAO,CAAC2B,OAAO,CAAC;QACtBJ,MAAMvB,OAAO,GAAG;IAClB;AACF;AAEA,eAAec,sBACbS,KAA6B,EAC7BpB,IAAkB;IAElB,6EAA6E;IAC7E,yEAAyE;IACzE,0EAA0E;IAC1E,kEAAkE;IAClE,MAAMb,MAAMa,KAAKb,GAAG;IACpB,MAAMT,OAAOS,IAAIT,IAAI;IACrB,MAAMC,UAAUQ,IAAIR,OAAO;IAC3B,IAAI;QACF,MAAMqD,WAAW,MAAMC,6BAA6BvD,MAAM,UAAUC;QACpE,IACE,CAACqD,YACD,CAACA,SAASE,EAAE,IACZ,uEAAuE;QACvE,yEAAyE;QACzE,oDAAoD;QACpDF,SAAS7B,MAAM,KAAK,OACpB,CAAC6B,SAASG,IAAI,EACd;YACA,wEAAwE;YACxE,uDAAuD;YACvDN,sBAAsBT,OAAOgB,KAAK3D,GAAG,KAAK,KAAK;YAC/C;QACF;QACA,MAAM4D,iBAAiBC,6BACrBN,SAASG,IAAI,EACblE,eACAmD;QAEF,MAAMmB,aAA+B,kOAAOlF,+BAAAA,EAC1CgF;QAEF,IAAIE,WAAWC,OAAO,qLAAK9E,gBAAAA,KAAiB;YAC1C,qEAAqE;YACrE,mEAAmE;YACnE,0EAA0E;YAC1E,sEAAsE;YACtE,6BAA6B;YAC7BmE,sBAAsBT,OAAOgB,KAAK3D,GAAG,KAAK,KAAK;YAC/C;QACF;QAEA,kEAAkE;QAClE,wBAAwB;QACxB,MAAMyB,eAAe8B,SAASS,UAAU,GACpC9E,kPAAAA,8NAAkBL,8BAAAA,EAA4B0E,SAASU,GAAG,KAC1DhE;QAEJ,kEAAkE;QAClE,MAAMiE,aAAaX,SAASY,OAAO,CAAC9D,GAAG,CAAC;QACxC,MAAMO,qBACJsD,eAAe,QAAQA,WAAWE,QAAQ,iMAAC5F,WAAAA;QAE7CwE,uBACEL,OACAmB,WAAWlC,IAAI,EACfkC,WAAWjC,IAAI,EACfiC,WAAWhC,aAAa,EACxB6B,KAAK3D,GAAG,KAAK8D,WAAWO,SAAS,EACjCzD,oBACAa;QAGF,IAAI,CAACb,sBAAsBV,YAAY,MAAM;YAC3C,yEAAyE;YACzE,wEAAwE;YACxE,6DAA6D;YAC7D,+BAA+B;YAC/B,EAAE;YACF,wEAAwE;YACxE,wEAAwE;YACxE,MAAMoE,iBAA4C;gBAACrE;gBAAMC;aAAQ;YACjE,MAAMqE,gBAAgBjF,cAAce,GAAG,CAACiE;YACxC,IAAIC,kBAAkB5B,OAAO;gBAC3BrD,cAAcuD,MAAM,CAACyB;gBACrB,MAAME,aAAwC;oBAACvE;iBAAK;gBACpDX,cAAc6C,GAAG,CAACqC,YAAY7B;gBAC9B,sEAAsE;gBACtE,qEAAqE;gBACrE,sEAAsE;gBACtEA,MAAMxC,OAAO,GAAGqE;YAClB,OAAO;YACL,qEAAqE;YACrE,0DAA0D;YAC5D;QACF;IACF,EAAE,OAAOC,OAAO;QACd,uEAAuE;QACvE,yBAAyB;QACzBrB,sBAAsBT,OAAOgB,KAAK3D,GAAG,KAAK,KAAK;IACjD;AACF;AAEA,eAAe0C,6BACbL,KAA+B,EAC/Bc,iBAA2C,EAC3CuB,QAAuB,EACvBC,WAAmB,EACnBrC,WAA0B;IAE1B,6EAA6E;IAC7E,yEAAyE;IACzE,qEAAqE;IACrE,EAAE;IACF,0EAA0E;IAC1E,iBAAiB;IACjB,MAAMrC,OAAOyE,SAASzE,IAAI;IAC1B,IAAI;QACF,MAAMsD,WAAW,MAAMC,6BACrBvD,MACAqC,gBAAgB,KAAKqC,cAAiBA,cAAY,MAAGrC,aACrDoC,SAASxE,OAAO;QAElB,IACE,CAACqD,YACD,CAACA,SAASE,EAAE,IACZF,SAAS7B,MAAM,KAAK,OAAO,aAAa;QACxC,CAAC6B,SAASG,IAAI,EACd;YACA,wEAAwE;YACxE,uDAAuD;YACvDJ,wBAAwBH,mBAAmBQ,KAAK3D,GAAG,KAAK,KAAK;YAC7D;QACF;QACA,2EAA2E;QAC3E,4DAA4D;QAC5D,MAAM4D,iBAAiBC,6BACrBN,SAASG,IAAI,EACb7D,iBACAsD;QAEF,MAAMW,aAAa,kOAAOlF,+BAAAA,EACxBgF;QAEF,IAAIE,WAAWC,OAAO,qLAAK9E,gBAAAA,KAAiB;YAC1C,qEAAqE;YACrE,mEAAmE;YACnE,0EAA0E;YAC1E,sEAAsE;YACtE,6BAA6B;YAC7BqE,wBAAwBH,mBAAmBQ,KAAK3D,GAAG,KAAK,KAAK;YAC7D;QACF;QACAkD,yBACEC,mBACAW,WAAWvB,GAAG,EACduB,WAAWtB,OAAO,EAClB,AACA,yCAAyC,6BAD6B;QAEtEH,MAAM/B,OAAO,EACbwD,WAAWrB,SAAS;IAExB,EAAE,OAAOgC,OAAO;QACd,uEAAuE;QACvE,yBAAyB;QACzBnB,wBAAwBH,mBAAmBQ,KAAK3D,GAAG,KAAK,KAAK;IAC/D;AACF;AAEA,eAAewD,6BACbvD,IAAoB,EACpB0E,WAAmB,EACnBzE,OAAiC;IAEjC,MAAMiE,UAA0B;QAC9B,iMAACzF,aAAAA,CAAW,EAAE;QACd,iMAACJ,8BAAAA,CAA4B,EAAE;QAC/B,CAACC,sOAAAA,CAAoC,EAAEoG;IACzC;IACA,IAAIzE,YAAY,MAAM;QACpBiE,OAAO,iMAAC3F,WAAAA,CAAS,GAAG0B;IACtB;IACA,MAAM0E,gBAAgB;IACtB,MAAMC,8OAAkBlG,cAAAA,EAAY,IAAImG,IAAI7E,OAAOkE,SAASS;QAC5D9F,qOAAAA,EAA8B+F;IAC9B,MAAMtB,WAAW,MAAMsB;IACvB,MAAME,cAAcxB,SAASY,OAAO,CAAC9D,GAAG,CAAC;IACzC,MAAM2E,mBACJD,eAAeA,YAAYE,UAAU,iMAACxG,0BAAAA;IACxC,IAAI,CAAC8E,SAASE,EAAE,IAAI,CAACuB,kBAAkB;QACrC,OAAO;IACT;IACA,OAAOzB;AACT;AAEA,SAASM,6BAGPqB,oBAAgD,EAChDC,GAAW,EACXC,QAAW;IAEX,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,EAAE;IACF,8EAA8E;IAC9E,iCAAiC;IACjC,IAAIC,kBAAkB;IACtB,MAAMC,SAASJ,qBAAqBK,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMN,OAAOO,IAAI;gBACzC,IAAI,CAACF,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWI,OAAO,CAACF;oBAEnB,+DAA+D;oBAC/D,kEAAkE;oBAClE,qEAAqE;oBACrE,6CAA6C;oBAC7CP,mBAAmBO,MAAMG,UAAU;oBACnCZ,IAAIa,UAAU,CAACZ,UAAUC;oBAEzB;gBACF;gBACA,qEAAqE;gBACrE,qBAAqB;gBACrB;YACF;QACF;IACF;AACF;AAEA,SAAShE;IACP,iDAAiD;IACjD,IAAI0B;IACJ,IAAIkD;IACJ,MAAM7E,UAAU,IAAI8E,QAAW,CAACC,KAAKC;QACnCrD,UAAUoD;QACVF,SAASG;IACX;IACA,OAAO;QAAErD,SAASA;QAAUkD,QAAQA;QAAS7E;IAAQ;AACvD", "ignoreList": [0]}}, {"offset": {"line": 2104, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2110, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/scheduler.ts"], "sourcesContent": ["import type { TreePrefetch } from '../../../server/app-render/collect-segment-data'\nimport {\n  requestRouteCacheEntryFromCache,\n  requestSegmentEntryFromCache,\n  EntryStatus,\n  type FulfilledRouteCacheEntry,\n  type RouteCacheEntry,\n} from './cache'\nimport type { RouteCacheKey } from './cache-key'\n\nconst scheduleMicrotask =\n  typeof queueMicrotask === 'function'\n    ? queueMicrotask\n    : (fn: () => unknown) =>\n        Promise.resolve()\n          .then(fn)\n          .catch((error) =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n\nexport type PrefetchTask = {\n  key: RouteCacheKey\n\n  /**\n   * sortId is an incrementing counter\n   *\n   * Newer prefetches are prioritized over older ones, so that as new links\n   * enter the viewport, they are not starved by older links that are no\n   * longer relevant. In the future, we can add additional prioritization\n   * heuristics, like removing prefetches once a link leaves the viewport.\n   *\n   * The sortId is assigned when the prefetch is initiated, and reassigned if\n   * the same URL is prefetched again (effectively bumping it to the top of\n   * the queue).\n   *\n   * TODO: We can add additional fields here to indicate what kind of prefetch\n   * it is. For example, was it initiated by a link? Or was it an imperative\n   * call? If it was initiated by a link, we can remove it from the queue when\n   * the link leaves the viewport, but if it was an imperative call, then we\n   * should keep it in the queue until it's fulfilled.\n   *\n   * We can also add priority levels. For example, hovering over a link could\n   * increase the priority of its prefetch.\n   */\n  sortId: number\n\n  /**\n   * True if the prefetch is blocked by network data. We remove tasks from the\n   * queue once they are blocked, and add them back when they receive data.\n   *\n   * isBlocked also indicates whether the task is currently in the queue; tasks\n   * are removed from the queue when they are blocked. Use this to avoid\n   * queueing the same task multiple times.\n   */\n  isBlocked: boolean\n\n  /**\n   * The index of the task in the heap's backing array. Used to efficiently\n   * change the priority of a task by re-sifting it, which requires knowing\n   * where it is in the array. This is only used internally by the heap\n   * algorithm. The naive alternative is indexOf every time a task is queued,\n   * which has O(n) complexity.\n   */\n  _heapIndex: number\n}\n\nconst enum PrefetchTaskExitStatus {\n  /**\n   * The task yielded because there are too many requests in progress.\n   */\n  InProgress,\n\n  /**\n   * The task is blocked. It needs more data before it can proceed.\n   *\n   * Currently the only reason this happens is we're still waiting to receive a\n   * route tree from the server, because we can't start prefetching the segments\n   * until we know what to prefetch.\n   */\n  Blocked,\n\n  /**\n   * There's nothing left to prefetch.\n   */\n  Done,\n}\n\nconst taskHeap: Array<PrefetchTask> = []\n\n// This is intentionally low so that when a navigation happens, the browser's\n// internal network queue is not already saturated with prefetch requests.\nconst MAX_CONCURRENT_PREFETCH_REQUESTS = 3\nlet inProgressRequests = 0\n\nlet sortIdCounter = 0\nlet didScheduleMicrotask = false\n\n/**\n * Initiates a prefetch task for the given URL. If a prefetch for the same URL\n * is already in progress, this will bump it to the top of the queue.\n *\n * This is not a user-facing function. By the time this is called, the href is\n * expected to be validated and normalized.\n *\n * @param key The RouteCacheKey to prefetch.\n */\nexport function schedulePrefetchTask(key: RouteCacheKey): void {\n  // Spawn a new prefetch task\n  const task: PrefetchTask = {\n    key,\n    sortId: sortIdCounter++,\n    isBlocked: false,\n    _heapIndex: -1,\n  }\n  heapPush(taskHeap, task)\n\n  // Schedule an async task to process the queue.\n  //\n  // The main reason we process the queue in an async task is for batching.\n  // It's common for a single JS task/event to trigger multiple prefetches.\n  // By deferring to a microtask, we only process the queue once per JS task.\n  // If they have different priorities, it also ensures they are processed in\n  // the optimal order.\n  ensureWorkIsScheduled()\n}\n\nfunction ensureWorkIsScheduled() {\n  if (didScheduleMicrotask || !hasNetworkBandwidth()) {\n    // Either we already scheduled a task to process the queue, or there are\n    // too many concurrent requests in progress. In the latter case, the\n    // queue will resume processing once more bandwidth is available.\n    return\n  }\n  didScheduleMicrotask = true\n  scheduleMicrotask(processQueueInMicrotask)\n}\n\n/**\n * Checks if we've exceeded the maximum number of concurrent prefetch requests,\n * to avoid saturating the browser's internal network queue. This is a\n * cooperative limit — prefetch tasks should check this before issuing\n * new requests.\n */\nfunction hasNetworkBandwidth(): boolean {\n  // TODO: Also check if there's an in-progress navigation. We should never\n  // add prefetch requests to the network queue if an actual navigation is\n  // taking place, to ensure there's sufficient bandwidth for render-blocking\n  // data and resources.\n  return inProgressRequests < MAX_CONCURRENT_PREFETCH_REQUESTS\n}\n\n/**\n * Notifies the scheduler of an in-progress prefetch request. This is used to\n * control network bandwidth by limiting the number of concurrent requests.\n *\n * @param promise A promise that resolves when the request has finished.\n */\nexport function trackPrefetchRequestBandwidth(\n  promiseForServerData: Promise<unknown>\n) {\n  inProgressRequests++\n  promiseForServerData.then(\n    onPrefetchRequestCompletion,\n    onPrefetchRequestCompletion\n  )\n}\n\nconst noop = () => {}\n\nexport function spawnPrefetchSubtask(promise: Promise<any>) {\n  // When the scheduler spawns an async task, we don't await its result\n  // directly. Instead, the async task writes its result directly into the\n  // cache, then pings the scheduler to continue.\n  //\n  // This function only exists to prevent warnings about unhandled promises.\n  promise.then(noop, noop)\n}\n\nfunction onPrefetchRequestCompletion(): void {\n  inProgressRequests--\n\n  // Notify the scheduler that we have more bandwidth, and can continue\n  // processing tasks.\n  ensureWorkIsScheduled()\n}\n\n/**\n * Notify the scheduler that we've received new data for an in-progress\n * prefetch. The corresponding task will be added back to the queue (unless the\n * task has been canceled in the meantime).\n */\nexport function pingPrefetchTask(task: PrefetchTask) {\n  // \"Ping\" a prefetch that's already in progress to notify it of new data.\n  if (!task.isBlocked) {\n    // Prefetch is already queued.\n    return\n  }\n  // Unblock the task and requeue it.\n  task.isBlocked = false\n  heapPush(taskHeap, task)\n  ensureWorkIsScheduled()\n}\n\nfunction processQueueInMicrotask() {\n  didScheduleMicrotask = false\n\n  // We aim to minimize how often we read the current time. Since nearly all\n  // functions in the prefetch scheduler are synchronous, we can read the time\n  // once and pass it as an argument wherever it's needed.\n  const now = Date.now()\n\n  // Process the task queue until we run out of network bandwidth.\n  let task = heapPeek(taskHeap)\n  while (task !== null && hasNetworkBandwidth()) {\n    const route = requestRouteCacheEntryFromCache(now, task)\n    const exitStatus = pingRouteTree(now, task, route)\n    switch (exitStatus) {\n      case PrefetchTaskExitStatus.InProgress:\n        // The task yielded because there are too many requests in progress.\n        // Stop processing tasks until we have more bandwidth.\n        return\n      case PrefetchTaskExitStatus.Blocked:\n        // The task is blocked. It needs more data before it can proceed.\n        // Keep the task out of the queue until the server responds.\n        task.isBlocked = true\n\n        // Continue to the next task\n        heapPop(taskHeap)\n        task = heapPeek(taskHeap)\n        continue\n      case PrefetchTaskExitStatus.Done:\n        // The prefetch is complete. Continue to the next task.\n        heapPop(taskHeap)\n        task = heapPeek(taskHeap)\n        continue\n      default: {\n        const _exhaustiveCheck: never = exitStatus\n        return\n      }\n    }\n  }\n}\n\nfunction pingRouteTree(\n  now: number,\n  task: PrefetchTask,\n  route: RouteCacheEntry\n): PrefetchTaskExitStatus {\n  switch (route.status) {\n    case EntryStatus.Pending: {\n      // Still pending. We can't start prefetching the segments until the route\n      // tree has loaded.\n      const blockedTasks = route.blockedTasks\n      if (blockedTasks === null) {\n        route.blockedTasks = new Set([task])\n      } else {\n        blockedTasks.add(task)\n      }\n      return PrefetchTaskExitStatus.Blocked\n    }\n    case EntryStatus.Rejected: {\n      // Route tree failed to load. Treat as a 404.\n      return PrefetchTaskExitStatus.Done\n    }\n    case EntryStatus.Fulfilled: {\n      // Recursively fill in the segment tree.\n      if (!hasNetworkBandwidth()) {\n        // Stop prefetching segments until there's more bandwidth.\n        return PrefetchTaskExitStatus.InProgress\n      }\n      const tree = route.tree\n      requestSegmentEntryFromCache(now, task, route, tree.path, '')\n      return pingSegmentTree(now, task, route, tree)\n    }\n    default: {\n      const _exhaustiveCheck: never = route\n      return PrefetchTaskExitStatus.Done\n    }\n  }\n}\n\nfunction pingSegmentTree(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  tree: TreePrefetch\n): PrefetchTaskExitStatus.InProgress | PrefetchTaskExitStatus.Done {\n  if (tree.slots !== null) {\n    // Recursively ping the children.\n    for (const parallelRouteKey in tree.slots) {\n      const childTree = tree.slots[parallelRouteKey]\n      if (!hasNetworkBandwidth()) {\n        // Stop prefetching segments until there's more bandwidth.\n        return PrefetchTaskExitStatus.InProgress\n      } else {\n        const childPath = childTree.path\n        const childToken = childTree.token\n        requestSegmentEntryFromCache(now, task, route, childPath, childToken)\n      }\n      const childExitStatus = pingSegmentTree(now, task, route, childTree)\n      if (childExitStatus === PrefetchTaskExitStatus.InProgress) {\n        // Child yielded without finishing.\n        return PrefetchTaskExitStatus.InProgress\n      }\n    }\n  }\n  // This segment and all its children have finished prefetching.\n  return PrefetchTaskExitStatus.Done\n}\n\n// -----------------------------------------------------------------------------\n// The remainider of the module is a MinHeap implementation. Try not to put any\n// logic below here unless it's related to the heap algorithm. We can extract\n// this to a separate module if/when we need multiple kinds of heaps.\n// -----------------------------------------------------------------------------\n\nfunction compareQueuePriority(a: PrefetchTask, b: PrefetchTask) {\n  // Since the queue is a MinHeap, this should return a positive number if b is\n  // higher priority than a, and a negative number if a is higher priority\n  // than b.\n  //\n  // sortId is an incrementing counter assigned to prefetches. We want to\n  // process the newest prefetches first.\n  return b.sortId - a.sortId\n}\n\nfunction heapPush(heap: Array<PrefetchTask>, node: PrefetchTask): void {\n  const index = heap.length\n  heap.push(node)\n  node._heapIndex = index\n  heapSiftUp(heap, node, index)\n}\n\nfunction heapPeek(heap: Array<PrefetchTask>): PrefetchTask | null {\n  return heap.length === 0 ? null : heap[0]\n}\n\nfunction heapPop(heap: Array<PrefetchTask>): PrefetchTask | null {\n  if (heap.length === 0) {\n    return null\n  }\n  const first = heap[0]\n  first._heapIndex = -1\n  const last = heap.pop() as PrefetchTask\n  if (last !== first) {\n    heap[0] = last\n    last._heapIndex = 0\n    heapSiftDown(heap, last, 0)\n  }\n  return first\n}\n\n// Not currently used, but will be once we add the ability to update a\n// task's priority.\n// function heapSift(heap: Array<PrefetchTask>, node: PrefetchTask) {\n//   const index = node._heapIndex\n//   if (index !== -1) {\n//     const parentIndex = (index - 1) >>> 1\n//     const parent = heap[parentIndex]\n//     if (compareQueuePriority(parent, node) > 0) {\n//       // The parent is larger. Sift up.\n//       heapSiftUp(heap, node, index)\n//     } else {\n//       // The parent is smaller (or equal). Sift down.\n//       heapSiftDown(heap, node, index)\n//     }\n//   }\n// }\n\nfunction heapSiftUp(\n  heap: Array<PrefetchTask>,\n  node: PrefetchTask,\n  i: number\n): void {\n  let index = i\n  while (index > 0) {\n    const parentIndex = (index - 1) >>> 1\n    const parent = heap[parentIndex]\n    if (compareQueuePriority(parent, node) > 0) {\n      // The parent is larger. Swap positions.\n      heap[parentIndex] = node\n      node._heapIndex = parentIndex\n      heap[index] = parent\n      parent._heapIndex = index\n\n      index = parentIndex\n    } else {\n      // The parent is smaller. Exit.\n      return\n    }\n  }\n}\n\nfunction heapSiftDown(\n  heap: Array<PrefetchTask>,\n  node: PrefetchTask,\n  i: number\n): void {\n  let index = i\n  const length = heap.length\n  const halfLength = length >>> 1\n  while (index < halfLength) {\n    const leftIndex = (index + 1) * 2 - 1\n    const left = heap[leftIndex]\n    const rightIndex = leftIndex + 1\n    const right = heap[rightIndex]\n\n    // If the left or right node is smaller, swap with the smaller of those.\n    if (compareQueuePriority(left, node) < 0) {\n      if (rightIndex < length && compareQueuePriority(right, left) < 0) {\n        heap[index] = right\n        right._heapIndex = index\n        heap[rightIndex] = node\n        node._heapIndex = rightIndex\n\n        index = rightIndex\n      } else {\n        heap[index] = left\n        left._heapIndex = index\n        heap[leftIndex] = node\n        node._heapIndex = leftIndex\n\n        index = leftIndex\n      }\n    } else if (rightIndex < length && compareQueuePriority(right, node) < 0) {\n      heap[index] = right\n      right._heapIndex = index\n      heap[rightIndex] = node\n      node._heapIndex = rightIndex\n\n      index = rightIndex\n    } else {\n      // Neither child is smaller. Exit.\n      return\n    }\n  }\n}\n"], "names": ["requestRouteCacheEntryFromCache", "requestSegmentEntryFromCache", "EntryStatus", "scheduleMicrotask", "queueMicrotask", "fn", "Promise", "resolve", "then", "catch", "error", "setTimeout", "taskHeap", "MAX_CONCURRENT_PREFETCH_REQUESTS", "inProgressRequests", "sortIdCounter", "didScheduleMicrotask", "schedulePrefetchTask", "key", "task", "sortId", "isBlocked", "_heapIndex", "heapPush", "ensureWorkIsScheduled", "hasNetworkBandwidth", "processQueueInMicrotask", "trackPrefetchRequestBandwidth", "promiseForServerData", "onPrefetchRequestCompletion", "noop", "spawnPrefetchSubtask", "promise", "pingPrefetchTask", "now", "Date", "heapPeek", "route", "exitStatus", "pingRouteTree", "heapPop", "_exhaustiveCheck", "status", "Pending", "blockedTasks", "Set", "add", "Rejected", "Fulfilled", "tree", "path", "pingSegmentTree", "slots", "parallelRouteKey", "childTree", "child<PERSON><PERSON>", "childToken", "token", "childExitStatus", "compareQueuePriority", "a", "b", "heap", "node", "index", "length", "push", "heapSiftUp", "first", "last", "pop", "heapSiftDown", "i", "parentIndex", "parent", "<PERSON><PERSON><PERSON><PERSON>", "leftIndex", "left", "rightIndex", "right"], "mappings": ";;;;;;AACA,SACEA,+BAA+B,EAC/BC,4BAA4B,EAC5BC,WAAW,QAGN,UAAS;;AAGhB,MAAMC,oBACJ,OAAOC,mBAAmB,aACtBA,iBACA,CAACC,KACCC,QAAQC,OAAO,GACZC,IAAI,CAACH,IACLI,KAAK,CAAC,CAACC,QACNC,WAAW;YACT,MAAMD;QACR;;AAsEZ,MAAME,WAAgC,EAAE;AAExC,6EAA6E;AAC7E,0EAA0E;AAC1E,MAAMC,mCAAmC;AACzC,IAAIC,qBAAqB;AAEzB,IAAIC,gBAAgB;AACpB,IAAIC,uBAAuB;AAWpB,SAASC,qBAAqBC,GAAkB;IACrD,4BAA4B;IAC5B,MAAMC,OAAqB;QACzBD;QACAE,QAAQL;QACRM,WAAW;QACXC,YAAY,CAAC;IACf;IACAC,SAASX,UAAUO;IAEnB,+CAA+C;IAC/C,EAAE;IACF,yEAAyE;IACzE,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,qBAAqB;IACrBK;AACF;AAEA,SAASA;IACP,IAAIR,wBAAwB,CAACS,uBAAuB;QAClD,wEAAwE;QACxE,oEAAoE;QACpE,iEAAiE;QACjE;IACF;IACAT,uBAAuB;IACvBb,kBAAkBuB;AACpB;AAEA;;;;;CAKC,GACD,SAASD;IACP,yEAAyE;IACzE,wEAAwE;IACxE,2EAA2E;IAC3E,sBAAsB;IACtB,OAAOX,qBAAqBD;AAC9B;AAQO,SAASc,8BACdC,oBAAsC;IAEtCd;IACAc,qBAAqBpB,IAAI,CACvBqB,6BACAA;AAEJ;AAEA,MAAMC,OAAO,KAAO;AAEb,SAASC,qBAAqBC,OAAqB;IACxD,qEAAqE;IACrE,wEAAwE;IACxE,+CAA+C;IAC/C,EAAE;IACF,0EAA0E;IAC1EA,QAAQxB,IAAI,CAACsB,MAAMA;AACrB;AAEA,SAASD;IACPf;IAEA,qEAAqE;IACrE,oBAAoB;IACpBU;AACF;AAOO,SAASS,iBAAiBd,IAAkB;IACjD,yEAAyE;IACzE,IAAI,CAACA,KAAKE,SAAS,EAAE;QACnB,8BAA8B;QAC9B;IACF;IACA,mCAAmC;IACnCF,KAAKE,SAAS,GAAG;IACjBE,SAASX,UAAUO;IACnBK;AACF;AAEA,SAASE;IACPV,uBAAuB;IAEvB,0EAA0E;IAC1E,4EAA4E;IAC5E,wDAAwD;IACxD,MAAMkB,MAAMC,KAAKD,GAAG;IAEpB,gEAAgE;IAChE,IAAIf,OAAOiB,SAASxB;IACpB,MAAOO,SAAS,QAAQM,sBAAuB;QAC7C,MAAMY,QAAQrC,uOAAAA,EAAgCkC,KAAKf;QACnD,MAAMmB,aAAaC,cAAcL,KAAKf,MAAMkB;QAC5C,OAAQC;YACN,KAAA;gBACE,oEAAoE;gBACpE,sDAAsD;gBACtD;YACF,KAAA;gBACE,iEAAiE;gBACjE,4DAA4D;gBAC5DnB,KAAKE,SAAS,GAAG;gBAEjB,4BAA4B;gBAC5BmB,QAAQ5B;gBACRO,OAAOiB,SAASxB;gBAChB;YACF,KAAA;gBACE,uDAAuD;gBACvD4B,QAAQ5B;gBACRO,OAAOiB,SAASxB;gBAChB;YACF;gBAAS;oBACP,MAAM6B,mBAA0BH;oBAChC;gBACF;QACF;IACF;AACF;AAEA,SAASC,cACPL,GAAW,EACXf,IAAkB,EAClBkB,KAAsB;IAEtB,OAAQA,MAAMK,MAAM;QAClB,sMAAKxC,cAAAA,CAAYyC,OAAO;YAAE;gBACxB,yEAAyE;gBACzE,mBAAmB;gBACnB,MAAMC,eAAeP,MAAMO,YAAY;gBACvC,IAAIA,iBAAiB,MAAM;oBACzBP,MAAMO,YAAY,GAAG,IAAIC,IAAI;wBAAC1B;qBAAK;gBACrC,OAAO;oBACLyB,aAAaE,GAAG,CAAC3B;gBACnB;gBACA,OAAA;YACF;QACA,KAAKjB,+MAAAA,CAAY6C,QAAQ;YAAE;gBACzB,6CAA6C;gBAC7C,OAAA;YACF;QACA,sMAAK7C,cAAAA,CAAY8C,SAAS;YAAE;gBAC1B,wCAAwC;gBACxC,IAAI,CAACvB,uBAAuB;oBAC1B,0DAA0D;oBAC1D,OAAA;gBACF;gBACA,MAAMwB,OAAOZ,MAAMY,IAAI;qNACvBhD,+BAAAA,EAA6BiC,KAAKf,MAAMkB,OAAOY,KAAKC,IAAI,EAAE;gBAC1D,OAAOC,gBAAgBjB,KAAKf,MAAMkB,OAAOY;YAC3C;QACA;YAAS;gBACP,MAAMR,mBAA0BJ;gBAChC,OAAA;YACF;IACF;AACF;AAEA,SAASc,gBACPjB,GAAW,EACXf,IAAkB,EAClBkB,KAA+B,EAC/BY,IAAkB;IAElB,IAAIA,KAAKG,KAAK,KAAK,MAAM;QACvB,iCAAiC;QACjC,IAAK,MAAMC,oBAAoBJ,KAAKG,KAAK,CAAE;YACzC,MAAME,YAAYL,KAAKG,KAAK,CAACC,iBAAiB;YAC9C,IAAI,CAAC5B,uBAAuB;gBAC1B,0DAA0D;gBAC1D,OAAA;YACF,OAAO;gBACL,MAAM8B,YAAYD,UAAUJ,IAAI;gBAChC,MAAMM,aAAaF,UAAUG,KAAK;qNAClCxD,+BAAAA,EAA6BiC,KAAKf,MAAMkB,OAAOkB,WAAWC;YAC5D;YACA,MAAME,kBAAkBP,gBAAgBjB,KAAKf,MAAMkB,OAAOiB;YAC1D,IAAII,oBAAAA,GAAuD;gBACzD,mCAAmC;gBACnC,OAAA;YACF;QACF;IACF;IACA,+DAA+D;IAC/D,OAAA;AACF;AAEA,gFAAgF;AAChF,+EAA+E;AAC/E,6EAA6E;AAC7E,qEAAqE;AACrE,gFAAgF;AAEhF,SAASC,qBAAqBC,CAAe,EAAEC,CAAe;IAC5D,6EAA6E;IAC7E,wEAAwE;IACxE,UAAU;IACV,EAAE;IACF,uEAAuE;IACvE,uCAAuC;IACvC,OAAOA,EAAEzC,MAAM,GAAGwC,EAAExC,MAAM;AAC5B;AAEA,SAASG,SAASuC,IAAyB,EAAEC,IAAkB;IAC7D,MAAMC,QAAQF,KAAKG,MAAM;IACzBH,KAAKI,IAAI,CAACH;IACVA,KAAKzC,UAAU,GAAG0C;IAClBG,WAAWL,MAAMC,MAAMC;AACzB;AAEA,SAAS5B,SAAS0B,IAAyB;IACzC,OAAOA,KAAKG,MAAM,KAAK,IAAI,OAAOH,IAAI,CAAC,EAAE;AAC3C;AAEA,SAAStB,QAAQsB,IAAyB;IACxC,IAAIA,KAAKG,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IACA,MAAMG,QAAQN,IAAI,CAAC,EAAE;IACrBM,MAAM9C,UAAU,GAAG,CAAC;IACpB,MAAM+C,OAAOP,KAAKQ,GAAG;IACrB,IAAID,SAASD,OAAO;QAClBN,IAAI,CAAC,EAAE,GAAGO;QACVA,KAAK/C,UAAU,GAAG;QAClBiD,aAAaT,MAAMO,MAAM;IAC3B;IACA,OAAOD;AACT;AAEA,sEAAsE;AACtE,mBAAmB;AACnB,qEAAqE;AACrE,kCAAkC;AAClC,wBAAwB;AACxB,4CAA4C;AAC5C,uCAAuC;AACvC,oDAAoD;AACpD,0CAA0C;AAC1C,sCAAsC;AACtC,eAAe;AACf,wDAAwD;AACxD,wCAAwC;AACxC,QAAQ;AACR,MAAM;AACN,IAAI;AAEJ,SAASD,WACPL,IAAyB,EACzBC,IAAkB,EAClBS,CAAS;IAET,IAAIR,QAAQQ;IACZ,MAAOR,QAAQ,EAAG;QAChB,MAAMS,cAAeT,QAAQ,MAAO;QACpC,MAAMU,SAASZ,IAAI,CAACW,YAAY;QAChC,IAAId,qBAAqBe,QAAQX,QAAQ,GAAG;YAC1C,wCAAwC;YACxCD,IAAI,CAACW,YAAY,GAAGV;YACpBA,KAAKzC,UAAU,GAAGmD;YAClBX,IAAI,CAACE,MAAM,GAAGU;YACdA,OAAOpD,UAAU,GAAG0C;YAEpBA,QAAQS;QACV,OAAO;YACL,+BAA+B;YAC/B;QACF;IACF;AACF;AAEA,SAASF,aACPT,IAAyB,EACzBC,IAAkB,EAClBS,CAAS;IAET,IAAIR,QAAQQ;IACZ,MAAMP,SAASH,KAAKG,MAAM;IAC1B,MAAMU,aAAaV,WAAW;IAC9B,MAAOD,QAAQW,WAAY;QACzB,MAAMC,YAAaZ,CAAAA,QAAQ,CAAA,IAAK,IAAI;QACpC,MAAMa,OAAOf,IAAI,CAACc,UAAU;QAC5B,MAAME,aAAaF,YAAY;QAC/B,MAAMG,QAAQjB,IAAI,CAACgB,WAAW;QAE9B,wEAAwE;QACxE,IAAInB,qBAAqBkB,MAAMd,QAAQ,GAAG;YACxC,IAAIe,aAAab,UAAUN,qBAAqBoB,OAAOF,QAAQ,GAAG;gBAChEf,IAAI,CAACE,MAAM,GAAGe;gBACdA,MAAMzD,UAAU,GAAG0C;gBACnBF,IAAI,CAACgB,WAAW,GAAGf;gBACnBA,KAAKzC,UAAU,GAAGwD;gBAElBd,QAAQc;YACV,OAAO;gBACLhB,IAAI,CAACE,MAAM,GAAGa;gBACdA,KAAKvD,UAAU,GAAG0C;gBAClBF,IAAI,CAACc,UAAU,GAAGb;gBAClBA,KAAKzC,UAAU,GAAGsD;gBAElBZ,QAAQY;YACV;QACF,OAAO,IAAIE,aAAab,UAAUN,qBAAqBoB,OAAOhB,QAAQ,GAAG;YACvED,IAAI,CAACE,MAAM,GAAGe;YACdA,MAAMzD,UAAU,GAAG0C;YACnBF,IAAI,CAACgB,WAAW,GAAGf;YACnBA,KAAKzC,UAAU,GAAGwD;YAElBd,QAAQc;QACV,OAAO;YACL,kCAAkC;YAClC;QACF;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 2405, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2411, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/prefetch.ts"], "sourcesContent": ["import { createPrefetchURL } from '../../components/app-router'\nimport { createCacheKey } from './cache-key'\nimport { schedulePrefetchTask } from './scheduler'\n\n/**\n * Entrypoint for prefetching a URL into the Segment Cache.\n * @param href - The URL to prefetch. Typically this will come from a <Link>,\n * or router.prefetch. It must be validated before we attempt to prefetch it.\n */\nexport function prefetch(href: string, nextUrl: string | null) {\n  const url = createPrefetchURL(href)\n  if (url === null) {\n    // This href should not be prefetched.\n    return\n  }\n  const cacheKey = createCacheKey(url.href, nextUrl)\n  schedulePrefetchTask(cacheKey)\n}\n"], "names": ["createPrefetchURL", "createCacheKey", "schedulePrefetchTask", "prefetch", "href", "nextUrl", "url", "cache<PERSON>ey"], "mappings": ";;;AAAA,SAASA,iBAAiB,QAAQ,8BAA6B;AAC/D,SAASC,cAAc,QAAQ,cAAa;AAC5C,SAASC,oBAAoB,QAAQ,cAAa;;;;AAO3C,SAASC,SAASC,IAAY,EAAEC,OAAsB;IAC3D,MAAMC,+LAAMN,oBAAAA,EAAkBI;IAC9B,IAAIE,QAAQ,MAAM;QAChB,sCAAsC;QACtC;IACF;IACA,MAAMC,uNAAWN,iBAAAA,EAAeK,IAAIF,IAAI,EAAEC;6MAC1CH,uBAAAA,EAAqBK;AACvB", "ignoreList": [0]}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/node_modules/next/dist/src/client/components/segment-cache/navigation.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport type {\n  CacheNode,\n  LoadingModuleData,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\nimport { fetchServerResponse } from '../router-reducer/fetch-server-response'\nimport {\n  updateCacheNodeOnNavigation,\n  listenForDynamicRequest,\n  type Task as PPRNavigationTask,\n} from '../router-reducer/ppr-navigations'\nimport { createHrefFromUrl as createCanonicalUrl } from '../router-reducer/create-href-from-url'\nimport {\n  EntryStatus,\n  readRouteCacheEntry,\n  readSegmentCacheEntry,\n  waitForSegmentCacheEntry,\n} from './cache'\nimport type { TreePrefetch } from '../../../server/app-render/collect-segment-data'\nimport { createCacheKey } from './cache-key'\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\ntype MPANavigationResult = {\n  tag: NavigationResultTag.MPA\n  data: string\n}\n\ntype NoOpNavigationResult = {\n  tag: NavigationResultTag.NoOp\n  data: null\n}\n\ntype SuccessfulNavigationResult = {\n  tag: NavigationResultTag.Success\n  data: {\n    flightRouterState: FlightRouterState\n    cacheNode: CacheNode\n    canonicalUrl: string\n  }\n}\n\ntype AsyncNavigationResult = {\n  tag: NavigationResultTag.Async\n  data: Promise<\n    MPANavigationResult | NoOpNavigationResult | SuccessfulNavigationResult\n  >\n}\n\nexport type NavigationResult =\n  | MPANavigationResult\n  | SuccessfulNavigationResult\n  | NoOpNavigationResult\n  | AsyncNavigationResult\n\nconst noOpNavigationResult: NoOpNavigationResult = {\n  tag: NavigationResultTag.NoOp,\n  data: null,\n}\n\n/**\n * Navigate to a new URL, using the Segment Cache to construct a response.\n *\n * To allow for synchronous navigations whenever possible, this is not an async\n * function. It returns a promise only if there's no matching prefetch in\n * the cache. Otherwise it returns an immediate result and uses Suspense/RSC to\n * stream in any missing data.\n */\nexport function navigate(\n  url: URL,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState,\n  nextUrl: string | null\n): AsyncNavigationResult | SuccessfulNavigationResult | NoOpNavigationResult {\n  const now = Date.now()\n\n  const cacheKey = createCacheKey(url.href, nextUrl)\n  const route = readRouteCacheEntry(now, cacheKey)\n  if (route !== null && route.status === EntryStatus.Fulfilled) {\n    // We have a matching prefetch.\n    const snapshot = readRenderSnapshotFromCache(now, route.tree)\n    const prefetchFlightRouterState = snapshot.flightRouterState\n    const prefetchSeedData = snapshot.seedData\n    const prefetchHead = route.head\n    const isPrefetchHeadPartial = route.isHeadPartial\n    const canonicalUrl = route.canonicalUrl\n    return navigateUsingPrefetchedRouteTree(\n      url,\n      nextUrl,\n      currentCacheNode,\n      currentFlightRouterState,\n      prefetchFlightRouterState,\n      prefetchSeedData,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      canonicalUrl\n    )\n  }\n  // There's no matching prefetch for this route in the cache.\n  return {\n    tag: NavigationResultTag.Async,\n    data: navigateDynamicallyWithNoPrefetch(\n      url,\n      nextUrl,\n      currentCacheNode,\n      currentFlightRouterState\n    ),\n  }\n}\n\nfunction navigateUsingPrefetchedRouteTree(\n  url: URL,\n  nextUrl: string | null,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState,\n  prefetchFlightRouterState: FlightRouterState,\n  prefetchSeedData: CacheNodeSeedData | null,\n  prefetchHead: React.ReactNode | null,\n  isPrefetchHeadPartial: boolean,\n  canonicalUrl: string\n): SuccessfulNavigationResult | NoOpNavigationResult {\n  // Recursively construct a prefetch tree by reading from the Segment Cache. To\n  // maintain compatibility, we output the same data structures as the old\n  // prefetching implementation: FlightRouterState and CacheNodeSeedData.\n  // TODO: Eventually updateCacheNodeOnNavigation (or the equivalent) should\n  // read from the Segment Cache directly. It's only structured this way for now\n  // so we can share code with the old prefetching implementation.\n  const task = updateCacheNodeOnNavigation(\n    currentCacheNode,\n    currentFlightRouterState,\n    prefetchFlightRouterState,\n    prefetchSeedData,\n    prefetchHead,\n    isPrefetchHeadPartial\n  )\n  if (task !== null) {\n    if (task.needsDynamicRequest) {\n      const promiseForDynamicServerResponse = fetchServerResponse(url, {\n        flightRouterState: currentFlightRouterState,\n        nextUrl,\n      })\n      listenForDynamicRequest(task, promiseForDynamicServerResponse)\n    } else {\n      // The prefetched tree does not contain dynamic holes — it's\n      // fully static. We can skip the dynamic request.\n    }\n    return navigationTaskToResult(task, currentCacheNode, canonicalUrl)\n  }\n  // The server sent back an empty tree patch. There's nothing to update.\n  return noOpNavigationResult\n}\n\nfunction navigationTaskToResult(\n  task: PPRNavigationTask,\n  currentCacheNode: CacheNode,\n  canonicalUrl: string\n): SuccessfulNavigationResult {\n  const newCacheNode = task.node\n  return {\n    tag: NavigationResultTag.Success,\n    data: {\n      flightRouterState: task.route,\n      cacheNode: newCacheNode !== null ? newCacheNode : currentCacheNode,\n      canonicalUrl,\n    },\n  }\n}\n\nfunction readRenderSnapshotFromCache(\n  now: number,\n  tree: TreePrefetch\n): { flightRouterState: FlightRouterState; seedData: CacheNodeSeedData } {\n  let childRouterStates: { [parallelRouteKey: string]: FlightRouterState } = {}\n  let childSeedDatas: {\n    [parallelRouteKey: string]: CacheNodeSeedData | null\n  } = {}\n  const slots = tree.slots\n  if (slots !== null) {\n    for (const parallelRouteKey in slots) {\n      const childTree = slots[parallelRouteKey]\n      const childResult = readRenderSnapshotFromCache(now, childTree)\n      childRouterStates[parallelRouteKey] = childResult.flightRouterState\n      childSeedDatas[parallelRouteKey] = childResult.seedData\n    }\n  }\n\n  let rsc: React.ReactNode | null = null\n  let loading: LoadingModuleData | Promise<LoadingModuleData> = null\n  let isPartial: boolean = true\n\n  const segmentEntry = readSegmentCacheEntry(now, tree.path)\n  if (segmentEntry !== null) {\n    switch (segmentEntry.status) {\n      case EntryStatus.Fulfilled: {\n        // Happy path: a cache hit\n        rsc = segmentEntry.rsc\n        loading = segmentEntry.loading\n        isPartial = segmentEntry.isPartial\n        break\n      }\n      case EntryStatus.Pending: {\n        // We haven't received data for this segment yet, but there's already\n        // an in-progress request. Since it's extremely likely to arrive\n        // before the dynamic data response, we might as well use it.\n        const promiseForFulfilledEntry = waitForSegmentCacheEntry(segmentEntry)\n        rsc = promiseForFulfilledEntry.then((entry) =>\n          entry !== null ? entry.rsc : null\n        )\n        loading = promiseForFulfilledEntry.then((entry) =>\n          entry !== null ? entry.loading : null\n        )\n        // Since we don't know yet whether the segment is partial or fully\n        // static, we must assume it's partial; we can't skip the\n        // dynamic request.\n        isPartial = true\n        break\n      }\n      case EntryStatus.Rejected:\n        break\n      default: {\n        const _exhaustiveCheck: never = segmentEntry\n        break\n      }\n    }\n  }\n\n  const extra = tree.extra\n  const flightRouterStateSegment = extra[0]\n  const isRootLayout = extra[1]\n\n  return {\n    flightRouterState: [\n      flightRouterStateSegment,\n      childRouterStates,\n      null,\n      null,\n      isRootLayout,\n    ],\n    seedData: [\n      flightRouterStateSegment,\n      rsc,\n      childSeedDatas,\n      loading,\n      isPartial,\n    ],\n  }\n}\n\nasync function navigateDynamicallyWithNoPrefetch(\n  url: URL,\n  nextUrl: string | null,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState\n): Promise<\n  MPANavigationResult | SuccessfulNavigationResult | NoOpNavigationResult\n> {\n  // Runs when a navigation happens but there's no cached prefetch we can use.\n  // Don't bother to wait for a prefetch response; go straight to a full\n  // navigation that contains both static and dynamic data in a single stream.\n  // (This is unlike the old navigation implementation, which instead blocks\n  // the dynamic request until a prefetch request is received.)\n  //\n  // To avoid duplication of logic, we're going to pretend that the tree\n  // returned by the dynamic request is, in fact, a prefetch tree. Then we can\n  // use the same server response to write the actual data into the CacheNode\n  // tree. So it's the same flow as the \"happy path\" (prefetch, then\n  // navigation), except we use a single server response for both stages.\n\n  const promiseForDynamicServerResponse = fetchServerResponse(url, {\n    flightRouterState: currentFlightRouterState,\n    nextUrl,\n  })\n  const { flightData, canonicalUrl: canonicalUrlOverride } =\n    await promiseForDynamicServerResponse\n\n  // TODO: Detect if the only thing that changed was the hash, like we do in\n  // in navigateReducer\n\n  if (typeof flightData === 'string') {\n    // This is an MPA navigation.\n    const newUrl = flightData\n    return {\n      tag: NavigationResultTag.MPA,\n      data: newUrl,\n    }\n  }\n\n  // Since the response format of dynamic requests and prefetches is slightly\n  // different, we'll need to massage the data a bit. Create FlightRouterState\n  // tree that simulates what we'd receive as the result of a prefetch.\n  const prefetchFlightRouterState = simulatePrefetchTreeUsingDynamicTreePatch(\n    currentFlightRouterState,\n    flightData\n  )\n\n  // In our simulated prefetch payload, we pretend that there's no seed data\n  // nor a prefetch head.\n  const prefetchSeedData = null\n  const prefetchHead = null\n  const isPrefetchHeadPartial = true\n\n  const canonicalUrl = createCanonicalUrl(\n    canonicalUrlOverride ? canonicalUrlOverride : url\n  )\n\n  // Now we proceed exactly as we would for normal navigation.\n  const task = updateCacheNodeOnNavigation(\n    currentCacheNode,\n    currentFlightRouterState,\n    prefetchFlightRouterState,\n    prefetchSeedData,\n    prefetchHead,\n    isPrefetchHeadPartial\n  )\n  if (task !== null) {\n    if (task.needsDynamicRequest) {\n      listenForDynamicRequest(task, promiseForDynamicServerResponse)\n    } else {\n      // The prefetched tree does not contain dynamic holes — it's\n      // fully static. We can skip the dynamic request.\n    }\n    return navigationTaskToResult(task, currentCacheNode, canonicalUrl)\n  }\n  // The server sent back an empty tree patch. There's nothing to update.\n  return noOpNavigationResult\n}\n\nfunction simulatePrefetchTreeUsingDynamicTreePatch(\n  currentTree: FlightRouterState,\n  flightData: Array<NormalizedFlightData>\n): FlightRouterState {\n  // Takes the current FlightRouterState and applies the router state patch\n  // received from the server, to create a full FlightRouterState tree that we\n  // can pretend was returned by a prefetch.\n  //\n  // (It sounds similar to what applyRouterStatePatch does, but it doesn't need\n  // to handle stuff like interception routes or diffing since that will be\n  // handled later.)\n  let baseTree = currentTree\n  for (const { segmentPath, tree: treePatch } of flightData) {\n    // If the server sends us multiple tree patches, we only need to clone the\n    // base tree when applying the first patch. After the first patch, we can\n    // apply the remaining patches in place without copying.\n    const canMutateInPlace = baseTree !== currentTree\n    baseTree = simulatePrefetchTreeUsingDynamicTreePatchImpl(\n      baseTree,\n      treePatch,\n      segmentPath,\n      canMutateInPlace,\n      0\n    )\n  }\n\n  return baseTree\n}\n\nfunction simulatePrefetchTreeUsingDynamicTreePatchImpl(\n  baseRouterState: FlightRouterState,\n  patch: FlightRouterState,\n  segmentPath: FlightSegmentPath,\n  canMutateInPlace: boolean,\n  index: number\n) {\n  if (index === segmentPath.length) {\n    // We reached the part of the tree that we need to patch.\n    return patch\n  }\n\n  // segmentPath represents the parent path of subtree. It's a repeating\n  // pattern of parallel route key and segment:\n  //\n  //   [string, Segment, string, Segment, string, Segment, ...]\n  //\n  // This path tells us which part of the base tree to apply the tree patch.\n  //\n  // NOTE: In the case of a fully dynamic request with no prefetch, we receive\n  // the FlightRouterState patch in the same request as the dynamic data.\n  // Therefore we don't need to worry about diffing the segment values; we can\n  // assume the server sent us a correct result.\n  const updatedParallelRouteKey: string = segmentPath[index]\n  // const segment: Segment = segmentPath[index + 1] <-- Not used, see note above\n\n  const baseChildren = baseRouterState[1]\n  const newChildren: { [parallelRouteKey: string]: FlightRouterState } = {}\n  for (const parallelRouteKey in baseChildren) {\n    if (parallelRouteKey === updatedParallelRouteKey) {\n      const childBaseRouterState = baseChildren[parallelRouteKey]\n      newChildren[parallelRouteKey] =\n        simulatePrefetchTreeUsingDynamicTreePatchImpl(\n          childBaseRouterState,\n          patch,\n          segmentPath,\n          canMutateInPlace,\n          // Advance the index by two and keep cloning until we reach\n          // the end of the segment path.\n          index + 2\n        )\n    } else {\n      // This child is not being patched. Copy it over as-is.\n      newChildren[parallelRouteKey] = baseChildren[parallelRouteKey]\n    }\n  }\n\n  if (canMutateInPlace) {\n    // We can mutate the base tree in place, because the base tree is already\n    // a clone.\n    baseRouterState[1] = newChildren\n    return baseRouterState\n  }\n\n  // Clone all the fields except the children.\n  //\n  // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n  // confirm whether we need to copy all of these fields. Not sure the server\n  // ever sends, e.g. the refetch marker.\n  const clone: FlightRouterState = [baseRouterState[0], newChildren]\n  if (2 in baseRouterState) {\n    clone[2] = baseRouterState[2]\n  }\n  if (3 in baseRouterState) {\n    clone[3] = baseRouterState[3]\n  }\n  if (4 in baseRouterState) {\n    clone[4] = baseRouterState[4]\n  }\n  return clone\n}\n"], "names": ["fetchServerResponse", "updateCacheNodeOnNavigation", "listenForDynamicRequest", "createHrefFromUrl", "createCanonicalUrl", "EntryStatus", "readRouteCacheEntry", "readSegmentCacheEntry", "waitForSegmentCacheEntry", "createCacheKey", "NavigationResultTag", "noOpNavigationResult", "tag", "data", "navigate", "url", "currentCacheNode", "currentFlightRouterState", "nextUrl", "now", "Date", "cache<PERSON>ey", "href", "route", "status", "Fulfilled", "snapshot", "readRenderSnapshotFromCache", "tree", "prefetchFlightRouterState", "flightRouterState", "prefetchSeedData", "seedData", "prefetchHead", "head", "isPrefetchHeadPartial", "isHeadPartial", "canonicalUrl", "navigateUsingPrefetchedRouteTree", "navigateDynamicallyWithNoPrefetch", "task", "needsDynamicRequest", "promiseForDynamicServerResponse", "navigationTaskToResult", "newCacheNode", "node", "cacheNode", "childRouterStates", "childSeedDatas", "slots", "parallelRouteKey", "childTree", "childResult", "rsc", "loading", "isPartial", "segmentEntry", "path", "Pending", "promiseForFulfilledEntry", "then", "entry", "Rejected", "_exhaustiveCheck", "extra", "flightRouterStateSegment", "isRootLayout", "flightData", "canonicalUrlOverride", "newUrl", "simulatePrefetchTreeUsingDynamicTreePatch", "currentTree", "baseTree", "segmentPath", "treePatch", "canMutateInPlace", "simulatePrefetchTreeUsingDynamicTreePatchImpl", "baseRouterState", "patch", "index", "length", "updatedParallelRouteKey", "baseChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childBaseRouterState", "clone"], "mappings": ";;;;AAUA,SAASA,mBAAmB,QAAQ,0CAAyC;AAC7E,SACEC,2BAA2B,EAC3BC,uBAAuB,QAElB,oCAAmC;AAC1C,SAASC,qBAAqBC,kBAAkB,QAAQ,yCAAwC;AAChG,SACEC,WAAW,EACXC,mBAAmB,EACnBC,qBAAqB,EACrBC,wBAAwB,QACnB,UAAS;AAEhB,SAASC,cAAc,QAAQ,cAAa;;;;;;AAErC,IAAWC,sBAAAA,WAAAA,GAAAA,SAAAA,mBAAAA;;;;;WAAAA;MAKjB;AAkCD,MAAMC,uBAA6C;IACjDC,GAAG,EAAA;IACHC,MAAM;AACR;AAUO,SAASC,SACdC,GAAQ,EACRC,gBAA2B,EAC3BC,wBAA2C,EAC3CC,OAAsB;IAEtB,MAAMC,MAAMC,KAAKD,GAAG;IAEpB,MAAME,uNAAWZ,iBAAAA,EAAeM,IAAIO,IAAI,EAAEJ;IAC1C,MAAMK,4MAAQjB,uBAAAA,EAAoBa,KAAKE;IACvC,IAAIE,UAAU,QAAQA,MAAMC,MAAM,sMAAKnB,cAAAA,CAAYoB,SAAS,EAAE;QAC5D,+BAA+B;QAC/B,MAAMC,WAAWC,4BAA4BR,KAAKI,MAAMK,IAAI;QAC5D,MAAMC,4BAA4BH,SAASI,iBAAiB;QAC5D,MAAMC,mBAAmBL,SAASM,QAAQ;QAC1C,MAAMC,eAAeV,MAAMW,IAAI;QAC/B,MAAMC,wBAAwBZ,MAAMa,aAAa;QACjD,MAAMC,eAAed,MAAMc,YAAY;QACvC,OAAOC,iCACLvB,KACAG,SACAF,kBACAC,0BACAY,2BACAE,kBACAE,cACAE,uBACAE;IAEJ;IACA,4DAA4D;IAC5D,OAAO;QACLzB,GAAG,EAAA;QACHC,MAAM0B,kCACJxB,KACAG,SACAF,kBACAC;IAEJ;AACF;AAEA,SAASqB,iCACPvB,GAAQ,EACRG,OAAsB,EACtBF,gBAA2B,EAC3BC,wBAA2C,EAC3CY,yBAA4C,EAC5CE,gBAA0C,EAC1CE,YAAoC,EACpCE,qBAA8B,EAC9BE,YAAoB;IAEpB,8EAA8E;IAC9E,wEAAwE;IACxE,uEAAuE;IACvE,0EAA0E;IAC1E,8EAA8E;IAC9E,gEAAgE;IAChE,MAAMG,OAAOvC,iPAAAA,EACXe,kBACAC,0BACAY,2BACAE,kBACAE,cACAE;IAEF,IAAIK,SAAS,MAAM;QACjB,IAAIA,KAAKC,mBAAmB,EAAE;YAC5B,MAAMC,8PAAkC1C,sBAAAA,EAAoBe,KAAK;gBAC/De,mBAAmBb;gBACnBC;YACF;YACAhB,6OAAAA,EAAwBsC,MAAME;QAChC,OAAO;QACL,4DAA4D;QAC5D,iDAAiD;QACnD;QACA,OAAOC,uBAAuBH,MAAMxB,kBAAkBqB;IACxD;IACA,uEAAuE;IACvE,OAAO1B;AACT;AAEA,SAASgC,uBACPH,IAAuB,EACvBxB,gBAA2B,EAC3BqB,YAAoB;IAEpB,MAAMO,eAAeJ,KAAKK,IAAI;IAC9B,OAAO;QACLjC,GAAG,EAAA;QACHC,MAAM;YACJiB,mBAAmBU,KAAKjB,KAAK;YAC7BuB,WAAWF,iBAAiB,OAAOA,eAAe5B;YAClDqB;QACF;IACF;AACF;AAEA,SAASV,4BACPR,GAAW,EACXS,IAAkB;IAElB,IAAImB,oBAAuE,CAAC;IAC5E,IAAIC,iBAEA,CAAC;IACL,MAAMC,QAAQrB,KAAKqB,KAAK;IACxB,IAAIA,UAAU,MAAM;QAClB,IAAK,MAAMC,oBAAoBD,MAAO;YACpC,MAAME,YAAYF,KAAK,CAACC,iBAAiB;YACzC,MAAME,cAAczB,4BAA4BR,KAAKgC;YACrDJ,iBAAiB,CAACG,iBAAiB,GAAGE,YAAYtB,iBAAiB;YACnEkB,cAAc,CAACE,iBAAiB,GAAGE,YAAYpB,QAAQ;QACzD;IACF;IAEA,IAAIqB,MAA8B;IAClC,IAAIC,UAA0D;IAC9D,IAAIC,YAAqB;IAEzB,MAAMC,oNAAejD,wBAAAA,EAAsBY,KAAKS,KAAK6B,IAAI;IACzD,IAAID,iBAAiB,MAAM;QACzB,OAAQA,aAAahC,MAAM;YACzB,sMAAKnB,cAAAA,CAAYoB,SAAS;gBAAE;oBAC1B,0BAA0B;oBAC1B4B,MAAMG,aAAaH,GAAG;oBACtBC,UAAUE,aAAaF,OAAO;oBAC9BC,YAAYC,aAAaD,SAAS;oBAClC;gBACF;YACA,sMAAKlD,cAAAA,CAAYqD,OAAO;gBAAE;oBACxB,qEAAqE;oBACrE,gEAAgE;oBAChE,6DAA6D;oBAC7D,MAAMC,gOAA2BnD,2BAAAA,EAAyBgD;oBAC1DH,MAAMM,yBAAyBC,IAAI,CAAC,CAACC,QACnCA,UAAU,OAAOA,MAAMR,GAAG,GAAG;oBAE/BC,UAAUK,yBAAyBC,IAAI,CAAC,CAACC,QACvCA,UAAU,OAAOA,MAAMP,OAAO,GAAG;oBAEnC,kEAAkE;oBAClE,yDAAyD;oBACzD,mBAAmB;oBACnBC,YAAY;oBACZ;gBACF;YACA,sMAAKlD,cAAAA,CAAYyD,QAAQ;gBACvB;YACF;gBAAS;oBACP,MAAMC,mBAA0BP;oBAChC;gBACF;QACF;IACF;IAEA,MAAMQ,QAAQpC,KAAKoC,KAAK;IACxB,MAAMC,2BAA2BD,KAAK,CAAC,EAAE;IACzC,MAAME,eAAeF,KAAK,CAAC,EAAE;IAE7B,OAAO;QACLlC,mBAAmB;YACjBmC;YACAlB;YACA;YACA;YACAmB;SACD;QACDlC,UAAU;YACRiC;YACAZ;YACAL;YACAM;YACAC;SACD;IACH;AACF;AAEA,eAAehB,kCACbxB,GAAQ,EACRG,OAAsB,EACtBF,gBAA2B,EAC3BC,wBAA2C;IAI3C,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,6DAA6D;IAC7D,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,2EAA2E;IAC3E,kEAAkE;IAClE,uEAAuE;IAEvE,MAAMyB,8PAAkC1C,sBAAAA,EAAoBe,KAAK;QAC/De,mBAAmBb;QACnBC;IACF;IACA,MAAM,EAAEiD,UAAU,EAAE9B,cAAc+B,oBAAoB,EAAE,GACtD,MAAM1B;IAER,0EAA0E;IAC1E,qBAAqB;IAErB,IAAI,OAAOyB,eAAe,UAAU;QAClC,6BAA6B;QAC7B,MAAME,SAASF;QACf,OAAO;YACLvD,GAAG,EAAA;YACHC,MAAMwD;QACR;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,qEAAqE;IACrE,MAAMxC,4BAA4ByC,0CAChCrD,0BACAkD;IAGF,0EAA0E;IAC1E,uBAAuB;IACvB,MAAMpC,mBAAmB;IACzB,MAAME,eAAe;IACrB,MAAME,wBAAwB;IAE9B,MAAME,6OAAejC,oBAAAA,EACnBgE,uBAAuBA,uBAAuBrD;IAGhD,4DAA4D;IAC5D,MAAMyB,0NAAOvC,8BAAAA,EACXe,kBACAC,0BACAY,2BACAE,kBACAE,cACAE;IAEF,IAAIK,SAAS,MAAM;QACjB,IAAIA,KAAKC,mBAAmB,EAAE;+NAC5BvC,0BAAAA,EAAwBsC,MAAME;QAChC,OAAO;QACL,4DAA4D;QAC5D,iDAAiD;QACnD;QACA,OAAOC,uBAAuBH,MAAMxB,kBAAkBqB;IACxD;IACA,uEAAuE;IACvE,OAAO1B;AACT;AAEA,SAAS2D,0CACPC,WAA8B,EAC9BJ,UAAuC;IAEvC,yEAAyE;IACzE,4EAA4E;IAC5E,0CAA0C;IAC1C,EAAE;IACF,6EAA6E;IAC7E,yEAAyE;IACzE,kBAAkB;IAClB,IAAIK,WAAWD;IACf,KAAK,MAAM,EAAEE,WAAW,EAAE7C,MAAM8C,SAAS,EAAE,IAAIP,WAAY;QACzD,0EAA0E;QAC1E,yEAAyE;QACzE,wDAAwD;QACxD,MAAMQ,mBAAmBH,aAAaD;QACtCC,WAAWI,8CACTJ,UACAE,WACAD,aACAE,kBACA;IAEJ;IAEA,OAAOH;AACT;AAEA,SAASI,8CACPC,eAAkC,EAClCC,KAAwB,EACxBL,WAA8B,EAC9BE,gBAAyB,EACzBI,KAAa;IAEb,IAAIA,UAAUN,YAAYO,MAAM,EAAE;QAChC,yDAAyD;QACzD,OAAOF;IACT;IAEA,sEAAsE;IACtE,6CAA6C;IAC7C,EAAE;IACF,6DAA6D;IAC7D,EAAE;IACF,0EAA0E;IAC1E,EAAE;IACF,4EAA4E;IAC5E,uEAAuE;IACvE,4EAA4E;IAC5E,8CAA8C;IAC9C,MAAMG,0BAAkCR,WAAW,CAACM,MAAM;IAC1D,+EAA+E;IAE/E,MAAMG,eAAeL,eAAe,CAAC,EAAE;IACvC,MAAMM,cAAiE,CAAC;IACxE,IAAK,MAAMjC,oBAAoBgC,aAAc;QAC3C,IAAIhC,qBAAqB+B,yBAAyB;YAChD,MAAMG,uBAAuBF,YAAY,CAAChC,iBAAiB;YAC3DiC,WAAW,CAACjC,iBAAiB,GAC3B0B,8CACEQ,sBACAN,OACAL,aACAE,kBACA,AACA,+BAA+B,4BAD4B;YAE3DI,QAAQ;QAEd,OAAO;YACL,uDAAuD;YACvDI,WAAW,CAACjC,iBAAiB,GAAGgC,YAAY,CAAChC,iBAAiB;QAChE;IACF;IAEA,IAAIyB,kBAAkB;QACpB,yEAAyE;QACzE,WAAW;QACXE,eAAe,CAAC,EAAE,GAAGM;QACrB,OAAON;IACT;IAEA,4CAA4C;IAC5C,EAAE;IACF,4EAA4E;IAC5E,2EAA2E;IAC3E,uCAAuC;IACvC,MAAMQ,QAA2B;QAACR,eAAe,CAAC,EAAE;QAAEM;KAAY;IAClE,IAAI,KAAKN,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,OAAOQ;AACT", "ignoreList": [0]}}, {"offset": {"line": 2709, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}